SELECT 
    table_name,
    source_count,
    target_count,
    ABS(source_count - target_count) as difference,
    CASE 
        WHEN source_count = target_count THEN 'IDENTICAL'
        ELSE 'DIFFERENT'
    END as status
FROM (
    SELECT 'tbl_attachment' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_attachment) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_attachment) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_mdchapter_menuid' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_mdchapter_menuid) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_mdchapter_menuid) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_mdchapter' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_mdchapter) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_mdchapter) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_eclinichistory' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_eclinichistory) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_eclinichistory) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_esign_account' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_esign_account) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_esign_account) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_visit_set' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_visit_set) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_visit_set) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_visit_table' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_visit_table) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_visit_table) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_crf_visit' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_crf_visit) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_crf_visit) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_crf_visit_table' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_crf_visit_table) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_crf_visit_table) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_crf_table_pctpt' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_crf_table_pctpt) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_crf_table_pctpt) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_log_event' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_log_event) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_log_event) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_systemcode' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_systemcode) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_systemcode) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_xmgt' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_xmgt) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_xmgt) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_partner' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_partner) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_partner) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_proj_plan' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_proj_plan) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_proj_plan) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_yqsq' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_yqsq) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_yqsq) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_risk_management' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_risk_management) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_risk_management) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_qc' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_qc) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_qc) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_schedule' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_schedule) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_schedule) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_crf_design' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_crf_design) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_crf_design) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_manual_rev_plan' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_manual_rev_plan) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_manual_rev_plan) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_coding_ver_update1' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_coding_ver_update1) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_coding_ver_update1) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_im' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_im) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_im) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_edm_account' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_edm_account) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_edm_account) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_ext_test_data' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_ext_test_data) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_ext_test_data) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_prot_workflow' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_prot_workflow) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_prot_workflow) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_edcblind' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_edcblind) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_edcblind) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dm_report' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_dm_report) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dm_report) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_fahsjkxd' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_fahsjkxd) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_fahsjkxd) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dm_disc_log' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_dm_disc_log) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dm_disc_log) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_xtsj' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_xtsj) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_xtsj) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_interim_analyplan' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_interim_analyplan) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_interim_analyplan) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_non_outboard_data_manager' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_non_outboard_data_manager) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_non_outboard_data_manager) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_external_data_dta_check' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_external_data_dta_check) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_external_data_dta_check) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_sas_ver_pro' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_sas_ver_pro) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_sas_ver_pro) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_sascheck' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_sascheck) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_sascheck) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_blindedtransfer' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_blindedtransfer) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_blindedtransfer) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_sas_check_run' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_sas_check_run) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_sas_check_run) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_project_oreport' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_project_oreport) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_project_oreport) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_blinding_export' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_blinding_export) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_blinding_export) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_final_manage_report' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_final_manage_report) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_final_manage_report) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_db_archive' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_db_archive) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_db_archive) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_db_unlock_application' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_db_unlock_application) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_db_unlock_application) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_verification_pr_data' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_verification_pr_data) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_verification_pr_data) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_verification_report_data' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_verification_report_data) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_verification_report_data) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_db_lock_data_qc' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_db_lock_data_qc) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_db_lock_data_qc) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_ext_data_final' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_ext_data_final) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_ext_data_final) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_zzjg' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_zzjg) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_zzjg) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_ryjl' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_ryjl) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_ryjl) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_employe_probation_report' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_employe_probation_report) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_employe_probation_report) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_resume' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_resume) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_resume) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_employe_work_experience' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_employe_work_experience) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_employe_work_experience) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_employe_growth' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_employe_growth) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_employe_growth) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_rewards' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_rewards) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_rewards) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_pxjl' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_pxjl) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_pxjl) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_pxjlmx' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_pxjlmx) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_pxjlmx) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_jsxx' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_jsxx) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_jsxx) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_ryjbzl' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_ryjbzl) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_ryjbzl) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_jdhzlb' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_jdhzlb) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_jdhzlb) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_sjglzd' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_sjglzd) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_sjglzd) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_pxzd' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_pxzd) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_pxzd) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dm_template_file' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_dm_template_file) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dm_template_file) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_jlmb' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_jlmb) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_jlmb) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_yjzwjlbzd' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_yjzwjlbzd) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_yjzwjlbzd) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_lcwjlbsjg' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_lcwjlbsjg) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_lcwjlbsjg) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_mksjg' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_mksjg) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_mksjg) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_help' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_help) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_help) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dta_ds' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_dta_ds) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dta_ds) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dta_ds_item_template' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_dta_ds_item_template) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dta_ds_item_template) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dta_ds_item' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_dta_ds_item) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dta_ds_item) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dta_ds_template' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_dta_ds_template) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dta_ds_template) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_db_lock_item_tmpl' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_db_lock_item_tmpl) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_db_lock_item_tmpl) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_user_resp_tmpl' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_user_resp_tmpl) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_user_resp_tmpl) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_new_post' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_new_post) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_new_post) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_new_post1' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_new_post1) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_new_post1) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_dm_hour_sum' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_dm_hour_sum) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_dm_hour_sum) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_delete_study_listy' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_delete_study_listy) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_delete_study_listy) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_roster_lead' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_roster_lead) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_roster_lead) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_employee_code' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_employee_code) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_employee_code) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_oa' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_oa) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_oa) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_holiday' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_holiday) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_holiday) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_temp1' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_temp1) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_temp1) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_jyjl2' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_jyjl2) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_jyjl2) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_dmp_update' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_dmp_update) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_dmp_update) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_for_diary' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_for_diary) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_for_diary) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_diary_task_template' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_diary_task_template) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_diary_task_template) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_employee_manager_map' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_employee_manager_map) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_employee_manager_map) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_email_send_log' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_email_send_log) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_email_send_log) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dm_guide' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_dm_guide) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dm_guide) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_standard_email' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_standard_email) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_standard_email) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_data_modify_track' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_data_modify_track) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_data_modify_track) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_innovation_team' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_innovation_team) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_innovation_team) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dvs_edm_plan' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_dvs_edm_plan) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dvs_edm_plan) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_ecrf' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_ecrf) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_ecrf) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_ec' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_ec) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_ec) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_rtmstest' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_rtmstest) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_rtmstest) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_training' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_training) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_training) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dmrp_map_test' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_dmrp_map_test) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dmrp_map_test) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_job_duty' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_job_duty) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_job_duty) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_content_text' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_content_text) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_content_text) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_gzzd' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_gzzd) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_gzzd) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_tzhgg' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_tzhgg) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_tzhgg) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_sop' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_sop) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_sop) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_mb' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_mb) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_mb) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_xxyjl' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_xxyjl) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_xxyjl) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_fgzn' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_fgzn) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_fgzn) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_edc_support_agreement' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_edc_support_agreement) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_edc_support_agreement) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_sjgl' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_sjgl) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_sjgl) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dbworkfile' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_dbworkfile) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dbworkfile) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dbworkfile_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_dbworkfile_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dbworkfile_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dbjsfile' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_dbjsfile) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dbjsfile) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dbjsfile_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_dbjsfile_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dbjsfile_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_regularattachment' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_regularattachment) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_regularattachment) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_db_backup' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_db_backup) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_db_backup) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_qa_management' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_qa_management) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_qa_management) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_db_qc' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_db_qc) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_db_qc) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_ext_data_load' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_ext_data_load) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_ext_data_load) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_final_report' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_final_report) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_final_report) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_bb' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_bb) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_bb) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_yhxq' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_yhxq) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_yhxq) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_gngg' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_gngg) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_gngg) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_yzbg' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_yzbg) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_yzbg) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_sc' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_sc) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_sc) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_sxtz' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_sxtz) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_sxtz) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_fwq' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_fwq) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_fwq) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_xthf' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_xthf) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_xthf) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_znhfgc' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_znhfgc) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_znhfgc) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_edc_system_trace' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_edc_system_trace) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_edc_system_trace) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_system_verification_list' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_system_verification_list) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_system_verification_list) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_verification_list_dict' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_verification_list_dict) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_verification_list_dict) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_verification_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_verification_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_verification_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_tool_verification' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_tool_verification) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_tool_verification) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_tool_verification_list' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_tool_verification_list) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_tool_verification_list) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_manual_rev_prog_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_manual_rev_prog_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_manual_rev_prog_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_xm_view_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_xm_view_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_xm_view_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_crf_design_history' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_crf_design_history) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_crf_design_history) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_ecrf_build_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_ecrf_build_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_ecrf_build_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_edit_check_plan_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_edit_check_plan_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_edit_check_plan_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_edit_check_prog_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_edit_check_prog_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_edit_check_prog_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_uta_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_uta_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_uta_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_pv_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_pv_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_pv_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_edc_accountd_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_edc_accountd_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_edc_accountd_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_prod_report_dt' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_prod_report_dt) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_prod_report_dt) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dmp_gen_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_dmp_gen_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dmp_gen_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_ecrf_build1_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_ecrf_build1_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_ecrf_build1_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_sjk_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_sjk_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_sjk_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_ecrf_build2_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_ecrf_build2_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_ecrf_build2_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_rand_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_rand_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_rand_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_rand_prohis' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_rand_prohis) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_rand_prohis) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_final_manage_report_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_final_manage_report_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_final_manage_report_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_esignature_statement_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_esignature_statement_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_esignature_statement_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_lnr_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_lnr_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_lnr_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_edc_permission_his_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_edc_permission_his_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_edc_permission_his_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_q_management_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_q_management_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_q_management_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_drugnum_create_his' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_drugnum_create_his) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_drugnum_create_his) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_weekly_summary_list' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_weekly_summary_list) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_weekly_summary_list) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_yzj' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_yzj) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_yzj) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_monthly_summary_list' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_monthly_summary_list) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_monthly_summary_list) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_daily_monthly_sum' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_daily_monthly_sum) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_daily_monthly_sum) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_monthly_sort_dict' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_monthly_sort_dict) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_monthly_sort_dict) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_studyid_oa_map' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_studyid_oa_map) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_studyid_oa_map) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_phase_daily_sum' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_phase_daily_sum) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_phase_daily_sum) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_rtsmsqcshj' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_rtsmsqcshj) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_rtsmsqcshj) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_drug_blind' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_drug_blind) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_drug_blind) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_drug_blindtrain' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_drug_blindtrain) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_drug_blindtrain) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_rstm_account' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_rstm_account) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_rstm_account) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_jjjm' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_jjjm) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_jjjm) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_xmzqjm' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_xmzqjm) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_xmzqjm) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_drugnum_apply' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_drugnum_apply) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_drugnum_apply) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_tconfigure_reoort' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_tconfigure_reoort) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_tconfigure_reoort) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_rconfigure_reoort' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_rconfigure_reoort) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_rconfigure_reoort) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_rtsm_plan_version' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_rtsm_plan_version) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_rtsm_plan_version) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_rtsm_pg_version' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_rtsm_pg_version) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_rtsm_pg_version) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_rtsm_sjk' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_rtsm_sjk) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_rtsm_sjk) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_rtsm_jc' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_rtsm_jc) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_rtsm_jc) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_site_trainee' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_site_trainee) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_site_trainee) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_site_mmcai' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_site_mmcai) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_site_mmcai) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_site_train' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_site_train) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_site_train) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_item_control' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_item_control) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_item_control) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_tpaper_control' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_tpaper_control) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_tpaper_control) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_detailed_item' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_detailed_item) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_detailed_item) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_exam_plan' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_exam_plan) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_exam_plan) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_exam_user' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_exam_user) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_exam_user) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_scores_info' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_scores_info) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_scores_info) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_detailed_item1' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_detailed_item1) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_detailed_item1) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_mmcai_control' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_mmcai_control) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_mmcai_control) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_course_control' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_course_control) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_course_control) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_trainee' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_trainee) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_trainee) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_detailed_mmcai' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_detailed_mmcai) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_detailed_mmcai) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_pxjl1' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_pxjl1) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_pxjl1) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_llscb' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_llscb) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_llscb) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_pxsm' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_pxsm) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_pxsm) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_zzxx' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_zzxx) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_zzxx) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_train_credential' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_train_credential) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_train_credential) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_credential' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_credential) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_credential) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dm_quantity' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_dm_quantity) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dm_quantity) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_pubhis' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_pubhis) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_pubhis) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_contable' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_contable) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_contable) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_file' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_file) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_file) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_tableid' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_tableid) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_tableid) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_datamerge1' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_datamerge1) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_datamerge1) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_datamergelog' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_datamergelog) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_datamergelog) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_log_event_2021_06' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_log_event_2021_06) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_log_event_2021_06) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_log_event_2022_05' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_log_event_2022_05) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_log_event_2022_05) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_log_event_2022_07' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_log_event_2022_07) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_log_event_2022_07) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_log_event_2023_06' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_log_event_2023_06) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_log_event_2023_06) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_crocont' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_crocont) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_crocont) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_task' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_task) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_task) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_edcblindtransfer' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_edcblindtransfer) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_edcblindtransfer) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_xmsjjhzd' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_xmsjjhzd) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_xmsjjhzd) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_progress_template' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_progress_template) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_progress_template) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_special_working_day' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_special_working_day) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_special_working_day) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_work_subject' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_work_subject) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_work_subject) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_remotebuttontask' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_remotebuttontask) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_remotebuttontask) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dmrp_map' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_dmrp_map) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dmrp_map) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_edc_validation_report' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_edc_validation_report) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_edc_validation_report) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_edc_v_name' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_edc_v_name) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_edc_v_name) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_edc_user_manual' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_edc_user_manual) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_edc_user_manual) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_db_lock_appr' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_db_lock_appr) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_db_lock_appr) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_xtwt' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_xtwt) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_xtwt) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_system_verification' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_system_verification) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_system_verification) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_rtsm_report_version' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_rtsm_report_version) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_rtsm_report_version) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_ctgzb' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_ctgzb) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_ctgzb) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_langmanage' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_langmanage) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_langmanage) as target_count
    FROM dual
)
WHERE source_count != target_count
ORDER BY ABS(source_count - target_count) DESC;
