WITH minio_tbl AS (
SELECT
	*,
	toString ( sipHash64 ( * ) ) AS lab_data_id 
FROM
	s3 ( #{entity.filePath}, 'minioadmin', 'minioadmin', 'CSVWithNames')
	),
	temp1 AS ( SELECT * FROM minio_tbl ),
	result AS (
SELECT
	mysql_lab_review.*,
	temp1.* 
FROM
	(
SELECT
	*,
	multiIf ( operate_type & lt; 1, '取消审核', operate_type & gt; 0, '审核', '未知' ) AS operate_type_new 
FROM
	(
SELECT
	lab_data_id,
	operate_user,
	operate_type,
	toString ( create_time, 'Asia/Shanghai' ) AS create_time 
FROM
	mysql ( '***********:3306', 'dm_platform', 'dm_operation_records', 'susaruser', 'Hr@db0316' ) 
WHERE
	( file_name = ? ) 
	AND ( ( operate_type = 0 ) OR ( operate_type = 1 ) ) 
	) AS t1 
	) AS mysql_lab_review
	LEFT JOIN temp1 ON temp1.lab_data_id = mysql_lab_review.lab_data_id 
	) 
	SELECT
	* 
FROM
	result