%global system studyid root version dir lang m_minio repdat;
proc datasets lib=work nolist kill; quit;%let dir=%sysfunc(getoption(work));

x "cd &dir";x "mkdir ./pgm";x 'mc find minios3/pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';
%include "&dir/pgm/*.sas";
option mprint symbolgen validvarname=v7;

%let m_minio=minios3;
%let repdat=&sysdate.;

%let studyid=&studyid.;

%let lang="&lang.";
/*%put &lang.;*/


%m_post2s3(studyid=&studyid.,env=uat);
%m_gets3data(studyid=&studyid.,data=@,env=uat);

option mprint symbolgen validvarname=v7;


x "mc find &m_minio./sascomp/json --name ""&studyid._rtsmedc.json"" | xargs -I{} mc cp {} ./doc/";
filename y "&root./doc/&studyid._rtsmedc.json";
libname jsonrec json fileref=y;
proc copy in=jsonrec out=work;
run;

data _null_;
	set alldata;
	if  P1='parm' then  call symputx("parm",tranwrd(tranwrd(tranwrd(value,"，",","),"<",""),">",""));
	if  P1='sascode' then  do;
		if value^='' then call symputx("sascode",value);
		else  call symputx("sascode","");
	end;
run;
%put  parm="&parm."   sascode="&sascode." ;


option mprint symbolgen validvarname=v7;
&sascode.
%M_std_con_random(isminio=Y,&parm.);