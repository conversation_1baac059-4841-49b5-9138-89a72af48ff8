INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (1, 'SHR-A2102-I-102', 'SHR-A2102', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'An Open-Label, Single-Arm, Multi-Center Phase I Clinical Study to Evaluate the Safety, Tolerability, Efficacy and Pharmacokinetics of SHR-A2102 in Patients With Advanced Solid Tumors');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (2, 'UAP-20220302', 'UAP-20220302', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'UAP-20220302');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (3, 'SHR-7367-I-101', 'SHR-7367', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'SHR-7367注射液（FAP/CD40双抗）在晚期恶性肿瘤患者中的安全性、耐受性、药代动力学和疗效的I期临床研究');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (4, 'E417', 'UAP-20220302', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'E4017R300集群全系统验证');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (5, 'HR_A1008', 'SHR7280', '2024-03-18 14:49:35', '2024-03-13 16:44:11', '多中心、随机、双盲、安慰剂平行对照评价SHR4640片联合非布司他片治疗经非布司他治疗未达标的原发性高尿酸血症受试者的降尿酸疗效与安全性研究');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (6, 'SHR-6390-III-301', 'SHR6390', '2024-03-13 16:44:11', '2024-03-13 16:44:11', '用于SUSAR报告平台内部人员测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (7, 'teststudy', 'EDC-20220302', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'test study');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (8, 'HR_TEST_HOME1', 'SHR1020', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'EDC封面数据统计测试1');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (9, 'SHR4640-301', 'SHR4640', '2024-03-13 16:44:11', '2024-03-13 16:44:11', '用于SUSAR报告平台内部人员测试01，多中心添加研究者');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (10, 'Account test', 'SHR1020', '2024-03-13 16:44:11', '2024-03-13 16:44:11', '在线账号申请');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (11, 'SHR-6390-209', 'SHR6390', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'SHR-6390-209');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (12, 'RTSM_A001_WJ', 'SHR1020', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'RTSM_A001_WJ');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (13, 'EDC-UAP TEST', 'HR17031', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'EDC UAP测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (14, 'SHR-A1811-II-202', 'SHR-A1811、SHR-1316', '2024-03-13 16:44:11', '2024-03-13 16:44:11', '用于SUSAR报告平台内部人员测试02，联合用药');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (15, 'SHR1020-I-201', 'SHR1020', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'SHR1020联合测试-CDTMS');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (16, 'EDC_001_CHN', 'HR17031', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'EDC_001 整体流程测试(中文)');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (17, 'HRS-1358-I-101', 'HRS-1358', '2024-03-13 16:44:11', '2024-03-13 16:44:11', '用于SUSAR报告平台PV&CTA人员测试用001研究项目');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (18, 'EDC_002_CHN', 'HR17031', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'EDC_002 整体流程测试(中文)');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (19, 'EDC_004_EN', 'HR17031', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'EDC_004 English Study');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (20, 'EDC_005_EN', 'HR17031', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'EDC_005 English Study');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (21, 'HRS9531-102', 'HRS9531', '2024-03-13 16:44:11', '2024-03-13 16:44:11', '用于SUSAR报告平台PV&CTA人员测试用002研究项目');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (22, 'RTSM_001_CHN', 'HR17031', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'RTMS_001 整体流程测试(中文)');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (23, 'RTSM_002_CHN', 'HR17031', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'RTMS_002 整体流程测试(中文)');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (24, 'RTSM_004_EN', 'HR17031', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'RTSM_004 English Study');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (25, 'INS068-301', 'INS068', '2024-03-13 16:44:11', '2024-03-13 16:44:11', '用于SUSAR报告平台PV&CTA人员测试用003研究项目');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (26, 'RTSM_005_EN', 'HR17031', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'RTSM_005 English Study');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (27, 'UAP_TEST_001', 'SHR1020', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'UAP_TEST_001');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (28, 'SHR-A1811-II-203', 'SHR-A1811,SHR-1316', '2024-03-13 16:44:11', '2024-03-13 16:44:11', '用于SUSAR报告平台内部人员测试04，联合用药');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (29, 'CDTMS_TEST_002', 'SHR1210', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'CDTMS_TEST_002 Study');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (30, 'Test_Yan', 'HR19034', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'test 123');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (31, 'SHR-1701-209', 'SHR-1701,HRS-1358', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'SUSAR平台测试（PV）');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (32, 'HR_A007_01', 'HR070803', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'HR_A007_01阿美替尼联合SHR-1701等创新药物治疗EGFR突变的复发或晚期非小细胞肺癌开放、多中心的Ⅰb/Ⅱ期临床研究阿美替尼联合SHR-1701等创新药物治疗EGFR突变');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (33, 'SHR-1316-322', 'SHR-1316', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'SUSAR平台测试（PV）');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (34, 'EDC_TEST_005', 'SHR8554', '2024-03-13 16:44:11', '2024-03-13 16:44:11', '多中心、开放、固定序列的SHR3680对地高辛（P-gp底物），瑞舒伐他汀钙（BCRP和/或OATP1B1/1B3底物）和盐酸二甲双胍（MATE1/2-K底物）在前列腺癌患者中的药代动力学影响研究');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (35, 'SUSAR-301', 'Compound A', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (36, 'RTSM_006_EN', 'SHR8554', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'RTSM_006_EN');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (37, 'RTSM_007_EN', 'SHR8554', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'RTSM_007_EN');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (38, 'RTSM_008_CHN', 'SHR8554', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'RTSM_008_CHN');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (39, 'RTSM_009_CHN', 'SHR8554', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'RTSM_009_CHN');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (40, 'RTSM_010_CHN', 'SHR8554', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'RTSM_010_CHN');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (41, 'RTSM_011_CHN', 'SHR8554', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'RTSM_011_CHN');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (42, 'SHR-1802-II-201', 'SHR-1802', '2024-03-13 16:44:11', '2024-03-13 16:44:11', '抗LAG-3抗体SHR-1802联合卡瑞利珠单抗及苹果酸法米替尼治疗晚期实体肿瘤的剂量探索、疗效拓展的Ⅰb/Ⅱ期研究');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (43, 'RTSM_011', 'SHR8554', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'RTSM_011');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (44, 'RTSM_012', 'SHR-1802', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'RTSM_012');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (45, 'HR18034-201', 'EDCTEST', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'HR18034-201');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (46, 'Atest01', 'EDCTEST', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'Atest01(对接EDC)');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (47, 'RTSM_001', 'SHR0410', '2024-03-13 16:44:11', '2024-03-13 16:44:11', 'RTSM_001');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (48, 'CTMS-eTMF', 'HR20014,INS062', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'CTMS-eTMF测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (49, 'RTSM-TEST-0824', 'EDCTEST', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '1013测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (50, 'SHR-8068-2-201-HCC-1', 'SHR-1316(SHR-8068)', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '抗CTLA-4抗体SHR-8068联合阿得贝利单抗及贝伐珠单抗治疗晚期肝细胞癌的开放、多中心的b/期临床研究');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (51, 'SHR-8068-Ⅱ-201-HCC-tst-1104', '1104', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '(测试1111)抗CTLA-4抗体SHR-8068联合阿得贝利单抗及贝伐珠单抗治疗晚期肝细胞癌的开放、多中心的Ⅰb/Ⅱ期临床研究');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (52, '1114Ⅱ', '1114', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '1114Ⅱ');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (53, '1209', '1209', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '1209');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (54, '1213', '1213', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '1213');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (55, 'INS068-302', '1104', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'INS068-302');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (56, 'SHR-001-CMM', '1104', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'SHR-001-CMM（修改研究名称测试）');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (57, 'SUSAR-302', 'Compound A,Compound B', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (58, 'SUSAR-303', 'Compound C,Compound A', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (59, 'SUSAR-304', 'Compound A,Compound  C', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (60, 'SUSAR-305', 'SHR0302,SHR6390', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (61, 'SHR6390-306', 'SHR6390,APTN(YN968D1)', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (62, 'SUSAR-306', 'APTN(YN968D1),SHR0302', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (63, 'SUSAR-307', 'SHR4640,SHR-1802', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (64, 'SUSAR-308', 'HRS001,APTN(YN968D1)', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (65, 'HRS8427-103', 'HR18034', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'HRS8427-103');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (66, 'HR7777-101', 'HR7777', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'Test01 gzh');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (67, 'HR7777-101_EN', 'HR7777', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '中心账号申请英文测试项目');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (68, 'SHR-7367-BY', 'SHR-7367-测试', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'SHR-7367-BY测试用');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (69, 'EDC2323', 'SHR-7367-测试', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '随机3.3.1新集群新项目下发测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (70, 'EDC-001-UAP', 'RTSM330TEST', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'UAP测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (71, 'EDC-003-UAP', 'SHR-7367-测试', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'EDC-003-UAP');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (72, 'E421（1）', 'SHR-7367-测试', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'E421测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (73, 'HR7777-102', 'HR7777', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '添加pv人员登录uap测试项目');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (74, 'SHR-1707-Dummy', 'SHR-1707', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'Phase I study to evaluate the safety, tolerability, and pharmacokinetics of HRS-4642 in patients with advanced solid tumors harboring KRAS G12D mutation');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (75, '20230713', '20230713', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '20230713医学二部');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (76, '20230717', '20230717', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '20230717');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (77, '20230719-1', '20230719-1', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '20230719-1');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (78, '20230720', '20230720', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '镓[⁶⁸Ga]伊索曲肽用于生长抑素受体过表达的高分化胃肠胰神经内分泌瘤PET/CT诊断的前瞻性、单臂、盲态阅片、自身对照III期临床研究');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (79, 'SUSAR-TEST-101', 'TEST-1', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'SUSAR平台测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (80, 'SUSAR-TEST-102', 'TEST-2', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'SUSAR平台测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (81, 'SUSAR-TEST-103', 'TEST-3', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'SUSAR平台测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (82, '20230727', '20230727', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '20230727');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (83, '20230807', '20230807', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '20230807');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (84, 'SHR-9839-I-101', 'SHR-9839', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '注射用SHR-9839在晚期实体瘤患者中的安全性、耐受性、药代动力学及疗效的开放、多中心I期临床研究');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (85, 'SUSAR-309', 'HRS001,APTN(YN968D1)', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (86, 'SUSAR-310', 'HRS002', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (87, 'SUSAR-311', 'HRS001,HRS002', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (88, 'E4.1.9R3.3.6测试项目', 'EDCTEST', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'E4.1.9R3.3.6测试项目');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (89, 'EDC-2023-01', 'HRS001,HRS002', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'EDC新版本测试创建的第一个新项目');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (90, 'EDC-TEST-QLN-01', 'HRS001,HRS002', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'EDC-TEST-QLN-01');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (91, 'EDC-TEST-DL-02', 'HRS001,HRS002', '2024-03-13 16:44:12', '2024-03-13 16:44:12', 'EDC-TEST-DL-02');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (92, 'HR7777-105', 'HR7777', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '中心账号申请测试项目');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (93, 'HR7777-I-104', 'HR7777', '2024-03-13 16:44:12', '2024-03-13 16:44:12', '核查规则测试项目');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (94, 'HR_A1009', 'EDC-20220302', '2024-03-25 14:19:12', '2024-03-18 15:09:53', 'TEST_A1009-101');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (95, 'TEST-201', 'EDCTEST', '2024-03-25 14:17:27', '2024-03-25 14:17:27', 'TEST-201');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (96, 'TEST-202', 'EDCTEST', '2024-03-25 14:28:05', '2024-03-25 14:28:05', 'TEST-202');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (97, 'TEST-203', 'TEST-1,SHR-1707', '2024-03-26 10:26:58', '2024-03-26 10:26:58', 'TEST-203');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (98, 'TEST-301', 'TEST-1', '2024-03-26 13:35:59', '2024-03-26 13:35:59', 'TEST-301');
INSERT INTO `t_project_info`(`id`, `study_no`, `compound_no`, `update_time`, `create_time`, `study_name`) VALUES (99, 'SUSAR-TEST-401', 'TEST-4', '2024-04-02 09:31:30', '2024-04-02 09:31:12', 'SUSAR-TEST-401');
