%global system studyid root version dir lang m_minio repdat;
proc datasets lib=work nolist kill; quit;%let dir=%sysfunc(getoption(work));

x "cd &dir";x "mkdir ./pgm";x 'mc find minios3-t/pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';
%include "&dir/pgm/*.sas";
option mprint symbolgen validvarname=v7;

%let m_minio=minios3-t;
%let repdat=&sysdate.;

%let studyid=&studyid.;

%let lang="&lang.";
/*%put &lang.;*/




%m_post2s3(studyid=&studyid.);
%m_gets3data(studyid=&studyid.,data=@);

option mprint symbolgen validvarname=v7;



/*%let repdat=&sysdate.;*/
/*%let m_minio=minios3-t;*/
x "mc find &m_minio./sdv/json --name ""&studyid._AE-EX-EOT.json"" | xargs -I{} mc cp {} ./doc/";
filename y "&root./doc/&studyid._AE-EX-EOT.json";
libname jsonrec json fileref=y;
proc copy in=jsonrec out=work;
run;
data _null_;
set alldata;
	if  P1='exo' then  do;
		if value^='' then call symputx("exo_",value);
		else  call symputx("exo_","N");
	end;
	if  P1='exi' then  do;
		if value^='' then call symputx("exi_",value);
		else  call symputx("exi_","N");
	end;
	if  P1='drug_allday' then  do;
		if value^='' then call symputx("drug_allday_",value);
		else  call symputx("drug_allday_","");
	end;
run;
%put exo="&exo_."  exi="&exi_." drug_allday="&drug_allday_." ;

option mprint symbolgen validvarname=v7;
%m_custom_function;   
%M_std_dvs_ae_ex_all(isminio=Y,exo=&exo_.,exi=&exi_.,drug_allday=&drug_allday_.);
%m_exportxlsx_dmreview(check=AE-EX-EOT,outputfile=one,outputlocal=&m_minio./sdv/output/,zipyn=N,tagyn=Y) 
