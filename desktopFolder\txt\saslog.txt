1                                                            SAS 系统            2023年12月 1日 星期五 下午05时13分00秒

NOTE: Copyright (c) 2016 by SAS Institute Inc., Cary, NC, USA. 
NOTE: SAS (r) Proprietary Software 9.4 (TS1M6 MBCS3170) 
      Licensed to JIANGSU HENGRUI MEDICINE CO LTD, Site 70283013.
NOTE: 该会话正在平台 Linux 3.10.0-514.el7.x86_64 (LIN X64) 上执行。



NOTE: Analytical products:
      
      SAS/STAT 15.1
      SAS/IML 15.1

NOTE: Additional host information:

 Linux LIN X64 3.10.0-514.el7.x86_64 #1 SMP Wed Oct 19 11:24:13 EDT 2016 x86_64 Red Hat Enterprise Linux Server release 7.3 (Maipo) 

You are running SAS 9. Some SAS 8 files will be automatically converted 
by the V9 engine; others are incompatible.  Please see 
http://support.sas.com/rnd/migration/planning/platform/64bit.html

PROC MIGRATE will preserve current SAS file attributes and is 
recommended for converting all your SAS libraries from any 
SAS 8 release to SAS 9.  For details and examples, please see
http://support.sas.com/rnd/migration/index.html


This message is contained in the SAS news file, and is presented upon
initialization.  Edit the file "news" in the "misc/base" directory to
display site-specific news and information in the program log.
The command line option "-nonews" will prevent this display.




NOTE: “SAS 初始化”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      
NOTE:  autoexec 文件“/u01/app/sas/sas9.4/config/Lev1/SASApp/WorkspaceServer/autoexec.sas”在服务器初始化时执行。
1          /*璇存槑&root涓嬬洰褰曠粨鏋�---accdoc\EDM\DTAmacro
1        ! ***sas瀹忕▼搴忎綅缃�outoutput---杈撳嚭鐨刢sv鏂囦欢璺緞浣嶄簬&root/doc/output璋冪敤:%m_edm_bl
1        ! ind*/%let root=/u01/app/sas/sas9.4/DocumentRepository1/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM;%let jsonpath = ;%let
1        ! rawpath = ;options mprint;%include "&root./macro/m_edm_blind_.sas" ;
1        ! %m_edm_blind(jsonpath=20231201171344.json,rawpath=202205BF2EA5646BD24015967EEA76FA099225.csv);
SYMBOLGEN:  宏变量 ROOT 解析为 /u01/app/sas/sas9.4/DocumentRepository1/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM
SYMBOLGEN:  宏变量 JSONPATH 解析为 20231201171344.json
MPRINT(M_EDM_BLIND):   filename y 
"/u01/app/sas/sas9.4/DocumentRepository1/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/doc/EDM/json_blind/20231201171344.json";
MPRINT(M_EDM_BLIND):   libname jsonrec json fileref=y;
NOTE: JSON data is only read once.  To read the JSON again, reassign the JSON LIBNAME.
NOTE: 已成功分配逻辑库引用名“JSONREC”，如下所示: 
       引擎:        JSON 
       物理名: /u01/app/sas/sas9.4/DocumentRepositor
      y1/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/doc/EDM/json_blind/20231201171344.json
MPRINT(M_EDM_BLIND):   proc copy in=jsonrec out=work;
MPRINT(M_EDM_BLIND):   run;
NOTE: 正在复制 JSONREC.ALLDATA 至 WORK.ALLDATA (memtype=DATA)。
NOTE: 在跨不同引擎复制时，没有克隆 BUFSIZE。 使用了 BUFSIZE 的系统选项。
2                                                            SAS 系统            2023年12月 1日 星期五 下午05时13分00秒

NOTE: 从数据集 JSONREC.ALLDATA. 读取了 33 个观测
NOTE: 数据集 WORK.ALLDATA 有 33 个观测和 6 个变量。
NOTE: 正在复制 JSONREC.BLINDCODEFORM 至 WORK.BLINDCODEFORM (memtype=DATA)。
NOTE: 在跨不同引擎复制时，没有克隆 BUFSIZE。 使用了 BUFSIZE 的系统选项。
NOTE: 从数据集 JSONREC.BLINDCODEFORM. 读取了 1 个观测
NOTE: 数据集 WORK.BLINDCODEFORM 有 1 个观测和 4 个变量。
NOTE: 正在复制 JSONREC.BLINDCODEFORM_MASKVAL 至 WORK.BLINDCODEFORM_MASKVAL (memtype=DATA)。
NOTE: 在跨不同引擎复制时，没有克隆 BUFSIZE。 使用了 BUFSIZE 的系统选项。
NOTE: 从数据集 JSONREC.BLINDCODEFORM_MASKVAL. 读取了 1 个观测
NOTE: 数据集 WORK.BLINDCODEFORM_MASKVAL 有 1 个观测和 2 个变量。
NOTE: 正在复制 JSONREC.CLEARVAR_S 至 WORK.CLEARVAR_S (memtype=DATA)。
NOTE: 在跨不同引擎复制时，没有克隆 BUFSIZE。 使用了 BUFSIZE 的系统选项。
NOTE: 从数据集 JSONREC.CLEARVAR_S. 读取了 1 个观测
NOTE: 数据集 WORK.CLEARVAR_S 有 1 个观测和 2 个变量。
NOTE: 正在复制 JSONREC.DOUBLECODEFORM 至 WORK.DOUBLECODEFORM (memtype=DATA)。
NOTE: 在跨不同引擎复制时，没有克隆 BUFSIZE。 使用了 BUFSIZE 的系统选项。
NOTE: 从数据集 JSONREC.DOUBLECODEFORM. 读取了 1 个观测
NOTE: 数据集 WORK.DOUBLECODEFORM 有 1 个观测和 9 个变量。
NOTE: 正在复制 JSONREC.MASKVAR_S 至 WORK.MASKVAR_S (memtype=DATA)。
NOTE: 在跨不同引擎复制时，没有克隆 BUFSIZE。 使用了 BUFSIZE 的系统选项。
NOTE: 从数据集 JSONREC.MASKVAR_S. 读取了 1 个观测
NOTE: 数据集 WORK.MASKVAR_S 有 1 个观测和 2 个变量。
NOTE: 正在复制 JSONREC.RANDVALUES 至 WORK.RANDVALUES (memtype=DATA)。
NOTE: 在跨不同引擎复制时，没有克隆 BUFSIZE。 使用了 BUFSIZE 的系统选项。
NOTE: 从数据集 JSONREC.RANDVALUES. 读取了 1 个观测
NOTE: 数据集 WORK.RANDVALUES 有 1 个观测和 2 个变量。
NOTE: 正在复制 JSONREC.ROOT 至 WORK.ROOT (memtype=DATA)。
NOTE: 在跨不同引擎复制时，没有克隆 BUFSIZE。 使用了 BUFSIZE 的系统选项。
NOTE: 从数据集 JSONREC.ROOT. 读取了 1 个观测
NOTE: 数据集 WORK.ROOT 有 1 个观测和 11 个变量。
NOTE: 正在复制 JSONREC.SENSITIVEFORM 至 WORK.SENSITIVEFORM (memtype=DATA)。
NOTE: 在跨不同引擎复制时，没有克隆 BUFSIZE。 使用了 BUFSIZE 的系统选项。
NOTE: 从数据集 JSONREC.SENSITIVEFORM. 读取了 1 个观测
NOTE: 数据集 WORK.SENSITIVEFORM 有 1 个观测和 4 个变量。
NOTE: 正在复制 JSONREC.SENSITIVEFORM_DELVAL 至 WORK.SENSITIVEFORM_DELVAL (memtype=DATA)。
NOTE: 在跨不同引擎复制时，没有克隆 BUFSIZE。 使用了 BUFSIZE 的系统选项。
NOTE: 从数据集 JSONREC.SENSITIVEFORM_DELVAL. 读取了 1 个观测
NOTE: 数据集 WORK.SENSITIVEFORM_DELVAL 有 1 个观测和 6 个变量。
NOTE: “PROCEDURE COPY”所用时间（总处理时间）:
      实际时间          0.04 秒
      CPU 时间          0.04 秒
      

MPRINT(M_EDM_BLIND):   data _null_;
MPRINT(M_EDM_BLIND):   set jsonrec.root;
MPRINT(M_EDM_BLIND):   call symputx("file_name",filename);
MPRINT(M_EDM_BLIND):   call symputx("blind_method",blind_method);
MPRINT(M_EDM_BLIND):   call symputx("rule_num",rule);
MPRINT(M_EDM_BLIND):   call symputx("rand",rand);
MPRINT(M_EDM_BLIND):   call symputx("corresponding",corresponding);
MPRINT(M_EDM_BLIND):   run;

NOTE: 从数据集 JSONREC.ROOT. 读取了 1 个观测
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

3                                                            SAS 系统            2023年12月 1日 星期五 下午05时13分00秒

SYMBOLGEN:  宏变量 FILE_NAME 解析为 hrs-3738-i-102_pk_xihua_20230512_prod.csv
hrs-3738-i-102_pk_xihua_20230512_prod.csv
SYMBOLGEN:  宏变量 BLIND_METHOD 解析为 1
1
SYMBOLGEN:  宏变量 RULE_NUM 解析为 2
2
SYMBOLGEN:  宏变量 RAND 解析为 

SYMBOLGEN:  宏变量 CORRESPONDING 解析为 

SYMBOLGEN:  宏变量 ROOT 解析为 /u01/app/sas/sas9.4/DocumentRepository1/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM
SYMBOLGEN:  宏变量 RAWPATH 解析为 202205BF2EA5646BD24015967EEA76FA099225.csv
MPRINT(M_EDM_BLIND):   FILENAME nls 
"/u01/app/sas/sas9.4/DocumentRepository1/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/doc/EDM/csv_raw/202205BF2EA5646BD24015967EEA76FA09
9225.csv" ENCODING="UTF-8";
MPRINT(M_EDM_BLIND):   PROC IMPORT OUT= outdata DATAFILE= nls DBMS=CSV REPLACE;
MPRINT(M_EDM_BLIND):   ADLM;
MPRINT(M_EDM_BLIND):   GETNAMES=YES;
MPRINT(M_EDM_BLIND):   guessingrows=max;
MPRINT(M_EDM_BLIND):   run;

NOTE: Unable to open parameter catalog: SASUSER.PARMS.PARMS.SLIST in update mode. Temporary parameter values will be saved to 
WORK.PARMS.PARMS.SLIST.
1190        /**********************************************************************
1191        *   PRODUCT:   SAS
1192        *   VERSION:   9.4
1193        *   CREATOR:   External File Interface
1194        *   DATE:      01DEC23
1195        *   DESC:      Generated SAS Datastep Code
1196        *   TEMPLATE SOURCE:  (None Specified.)
1197        ***********************************************************************/
1198           data WORK.OUTDATA    ;
MPRINT(M_EDM_BLIND):   data WORK.OUTDATA ;
1199           %let _EFIERR_ = 0; /* set the ERROR detection macro variable */
1200           infile NLS delimiter = ',' MISSOVER DSD  firstobs=2 ;
MPRINT(M_EDM_BLIND):   infile NLS delimiter = ',' MISSOVER DSD firstobs=2 ;
1201              informat STUDYID $11. ;
MPRINT(M_EDM_BLIND):   informat STUDYID $11. ;
1202              informat SUBJID $8. ;
MPRINT(M_EDM_BLIND):   informat SUBJID $8. ;
1203              informat SITEID $5. ;
MPRINT(M_EDM_BLIND):   informat SITEID $5. ;
1204              informat LBNAM $42. ;
MPRINT(M_EDM_BLIND):   informat LBNAM $42. ;
1205              informat LBREFID best32. ;
MPRINT(M_EDM_BLIND):   informat LBREFID best32. ;
1206              informat VISIT $25. ;
MPRINT(M_EDM_BLIND):   informat VISIT $25. ;
1207              informat LBDTC yymmdd10. ;
MPRINT(M_EDM_BLIND):   informat LBDTC yymmdd10. ;
1208              informat LBTIM time20.3 ;
MPRINT(M_EDM_BLIND):   informat LBTIM time20.3 ;
1209              informat LBSPEC $5. ;
MPRINT(M_EDM_BLIND):   informat LBSPEC $5. ;
1210              informat LBTEST $3. ;
MPRINT(M_EDM_BLIND):   informat LBTEST $3. ;
1211              informat LBORRES best32. ;
MPRINT(M_EDM_BLIND):   informat LBORRES best32. ;
4                                                            SAS 系统            2023年12月 1日 星期五 下午05时13分00秒

1212              informat LBORRESU $5. ;
MPRINT(M_EDM_BLIND):   informat LBORRESU $5. ;
1213              informat LBORNRLO $1. ;
MPRINT(M_EDM_BLIND):   informat LBORNRLO $1. ;
1214              informat LBORNRHI best32. ;
MPRINT(M_EDM_BLIND):   informat LBORNRHI best32. ;
1215              informat LBSTAT $1. ;
MPRINT(M_EDM_BLIND):   informat LBSTAT $1. ;
1216              informat LBCOM $1. ;
MPRINT(M_EDM_BLIND):   informat LBCOM $1. ;
1217              format STUDYID $11. ;
MPRINT(M_EDM_BLIND):   format STUDYID $11. ;
1218              format SUBJID $8. ;
MPRINT(M_EDM_BLIND):   format SUBJID $8. ;
1219              format SITEID $5. ;
MPRINT(M_EDM_BLIND):   format SITEID $5. ;
1220              format LBNAM $42. ;
MPRINT(M_EDM_BLIND):   format LBNAM $42. ;
1221              format LBREFID best12. ;
MPRINT(M_EDM_BLIND):   format LBREFID best12. ;
1222              format VISIT $25. ;
MPRINT(M_EDM_BLIND):   format VISIT $25. ;
1223              format LBDTC yymmdd10. ;
MPRINT(M_EDM_BLIND):   format LBDTC yymmdd10. ;
1224              format LBTIM time20.3 ;
MPRINT(M_EDM_BLIND):   format LBTIM time20.3 ;
1225              format LBSPEC $5. ;
MPRINT(M_EDM_BLIND):   format LBSPEC $5. ;
1226              format LBTEST $3. ;
MPRINT(M_EDM_BLIND):   format LBTEST $3. ;
1227              format LBORRES best12. ;
MPRINT(M_EDM_BLIND):   format LBORRES best12. ;
1228              format LBORRESU $5. ;
MPRINT(M_EDM_BLIND):   format LBORRESU $5. ;
1229              format LBORNRLO $1. ;
MPRINT(M_EDM_BLIND):   format LBORNRLO $1. ;
1230              format LBORNRHI best12. ;
MPRINT(M_EDM_BLIND):   format LBORNRHI best12. ;
1231              format LBSTAT $1. ;
MPRINT(M_EDM_BLIND):   format LBSTAT $1. ;
1232              format LBCOM $1. ;
MPRINT(M_EDM_BLIND):   format LBCOM $1. ;
1233           input
1234                       STUDYID  $
1235                       SUBJID  $
1236                       SITEID  $
1237                       LBNAM  $
1238                       LBREFID
1239                       VISIT  $
1240                       LBDTC
1241                       LBTIM
1242                       LBSPEC  $
1243                       LBTEST  $
1244                       LBORRES
1245                       LBORRESU  $
1246                       LBORNRLO  $
1247                       LBORNRHI
1248                       LBSTAT  $
5                                                            SAS 系统            2023年12月 1日 星期五 下午05时13分00秒

1249                       LBCOM  $
1250           ;
MPRINT(M_EDM_BLIND):   input STUDYID $ SUBJID $ SITEID $ LBNAM $ LBREFID VISIT $ LBDTC LBTIM LBSPEC $ LBTEST $ LBORRES LBORRESU $ 
LBORNRLO $ LBORNRHI LBSTAT $ LBCOM $ ;
1251           if _ERROR_ then call symputx('_EFIERR_',1);  /* set ERROR detection macro variable */
MPRINT(M_EDM_BLIND):   if _ERROR_ then call symputx('_EFIERR_',1);
1252           run;
MPRINT(M_EDM_BLIND):   run;

NOTE: INFILE NLS 是:
      
      文件名=/u01/app/sas/sas9.4/DocumentRepository1/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/doc/EDM/csv_raw/202205BF2EA5646BD24
      015967EEA76FA099225.csv,
      所有者名=weix5,组名称=grp_cdsc_dev,
      访问权限=-rwxrwx---,
      上次修改时间=2023年11月29日 13时43分11秒,
      文件大小（字节）=7659

NOTE: 54 records were read from the infile NLS.
      最小记录长度是 134。
      最大记录长度是 157。
NOTE: 数据集 WORK.OUTDATA 有 54 个观测和 16 个变量。
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.01 秒
      

54 rows created in WORK.OUTDATA from NLS.
  
  
  
NOTE: 成功创建“WORK.OUTDATA”数据集。
NOTE: 数据集 WORK.OUTDATA 有 54 个观测和 16 个变量。
NOTE: “PROCEDURE IMPORT”所用时间（总处理时间）:
      实际时间          0.21 秒
      CPU 时间          0.12 秒
      

MPRINT(M_EDM_BLIND):    ;
MPRINT(M_EDM_BLIND):   proc contents data=outdata out=outdata_t (keep=MEMNAME NAME TYPE LENGTH VARNUM FORMAT);
MPRINT(M_EDM_BLIND):  quit;

NOTE: 数据集 WORK.OUTDATA_T 有 16 个观测和 6 个变量。
NOTE: PROCEDURE CONTENTS 已打印第 1 页。
NOTE: “PROCEDURE CONTENTS”所用时间（总处理时间）:
      实际时间          0.01 秒
      CPU 时间          0.01 秒
      

MPRINT(M_EDM_BLIND):   proc sort;
MPRINT(M_EDM_BLIND):  by VARNUM;
MPRINT(M_EDM_BLIND):  run;

NOTE: 从数据集 WORK.OUTDATA_T. 读取了 16 个观测
NOTE: 数据集 WORK.OUTDATA_T 有 16 个观测和 6 个变量。
NOTE: “PROCEDURE SORT”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
6                                                            SAS 系统            2023年12月 1日 星期五 下午05时13分00秒

      

MPRINT(M_EDM_BLIND):   data invar;
MPRINT(M_EDM_BLIND):   set outdata_t;
MPRINT(M_EDM_BLIND):   varlist=cats(NAME,':$','500.');
MPRINT(M_EDM_BLIND):   run;

NOTE: 从数据集 WORK.OUTDATA_T. 读取了 16 个观测
NOTE: 数据集 WORK.INVAR 有 16 个观测和 7 个变量。
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

MPRINT(M_EDM_BLIND):   proc sql noprint;
MPRINT(M_EDM_BLIND):   select varlist into: inputlist separated by ' ' from invar;
MPRINT(M_EDM_BLIND):   quit;
NOTE: “PROCEDURE SQL”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

MPRINT(M_EDM_BLIND):   data rawdata;
SYMBOLGEN:  宏变量 ROOT 解析为 /u01/app/sas/sas9.4/DocumentRepository1/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM
SYMBOLGEN:  宏变量 RAWPATH 解析为 202205BF2EA5646BD24015967EEA76FA099225.csv
MPRINT(M_EDM_BLIND):   infile 
"/u01/app/sas/sas9.4/DocumentRepository1/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/doc/EDM/csv_raw/202205BF2EA5646BD24015967EEA76FA09
9225.csv" ENCODING="UTF-8" dlm=',' dsd truncover;
SYMBOLGEN:  宏变量 INPUTLIST 解析为 STUDYID:$500. SUBJID:$500. SITEID:$500. LBNAM:$500. LBREFID:$500. VISIT:$500. LBDTC:$500. 
           LBTIM:$500. LBSPEC:$500. LBTEST:$500. LBORRES:$500. LBORRESU:$500. LBORNRLO:$500. LBORNRHI:$500. LBSTAT:$500. LBCOM:$500.
MPRINT(M_EDM_BLIND):   input STUDYID:$500. SUBJID:$500. SITEID:$500. LBNAM:$500. LBREFID:$500. VISIT:$500. LBDTC:$500. LBTIM:$500. 
LBSPEC:$500. LBTEST:$500. LBORRES:$500. LBORRESU:$500. LBORNRLO:$500. LBORNRHI:$500. LBSTAT:$500. LBCOM:$500.;
MPRINT(M_EDM_BLIND):   if _n_=1 then delete;
MPRINT(M_EDM_BLIND):   run;

NOTE: INFILE 
      "/u01/app/sas/sas9.4/DocumentRepository1/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/doc/EDM/csv_raw/202205BF2EA5646BD24015967EEA
      76FA099225.csv" 是:
      
      文件名=/u01/app/sas/sas9.4/DocumentRepository1/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/doc/EDM/csv_raw/202205BF2EA5646BD24
      015967EEA76FA099225.csv,
      所有者名=weix5,组名称=grp_cdsc_dev,
      访问权限=-rwxrwx---,
      上次修改时间=2023年11月29日 13时43分11秒,
      文件大小（字节）=7659

NOTE: 55 records were read from the infile 
      "/u01/app/sas/sas9.4/DocumentRepository1/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/doc/EDM/csv_raw/202205BF2EA5646BD24015967EEA
      76FA099225.csv".
      最小记录长度是 115。
      最大记录长度是 157。
NOTE: 数据集 WORK.RAWDATA 有 54 个观测和 16 个变量。
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

MPRINT(M_EDM_BLIND):   data blinddata;
7                                                            SAS 系统            2023年12月 1日 星期五 下午05时13分00秒

MPRINT(M_EDM_BLIND):   set rawdata;
MPRINT(M_EDM_BLIND):   run;

NOTE: 从数据集 WORK.RAWDATA. 读取了 54 个观测
NOTE: 数据集 WORK.BLINDDATA 有 54 个观测和 16 个变量。
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.01 秒
      

MPRINT(M_EDM_BLIND):   proc sql;
MPRINT(M_EDM_BLIND):  select Value into:yndelete from alldata where P1='del_si';
MPRINT(M_EDM_BLIND):  quit;
NOTE: PROCEDURE SQL 已打印第 2 页。
NOTE: “PROCEDURE SQL”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

SYMBOLGEN:  宏变量 YNDELETE 解析为 1                                        
1
SYMBOLGEN:  宏变量 YNDELETE 解析为 1                                        
MPRINT(DELETEDATA):   data sensitiveform_delval_;
MPRINT(DELETEDATA):   set sensitiveform_delval;
MPRINT(DELETEDATA):   varall=catx(',',of delval:);
MPRINT(DELETEDATA):   n=count(varall,',')+1;
MPRINT(DELETEDATA):   do i=1 to n;
MPRINT(DELETEDATA):   new=scan(varall,i,',');
MPRINT(DELETEDATA):  output;
MPRINT(DELETEDATA):   end;
MPRINT(DELETEDATA):   run;

NOTE: 从数据集 WORK.SENSITIVEFORM_DELVAL. 读取了 1 个观测
NOTE: 数据集 WORK.SENSITIVEFORM_DELVAL_ 有 4 个观测和 10 个变量。
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

MPRINT(DELETEDATA):   proc sql;
MPRINT(DELETEDATA):  select count(*) into:numsen from sensitiveform_delval;
MPRINT(DELETEDATA):  quit;
NOTE: PROCEDURE SQL 已打印第 3 页。
NOTE: “PROCEDURE SQL”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

SYMBOLGEN:  宏变量 NUMSEN 解析为        1
1
SYMBOLGEN:  宏变量 NUMSEN 解析为        1
MPRINT(NUMSEN):   proc sql noprint ;
SYMBOLGEN:  宏变量 I 解析为 1
SYMBOLGEN:  宏变量 I 解析为 1
MPRINT(NUMSEN):   select "'"||strip(new)||"'" into: senval1 separated ',' from sensitiveform_delval_ where ordinal_sensitiveForm=1;
MPRINT(NUMSEN):   quit;
NOTE: “PROCEDURE SQL”所用时间（总处理时间）:
      实际时间          0.00 秒
8                                                            SAS 系统            2023年12月 1日 星期五 下午05时13分00秒

      CPU 时间          0.01 秒
      

MPRINT(NUMSEN):   data sensitiveform_delval;
MPRINT(NUMSEN):   set sensitiveform_delval;
MPRINT(NUMSEN):   length newval $1000;
SYMBOLGEN:  宏变量 I 解析为 1
SYMBOLGEN: && 解析为 &。
SYMBOLGEN:  宏变量 I 解析为 1
SYMBOLGEN:  宏变量 SENVAL1 解析为 'CN001007','CN001006','CN001008','CN001001'
MPRINT(NUMSEN):   if ordinal_sensitiveForm=1 then newval="'CN001007','CN001006','CN001008','CN001001'";
MPRINT(NUMSEN):   run;

NOTE: 从数据集 WORK.SENSITIVEFORM_DELVAL. 读取了 1 个观测
NOTE: 数据集 WORK.SENSITIVEFORM_DELVAL 有 1 个观测和 7 个变量。
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

MPRINT(DELETEDATA):  ;
MPRINT(DELETEDATA):   proc sql;
MPRINT(DELETEDATA):   create table sensitive as select a.delvar,a.delop,b.* from sensitiveform as a left join sensitiveform_delval 
as b on a.ordinal_sensitiveForm=b.ordinal_sensitiveForm;
NOTE: 表 WORK.SENSITIVE 创建完成，有 1 行，9 列。

MPRINT(DELETEDATA):   quit;
NOTE: “PROCEDURE SQL”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

MPRINT(DELETEDATA):   data sensitive1;
MPRINT(DELETEDATA):   length sentense $1000 delop1 $10;
MPRINT(DELETEDATA):   set sensitive;
MPRINT(DELETEDATA):   if delop='=' then delop1='in';
MPRINT(DELETEDATA):   if delop='^=' then delop1='not in';
MPRINT(DELETEDATA):   sentense=strip(delvar)||' '||strip(delop1)||' '||'('||strip(newval)||')';
MPRINT(DELETEDATA):   run;

NOTE: 从数据集 WORK.SENSITIVE. 读取了 1 个观测
NOTE: 数据集 WORK.SENSITIVE1 有 1 个观测和 11 个变量。
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

MPRINT(DELETEDATA):   proc sql;
MPRINT(DELETEDATA):  select sentense into:delsen separated by ' and ' from sensitive1;
MPRINT(DELETEDATA):  quit;
NOTE: PROCEDURE SQL 已打印第 4 页。
NOTE: “PROCEDURE SQL”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

SYMBOLGEN:  宏变量 DELSEN 解析为 SUBJID in ('CN001007','CN001006','CN001008','CN001001')
SUBJID in ('CN001007','CN001006','CN001008','CN001001')
9                                                            SAS 系统            2023年12月 1日 星期五 下午05时13分00秒

MPRINT(DELETEDATA):   data blinddata;
MPRINT(DELETEDATA):   set blinddata;
SYMBOLGEN:  宏变量 DELSEN 解析为 SUBJID in ('CN001007','CN001006','CN001008','CN001001')
MPRINT(DELETEDATA):   if SUBJID in ('CN001007','CN001006','CN001008','CN001001') then delete;
MPRINT(DELETEDATA):   run;

NOTE: 从数据集 WORK.BLINDDATA. 读取了 54 个观测
NOTE: 数据集 WORK.BLINDDATA 有 6 个观测和 16 个变量。
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

MPRINT(M_EDM_BLIND):  ;
MPRINT(M_EDM_BLIND):   proc sql noprint;
MPRINT(M_EDM_BLIND):   select count(name) into :var_count from dictionary.columns where libname='JSONREC' and memname='RANDVALUES' 
and upcase(name) like 'RANDVALUES%';
MPRINT(M_EDM_BLIND):   quit;
NOTE: “PROCEDURE SQL”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

SYMBOLGEN:  宏变量 VAR_COUNT 解析为        0
SYMBOLGEN:  宏变量 RANDVALUES_LIST 解析为 

MPRINT(M_EDM_BLIND):   data _null_;
MPRINT(M_EDM_BLIND):   set jsonrec.doublecodeform;
MPRINT(M_EDM_BLIND):   call symputx("value",value);
MPRINT(M_EDM_BLIND):   call symputx("replvalue",replvalue);
MPRINT(M_EDM_BLIND):   call symputx("replvar",replvar);
MPRINT(M_EDM_BLIND):   call symputx("replace_var_type",replace_var_type);
MPRINT(M_EDM_BLIND):   call symputx("is_range",is_range);
MPRINT(M_EDM_BLIND):   call symputx("upper",upper);
MPRINT(M_EDM_BLIND):   call symputx("lower",lower);
MPRINT(M_EDM_BLIND):   run;

NOTE: 从数据集 JSONREC.DOUBLECODEFORM. 读取了 1 个观测
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.01 秒
      

SYMBOLGEN:  宏变量 BLIND_METHOD 解析为 1
SYMBOLGEN:  宏变量 RULE_NUM 解析为 2
SYMBOLGEN:  宏变量 RULE_NUM 解析为 2
SYMBOLGEN:  宏变量 REPLACE_VAR_TYPE 解析为 1
SYMBOLGEN:  宏变量 IS_RANGE 解析为 0
SYMBOLGEN:  宏变量 VALUE 解析为 STUDYID
SYMBOLGEN:  宏变量 REPLVALUE 解析为 SUBJID
MPRINT(M_REPLACE_NUM_M):   data replaced_temp;
SYMBOLGEN:  宏变量 DATASET 解析为 blinddata
MPRINT(M_REPLACE_NUM_M):   set blinddata;
MPRINT(M_REPLACE_NUM_M):   run;

NOTE: 从数据集 WORK.BLINDDATA. 读取了 6 个观测
NOTE: 数据集 WORK.REPLACED_TEMP 有 6 个观测和 16 个变量。
NOTE: “DATA 语句”所用时间（总处理时间）:
10                                                           SAS 系统            2023年12月 1日 星期五 下午05时13分00秒

      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

SYMBOLGEN:  宏变量 VAR_NUM 解析为 STUDYID SUBJID
SYMBOLGEN:  宏变量 VAR_COUNT 解析为 2
SYMBOLGEN:  宏变量 VAR_NUM 解析为 STUDYID SUBJID
SYMBOLGEN:  宏变量 I 解析为 1
SYMBOLGEN:  宏变量 CURRENT_VAR 解析为 STUDYID
SYMBOLGEN:  宏变量 DATASET 解析为 replaced_temp
MPRINT(M_REPLACE_NUM):   data al_replaced_temp;
SYMBOLGEN:  宏变量 DATASET 解析为 replaced_temp
MPRINT(M_REPLACE_NUM):   set replaced_temp;
SYMBOLGEN:  宏变量 VAR_NUM 解析为 STUDYID
SYMBOLGEN:  宏变量 VAR_NUM 解析为 STUDYID
MPRINT(M_REPLACE_NUM):   STUDYID = compress(prxchange('s/[^0-9.]//', -1, STUDYID));
SYMBOLGEN:  宏变量 VAR_NUM 解析为 STUDYID
MPRINT(M_REPLACE_NUM):   if index(STUDYID, '.') = 0 then do;
MPRINT(M_REPLACE_NUM):   dd_count = 0;
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   else do;
SYMBOLGEN:  宏变量 VAR_NUM 解析为 STUDYID
SYMBOLGEN:  宏变量 VAR_NUM 解析为 STUDYID
MPRINT(M_REPLACE_NUM):   dd_count = length(STUDYID) - index(STUDYID, '.');
MPRINT(M_REPLACE_NUM):   end;
SYMBOLGEN:  宏变量 VAR_NUM 解析为 STUDYID
MPRINT(M_REPLACE_NUM):   sd_count = length(compress(put(input(compress(compress(translate(STUDYID, ' ', 
'.')),'p'),best.),??best.)));
MPRINT(M_REPLACE_NUM):   run;

NOTE: 从数据集 WORK.REPLACED_TEMP. 读取了 6 个观测
NOTE: 数据集 WORK.AL_REPLACED_TEMP 有 6 个观测和 18 个变量。
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

MPRINT(M_REPLACE_NUM):   proc sql;
SYMBOLGEN:  宏变量 DATASET 解析为 replaced_temp
SYMBOLGEN:  宏变量 DATASET 解析为 replaced_temp
MPRINT(M_REPLACE_NUM):   create table al_replaced_temp2 as select a.*, count(distinct dd_count) as dd_group, count(distinct 
sd_count) as sd_group from al_replaced_temp as a;
NOTE: 查询要求将汇总统计量与原始的数据重新合并。
NOTE: 表 WORK.AL_REPLACED_TEMP2 创建完成，有 6 行，20 列。

MPRINT(M_REPLACE_NUM):   quit;
NOTE: “PROCEDURE SQL”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.01 秒
      

SYMBOLGEN:  宏变量 DATASET 解析为 replaced_temp
MPRINT(M_REPLACE_NUM):   data al_replaced_temp3;
SYMBOLGEN:  宏变量 DATASET 解析为 replaced_temp
MPRINT(M_REPLACE_NUM):   set al_replaced_temp2;
MPRINT(M_REPLACE_NUM):   if sd_group > 1 then sd_count = .;
MPRINT(M_REPLACE_NUM):   if dd_group > 1 then dd_count = .;
MPRINT(M_REPLACE_NUM):   run;
11                                                           SAS 系统            2023年12月 1日 星期五 下午05时13分00秒


NOTE: 从数据集 WORK.AL_REPLACED_TEMP2. 读取了 6 个观测
NOTE: 数据集 WORK.AL_REPLACED_TEMP3 有 6 个观测和 20 个变量。
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

SYMBOLGEN:  宏变量 OUT_DATA 解析为 replaced_temp
MPRINT(M_REPLACE_NUM):   data replaced_temp;
SYMBOLGEN:  宏变量 DATASET 解析为 replaced_temp
MPRINT(M_REPLACE_NUM):   set al_replaced_temp3;
SYMBOLGEN:  宏变量 VAR_NUM 解析为 STUDYID
MPRINT(M_REPLACE_NUM):   _varnum = STUDYID*(0.5 + 0.5 * ranuni(0));
MPRINT(M_REPLACE_NUM):   current_decimal_places = index(compress(put(_varnum, BEST.)), ".");
MPRINT(M_REPLACE_NUM):   if current_decimal_places = 0 then do;
MPRINT(M_REPLACE_NUM):   decimal_num = 0;
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   else do;
MPRINT(M_REPLACE_NUM):   decimal_num = length(_varnum) - current_decimal_places;
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   if (dd_count eq . and sd_count ne .) or (dd_count ne . and sd_count ne .) then do;
SYMBOLGEN:  宏变量 INPUTNUMERIC 解析为 _varnum
MPRINT(FORMATNUM_SD):   inputNumeric=_varnum;
MPRINT(FORMATNUM_SD):   if inputNumeric ^=0 then do;
SYMBOLGEN:  宏变量 SIGNIFICANTDIGIT 解析为 sd_count
MPRINT(FORMATNUM_SD):   significantDigit=sd_count;
MPRINT(FORMATNUM_SD):   inputNumericprx=compress(vvalue(inputNumeric));
MPRINT(FORMATNUM_SD):   inputNumericprx1=strip(kscan(inputNumericprx,1,'.'));
MPRINT(FORMATNUM_SD):   inputNumericheader=length(compress(inputNumericprx1,'-',''));
MPRINT(FORMATNUM_SD):   if inputNumericprx1 not in ('0' '-0') then inputNumeric1=inputNumeric/10**inputNumericheader;
MPRINT(FORMATNUM_SD):   else inputNumeric1=inputNumeric;
MPRINT(FORMATNUM_SD):   BinputNumericprx=compress(vvalue(inputNumeric1));
MPRINT(FORMATNUM_SD):   BinputNumericprx1=strip(kscan(BinputNumericprx,1,'.'));
MPRINT(FORMATNUM_SD):   BinputNumericprx2=strip(kscan(BinputNumericprx,2,'.'));
MPRINT(FORMATNUM_SD):   BinputNumericindex=indexc(BinputNumericprx2,'123456789');
MPRINT(FORMATNUM_SD):   inputNumericformat=strip("12.")||strip(put(BinputNumericindex+significantDigit-1,12.))||strip(".");
SYMBOLGEN:  宏变量 OUTPUTNUMERIC 解析为 var
MPRINT(FORMATNUM_SD):   var=strip(putn(inputNumeric1,inputNumericformat));
MPRINT(FORMATNUM_SD):   
BinputNumericformat=strip("12.")||strip(put(significantDigit-length(compress(inputNumericprx1,'-','')),??best12.))||strip(".");
SYMBOLGEN:  宏变量 OUTPUTNUMERIC 解析为 var
SYMBOLGEN:  宏变量 OUTPUTNUMERIC 解析为 var
MPRINT(FORMATNUM_SD):   if length(compress(inputNumericprx1,'-',''))>significantDigit-1 then 
var=strip(input(var,??best12.)*(10**inputNumericheader));
SYMBOLGEN:  宏变量 OUTPUTNUMERIC 解析为 var
SYMBOLGEN:  宏变量 OUTPUTNUMERIC 解析为 var
MPRINT(FORMATNUM_SD):   else if compress(inputNumericprx1,'-','')^='0' then 
var=strip(putn(input(var,??best12.)*(10**inputNumericheader),BinputNumericformat));
MPRINT(FORMATNUM_SD):   drop inputNumeric significantDigit inputNumericprx inputNumericprx1 inputNumericheader inputNumeric1 
BinputNumericprx BinputNumericprx1 BinputNumericprx2 BinputNumericindex inputNumericformat BinputNumericformat;
MPRINT(FORMATNUM_SD):   end;
MPRINT(FORMATNUM_SD):   if inputNumeric =0 then do;
SYMBOLGEN:  宏变量 OUTPUTNUMERIC 解析为 var
MPRINT(FORMATNUM_SD):   var='0';
MPRINT(FORMATNUM_SD):   end;
MPRINT(M_REPLACE_NUM):  ;
MPRINT(M_REPLACE_NUM):   end;
12                                                           SAS 系统            2023年12月 1日 星期五 下午05时13分00秒

MPRINT(M_REPLACE_NUM):   else if dd_count ne . and sd_count eq . then do;
MPRINT(M_REPLACE_NUM):   if decimal_num < dd_count then do;
MPRINT(M_REPLACE_NUM):   _numc = dd_count - decimal_num;
MPRINT(M_REPLACE_NUM):   var = compress(catx('',_varnum,compress(put(10**_numc,best.),"1")));
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   else do;
MPRINT(M_REPLACE_NUM):   var = compress(put(round(_varnum, 10**(-dd_count)),best.));
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   else do;
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   current_decimal_places2 = index(compress(var), ".");
MPRINT(M_REPLACE_NUM):   if current_decimal_places2 = 0 then do;
MPRINT(M_REPLACE_NUM):   decimal_num2 = 0;
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   else do;
MPRINT(M_REPLACE_NUM):   decimal_num2 = length(input(var,best.)) - current_decimal_places2;
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   if decimal_num2 < dd_count then do;
MPRINT(M_REPLACE_NUM):   _numc2 = dd_count - decimal_num2;
MPRINT(M_REPLACE_NUM):   if decimal_num2 = 0 then do;
MPRINT(M_REPLACE_NUM):   var = compress(catx('.',var,compress(put(10**_numc2,best.),"1")));
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   else do;
MPRINT(M_REPLACE_NUM):   var = compress(catx('',var,compress(put(10**_numc2,best.),"1")));
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   end;
SYMBOLGEN:  宏变量 VAR_NUM 解析为 STUDYID
MPRINT(M_REPLACE_NUM):   STUDYID = var;
MPRINT(M_REPLACE_NUM):   drop var _: decimal_: dd_: sd_: current_: ;
MPRINT(M_REPLACE_NUM):   run;

NOTE: 字符值已转换为数值，位置:（行:列）。
      1252:2   
NOTE: 数值已转换为字符值，位置:（行:列）。
      1252:36    1252:82    1252:158   
NOTE: 从数据集 WORK.AL_REPLACED_TEMP3. 读取了 6 个观测
NOTE: 数据集 WORK.REPLACED_TEMP 有 6 个观测和 16 个变量。
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.01 秒
      

MPRINT(M_REPLACE_NUM_M):  ;
SYMBOLGEN:  宏变量 VAR_NUM 解析为 STUDYID SUBJID
SYMBOLGEN:  宏变量 I 解析为 2
SYMBOLGEN:  宏变量 CURRENT_VAR 解析为 SUBJID
SYMBOLGEN:  宏变量 DATASET 解析为 replaced_temp
MPRINT(M_REPLACE_NUM):   data al_replaced_temp;
SYMBOLGEN:  宏变量 DATASET 解析为 replaced_temp
MPRINT(M_REPLACE_NUM):   set replaced_temp;
SYMBOLGEN:  宏变量 VAR_NUM 解析为 SUBJID
SYMBOLGEN:  宏变量 VAR_NUM 解析为 SUBJID
MPRINT(M_REPLACE_NUM):   SUBJID = compress(prxchange('s/[^0-9.]//', -1, SUBJID));
SYMBOLGEN:  宏变量 VAR_NUM 解析为 SUBJID
MPRINT(M_REPLACE_NUM):   if index(SUBJID, '.') = 0 then do;
MPRINT(M_REPLACE_NUM):   dd_count = 0;
MPRINT(M_REPLACE_NUM):   end;
13                                                           SAS 系统            2023年12月 1日 星期五 下午05时13分00秒

MPRINT(M_REPLACE_NUM):   else do;
SYMBOLGEN:  宏变量 VAR_NUM 解析为 SUBJID
SYMBOLGEN:  宏变量 VAR_NUM 解析为 SUBJID
MPRINT(M_REPLACE_NUM):   dd_count = length(SUBJID) - index(SUBJID, '.');
MPRINT(M_REPLACE_NUM):   end;
SYMBOLGEN:  宏变量 VAR_NUM 解析为 SUBJID
MPRINT(M_REPLACE_NUM):   sd_count = length(compress(put(input(compress(compress(translate(SUBJID, ' ', '.')),'p'),best.),??best.)));
MPRINT(M_REPLACE_NUM):   run;

NOTE: 从数据集 WORK.REPLACED_TEMP. 读取了 6 个观测
NOTE: 数据集 WORK.AL_REPLACED_TEMP 有 6 个观测和 18 个变量。
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

MPRINT(M_REPLACE_NUM):   proc sql;
SYMBOLGEN:  宏变量 DATASET 解析为 replaced_temp
SYMBOLGEN:  宏变量 DATASET 解析为 replaced_temp
MPRINT(M_REPLACE_NUM):   create table al_replaced_temp2 as select a.*, count(distinct dd_count) as dd_group, count(distinct 
sd_count) as sd_group from al_replaced_temp as a;
NOTE: 查询要求将汇总统计量与原始的数据重新合并。
NOTE: 表 WORK.AL_REPLACED_TEMP2 创建完成，有 6 行，20 列。

MPRINT(M_REPLACE_NUM):   quit;
NOTE: “PROCEDURE SQL”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

SYMBOLGEN:  宏变量 DATASET 解析为 replaced_temp
MPRINT(M_REPLACE_NUM):   data al_replaced_temp3;
SYMBOLGEN:  宏变量 DATASET 解析为 replaced_temp
MPRINT(M_REPLACE_NUM):   set al_replaced_temp2;
MPRINT(M_REPLACE_NUM):   if sd_group > 1 then sd_count = .;
MPRINT(M_REPLACE_NUM):   if dd_group > 1 then dd_count = .;
MPRINT(M_REPLACE_NUM):   run;

NOTE: 从数据集 WORK.AL_REPLACED_TEMP2. 读取了 6 个观测
NOTE: 数据集 WORK.AL_REPLACED_TEMP3 有 6 个观测和 20 个变量。
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

SYMBOLGEN:  宏变量 OUT_DATA 解析为 replaced_temp
MPRINT(M_REPLACE_NUM):   data replaced_temp;
SYMBOLGEN:  宏变量 DATASET 解析为 replaced_temp
MPRINT(M_REPLACE_NUM):   set al_replaced_temp3;
SYMBOLGEN:  宏变量 VAR_NUM 解析为 SUBJID
MPRINT(M_REPLACE_NUM):   _varnum = SUBJID*(0.5 + 0.5 * ranuni(0));
MPRINT(M_REPLACE_NUM):   current_decimal_places = index(compress(put(_varnum, BEST.)), ".");
MPRINT(M_REPLACE_NUM):   if current_decimal_places = 0 then do;
MPRINT(M_REPLACE_NUM):   decimal_num = 0;
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   else do;
MPRINT(M_REPLACE_NUM):   decimal_num = length(_varnum) - current_decimal_places;
MPRINT(M_REPLACE_NUM):   end;
14                                                           SAS 系统            2023年12月 1日 星期五 下午05时13分00秒

MPRINT(M_REPLACE_NUM):   if (dd_count eq . and sd_count ne .) or (dd_count ne . and sd_count ne .) then do;
SYMBOLGEN:  宏变量 INPUTNUMERIC 解析为 _varnum
MPRINT(FORMATNUM_SD):   inputNumeric=_varnum;
MPRINT(FORMATNUM_SD):   if inputNumeric ^=0 then do;
SYMBOLGEN:  宏变量 SIGNIFICANTDIGIT 解析为 sd_count
MPRINT(FORMATNUM_SD):   significantDigit=sd_count;
MPRINT(FORMATNUM_SD):   inputNumericprx=compress(vvalue(inputNumeric));
MPRINT(FORMATNUM_SD):   inputNumericprx1=strip(kscan(inputNumericprx,1,'.'));
MPRINT(FORMATNUM_SD):   inputNumericheader=length(compress(inputNumericprx1,'-',''));
MPRINT(FORMATNUM_SD):   if inputNumericprx1 not in ('0' '-0') then inputNumeric1=inputNumeric/10**inputNumericheader;
MPRINT(FORMATNUM_SD):   else inputNumeric1=inputNumeric;
MPRINT(FORMATNUM_SD):   BinputNumericprx=compress(vvalue(inputNumeric1));
MPRINT(FORMATNUM_SD):   BinputNumericprx1=strip(kscan(BinputNumericprx,1,'.'));
MPRINT(FORMATNUM_SD):   BinputNumericprx2=strip(kscan(BinputNumericprx,2,'.'));
MPRINT(FORMATNUM_SD):   BinputNumericindex=indexc(BinputNumericprx2,'123456789');
MPRINT(FORMATNUM_SD):   inputNumericformat=strip("12.")||strip(put(BinputNumericindex+significantDigit-1,12.))||strip(".");
SYMBOLGEN:  宏变量 OUTPUTNUMERIC 解析为 var
MPRINT(FORMATNUM_SD):   var=strip(putn(inputNumeric1,inputNumericformat));
MPRINT(FORMATNUM_SD):   
BinputNumericformat=strip("12.")||strip(put(significantDigit-length(compress(inputNumericprx1,'-','')),??best12.))||strip(".");
SYMBOLGEN:  宏变量 OUTPUTNUMERIC 解析为 var
SYMBOLGEN:  宏变量 OUTPUTNUMERIC 解析为 var
MPRINT(FORMATNUM_SD):   if length(compress(inputNumericprx1,'-',''))>significantDigit-1 then 
var=strip(input(var,??best12.)*(10**inputNumericheader));
SYMBOLGEN:  宏变量 OUTPUTNUMERIC 解析为 var
SYMBOLGEN:  宏变量 OUTPUTNUMERIC 解析为 var
MPRINT(FORMATNUM_SD):   else if compress(inputNumericprx1,'-','')^='0' then 
var=strip(putn(input(var,??best12.)*(10**inputNumericheader),BinputNumericformat));
MPRINT(FORMATNUM_SD):   drop inputNumeric significantDigit inputNumericprx inputNumericprx1 inputNumericheader inputNumeric1 
BinputNumericprx BinputNumericprx1 BinputNumericprx2 BinputNumericindex inputNumericformat BinputNumericformat;
MPRINT(FORMATNUM_SD):   end;
MPRINT(FORMATNUM_SD):   if inputNumeric =0 then do;
SYMBOLGEN:  宏变量 OUTPUTNUMERIC 解析为 var
MPRINT(FORMATNUM_SD):   var='0';
MPRINT(FORMATNUM_SD):   end;
MPRINT(M_REPLACE_NUM):  ;
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   else if dd_count ne . and sd_count eq . then do;
MPRINT(M_REPLACE_NUM):   if decimal_num < dd_count then do;
MPRINT(M_REPLACE_NUM):   _numc = dd_count - decimal_num;
MPRINT(M_REPLACE_NUM):   var = compress(catx('',_varnum,compress(put(10**_numc,best.),"1")));
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   else do;
MPRINT(M_REPLACE_NUM):   var = compress(put(round(_varnum, 10**(-dd_count)),best.));
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   else do;
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   current_decimal_places2 = index(compress(var), ".");
MPRINT(M_REPLACE_NUM):   if current_decimal_places2 = 0 then do;
MPRINT(M_REPLACE_NUM):   decimal_num2 = 0;
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   else do;
MPRINT(M_REPLACE_NUM):   decimal_num2 = length(input(var,best.)) - current_decimal_places2;
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   if decimal_num2 < dd_count then do;
MPRINT(M_REPLACE_NUM):   _numc2 = dd_count - decimal_num2;
MPRINT(M_REPLACE_NUM):   if decimal_num2 = 0 then do;
15                                                           SAS 系统            2023年12月 1日 星期五 下午05时13分00秒

MPRINT(M_REPLACE_NUM):   var = compress(catx('.',var,compress(put(10**_numc2,best.),"1")));
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   else do;
MPRINT(M_REPLACE_NUM):   var = compress(catx('',var,compress(put(10**_numc2,best.),"1")));
MPRINT(M_REPLACE_NUM):   end;
MPRINT(M_REPLACE_NUM):   end;
SYMBOLGEN:  宏变量 VAR_NUM 解析为 SUBJID
MPRINT(M_REPLACE_NUM):   SUBJID = var;
MPRINT(M_REPLACE_NUM):   drop var _: decimal_: dd_: sd_: current_: ;
MPRINT(M_REPLACE_NUM):   run;

NOTE: 字符值已转换为数值，位置:（行:列）。
      1252:2   
NOTE: 数值已转换为字符值，位置:（行:列）。
      1252:36    1252:82    1252:158   
NOTE: 从数据集 WORK.AL_REPLACED_TEMP3. 读取了 6 个观测
NOTE: 数据集 WORK.REPLACED_TEMP 有 6 个观测和 16 个变量。
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.00 秒
      

MPRINT(M_REPLACE_NUM_M):  ;
SYMBOLGEN:  宏变量 OUT_DATA 解析为 blinddata
MPRINT(M_REPLACE_NUM_M):   data blinddata;
MPRINT(M_REPLACE_NUM_M):   set replaced_temp;
MPRINT(M_REPLACE_NUM_M):   run;

NOTE: 从数据集 WORK.REPLACED_TEMP. 读取了 6 个观测
NOTE: 数据集 WORK.BLINDDATA 有 6 个观测和 16 个变量。
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.00 秒
      CPU 时间          0.01 秒
      

MPRINT(M_EDM_BLIND):  ;
SYMBOLGEN:  宏变量 BLIND_METHOD 解析为 1
SYMBOLGEN:  宏变量 BLIND_METHOD 解析为 1
SYMBOLGEN:  宏变量 BLIND_METHOD 解析为 1
SYMBOLGEN:  宏变量 BLIND_METHOD 解析为 1
SYMBOLGEN:  宏变量 BLIND_METHOD 解析为 1
SYMBOLGEN:  宏变量 BLIND_METHOD 解析为 1
SYMBOLGEN:  宏变量 FILE_NAME 解析为 hrs-3738-i-102_pk_xihua_20230512_prod.csv
SYMBOLGEN:  宏变量 OUTNAME 解析为 hrs-3738-i-102_pk_xihua_20230512_prod
hrs-3738-i-102_pk_xihua_20230512_prod
SYMBOLGEN:  宏变量 ROOT 解析为 /u01/app/sas/sas9.4/DocumentRepository1/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM
SYMBOLGEN:  宏变量 OUTNAME 解析为 hrs-3738-i-102_pk_xihua_20230512_prod
MPRINT(M_EDM_BLIND):   filename outname 
"/u01/app/sas/sas9.4/DocumentRepository1/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/output/hrs-3738-i-102_pk_xihua_20230512_prod_codin
g.csv" encoding="utf-8";
MPRINT(M_EDM_BLIND):   proc export data=blinddata outfile=outname dbms=csv replace;
MPRINT(M_EDM_BLIND):   ADLM;
MPRINT(M_EDM_BLIND):   putnames=yes;
MPRINT(M_EDM_BLIND):   run;

NOTE: Unable to open parameter catalog: SASUSER.PARMS.PARMS.SLIST in update mode. Temporary parameter values will be saved to 
WORK.PARMS.PARMS.SLIST.
1253        /**********************************************************************
16                                                           SAS 系统            2023年12月 1日 星期五 下午05时13分00秒

1254        *   PRODUCT:   SAS
1255        *   VERSION:   9.4
1256        *   CREATOR:   External File Interface
1257        *   DATE:      01DEC23
1258        *   DESC:      Generated SAS Datastep Code
1259        *   TEMPLATE SOURCE:  (None Specified.)
1260        ***********************************************************************/
1261           data _null_;
MPRINT(M_EDM_BLIND):   data _null_;
1262           %let _EFIERR_ = 0; /* set the ERROR detection macro variable */
1263           %let _EFIREC_ = 0;     /* clear export record count macro variable */
1264           file OUTNAME delimiter=',' DSD DROPOVER ;
MPRINT(M_EDM_BLIND):   file OUTNAME delimiter=',' DSD DROPOVER ;
1265           if _n_ = 1 then        /* write column names or labels */
1266            do;
MPRINT(M_EDM_BLIND):   if _n_ = 1 then do;
1267              put
1268                 "STUDYID"
1269              ','
1270                 "SUBJID"
1271              ','
1272                 "SITEID"
1273              ','
1274                 "LBNAM"
1275              ','
1276                 "LBREFID"
1277              ','
1278                 "VISIT"
1279              ','
1280                 "LBDTC"
1281              ','
1282                 "LBTIM"
1283              ','
1284                 "LBSPEC"
1285              ','
1286                 "LBTEST"
1287              ','
1288                 "LBORRES"
1289              ','
1290                 "LBORRESU"
1291              ','
1292                 "LBORNRLO"
1293              ','
1294                 "LBORNRHI"
1295              ','
1296                 "LBSTAT"
1297              ','
1298                 "LBCOM"
1299              ;
MPRINT(M_EDM_BLIND):   put "STUDYID" ',' "SUBJID" ',' "SITEID" ',' "LBNAM" ',' "LBREFID" ',' "VISIT" ',' "LBDTC" ',' "LBTIM" ',' 
"LBSPEC" ',' "LBTEST" ',' "LBORRES" ',' "LBORRESU" ',' "LBORNRLO" ',' "LBORNRHI" ',' "LBSTAT" ',' "LBCOM" ;
1300            end;
MPRINT(M_EDM_BLIND):   end;
1301          set  BLINDDATA   end=EFIEOD;
MPRINT(M_EDM_BLIND):   set BLINDDATA end=EFIEOD;
1302              format STUDYID $500. ;
MPRINT(M_EDM_BLIND):   format STUDYID $500. ;
1303              format SUBJID $500. ;
17                                                           SAS 系统            2023年12月 1日 星期五 下午05时13分00秒

MPRINT(M_EDM_BLIND):   format SUBJID $500. ;
1304              format SITEID $500. ;
MPRINT(M_EDM_BLIND):   format SITEID $500. ;
1305              format LBNAM $500. ;
MPRINT(M_EDM_BLIND):   format LBNAM $500. ;
1306              format LBREFID $500. ;
MPRINT(M_EDM_BLIND):   format LBREFID $500. ;
1307              format VISIT $500. ;
MPRINT(M_EDM_BLIND):   format VISIT $500. ;
1308              format LBDTC $500. ;
MPRINT(M_EDM_BLIND):   format LBDTC $500. ;
1309              format LBTIM $500. ;
MPRINT(M_EDM_BLIND):   format LBTIM $500. ;
1310              format LBSPEC $500. ;
MPRINT(M_EDM_BLIND):   format LBSPEC $500. ;
1311              format LBTEST $500. ;
MPRINT(M_EDM_BLIND):   format LBTEST $500. ;
1312              format LBORRES $500. ;
MPRINT(M_EDM_BLIND):   format LBORRES $500. ;
1313              format LBORRESU $500. ;
MPRINT(M_EDM_BLIND):   format LBORRESU $500. ;
1314              format LBORNRLO $500. ;
MPRINT(M_EDM_BLIND):   format LBORNRLO $500. ;
1315              format LBORNRHI $500. ;
MPRINT(M_EDM_BLIND):   format LBORNRHI $500. ;
1316              format LBSTAT $500. ;
MPRINT(M_EDM_BLIND):   format LBSTAT $500. ;
1317              format LBCOM $500. ;
MPRINT(M_EDM_BLIND):   format LBCOM $500. ;
1318            do;
MPRINT(M_EDM_BLIND):   do;
1319              EFIOUT + 1;
MPRINT(M_EDM_BLIND):   EFIOUT + 1;
1320              put STUDYID $ @;
MPRINT(M_EDM_BLIND):   put STUDYID $ @;
1321              put SUBJID $ @;
MPRINT(M_EDM_BLIND):   put SUBJID $ @;
1322              put SITEID $ @;
MPRINT(M_EDM_BLIND):   put SITEID $ @;
1323              put LBNAM $ @;
MPRINT(M_EDM_BLIND):   put LBNAM $ @;
1324              put LBREFID $ @;
MPRINT(M_EDM_BLIND):   put LBREFID $ @;
1325              put VISIT $ @;
MPRINT(M_EDM_BLIND):   put VISIT $ @;
1326              put LBDTC $ @;
MPRINT(M_EDM_BLIND):   put LBDTC $ @;
1327              put LBTIM $ @;
MPRINT(M_EDM_BLIND):   put LBTIM $ @;
1328              put LBSPEC $ @;
MPRINT(M_EDM_BLIND):   put LBSPEC $ @;
1329              put LBTEST $ @;
MPRINT(M_EDM_BLIND):   put LBTEST $ @;
1330              put LBORRES $ @;
MPRINT(M_EDM_BLIND):   put LBORRES $ @;
1331              put LBORRESU $ @;
MPRINT(M_EDM_BLIND):   put LBORRESU $ @;
1332              put LBORNRLO $ @;
18                                                           SAS 系统            2023年12月 1日 星期五 下午05时13分00秒

MPRINT(M_EDM_BLIND):   put LBORNRLO $ @;
1333              put LBORNRHI $ @;
MPRINT(M_EDM_BLIND):   put LBORNRHI $ @;
1334              put LBSTAT $ @;
MPRINT(M_EDM_BLIND):   put LBSTAT $ @;
1335              put LBCOM $ ;
MPRINT(M_EDM_BLIND):   put LBCOM $ ;
1336              ;
MPRINT(M_EDM_BLIND):   ;
1337            end;
MPRINT(M_EDM_BLIND):   end;
1338           if _ERROR_ then call symputx('_EFIERR_',1);  /* set ERROR detection macro variable */
MPRINT(M_EDM_BLIND):   if _ERROR_ then call symputx('_EFIERR_',1);
1339           if EFIEOD then call symputx('_EFIREC_',EFIOUT);
MPRINT(M_EDM_BLIND):   if EFIEOD then call symputx('_EFIREC_',EFIOUT);
1340           run;
MPRINT(M_EDM_BLIND):   run;

NOTE: FILE OUTNAME 是:
      
      文件名=/u01/app/sas/sas9.4/DocumentRepository1/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/output/hrs-3738-i-102_pk_xihua_2023
      0512_prod_coding.csv,
      所有者名=weix5,组名称=grp_cdsc_dev,
      访问权限=-rwxrwx---,
      上次修改时间=2023年12月01日 17时13分46秒

NOTE: 有 7 条记录写入到 FILE OUTNAME 中。
      最小记录长度是 115。
      最大记录长度是 148。
NOTE: 从数据集 WORK.BLINDDATA. 读取了 6 个观测
NOTE: “DATA 语句”所用时间（总处理时间）:
      实际时间          0.01 秒
      CPU 时间          0.01 秒
      

6 records created in OUTNAME from BLINDDATA.
  
  
NOTE: "OUTNAME" file was successfully created.
NOTE: “PROCEDURE EXPORT”所用时间（总处理时间）:
      实际时间          0.05 秒
      CPU 时间          0.05 秒
      

MPRINT(M_EDM_BLIND):    ;
SYMBOLGEN:  宏变量 JSONPATH 解析为 20231201171344.json
SYMBOLGEN:  宏变量 STUDYNAME 解析为 
MPRINT(M_EDM_BLIND):   x "mc mb minios3/externalfile//csv_blind";
SYMBOLGEN:  宏变量 ROOT 解析为 /u01/app/sas/sas9.4/DocumentRepository1/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM
SYMBOLGEN:  宏变量 OUTNAME 解析为 hrs-3738-i-102_pk_xihua_20230512_prod
SYMBOLGEN:  宏变量 STUDYNAME 解析为 
MPRINT(M_EDM_BLIND):   x "mc cp 
/u01/app/sas/sas9.4/DocumentRepository1/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/output/hrs-3738-i-102_pk_xihua_20230512_prod_coding
.csv minios3/externalfile//csv_blind/";