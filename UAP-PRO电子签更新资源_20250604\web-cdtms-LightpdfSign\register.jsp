﻿<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=utf-8" %>
<%@ page language="java" pageEncoding="utf-8" %>
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<fmt:setBundle  basename="net.bioknow.cdtms.lightpdfSign.i18n.${system_language}"/>


<%--<fmt:setLocale value="${system_language}" />--%>
<%--<fmt:setBundle basename="register"  var="amg"/>--%>
<%--<fmt:setBundle basename="i18n\register_${system_language}"/>--%>

<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title><fmt:message key="register.title" /></title>


    <script src="/cdtms/esign/js/jquery-3.4.1.min.js"></script>

    <style>
  body {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
  }
  .container {
    width: 500px;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 5px;
  }
  .input-container {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }
  .label {
    width: 100px;
    flex-shrink: 0;
    margin-right: 10px;
    text-align: right;
  }
  .verification-code-container {
    display: flex;
    align-items: center;
  }
  /*.verification-code-input {*/
  /*  width: 40px;*/
  /*  height: 40px;*/
  /*  text-align: center;*/
  /*  font-size: 18px;*/
  /*  margin: 0 5px;*/
  /*  border: 1px solid #ccc;*/
  /*  border-radius: 5px;*/
  /*}*/
  .verification-code-input {
      width: 40px;
      height: 40px;
      text-align: center;
      font-size: 18px;
      margin: 0 5px;
      border: none;
      outline: none;
      border-bottom: 1px solid #e6e6e6;
  }
  .input-field {
    flex-grow: 1;
    padding: 8px;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: 5px;
  }
  #signatureReason {
    width: 100%;
    padding: 8px;
    font-size: 16px;
  }
  .required {
    color: red;
  }
  button {
    display: block;
    width: 100%;
    padding: 10px;
    margin: 0 auto;
    font-size: 18px;
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 20px;
    cursor: pointer;

  }

  @media screen and (max-width: 480px) {
    .input-container {
      flex-direction: column;
      align-items: flex-start;
    }
    .label {
      width: auto;
      margin-bottom: 5px;
    }
    .verification-code-container {
      flex-direction: row;
      align-items: center;
    }
    .input-field {
      width: 95%; /* Adjust this percentage as needed */
    }
  }



  /*! CSS Used from: https://dev.bioknow.net:2299/public/bootstrap/css/bootstrap.min.css */
  /**,::after,::before{box-sizing:border-box;}*/
  /*ul{margin-top:0;margin-bottom:1rem;}*/
  /*button{border-radius:0;}*/
  button:focus{outline:1px dotted;outline:5px auto -webkit-focus-ring-color;}
  button{margin:0;font-family:inherit;font-size:inherit;line-height:inherit;}
  button{overflow:visible;}
  button{text-transform:none;}
  button,html [type=button]{-webkit-appearance:button;}
  .btn{display:inline-block;font-weight:400;text-align:center;white-space:nowrap;vertical-align:middle;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;border:1px solid transparent;padding:.375rem .75rem;font-size:1rem;line-height:1.5;border-radius:.25rem;transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;}
  .btn:focus,.btn:hover{text-decoration:none;}
  .btn:focus{outline:0;box-shadow:0 0 0 .2rem rgba(0,123,255,.25);}
  .btn:disabled{opacity:.65;}
  .btn:not(:disabled):not(.disabled){cursor:pointer;}
  .btn:not(:disabled):not(.disabled):active{background-image:none;}

  .text-center{text-align:center!important;}

  /*! CSS Used from: https://dev.bioknow.net:2299/public/css/public.css?v=1693208763838 */
  button:focus{border-color:#ced4da!important;outline:0!important;box-shadow:none!important;}
  div:focus{outline:none;}
  ul,li{list-style:none;margin:0;padding:0;}
  .btn{font-size:12px!important;outline:none!important;box-shadow:none!important;}
  .bio-dropdown{position:relative;}
  .bio-dropdown-toggle::after{display:inline-block;width:0;height:0;margin-left:.255em;vertical-align:.255em;content:"";border-top:.3em solid;border-right:.3em solid transparent;border-bottom:0;border-left:.3em solid transparent;}
  /*! CSS Used from: https://dev.bioknow.net:2299/css/login.css?v=1693208763838 */
  .bio-dropdown-toggle{background-color:#fff;border:1px solid #ddd;color:#666;border-radius:4px;padding:3px 16px;}
  .bio-dropdown-toggle span{margin-left:8px;margin-right:20px;}
  /*! CSS Used from: https://dev.bioknow.net:2299/public/bootstrap/css/bootstrap.min.css */
  /**,::after,::before{box-sizing:border-box;}*/

  /*! CSS Used from: https://dev.bioknow.net:2299/public/css/public.css?v=1693208763838 */
  /**,*:before,*:after{margin:0;padding:0;}*/
  li{list-style:none;margin:0;padding:0;}
  /*! CSS Used from: https://dev.bioknow.net:2299/css/login.css?v=1693208763838 */





  /*! CSS Used from: https://dev.bioknow.net:2299/public/bootstrap/css/bootstrap.min.css */
  ul{margin-top:0;margin-bottom:1rem;}
  img{vertical-align:middle;border-style:none;}
  .dropdown-item{display:block;width:100%;clear:both;font-weight:400;color:#212529;text-align:inherit;white-space:nowrap;background-color:transparent;border:0;}
  .dropdown-item:focus,.dropdown-item:hover{color:#16181b;text-decoration:none;background-color:#f8f9fa;}
  .dropdown-item:active{color:#fff;text-decoration:none;background-color:#007bff;}
  .dropdown-item:disabled{color:#6c757d;background-color:transparent;}
  @media print{
      *,::after,::before{text-shadow:none!important;box-shadow:none!important;}
      img{page-break-inside:avoid;}
  }
  /*! CSS Used from: https://dev.bioknow.net:2299/public/css/public.css?v=1693208763838 */
  ul,li{list-style:none;margin:0;padding:0;}
  .bio-dropdown-menu{position:absolute;top:100%;left:0;z-index:1000;display:none;float:left;font-size:1rem;text-align:left;list-style:none;background-color:#fff;background-clip:padding-box;border:1px solid #EBEEF5;border-radius:.25rem;}
  .bio-dropdown:hover .bio-dropdown-menu{display:block;}
  /*! CSS Used from: https://dev.bioknow.net:2299/css/login.css?v=1693208763838 */
  .bio-dropdown:hover .bio-dropdown-menu{display:block;}
  .bio-dropdown-menu{padding:0;border-color:#ddd;width:100%;margin:0;}
  .bio-dropdown-menu > li:hover{background-color:#e3e3e3;cursor:pointer;}
  .login-header-main .bio-dropdown-menu > li > span{margin-left:8px;}



</style>


</head>
<body>
<div class="container">


<%--    <fmt:setLocale value="cn"/>--%>

<%--        <fmt:message key="register.name" />--%>
<%--    </fmt:bundle>--%>





    <div style="justify-content: space-between;display: flex;align-items: start;">

        <h2 style="text-align: left; margin-top: 5px;">
            <c:if test="${currUserInfoMap.reg1=='1'}"><fmt:message key="register.titleFirst" /><span style="color:red">(<fmt:message key="register.titleFirst2" />)</span></c:if>
            <c:if test="${currUserInfoMap.reg1!='1'}"><fmt:message key="register.title" /></c:if>
        </h2>

<%--            <div class="bio-dropdown text-center">--%>
<%--                <button class="btn bio-dropdown-toggle" type="button">--%>

<%--                    <c:choose>--%>
<%--                        <c:when test="${cookie['signlang'].value == 'en'}">--%>
<%--                            <img src="css/images/flag_en.png" alt="">--%>
<%--                            <span>English</span>--%>
<%--                        </c:when>--%>
<%--                        <c:otherwise>--%>
<%--                            <img src="css/images/flag_cn.png" alt="">--%>
<%--                            <span>中文</span>--%>
<%--                        </c:otherwise>--%>
<%--                    </c:choose>--%>



<%--                </button>--%>
<%--                <div style="padding-top: 1px"></div>--%>
<%--                <ul class="bio-dropdown-menu">--%>
<%--                    <li class="dropdown-item" onclick="choosech(); return false;">--%>
<%--                        <img src="css/images/flag_cn.png" alt=""> <span>中文</span>--%>
<%--                    </li>--%>
<%--                    <li class="dropdown-item" onclick="chooseen(); return false;">--%>
<%--                        <img src="css/images/flag_en.png" alt=""> <span>English</span>--%>
<%--                    </li>--%>
<%--                </ul>--%>
<%--            </div>--%>
        </div>
<c:if test="${currUserInfoMap.reg1!='2'}">


    <h4 style="text-align: left; margin-top: 5px;"><fmt:message key="register.Informed" /></h4>

</c:if>

<form id="verificationForm">
    <div class="input-container">
        <label class="label" for="name"><fmt:message key="Name" />：</label>
        <c:choose>
            <c:when test="${not empty currUserInfoMap.name}">
                <span>${currUserInfoMap.name}</span>
            </c:when>
            <c:otherwise>
                <input class="input-field" type="text" id="chineseName" name="chineseName"
                       placeholder="<fmt:message key="Name.placeholder" />"
                       required
                       oninvalid="setCustomValidity('Please enter your ChineseName')"
                       oninput="setCustomValidity('')"/>
            </c:otherwise>
        </c:choose>
    </div>
    <!-- 在邮箱显示的div后面添加以下代码 -->
    <div class="input-container">
        <label class="label" for="englishName"><fmt:message key="EnglishName" />：</label>
        <input class="input-field" type="text" id="englishName" name="englishName" placeholder="<fmt:message key="EnglishName.placeholder" />"
               required oninvalid="setCustomValidity('Please enter your englishName')"
               oninput="setCustomValidity('')" />
    </div>
    <div class="input-container">
      <label class="label" for="email"><fmt:message key="Email" />：</label>
      <span>${currUserInfoMap.email}</span>
    </div>

<%--    <div class="input-container">--%>
<%--        <label class="label">验证码：</label>--%>
<%--        <div class="login-input mr-8">--%>
<%--            <input type="text" name="resetcheckcode" id="resetcheckcode" placeholder="验证码" onblur="if($(this).val()==''){$('#checkcode_info').html('必须填写验证码！')}else{$('#checkcode_info').html('')}">--%>
<%--        </div>--%>
<%--        <button type="button" id="checkcodebutton" onclick="sendcheckcode();" class="btn btn-outline-secondary btn-code">--%>
<%--            发送验证码--%>
<%--        </button>--%>
<%--    </div>--%>

<c:if test="${currUserInfoMap.reg1=='2'}">
    <div class="input-container">

        <button type="button" id="checkcodebutton" onclick="sendcheckcode();" class="btn btn-outline-secondary btn-code">
            <fmt:message key="register.sendcheckcode" />
        </button>

    </div>

    <div class="input-container"
            <c:if test="${currUserInfoMap.reg1=='2'}">

                style=" display: none;"
            </c:if>

         id="div1">
      <label class="label" for="verificationCode"><fmt:message key="register.Checkcode" />：</label>
      <div class="verification-code-container" id="verificationCodeContainer">
        <input class="verification-code-input" type="text" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
        <input class="verification-code-input" type="text" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
        <input class="verification-code-input" type="text" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
        <input class="verification-code-input" type="text" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
        <input class="verification-code-input" type="text" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
        <input class="verification-code-input" type="text" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
      </div>

    </div>
</c:if>
    <div class="input-container"
<c:if test="${currUserInfoMap.reg1=='2'}">

    style=" display: none;"
    </c:if>
 id="div2">
        <label class="label" for="verificationCode_2"><fmt:message key="Password" />：</label>
        <div class="verification-code-container" id="verificationCodeContainer_2">
            <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
            <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
            <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
            <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
            <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
            <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
        </div>
    </div>

    <div class="input-container"
            <c:if test="${currUserInfoMap.reg1=='2'}">

                style=" display: none;"
            </c:if>

         id="div3">
        <label class="label" for="verificationCode_3"><fmt:message key="confirmPassword" />：</label>
        <div class="verification-code-container" id="verificationCodeContainer_3">
            <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
            <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
            <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
            <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
            <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
            <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
        </div>
    </div>
    <c:if test="${currUserInfoMap.reg1!='2'}">


        <h6 style="text-align: left; margin-top: 5px;"><fmt:message key="register.notice" /></h6>

    </c:if>

    <div class="input-container">

        <c:if test="${currUserInfoMap.reg1!='2'}">

            <button type="cancel" id="cancelButton" onclick="window.close()" style="background-color:#5a7087"><fmt:message key="cancel" /></button>
        </c:if>

    <button type="submit" id="verifyButton"

            <c:if test="${currUserInfoMap.reg1=='2'}">

                style=" display: none;"
            </c:if>
    ><fmt:message key="submit" /></button>

    </div>
	 <p id="errorMessage" style="color: red;"></p>
  <p id="successMessage" style="color: green;"></p>
  </form>
</div>


<script>


<%--    <c:if test="${cookie['signlang'].value != 'en'}">--%>
<%--    chooseen();--%>
<%--    </c:if>--%>
<%--    <c:if test="${cookie['signlang'].value != 'zh'}">--%>
<%--    choosech();--%>
<%--    </c:if>--%>

    function choosech() {
        setCookieByDay('signlang', 'cn', 30);
        window.location.reload();
        return false;
    }
    function chooseen() {
        setCookieByDay('signlang', 'en', 30);
        window.location.reload();
        return false;
    }
    function setCookieByDay(name, value, days) {
        if(days==undefined || days==null || days<=0){
            setCookie(name,value);
        }else{
            var today = new Date();
            var expires_date = new Date( today.getTime() + days * 1000 * 60 * 60 * 24 );
            setCookie(name,value,expires_date);
        }
    }

    function setCookie(name, value, expires, path, domain, secure) {
        var curCookie = name + "=" + escape(value) +
            ((expires) ? "; expires=" + expires.toGMTString() : "") +
            ((path) ? "; path=" + path : "") +
            ((domain) ? "; domain=" + domain : "") +
            ((secure) ? "; secure" : "");
        document.cookie = curCookie;
    }
    const verificationCodeContainer = document.getElementById("verificationCodeContainer");

    if(verificationCodeContainer){
    const verificationCodeInputs = verificationCodeContainer.querySelectorAll(".verification-code-input");


    verificationCodeInputs.forEach(input => {
      input.addEventListener("input", () => {
        const val = ((input.value || '').match(/[0-9]/) || [])[0];

        if (val) {
          input.value = val;

          if (input.nextElementSibling) input.nextElementSibling.focus();
        } else {
          input.value = '';
        }
      });

      input.addEventListener("keydown", event => {
        if (event.key === "Backspace" && !input.value && input.previousElementSibling) {
          input.previousElementSibling.focus();
        }
      });




      input.addEventListener("paste", event => {
        event.preventDefault();
        const pasteText = event.clipboardData.getData('text');
        const numbers = pasteText.match(/[0-9]/g) || [];
        const length = numbers.length;
        for (let i = 0; i < 6; i++) {
          const itemText = i < length ? numbers[i] : '';
          verificationCodeInputs[i].value = itemText;
        }
        if (verificationCodeInputs[length]) verificationCodeInputs[length].focus();
      })
    });

    }
    const verificationCodeContainer2 = document.getElementById("verificationCodeContainer_2");
    const verificationCodeInputs2 = verificationCodeContainer2.querySelectorAll(".verification-code-input");


    verificationCodeInputs2.forEach(input => {
        input.addEventListener("input", () => {
            const val = ((input.value || '').match(/[0-9]/) || [])[0];

            if (val) {
                input.value = val;

                if (input.nextElementSibling) input.nextElementSibling.focus();
            } else {
                input.value = '';
            }
        });

        input.addEventListener("keydown", event => {
            if (event.key === "Backspace" && !input.value && input.previousElementSibling) {
                input.previousElementSibling.focus();
            }
        });




        input.addEventListener("paste", event => {
            event.preventDefault();
            const pasteText = event.clipboardData.getData('text');
            const numbers = pasteText.match(/[0-9]/g) || [];
            const length = numbers.length;
            for (let i = 0; i < 6; i++) {
                const itemText = i < length ? numbers[i] : '';
                verificationCodeInputs[i].value = itemText;
            }
            if (verificationCodeInputs[length]) verificationCodeInputs[length].focus();
        })
    });

const verificationCodeContainer3 = document.getElementById("verificationCodeContainer_3");
const verificationCodeInputs3 = verificationCodeContainer3.querySelectorAll(".verification-code-input");


verificationCodeInputs3.forEach(input => {
    input.addEventListener("input", () => {
        const val = ((input.value || '').match(/[0-9]/) || [])[0];

        if (val) {
            input.value = val;

            if (input.nextElementSibling) input.nextElementSibling.focus();
        } else {
            input.value = '';
        }
    });

    input.addEventListener("keydown", event => {
        if (event.key === "Backspace" && !input.value && input.previousElementSibling) {
            input.previousElementSibling.focus();
        }
    });




    input.addEventListener("paste", event => {
        event.preventDefault();
        const pasteText = event.clipboardData.getData('text');
        const numbers = pasteText.match(/[0-9]/g) || [];
        const length = numbers.length;
        for (let i = 0; i < 6; i++) {
            const itemText = i < length ? numbers[i] : '';
            verificationCodeInputs[i].value = itemText;
        }
        if (verificationCodeInputs[length]) verificationCodeInputs[length].focus();
    })
});


  const errorMessage = document.getElementById("errorMessage");


    function sendcheckcode(){

        $("#checkcodebutton").attr("disabled",true);
        $("#div1").show();
        $("#div2").show();
        $("#div3").show();
        $("#verifyButton").show();
        // Start a 60-second countdown
        var countdown = 60;
        var countdownInterval = setInterval(function() {
            $("#checkcodebutton").text("<fmt:message key="register.sendcheckcode" /> (" + countdown + "s)");
            countdown--;

            // Enable the button after 60 seconds
            if (countdown < 0) {
                clearInterval(countdownInterval);
                $("#checkcodebutton").text("<fmt:message key="register.sendcheckcode" />");
                $("#checkcodebutton").attr("disabled", false);
            }
        }, 1000);

        $.ajax( {
            url: "lightpdfSign.sendcheckcode.do",
            type: "POST",
            async: false,
            data: {email:'${currUserInfoMap.email}',language:'${system_language}',system:'${system}'},
            success: function(msg){
                if(msg=="OK"){
                    let language = "${system_language}";
                    // alert("发送成功，请查收邮件！");
                    if(language === "cn") {
                        alert("发送成功，请查收邮件！");
                    }else{
                        alert("Sent successfully. Please check your email! ");
                    }
                }else{
                    alert(msg);
                    $("#checkcodebutton").attr("disabled",false);
                }
            }
        });
    }


    <%--function resetpwd(){--%>
    <%--    var isvalidate = true;--%>
    <%--    var mobile = "";--%>


    <%--    if(resetcheckcode==""){--%>
    <%--        $("#checkcode_info").html("必须填写验证码！");--%>
    <%--        isvalidate = false;--%>
    <%--    }else{--%>
    <%--        $("#checkcode_info").html("");--%>
    <%--    }--%>
    <%--    if($("#newpwd").val()==""||$("#newpwd").val()==null){--%>
    <%--        $("#newpwd_info").html("必须填写密码");--%>
    <%--        isvalidate = false;--%>
    <%--    }else{--%>
    <%--        $("#newpwd_info").html("");--%>
    <%--    }--%>
    <%--    var newpwd = $("#newpwd").val();--%>


    <%--    if(!isvalidate) return;--%>
    <%--    //密码有效性验证--%>

    <%--    $.ajax( {--%>
    <%--        url: "lightpdfSign.resetpwdsubmit.do",--%>
    <%--        type: "POST",--%>
    <%--        async: false,--%>
    <%--        data: {checkcode:resetcheckcode,email:${currUserInfoMap.email},newpwd:newpwd},--%>
    <%--        success: function(msg){--%>
    <%--            if(msg=="OK"){--%>
    <%--                notifySuccess('密码重置成功，请返回登录！');--%>
    <%--                window.location.reload();--%>
    <%--            }else{--%>
    <%--                notifyWarning(msg);--%>
    <%--            }--%>
    <%--        }--%>
    <%--    });--%>

    <%--}--%>

  var regtype="${currUserInfoMap.reg1}";
  verificationForm.addEventListener("submit", async (e) => {
	e.preventDefault();
    // const signatureReason = document.getElementById("signatureReason").value;
	const verificationCodeContainer = document.getElementById("verificationCodeContainer");
      let verificationCode = "";

    if(verificationCodeContainer){
    const verificationCodeInputs = verificationCodeContainer.querySelectorAll(".verification-code-input");

        verificationCodeInputs.forEach(input => {
            verificationCode += input.value;
        });
    }
      const englishName = document.getElementById("englishName").value;
      const chineseName = document.getElementById("chineseName") ? document.getElementById("chineseName").value : '${currUserInfoMap.name}';
      //
    const verificationCodeContainer2 = document.getElementById("verificationCodeContainer_2");
    const verificationCodeInputs2 = verificationCodeContainer2.querySelectorAll(".verification-code-input");
    const verificationCodeContainer3 = document.getElementById("verificationCodeContainer_3");
    const verificationCodeInputs3 = verificationCodeContainer3.querySelectorAll(".verification-code-input");


      let verificationCode2 = "";
      verificationCodeInputs2.forEach(input => {
          verificationCode2 += input.value;
      });

      let verificationCode3 = "";
      verificationCodeInputs3.forEach(input => {
          verificationCode3 += input.value;
      });


      if(verificationCode2!=verificationCode3){


          errorMessage.textContent = "<fmt:message key="register.changeErrorInfo" />!";

          return;


      }



      $.ajax( {
          url: "lightpdfSign.resetpwdsubmit.do",
          type: "POST",
          async: false,
          data: {checkcode:regtype==2?verificationCode:'${currUserInfoMap.verificationCode}',email:'${currUserInfoMap.email}',newpwd:verificationCode2,system:'${system}',englishName: englishName,
              name: (${not empty currUserInfoMap.name}) ? '${currUserInfoMap.name}' : chineseName
          },
          success: function(msg){
              if(msg=="OK"){
                  successMessage.textContent = "Verification success！";
                  window.location='${currUserInfoMap.redirectUrl}';
                  if(regtype==2) {
                      window.parent.location.reload();

                  }else{
                  window.location.reload();
                  }

              }else{
                  errorMessage.textContent = "Verification error!";
                  successMessage.textContent = "";
              }
          }
      });

  });


document.getElementById('cancelButton').addEventListener('click', function(event) {
        event.preventDefault();

    window.open('','_self');

        window.close();

    }
);
</script>

</body>
</html>
