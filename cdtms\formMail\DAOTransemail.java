//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package net.bioknow.cdtms.formMail;

import com.sun.mail.smtp.SMTPTransport;
import com.sun.mail.util.MailSSLSocketFactory;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.OutputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import javax.activation.DataHandler;
import javax.activation.FileDataSource;
import javax.mail.Authenticator;
import javax.mail.Message;
import javax.mail.Multipart;
import javax.mail.Session;
import javax.mail.Message.RecipientType;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;
import net.bioknow.mvc.tools.Language;
import net.bioknow.mvc.tools.WebPath;
import net.bioknow.passport.datamng.DAOPPData;
import net.bioknow.passport.datamng.JDOUser;
import net.bioknow.passport.datamng.PassportCacheUtil;
import net.bioknow.passport.uiframe.UIFrameUtil;
import net.bioknow.passport.webvar.ServerType;
import net.bioknow.webio.filedb.FileDBFactory;
import net.bioknow.webplug.transemail.CNT_Transemail;
import net.bioknow.webplug.transemail.LogEmail;
import net.bioknow.webutil.tools.Log;

public class DAOTransemail implements Runnable {
    public static String temppath = "webplug/transemail/temp";
    private static ConcurrentMap cacheMap = new ConcurrentHashMap();
    private static Map mailMapPublic = null;
    private String projectId;

    public static String getPwdTemplate(String projectId, Language lang) throws Exception {
        DAOTransemail dao = new DAOTransemail(projectId);
        Map map = dao.getMap();
        String transemail = (String)map.get("transemail");
        String ret = "";
        if (transemail != null && transemail.equals("1")) {
            String pwd_template = (String)map.get(CNT_Transemail.pwd_template);
            String pwd_templateen = (String)map.get(CNT_Transemail.pwd_templateen);
            if (pwd_templateen != null && !pwd_templateen.equals("") && Language.getLocString().indexOf("en") >= 0) {
                pwd_template = pwd_templateen;
            }

            ret = pwd_template;
        }

        return ret;
    }

    public static String getResetPwdTemplate(String projectId, Language lang) throws Exception {
        DAOTransemail dao = new DAOTransemail(projectId);
        Map map = dao.getMap();
        String transemail = (String)map.get("transemail");
        String ret = "";
        if (transemail != null && transemail.equals("1")) {
            String resetpwd_template = (String)map.get(CNT_Transemail.resetpwd_template);
            String resetpwd_templateen = (String)map.get(CNT_Transemail.resetpwd_templateen);
            if (resetpwd_templateen != null && !resetpwd_templateen.equals("") && Language.getLocString().indexOf("en") >= 0) {
                resetpwd_template = resetpwd_templateen;
            }

            ret = resetpwd_template;
        }

        return ret;
    }



    public static void reloadConfig() {
        mailMapPublic = null;
    }

    public static void reloadConfig(String projectId) {
        if (cacheMap.containsKey(projectId)) {
            cacheMap.remove(projectId);
        }

    }

    public static String replaceTemplate(String projectid, String content, String loginid, String password) {
        DAOPPData udao = new DAOPPData(projectid);
        if (password == null) {
            password = "";
        }

        JDOUser user = udao.getUser(loginid);
        boolean var6 = false;

        while(true) {
            int p = content.indexOf("{serveraddr}");
            if (p < 0) {
                while(true) {
                    p = content.indexOf("{username}");
                    if (p < 0) {
                        while(true) {
                            p = content.indexOf("{loginid}");
                            if (p < 0) {
                                while(true) {
                                    p = content.indexOf("{password}");
                                    if (p < 0) {
                                        while(true) {
                                            p = content.indexOf("{projectid}");
                                            if (p < 0) {
                                                p = content.indexOf("{rolename}");
                                                if (p >= 0) {
                                                    String rolename = PassportCacheUtil.getUserRoleStrById(projectid, user.getId());
                                                    content = content.substring(0, p) + rolename + content.substring(p + 10);
                                                }

                                                while(true) {
                                                    int pp1 = content.indexOf("${");
                                                    if (pp1 < 0) {
                                                        return content;
                                                    }

                                                    int pp2 = content.indexOf("}", pp1);
                                                    String param = content.substring(pp1 + 2, pp2);
                                                    String[] pA = param.split(",");
                                                    String v = "";
                                                    if (pA.length > 1) {
                                                        String tableid = pA[0];
                                                        String fid = pA[1];
                                                        String where = "";
                                                        if (pA.length == 3) {
                                                            where = pA[3];
                                                        }

                                                        v = UIFrameUtil.replaceValue(projectid, tableid, fid, where);
                                                    }

                                                    content = content.substring(0, pp1) + v + content.substring(pp2 + 1);
                                                }
                                            }

                                            content = content.substring(0, p) + projectid + content.substring(p + 11);
                                        }
                                    }

                                    content = content.substring(0, p) + password + content.substring(p + 10);
                                }
                            }

                            content = content.substring(0, p) + loginid + content.substring(p + 9);
                        }
                    }

                    content = content.substring(0, p) + user.getRealName() + content.substring(p + 10);
                }
            }

            content = content.substring(0, p) + ServerType.getCurrentAddr(projectid) + content.substring(p + 12);
        }
    }

    public static synchronized String sendmail(String subject, String content, File[] fA, String[] fNameA, String receiver, String sender, String replyto, String smtp, String port, String username, String pwd, boolean needSSL, String debug) throws Exception {
        return sendmail(subject, content, fA, fNameA, receiver, (String)null, sender, replyto, smtp, port, username, pwd, needSSL, debug);
    }

    public static synchronized String sendmail(String subject, String content, File[] fA, String[] fNameA, String receiver, String cc, String sender, String replyto, String smtp, String port, String username, String pwd, boolean needSSL, String debug) throws Exception {
        return sendmail(subject, content, fA, fNameA, receiver, cc, sender, replyto, smtp, port, username, pwd, needSSL, true, debug);
    }




    public static synchronized String sendmail(String subject, String content, File[] fA, String[] fNameA, String receiver, String cc, String sender, String replyto, String smtp, String port, String username, String pwd, boolean needSSL, boolean needAuth, String debug) throws Exception {
        if (replyto == null) {
            replyto = "";
        }

        StringBuffer sb = new StringBuffer();
        sb.append("----Sendmail Info----\r\n");
        sb.append("receiver:" + receiver + "\r\n");
        if (cc != null && !cc.equals("")) {
            sb.append("cc      :" + cc + "\r\n");
        }

        sb.append("sender  :" + sender + "\r\n");
        sb.append("replyto :" + replyto + "\r\n");
        sb.append("smtp    :" + smtp + "\r\n");
        sb.append("port    :" + port + "\r\n");
        sb.append("username:" + username + "\r\n");
        sb.append("needSSL :" + needSSL + "\r\n");
        sb.append("needAuth:" + needAuth + "\r\n");
        sb.append("subject :" + subject);
        LogEmail.info(sb.toString());
        if (receiver != null && !receiver.equals("") && receiver.indexOf("@") < 0) {
            return "ERR receiver";
        } else if (replyto != null && !replyto.equals("") && replyto.indexOf("@") < 0) {
            return "ERR replyto";
        } else {
            SMTPTransport t = null;

            String var29;
            try {
                Properties props = new Properties();
                props.put("mail.smtp.host", smtp);
                if (needAuth) {
                    props.put("mail.smtp.auth", "true");
                } else {
                    props.put("mail.smtp.auth", "false");
                }

                String portInuse = "25";
                if (port != null && !port.trim().equals("") && Integer.parseInt(port) > 0) {
                    portInuse = port.trim();
                }

                props.put("mail.smtp.port", portInuse);
                props.put("mail.smtp.connectiontimeout", "15000");
                props.put("mail.smtp.timeout", "15000");
                if (needSSL) {
                    portInuse = "587";
                    if (port != null && !port.trim().equals("")) {
                        portInuse = port.trim();
                    }

                    props.put("mail.smtp.starttls.enable", portInuse);
                    props.put("mail.smtp.ssl.enable", "true");
                    String SSL_FACTORY = "javax.net.ssl.SSLSocketFactory";
                    props.put("mail.smtp.socketFactory.class", SSL_FACTORY);
                    props.put("mail.smtp.socketFactory.fallback", "false");
                    props.put("mail.smtp.socketFactory.port", portInuse);
                    props.put("mail.smtp.port", portInuse);
                    MailSSLSocketFactory sf = new MailSSLSocketFactory();
                    sf.setTrustAllHosts(true);
                    props.put("mail.smtp.ssl.socketFactory", sf);
                }

                Session session = Session.getInstance(props, (Authenticator)null);
                if (debug != null && debug.equals("1")) {
                    session.setDebug(true);
                }

                Message msg = new MimeMessage(session);
                msg.setFrom(new InternetAddress(sender));
                msg.setRecipients(RecipientType.TO, InternetAddress.parse(receiver, false));
                if (replyto != null && !replyto.equals("")) {
                    msg.setReplyTo(InternetAddress.parse(replyto, false));
                }

                if (cc != null && !cc.equals("")) {
                    msg.setRecipients(RecipientType.CC, InternetAddress.parse(cc, false));
                }

                msg.setSubject(subject);
                msg.setHeader("X-Mailer", "bioknowtransemail");
                msg.setSentDate(new Date());
                if (fA != null && fA.length != 0) {
                    Multipart mainPart = new MimeMultipart();
                    MimeBodyPart messageBodyPart = new MimeBodyPart();
                    messageBodyPart.setContent(content, "text/html; charset=utf-8");
                    mainPart.addBodyPart(messageBodyPart);

                    for(int i = 0; i < fA.length; ++i) {
                        File f = fA[i];
                        String fname = f.getName();
                        if (fNameA != null && fNameA.length > i) {
                            fname = fNameA[i];
                        }

                        if (f.exists()) {
                            FileDataSource fds = new FileDataSource(f);
                            MimeBodyPart attachBodyPart = new MimeBodyPart();
                            attachBodyPart.setDataHandler(new DataHandler(fds));
                            attachBodyPart.setFileName(MimeUtility.encodeText(fname, "UTF-8", (String)null));
                            mainPart.addBodyPart(attachBodyPart);
                        }
                    }

                    msg.setContent(mainPart);
                } else {
                    msg.setContent(content, "text/html;charset=UTF-8");
                }

                t = (SMTPTransport)session.getTransport("smtp");
                if (username != null && !username.equals("")) {
                    if (needAuth) {
                        t.connect(smtp, username, pwd);
                    } else {
                        t.connect(smtp, username, (String)null);
                    }
                } else {
                    t.connect();
                }

                LogEmail.info("Before send message:" + t.getLastServerResponse());
                t.sendMessage(msg, msg.getAllRecipients());
                String str = t.getLastServerResponse();
                LogEmail.info("After send message:" + str);
                LogEmail.info("----Done----");
                String var37 = str;
                return var37;
            } catch (Exception var32) {
                LogEmail.error("[Error:]", var32);
                var29 = "ERR";
            } finally {
                if (t != null && t.isConnected()) {
                    t.close();
                }

            }

            return var29;
        }
    }

    public DAOTransemail(String projectId) {
        this.projectId = projectId;
    }

    public boolean allowSendMail() throws Exception {
        Map map = this.getMap();
        String transemail = (String)map.get("transemail");
        return transemail != null && transemail.equals("1");
    }

    public File getFile() throws Exception {
        return FileDBFactory.getInstance(this.projectId).getFile("config/notice", "transemail.txt");
    }

    public File getFilePublic() throws Exception {
        File fMailProperties = new File(WebPath.getRootPath() + "/WEB-INF/mail.properties");
        return fMailProperties;
    }

    public Map getMapOfSendMail() throws Exception {
        Map mapPublic = this.getMapOfPublic();
        String transemail = (String)mapPublic.get("transemail");
        return transemail != null && transemail.equals("1") ? mapPublic : this.getMap();
    }

    public boolean isOpen() throws Exception {
        Map map = this.getMap();
        String transemail = (String)map.get("transemail");
        return transemail != null && transemail.equals("1");
    }

    public String sendmail(String subject, String content, String receiver) throws Exception {
        return this.sendmail(subject, content, receiver, (String)null);
    }

    public String sendmail(String subject, String content, String receiver, String replyto) throws Exception {
        return this.sendmail(subject, content, receiver, replyto, (File[])null, (String[])null);
    }

    public String sendmail(String subject, String content, String receiver, String replyto, File[] fA, String[] fNameA) throws Exception {
        return this.sendmail((String)null, subject, content, receiver, (String)null, replyto, fA, fNameA);
    }

    public String sendmail(String emailSender, String subject, String content, String receiver, String cc, String replyto, File[] fA, String[] fNameA) throws Exception {
        Map map = this.getMapOfSendMail();
        String transemail = (String)map.get("transemail");
        if (transemail != null && transemail.equals("1")) {
            String smtp = (String)map.get("smtp");
            String username = (String)map.get("username");
            String pwd = (String)map.get("pwd");
            String port = (String)map.get("port");
            String sender = emailSender != null && !emailSender.equals("") ? emailSender : (String)map.get("sender");
            String signinfo = (String)map.get("signinfo");
            String needssl = (String)map.get("needssl");
            String closeauth = (String)map.get("closeauth");
            String debug = (String)map.get("debug");
            if (content == null) {
                content = "";
            }

            if (!content.equals("")) {
                content = content + "<br><br>";
            }

            if (signinfo != null && !signinfo.equals("")) {
                content = content + signinfo;
            }

            boolean ssl = false;
            if (needssl != null && needssl.equals("true")) {
                ssl = true;
            }

            boolean auth = true;
            if (closeauth != null && closeauth.equals("true")) {
                auth = false;
            }

            String ret = sendmail(subject, content, fA, fNameA, receiver, cc, sender, replyto, smtp, port, username, pwd, ssl, auth, debug);
            if (ret != null && ret.indexOf("ERR") == 0) {
                String smtp2 = (String)map.get("smtp2");
                String username2 = (String)map.get("username2");
                String pwd2 = (String)map.get("pwd2");
                String port2 = (String)map.get("port2");
                String sender2 = (String)map.get("sender2");
                String needssl2 = (String)map.get("needssl2");
                if (smtp2 != null && !smtp2.equals("")) {
                    boolean ssl2 = false;
                    if (needssl2 != null && needssl2.equals("true")) {
                        ssl2 = true;
                    }

                    ret = sendmail(subject, content, fA, fNameA, receiver, cc, sender2, replyto, smtp2, port2, username2, pwd2, ssl2, auth, debug);
                }
            }

            return ret;
        } else {
            Log.info("SMTP: 未设定邮件服务器，无法发送邮件(" + this.projectId + ")");
            return "";
        }
    }

    protected Map getMap() throws Exception {
        if (!cacheMap.containsKey(this.projectId)) {
            File file = this.getFile();
            Map map = new HashMap();
            if (file.exists()) {
                ObjectInputStream ois = new ObjectInputStream(new FileInputStream(file));
                map = (Map)ois.readObject();
                ois.close();
            }

            cacheMap.put(this.projectId, map);
        }

        return (Map)cacheMap.get(this.projectId);
    }

    protected Map getMapOfPublic() throws Exception {
        if (mailMapPublic == null) {
            File fMailProperties = this.getFilePublic();
            if (fMailProperties.exists()) {
                Properties config = new Properties();
                InputStream in = new FileInputStream(fMailProperties);
                config.load(in);
                mailMapPublic = config;
                Log.info("Load " + fMailProperties.getAbsolutePath());
                Log.info("  " + mailMapPublic.toString());
                return config;
            }

            mailMapPublic = new HashMap();
        }

        return mailMapPublic;
    }

    protected void saveMap(Map map) throws Exception {
        File file = this.getFile();
        if (file.exists()) {
            file.delete();
        }

        file.createNewFile();
        ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(file));
        oos.writeObject(map);
        oos.close();
        if (cacheMap.containsKey(this.projectId)) {
            cacheMap.remove(this.projectId);
        }

    }

    protected void saveMapPublic(Map map) throws Exception {
        File file = this.getFilePublic();
        if (file.exists()) {
            file.delete();
        }

        file.createNewFile();
        Properties config = new Properties();
        config.putAll(map);
        OutputStream os = new FileOutputStream(file);
        config.store(os, (String)null);
        os.close();
        mailMapPublic = null;
    }

    @Override
    public void run() {

    }
}
