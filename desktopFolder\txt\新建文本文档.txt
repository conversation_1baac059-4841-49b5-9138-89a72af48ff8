npm config set registry https://npm.aliyun.com



npm config set registry https://registry.npmmirror.com



this.$refs['compoundForm'].validate((valid) => {

var param = {}
      param.compoundNo = this.form.compound_no
      // param.compoundName=this.form.compound_name.replace(/,/g, ';')
      param.compoundName = 'NA'
      addCompoundInfo(param).then((res) => {
        debugger
        if (res.code === 200 && res.data === 'success') {
          this.$message.success('新增化合物信息成功!')
          this.compoundDialogVisible = false
          this.compoundInfoSearch()
          this.getProSelectInfo()
        } else if (res.data === 'exist') {
          this.$message.warning('该化合物代码已存在!')
        } else {
          this.$message.error('新增化合物信息失败!')
        }
      })

})




UPDATE orders
SET description = REPLACE(description, 'abc', 'def')
WHERE description LIKE '%abc%';


      OperateEntity operate = new OperateEntity(userId, String.valueOf(entity.getSiteId()), OperateType.EDIT_SITE.toString(), ObjectType.SITE_INFO.toString(), "项目代码：" + entity.getStudyNo()+"化合物代码：" + entity.getCompoundNo() + " 中心编号:" + entity.getSiteNo()+" 中心名称：" + entity.getSiteName(), "", 'Y');
            fileUploadMapper.addOperateRecord(operate);


List<TUserInfoEntity> getReceiverBySiteId=fileUploadMapper.getReceiverBySiteId(entity.getStudyNo() + entity.getSiteNo());
       for(TUserInfoEntity enti:getReceiverBySiteId){
                StringBuilder message = new StringBuilder();
                message.append("项目代码:").append(entity.getStudyNo())
                        .append(" 中心编号:").append(entity.getSiteNo())
                        .append(" 角色:").append(enti.getRoleName())
                        .append(" 邮箱:").append(enti.getEmail())
                        .append(" 是否接收SUSAR报告:").append(enti.getIsReceive().equals("Y") ? "是" : "否");
                //存储修改收件人的操作记录
                OperateEntity operateReceiver = new OperateEntity(userId, String.valueOf(enti.getId()), OperateType.EDIT_RECEIVER.toString(), ObjectType.RECEIVER_INFO.toString(), message.toString(), "", 'Y');
                fileUploadMapper.addOperateRecord(operateReceiver);
                
                
                
                
                !ObjectUtils.isEmpty(entity)&&!ObjectUtils.isEmpty(entity.getFileName())&&!entity.getFileName().isEmpty()&&entity.getFilterColumn()!=null&&!entity.getType().equals("2")
                !ObjectUtils.isEmpty(entity)&&!ObjectUtils.isEmpty(entity.getFileName())&&!entity.getFileName().isEmpty()&&entity.getFilterColumn()!=null&&entity.getType().equals("2")
                
                
                
                
                
     with minio_tbl AS (SELECT
        *,
        toString(sipHash64(*)) AS lab_data_id
        FROM s3(#{fileName}, 'minioadmin', 'minioadmin','CSVWithNames'))
        SELECT   distinct temp1.`${filterColumn}` as text, temp1.`${filterColumn}` as value
        FROM
        ( SELECT * FROM minio_tbl
        ) AS temp1
        GLOBAL LEFT JOIN ( SELECT * FROM mysql ( '***********:3306', 'dm_platform', 'dm_lab_ae_review', 'susaruser', 'Hr@db0316' ) ) AS mysql_lab_review USING lab_data_id
        <if test="centerCode != null and centerCode != '' and type==0">
            where  `中心`= #{centerCode} and `质疑` is not  null and `质疑`!='' and toString(sipHash64(`质疑`))!='15150039259969081993' and (or(equals(is_viewed,'0'),equals(is_viewed,'')))
        </if>
        <if test="centerCode == ''and type==0">
            where or(equals(is_viewed,''), equals(is_viewed,'0'))
        </if>
        <if test="centerCode != null and centerCode != '' and type==1">
            where  `中心`= #{centerCode} and `质疑` is not  null and `质疑`!='' and toString(sipHash64(`质疑`))!='15150039259969081993' and equals(is_viewed,'1')
        </if>
        <if test="centerCode == ''and type==1">
            where equals(is_viewed,'1')
        </if>
        <if test="labAeId != null and labAeId != ''and type==3">
            where  equals(lab_data_id,#{labAeId})
        </if>
        <if test="filterItem != null and filterItem.size() > 0 and columnName !=null and columnName.size() > 0">
            AND
            <foreach collection="columnName"  index="index" item="item" open="(" separator="AND" close=")">
                `${item}`  in #{filterItem}
            </foreach>

        </if>

        <if test="sortColumn != null and  sortOrder!=null ">
            order by  `${sortColumn}` ${sortOrder}
        </if>
    </select>           
                
                
           getColumnValue     
                
                param.filterItem = this.selectedColumnFilterItems
                
                
                
                
                getColumnSelectValues
                
                
                
                
                
                
                
                
                with minio_tbl AS (SELECT
        *,
        toString(sipHash64(*)) AS lab_data_id
        FROM s3(#{entity.fileName}, 'minioadmin', 'minioadmin','CSVWithNames'))
        SELECT
        temp1.*,
        mysql_lab_review.is_viewed
        FROM
        ( SELECT * FROM minio_tbl
        ) AS temp1
        GLOBAL LEFT JOIN ( SELECT * FROM mysql ( '***********:3306', 'dm_platform', 'dm_lab_ae_review', 'susaruser', 'Hr@db0316' ) ) AS mysql_lab_review USING lab_data_id
        <if test="entity.centerCode != null and entity.centerCode != '' and entity.type==0">
            where  `中心`= #{entity.centerCode} and `质疑` is not  null and `质疑`!='' and toString(sipHash64(`质疑`))!='15150039259969081993' and (or(equals(is_viewed,'0'),equals(is_viewed,'')))
        </if>
        <if test="entity.centerCode == ''and entity.type==0">
            where or(equals(is_viewed,''), equals(is_viewed,'0'))
        </if>
        <if test="entity.centerCode != null and entity.centerCode != '' and entity.type==1">
            where  `中心`= #{entity.centerCode} and `质疑` is not  null and `质疑`!='' and toString(sipHash64(`质疑`))!='15150039259969081993' and equals(is_viewed,'1')
        </if>
        <if test="entity.centerCode == ''and entity.type==1">
            where equals(is_viewed,'1')
        </if>
        <if test="entity.labAeId != null and entity.labAeId != ''and entity.type==3">
            where  equals(lab_data_id,#{entity.labAeId})
        </if>
        <if test="entity.filterItem != null and entity.filterItem.size() > 0 and entity.columnName !=null and entity.columnName.size() > 0">
            AND
            <foreach collection="entity.columnName"  index="index" item="item" open="(" separator="AND" close=")">
                 `${item}`  in #{entity.filterItem}
            </foreach>

        </if>

        <if test="entity.sortColumn != null and  entity.sortOrder!=null ">
            order by  `${entity.sortColumn}` ${entity.sortOrder}
        </if>
                 用户名:<EMAIL>', 密码:$2a$10$UJ.7YMepHJdkMwGbY9bnWej5kyyBtzbTnUxh59TTbRW4KveAFUJwG', 用户邮箱:null', 用户角色:CTA', 项目代码:null', 生效状态null
                
                ctaPro
                
                
                
          with minio_tbl AS (SELECT
        *,
        toString(sipHash64(*)) AS lab_data_id
        FROM s3(#{fileName}, 'minioadmin', 'minioadmin','CSVWithNames'))
        SELECT   distinct temp1.`${filterColumn}` as text, temp1.`${filterColumn}` as value
        FROM
        ( SELECT * FROM minio_tbl
        ) AS temp1
        GLOBAL LEFT JOIN ( SELECT * FROM mysql ( '***********:3306', 'dm_platform', 'dm_lab_ae_review', 'susaruser', 'Hr@db0316' ) ) AS mysql_lab_review USING lab_data_id
        <if test="centerCode != null and centerCode != '' and type==0">
            where  `中心`= #{centerCode} and `质疑` is not  null and `质疑`!='' and toString(sipHash64(`质疑`))!='15150039259969081993' and (or(equals(is_viewed,'0'),equals(is_viewed,'')))
        </if>
        <if test="centerCode == ''and type==0">
            where or(equals(is_viewed,''), equals(is_viewed,'0'))
        </if>
        <if test="centerCode != null and centerCode != '' and type==1">
            where  `中心`= #{centerCode} and `质疑` is not  null and `质疑`!='' and toString(sipHash64(`质疑`))!='15150039259969081993' and equals(is_viewed,'1')
        </if>
        <if test="centerCode == ''and type==1">
            where equals(is_viewed,'1')
        </if>
        <if test="labAeId != null and labAeId != ''and type==3">
            where  equals(lab_data_id,#{labAeId})
        </if>
        <if test="filterItem != null and filterItem.size() > 0 and columnName !=null and columnName.size() > 0">
            AND
            <foreach collection="columnName"  index="index" item="item" open="(" separator="AND" close=")">
                `${item}`  in #{filterItem}
            </foreach>

        </if>

        <if test="sortColumn != null and  sortOrder!=null ">
            order by  `${sortColumn}` ${sortOrder}
        </if>      
                
                
                
                
                mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/csv_raw /home/<USER>/ex_original_csv -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root
                
                mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/json_blind /home/<USER>/ex_json_file -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root
                
                
                 AND LOCATE(study_no,#{entity.CTAProject})
                
                .CTAProject=localStorage.ctaPro.toString()
                
                
                
                  <if test="CTAProject !=null and CTAProject !='' ">
            AND LOCATE(study_no,#{CTAProject})
        </if> 
                
                
                
                
                
                
完成DM数据审核平台整体功能开发，满足业务部门实际使用需求，并完成测试、生产、验证环境的更新与维护:
功能开发阶段：针对DM数据审核平台，完成包括数据处理、审核批注、用户操作记录等功能的设计和开发。需要满足业务部门的实际使用需求。
测试阶段：完成对所有功能的单元测试、集成测试，以确保系统的稳定性和可靠性。
更新和维护阶段：完成对生产、测试和验证环境的连续更新和维护，保障系统运行的稳定性。
在规定的时间内，能完成全部功能模块的开发并通过全部功能测试。
确保生产和验证环境的正常运行，且生产环境故障修复响应时间应不超过1个工作日。
制定完整的项目开发计划及时间表，采用敏捷的开发方式逐步实现所有功能模块。
定期巡检，及时发现和修复生产和验证环境的问题。

设定具体的项目开始和结束日期，每个阶段设定明确的时间计划，确保任务严格按照进度进行。




完善airflow调度平台dag文件权限控制与管理：
依据计算机系统验证相关法规、指南要求与风险管理的原则，对Airflow的DAG文件进行修改和管理。具体目标包括实现dag的版本控制，独立的dag文件审查，有效的错误处理，以及日志记录与操作记录等。
通过开发线上的dag文件管理模块，实现dag文件的权限控制与操作管理，并通过定期的备份实现dag文件的版本管理，通过设定具体用户角色、权限、审核流程实现对dag文件的上线前的有效审核，计划于2024年上半年完成base版的整体功能设计与核心功能如：dag文件管理的操作记录生成，dag文件的版本控制





在2024年1季度，计划在SUSAR平台上开发并部署一个新增的收件人功能模块和一个完善的用户管理功能模块。在确保所有新功能都已经稳定运行后，在接下来的一个月内将系统交接给PV部门，并为开发同事提供必要的技术支持，使他们能够成功进行日常运维管理和后期需求开发的工作。


计划在2024年上半年完成Airflow调度平台DAG文件权限控制及管理，包括开发在线DAG文件管理模块实现版本控制、权限控制和操作管理，以及日志和操作记录等功能。将定期备份DAG文件，并设定用户角色及权限和审核流程，确保DAG文件上线前进行有效审查

完成DM数据审核平台整体功能开发，满足业务部门实际使用需求，并完成测试、生产、验证环境的更新与维护: 功能开发阶段：针对DM数据审核平台，完成包括数据处理、审核批注、用户操作记录等功能的设计和开发。需要满足业务部门的实际使用需求。 测试阶段：完成对所有功能的单元测试、集成测试，以确保系统的稳定性和可靠性。 更新和维护阶段：完成对生产、测试和验证环境的连续更新和维护，保障系统运行的稳定性。 在规定的时间内，能完成全部功能模块的开发并通过全部功能测试。 制定完整的项目开发计划及时间表，采用敏捷的开发方式逐步实现所有功能模块。 定期巡检，及时发现和修复生产和验证环境的问题， 确保生产和验证环境的正常运行，且生产环境故障修复响应时间应不超过2个工作日。


DM数据审核平台计划在2024年上半年完成base版本pilot上线，包括数据处理、审核批注和用户操作记录等，以满足业务部门的实际需求。开发完成后，将通过单元测试和集成测试来确保系统的稳定性和可靠性。所有功能开发完毕并经过全面测试后，将继续进行生产、测试和验证环境的更新和维护，保证系统的正常运行。以敏捷的方式迭代开发，并定期进行系统巡检，确保生产和验证环境出现问题后能在两个工作日内得到修复。






DM数据审核平台计划在2024年完成一期全部功能测试、生产、验证环境上线，包括数据处理、审核批注和用户操作记录等，以满足业务部门的实际需求。开发完成后，将通过单元测试和集成测试来确保系统的稳定性和可靠性。所有功能开发完毕并经过全面测试后，将继续进行生产、测试和验证环境的更新和维护，保证系统的正常运行。以敏捷的方式迭代开发，并定期进行系统巡检，确保生产和验证环境出现问题后能在两个工作日内得到修复。


1.满足系统验证标准 2.全部功能模块的单元测试和集成测试在验证及生产环境的通过率应达到90%以上 3.在24年完成DM审核平台一期生产环境上线



计划在2024年完成Airflow调度平台DAG文件权限控制及管理的base版功能设计与核心功能开发，包括开发在线DAG文件管理模块实现版本控制、权限控制和操作管理，以及日志和操作记录等功能。将定期备份DAG文件，并设定用户角色及权限和审核流程，确保DAG文件上线前进行有效审查
2024年完成一期的整体功能开发：airflow任务调度dag管理平台搭建，dag文件管理的操作记录生成，dag文件版本记录与控制,权限控制和操作管理，以及日志和操作记录等功能


CDTMS日志填写记录大于百分比95%
                
                
                