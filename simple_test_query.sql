-- Simple test query to verify basic functionality
SELECT 
    'tbl_attachment' as table_name,
    (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_attachment) as source_count,
    (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_attachment) as target_count
FROM dual
UNION ALL
SELECT 
    'tbl_log_event' as table_name,
    (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_log_event) as source_count,
    (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_log_event) as target_count
FROM dual
UNION ALL
SELECT 
    'tbl_systemcode' as table_name,
    (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_systemcode) as source_count,
    (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_systemcode) as target_count
FROM dual;
