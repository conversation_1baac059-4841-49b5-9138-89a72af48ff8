#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Oracle Database Comparison Tool
Automated comparison of two Oracle databases after data synchronization
"""

import cx_Oracle
import pandas as pd
import hashlib
import json
from datetime import datetime
import logging
import argparse

class OracleDBComparator:
    def __init__(self, source_config, target_config):
        """
        Initialize database connections
        
        Args:
            source_config (dict): Source database connection config
            target_config (dict): Target database connection config
        """
        self.source_config = source_config
        self.target_config = target_config
        self.source_conn = None
        self.target_conn = None
        self.comparison_results = []
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('db_comparison.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def connect_databases(self):
        """Establish connections to both databases"""
        try:
            # Connect to source database
            source_dsn = cx_Oracle.makedsn(
                self.source_config['host'],
                self.source_config['port'],
                service_name=self.source_config['service_name']
            )
            self.source_conn = cx_Oracle.connect(
                self.source_config['username'],
                self.source_config['password'],
                source_dsn
            )
            
            # Connect to target database
            target_dsn = cx_Oracle.makedsn(
                self.target_config['host'],
                self.target_config['port'],
                service_name=self.target_config['service_name']
            )
            self.target_conn = cx_Oracle.connect(
                self.target_config['username'],
                self.target_config['password'],
                target_dsn
            )
            
            self.logger.info("Successfully connected to both databases")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to databases: {e}")
            return False
    
    def get_table_list(self):
        """Get list of tables from both databases"""
        try:
            # Get tables from source
            source_cursor = self.source_conn.cursor()
            source_cursor.execute("""
                SELECT table_name FROM user_tables 
                WHERE table_name NOT LIKE 'BIN$%'
                ORDER BY table_name
            """)
            source_tables = {row[0] for row in source_cursor.fetchall()}
            
            # Get tables from target
            target_cursor = self.target_conn.cursor()
            target_cursor.execute("""
                SELECT table_name FROM user_tables 
                WHERE table_name NOT LIKE 'BIN$%'
                ORDER BY table_name
            """)
            target_tables = {row[0] for row in target_cursor.fetchall()}
            
            # Find common tables and differences
            common_tables = source_tables.intersection(target_tables)
            source_only = source_tables - target_tables
            target_only = target_tables - source_tables
            
            self.logger.info(f"Common tables: {len(common_tables)}")
            self.logger.info(f"Source only tables: {len(source_only)}")
            self.logger.info(f"Target only tables: {len(target_only)}")
            
            return {
                'common': list(common_tables),
                'source_only': list(source_only),
                'target_only': list(target_only)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting table list: {e}")
            return None
    
    def compare_row_counts(self, tables):
        """Compare row counts for given tables"""
        results = []
        
        for table in tables:
            try:
                # Get source count
                source_cursor = self.source_conn.cursor()
                source_cursor.execute(f"SELECT COUNT(*) FROM {table}")
                source_count = source_cursor.fetchone()[0]
                
                # Get target count
                target_cursor = self.target_conn.cursor()
                target_cursor.execute(f"SELECT COUNT(*) FROM {table}")
                target_count = target_cursor.fetchone()[0]
                
                # Compare
                match = source_count == target_count
                
                result = {
                    'table_name': table,
                    'comparison_type': 'ROW_COUNT',
                    'source_value': source_count,
                    'target_value': target_count,
                    'match': match,
                    'difference': abs(source_count - target_count) if not match else 0,
                    'timestamp': datetime.now().isoformat()
                }
                
                results.append(result)
                self.logger.info(f"Row count comparison for {table}: {source_count} vs {target_count} ({'MATCH' if match else 'MISMATCH'})")
                
            except Exception as e:
                self.logger.error(f"Error comparing row counts for {table}: {e}")
                results.append({
                    'table_name': table,
                    'comparison_type': 'ROW_COUNT',
                    'source_value': 'ERROR',
                    'target_value': 'ERROR',
                    'match': False,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                })
        
        return results
    
    def compare_table_structure(self, tables):
        """Compare table structures (columns, data types)"""
        results = []
        
        for table in tables:
            try:
                # Get source structure
                source_cursor = self.source_conn.cursor()
                source_cursor.execute("""
                    SELECT column_name, data_type, data_length, nullable
                    FROM user_tab_columns
                    WHERE table_name = :table_name
                    ORDER BY column_id
                """, table_name=table)
                source_structure = source_cursor.fetchall()
                
                # Get target structure
                target_cursor = self.target_conn.cursor()
                target_cursor.execute("""
                    SELECT column_name, data_type, data_length, nullable
                    FROM user_tab_columns
                    WHERE table_name = :table_name
                    ORDER BY column_id
                """, table_name=table)
                target_structure = target_cursor.fetchall()
                
                # Compare structures
                source_hash = hashlib.md5(str(source_structure).encode()).hexdigest()
                target_hash = hashlib.md5(str(target_structure).encode()).hexdigest()
                match = source_hash == target_hash
                
                result = {
                    'table_name': table,
                    'comparison_type': 'STRUCTURE',
                    'source_value': source_hash,
                    'target_value': target_hash,
                    'match': match,
                    'source_columns': len(source_structure),
                    'target_columns': len(target_structure),
                    'timestamp': datetime.now().isoformat()
                }
                
                if not match:
                    # Find specific differences
                    source_cols = {row[0]: row[1:] for row in source_structure}
                    target_cols = {row[0]: row[1:] for row in target_structure}
                    
                    differences = []
                    for col in set(source_cols.keys()) | set(target_cols.keys()):
                        if col not in source_cols:
                            differences.append(f"Column {col} missing in source")
                        elif col not in target_cols:
                            differences.append(f"Column {col} missing in target")
                        elif source_cols[col] != target_cols[col]:
                            differences.append(f"Column {col} differs: {source_cols[col]} vs {target_cols[col]}")
                    
                    result['differences'] = differences
                
                results.append(result)
                self.logger.info(f"Structure comparison for {table}: {'MATCH' if match else 'MISMATCH'}")
                
            except Exception as e:
                self.logger.error(f"Error comparing structure for {table}: {e}")
                results.append({
                    'table_name': table,
                    'comparison_type': 'STRUCTURE',
                    'source_value': 'ERROR',
                    'target_value': 'ERROR',
                    'match': False,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                })
        
        return results
    
    def compare_data_checksums(self, tables, primary_key_columns=None):
        """Compare data checksums for tables"""
        results = []
        
        if primary_key_columns is None:
            primary_key_columns = {}
        
        for table in tables:
            try:
                # Get primary key column if not provided
                pk_column = primary_key_columns.get(table)
                if not pk_column:
                    # Try to find primary key
                    cursor = self.source_conn.cursor()
                    cursor.execute("""
                        SELECT column_name FROM user_cons_columns
                        WHERE constraint_name = (
                            SELECT constraint_name FROM user_constraints
                            WHERE table_name = :table_name AND constraint_type = 'P'
                        )
                    """, table_name=table)
                    pk_result = cursor.fetchone()
                    pk_column = pk_result[0] if pk_result else 'ROWID'
                
                # Build checksum query (simplified - you may need to customize)
                checksum_query = f"""
                    SELECT SUM(ORA_HASH({pk_column})) as table_checksum,
                           COUNT(*) as row_count
                    FROM {table}
                """
                
                # Get source checksum
                source_cursor = self.source_conn.cursor()
                source_cursor.execute(checksum_query)
                source_result = source_cursor.fetchone()
                source_checksum, source_count = source_result
                
                # Get target checksum
                target_cursor = self.target_conn.cursor()
                target_cursor.execute(checksum_query)
                target_result = target_cursor.fetchone()
                target_checksum, target_count = target_result
                
                # Compare
                match = (source_checksum == target_checksum and source_count == target_count)
                
                result = {
                    'table_name': table,
                    'comparison_type': 'DATA_CHECKSUM',
                    'source_value': f"{source_checksum}:{source_count}",
                    'target_value': f"{target_checksum}:{target_count}",
                    'match': match,
                    'primary_key_used': pk_column,
                    'timestamp': datetime.now().isoformat()
                }
                
                results.append(result)
                self.logger.info(f"Checksum comparison for {table}: {'MATCH' if match else 'MISMATCH'}")
                
            except Exception as e:
                self.logger.error(f"Error comparing checksums for {table}: {e}")
                results.append({
                    'table_name': table,
                    'comparison_type': 'DATA_CHECKSUM',
                    'source_value': 'ERROR',
                    'target_value': 'ERROR',
                    'match': False,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                })
        
        return results
    
    def run_full_comparison(self, table_filter=None):
        """Run complete database comparison"""
        self.logger.info("Starting full database comparison")
        
        if not self.connect_databases():
            return False
        
        try:
            # Get table lists
            table_info = self.get_table_list()
            if not table_info:
                return False
            
            # Filter tables if specified
            tables_to_compare = table_info['common']
            if table_filter:
                tables_to_compare = [t for t in tables_to_compare if t in table_filter]
            
            self.logger.info(f"Comparing {len(tables_to_compare)} tables")
            
            # Run comparisons
            self.comparison_results.extend(self.compare_row_counts(tables_to_compare))
            self.comparison_results.extend(self.compare_table_structure(tables_to_compare))
            self.comparison_results.extend(self.compare_data_checksums(tables_to_compare))
            
            # Add table existence results
            for table in table_info['source_only']:
                self.comparison_results.append({
                    'table_name': table,
                    'comparison_type': 'TABLE_EXISTENCE',
                    'source_value': 'EXISTS',
                    'target_value': 'MISSING',
                    'match': False,
                    'timestamp': datetime.now().isoformat()
                })
            
            for table in table_info['target_only']:
                self.comparison_results.append({
                    'table_name': table,
                    'comparison_type': 'TABLE_EXISTENCE',
                    'source_value': 'MISSING',
                    'target_value': 'EXISTS',
                    'match': False,
                    'timestamp': datetime.now().isoformat()
                })
            
            self.logger.info("Database comparison completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error during comparison: {e}")
            return False
        
        finally:
            self.close_connections()
    
    def close_connections(self):
        """Close database connections"""
        if self.source_conn:
            self.source_conn.close()
        if self.target_conn:
            self.target_conn.close()
        self.logger.info("Database connections closed")
    
    def export_results(self, format='json', filename=None):
        """Export comparison results"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"db_comparison_results_{timestamp}"
        
        if format.lower() == 'json':
            with open(f"{filename}.json", 'w') as f:
                json.dump(self.comparison_results, f, indent=2, default=str)
        
        elif format.lower() == 'csv':
            df = pd.DataFrame(self.comparison_results)
            df.to_csv(f"{filename}.csv", index=False)
        
        elif format.lower() == 'excel':
            df = pd.DataFrame(self.comparison_results)
            df.to_excel(f"{filename}.xlsx", index=False)
        
        self.logger.info(f"Results exported to {filename}.{format}")
    
    def get_summary(self):
        """Get comparison summary"""
        if not self.comparison_results:
            return "No comparison results available"
        
        df = pd.DataFrame(self.comparison_results)
        
        summary = {
            'total_comparisons': len(df),
            'matches': len(df[df['match'] == True]),
            'mismatches': len(df[df['match'] == False]),
            'by_type': df.groupby('comparison_type')['match'].value_counts().to_dict(),
            'tables_with_issues': df[df['match'] == False]['table_name'].unique().tolist()
        }
        
        return summary

def main():
    """Main function for command line usage"""
    parser = argparse.ArgumentParser(description='Oracle Database Comparison Tool')
    parser.add_argument('--source-host', required=True, help='Source database host')
    parser.add_argument('--source-port', default=1521, type=int, help='Source database port')
    parser.add_argument('--source-service', required=True, help='Source database service name')
    parser.add_argument('--source-user', required=True, help='Source database username')
    parser.add_argument('--source-password', required=True, help='Source database password')
    
    parser.add_argument('--target-host', required=True, help='Target database host')
    parser.add_argument('--target-port', default=1521, type=int, help='Target database port')
    parser.add_argument('--target-service', required=True, help='Target database service name')
    parser.add_argument('--target-user', required=True, help='Target database username')
    parser.add_argument('--target-password', required=True, help='Target database password')
    
    parser.add_argument('--tables', nargs='*', help='Specific tables to compare (optional)')
    parser.add_argument('--output-format', choices=['json', 'csv', 'excel'], default='json', help='Output format')
    parser.add_argument('--output-file', help='Output filename (without extension)')
    
    args = parser.parse_args()
    
    # Setup configurations
    source_config = {
        'host': args.source_host,
        'port': args.source_port,
        'service_name': args.source_service,
        'username': args.source_user,
        'password': args.source_password
    }
    
    target_config = {
        'host': args.target_host,
        'port': args.target_port,
        'service_name': args.target_service,
        'username': args.target_user,
        'password': args.target_password
    }
    
    # Run comparison
    comparator = OracleDBComparator(source_config, target_config)
    
    if comparator.run_full_comparison(table_filter=args.tables):
        # Export results
        comparator.export_results(format=args.output_format, filename=args.output_file)
        
        # Print summary
        summary = comparator.get_summary()
        print("\n" + "="*50)
        print("COMPARISON SUMMARY")
        print("="*50)
        print(f"Total comparisons: {summary['total_comparisons']}")
        print(f"Matches: {summary['matches']}")
        print(f"Mismatches: {summary['mismatches']}")
        print(f"Tables with issues: {len(summary['tables_with_issues'])}")
        
        if summary['tables_with_issues']:
            print("\nTables with issues:")
            for table in summary['tables_with_issues']:
                print(f"  - {table}")
    
    else:
        print("Comparison failed. Check logs for details.")

if __name__ == "__main__":
    main()
