﻿%global system studyid root version dir lang m_minio repdat;
proc datasets lib=work nolist kill; quit;
%let dir=%sysfunc(getoption(work));
%let root=&dir.;
x "cd &dir";
%put &dir;
%put &root;

/*指定minio宏参数*/

%let m_minio=minios3;


x "mkdir ./pgm";
x 'mc find minios3/pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';
/*x 'mc find minios3-t/pgm/ --name "m_post2s3.sas" --exec "mc cp {} ./pgm/"';*/
/*x 'mc find minios3-t/pgm/ --name "m_gets3data.sas" --exec "mc cp {} ./pgm/"';*/
/*x 'mc find minios3-t/pgm/ --name "M_std_dimage.sas" --exec "mc cp {} ./pgm/"';*/
/*x 'mc find minios3-t/pgm/ --name "m_exportxlsx_irc.sas" --exec "mc cp {} ./pgm/"';*/

%include "&root./pgm/*.sas";


/*x "mkdir ./pgm";x 'mc find &m_minio./pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';%include "&dir/pgm/*.sas";*/

%let outputlocal=&m_minio./dimage/output/;
option mprint symbolgen validvarname=v7;

%let studyid=&studyid.;
/*%let cohort=&cohort.;*/


data _null_;
call symput('file_name',scan("&jsonPath.",-1,'/'));
run;

%put &file_name;

x "mkdir ./doc/sdv";
x "mc find minios3/sdv/json --name ""&file_name."" | xargs -I{} mc cp {} ./doc/sdv/";
filename y "./doc/sdv/&file_name.";

libname jsonrec json fileref=y;
proc copy in=jsonrec out=work;
run;



/*处理jason以及自定义*/

data alldata_;
set alldata;
if P1 eq 'parm' then do;
	cohort=compress(scan(Value,2,'='),'<>');
end;
run;


proc sql noprint;
	select cohort into :cohort from alldata_ where P1='parm';
	select Value into :sascode from alldata_ where P1='sascode';

quit;
%put &cohort. ;

/*proc sql noprint;*/
/*	select Value into :cohort from alldata ;*/
/*quit;*/
/*%put &cohort. ;*/


/*%let studyid=SHR-A1811-307;*/
/*%let studyid=SHR-A1811-309;*/
/*%let studyid=SHR-A1811-212;*/
/*%let lang="CH";*/
/*%let cohort=Y;*/
/*%let system=HRTAU4;*/
/*%let vis='C1D1';*/


%m_post2s3(studyid=&studyid.);
%m_gets3data(studyid=&studyid.,data=@);

&sascode;
%M_std_dimage;


/*x "mc cp minios3/raw/SHR-A1811-307_sas.zip  minios3-t/raw/SHR-A1811-307_sas.zip --tags  'env=pro&key1=HRTAU&key2=SHR-A1811-307&key3=2024/11/28/17/00'";*/


