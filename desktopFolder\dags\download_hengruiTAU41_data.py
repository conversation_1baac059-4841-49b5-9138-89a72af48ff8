import airflow
from airflow.models import DAG
from airflow.operators.python import PythonOperator
import json
import subprocess
from datetime import datetime,timedelta 

args = {
    'owner': 'zhouhui',
    'start_date': datetime(2023, 7, 28),
    'depends_on_past': False
}

dag = DAG(dag_id='download_hengruiTAU41_data', default_args=args, schedule_interval="0 16 * * *")
json_data = []
	
		
def download_files(ti):
    global json_data
    with open('/home/<USER>', 'w') as f:
        json.dump(json_data, f)
    #判断本地是否有/home/<USER>
    remove_command= 'if [ -d "/home/<USER>/data" ]; then rm -rf /home/<USER>/data; fi' 
    subprocess.check_output(remove_command,shell=True)
    #执行下载命令
    download_command = f'sshpass -p Hr@0601 scp -P 22 -r satableau@10.10.12.41:/data/docker/overlay2/f554739747276b8e5a88710b507426993e75c740beefb0baa2f8f4dcfbcca054/merged/home/<USER>/data /home/<USER>' 
    output = subprocess.check_output(download_command,shell=True)

task1 = PythonOperator(
    task_id='download_files', 
    python_callable=download_files,
    dag=dag,
) 

task1