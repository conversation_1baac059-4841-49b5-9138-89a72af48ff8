package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTRecordFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


public class DTRFlightpdfSignView extends DTRecordFuncAction {


	public boolean canUse(int auth, String tableid, Long recordid) {

		try {
			String projectId = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng=new DAODataMng(projectId);

			List<Map<String, String>> LightpdfSignIntegrateList = DAOLightpdfSignIntegrate.listRule(projectId);
			if (CollectionUtils.isEmpty(LightpdfSignIntegrateList)) {
				return  false;
			}


			List esignInstanceList = daoDataMng.listRecord("esign_instance", "obj.active = 1 and obj.tableid='" + tableid + "' and obj.recordid='" + recordid + "' and obj.sign_flow_id is not null", null, 1);

			if (!CollectionUtils.isEmpty(esignInstanceList)) {

				return true;

			}



		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}

	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBean fpb) {
		try {

			String projectId = SessUtil.getSessInfo().getProjectid();
			String recordid = fpb.getRecordid();
			String tableid = fpb.getTableid();
			DAODataMng daoDataMng=new DAODataMng(projectId);

			this.redirectByUri(request, response,"/LightpdfSignIntergrate.View.do?recordid="+recordid+"&tableid="+tableid);
			return;



		} catch (Exception e) {
			Log.error("",e);
		}
	}

	public FuncInfoBean getFIB(String tableid) {
		FuncInfoBean fib = new FuncInfoBean();
		try{
			fib.setName(this.getLanguage().get("Signatures"));
			fib.setType(FuncInfoBean.FUNCTYPE_TOPMASKDIV);
			fib.setWinHeight(600);
			fib.setWinWidth(800);
			fib.setSimpleViewShow(true);
		} catch (Exception e) {
			Log.error("",e);
		}
		return fib;
	}

}
