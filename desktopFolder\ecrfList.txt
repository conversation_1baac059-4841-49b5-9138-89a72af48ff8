{"code": 200, "success": true, "msg": "successfully", "data": {"tableList": [{"tid": "SUBJECT", "tname": "受试者信息", "fieldList": [{"innerId": "studyid", "fieldName": "研究代码", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "STUDYID"}, {"innerId": "sitename", "fieldName": "中心名称", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "SITENAME"}, {"innerId": "sitecode", "fieldName": "中心编号", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "SITECODE"}, {"innerId": "invname", "fieldName": "研究者", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "INVNAME"}, {"innerId": "rficdat", "fieldName": "知情同意签署日期", "codeName": "", "format": "", "fieldType": "date", "defValue": "", "fieldId": "RFICDAT"}, {"innerId": "subjid", "fieldName": "受试者代码", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "SUBJID"}, {"innerId": "status", "fieldName": "受试者状态", "codeName": "", "format": "", "fieldType": "string", "defValue": "10", "fieldId": "STATUS"}, {"innerId": "protover", "fieldName": "方案版本", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "PROTOVER"}, {"innerId": "crfver", "fieldName": "eCRF版本号", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "CRFVER"}, {"innerId": "crfsts", "fieldName": "eCRF状态", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "CRFSTS"}, {"innerId": "invid", "fieldName": "研究者代码", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "INVID"}, {"innerId": "country", "fieldName": "国家", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "COUNTRY"}, {"innerId": "usub<PERSON>", "fieldName": "唯一受试者代码", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "USUBJID"}]}, {"tid": "SV", "tname": "访视日期", "fieldList": [{"innerId": "v001", "fieldName": "是否进行访视？", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "SVYN"}, {"innerId": "v002", "fieldName": "未进行访视的原因", "codeName": "NDREAS", "format": "", "fieldType": "select", "defValue": "", "fieldId": "SVREAS"}, {"innerId": "v003", "fieldName": "访视日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "VISDAT"}]}, {"tid": "DM", "tname": "人口学资料", "fieldList": [{"innerId": "v001", "fieldName": "知情同意签署日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "RFICDAT"}, {"innerId": "v002", "fieldName": "出生日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "BRTHDAT"}, {"innerId": "v003", "fieldName": "性别", "codeName": "SEX", "format": "", "fieldType": "select", "defValue": "", "fieldId": "SEX"}, {"innerId": "v004", "fieldName": "是否有生育能力", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PREGYN"}, {"innerId": "v005", "fieldName": "民族", "codeName": "ETHNIC", "format": "1", "fieldType": "select", "defValue": "", "fieldId": "ETHNIC"}, {"innerId": "v006", "fieldName": "民族", "codeName": "ETHNICC", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CETHNIC"}, {"innerId": "t001", "fieldName": "其他民族", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CETHNICO"}, {"innerId": "v007", "fieldName": "种族", "codeName": "RACE", "format": "1", "fieldType": "select", "defValue": "", "fieldId": "RACE"}, {"innerId": "v008", "fieldName": "身高", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "HEIGHT"}, {"innerId": "v009", "fieldName": "身高单位", "codeName": "HEIGHTU", "format": "", "fieldType": "select", "defValue": "", "fieldId": "HEIGHTU"}, {"innerId": "v010", "fieldName": "复筛前受试者代码", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "PSUBJID"}]}, {"tid": "MH", "tname": "既往病史", "fieldList": [{"innerId": "t001", "fieldName": "疾病名称/症状", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "MHTERM"}, {"innerId": "v001", "fieldName": "确诊/开始日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "MHSTDAT"}, {"innerId": "v002", "fieldName": "是否持续", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "MHONGO"}, {"innerId": "v003", "fieldName": "结束日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "MHENDAT"}]}, {"tid": "SUCIGR", "tname": "吸烟史", "fieldList": [{"innerId": "v001", "fieldName": "吸烟状况", "codeName": "SUNCF", "format": "1", "fieldType": "select", "defValue": "", "fieldId": "SUNCF"}]}, {"tid": "SUALCO", "tname": "饮酒史", "fieldList": [{"innerId": "v001", "fieldName": "饮酒状况", "codeName": "SUNCF", "format": "1", "fieldType": "select", "defValue": "", "fieldId": "SUNCF"}]}, {"tid": "PE", "tname": "体格检查", "fieldList": [{"innerId": "v001", "fieldName": "检查日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "PEDAT"}]}, {"tid": "PE_SUB", "tname": "体格检查明细", "fieldList": [{"innerId": "v001", "fieldName": "检查项", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "PETEST"}, {"innerId": "v002", "fieldName": "结果", "codeName": "PERES", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PERES"}, {"innerId": "t001", "fieldName": "异常请描述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "PEDESC"}]}, {"tid": "VSWT", "tname": "体重", "fieldList": [{"innerId": "v001", "fieldName": "检查日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "VSDAT"}, {"innerId": "v002", "fieldName": "体重", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "WEIGHT"}, {"innerId": "v003", "fieldName": "体重单位", "codeName": "WEIGHTU", "format": "", "fieldType": "select", "defValue": "", "fieldId": "WEIGHTU"}]}, {"tid": "VS", "tname": "生命体征", "fieldList": [{"innerId": "v004", "fieldName": "模块名称", "codeName": "VSCAT", "format": "", "fieldType": "select", "defValue": "", "fieldId": "VSCAT"}, {"innerId": "v001", "fieldName": "检查日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "VSDAT"}]}, {"tid": "VS_SUB", "tname": "生命体征明细", "fieldList": [{"innerId": "v001", "fieldName": "检查项", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "VSTEST"}, {"innerId": "v002", "fieldName": "结果", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "VSORRES"}, {"innerId": "v003", "fieldName": "单位", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "VSORRESU"}]}, {"tid": "EG", "tname": "12导联心电图", "fieldList": [{"innerId": "v001", "fieldName": "检查日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "EGDAT"}, {"innerId": "v003", "fieldName": "临床意义", "codeName": "CLSIG", "format": "", "fieldType": "select", "defValue": "", "fieldId": "EGCLSIG"}, {"innerId": "t001", "fieldName": "异常描述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "EGDESC"}]}, {"tid": "EG_SUB", "tname": "12导联心电图明细", "fieldList": [{"innerId": "v001", "fieldName": "检查项", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "EGTEST"}, {"innerId": "v002", "fieldName": "结果", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "EGORRES"}, {"innerId": "v003", "fieldName": "单位", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "EGORRESU"}]}, {"tid": "LB", "tname": "实验室检查", "fieldList": [{"innerId": "v001", "fieldName": "模块名称", "codeName": "LBCAT", "format": "", "fieldType": "select", "defValue": "", "fieldId": "LBCAT"}, {"innerId": "v002", "fieldName": "采样日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "LBDAT"}, {"innerId": "v004", "fieldName": "LAB名称", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "LBNAM"}]}, {"tid": "LB_SUB", "tname": "实验室检查明细", "fieldList": [{"innerId": "v001", "fieldName": "检查项", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "LBTEST"}, {"innerId": "v002", "fieldName": "结果", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "LBORRES"}, {"innerId": "v003", "fieldName": "单位", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "LBORRESU"}, {"innerId": "v004", "fieldName": "临床意义", "codeName": "LBCLSIG", "format": "", "fieldType": "select", "defValue": "", "fieldId": "LBCLSIG"}, {"innerId": "v005", "fieldName": "正常值范围-下限", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "LBORNRLO"}, {"innerId": "v006", "fieldName": "正常值范围-上限", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "LBORNRHI"}]}, {"tid": "LBHCG", "tname": "妊娠试验", "fieldList": [{"innerId": "v001", "fieldName": "采样日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "LBDAT"}, {"innerId": "v002", "fieldName": "妊娠试验样本", "codeName": "LBSPEC", "format": "", "fieldType": "select", "defValue": "", "fieldId": "LBSPEC"}, {"innerId": "v003", "fieldName": "妊娠试验结果", "codeName": "NP", "format": "", "fieldType": "select", "defValue": "", "fieldId": "LBORRES"}]}, {"tid": "LBQUAL", "tname": "实验室定性检查", "fieldList": [{"innerId": "v002", "fieldName": "模块名称", "codeName": "LBCAT_QUAL", "format": "", "fieldType": "select", "defValue": "", "fieldId": "LBCAT"}, {"innerId": "v001", "fieldName": "采样日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "LBDAT"}]}, {"tid": "LBQUAL_SUB", "tname": "实验室定性检查明细", "fieldList": [{"innerId": "v001", "fieldName": "检查项", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "LBTEST"}, {"innerId": "v002", "fieldName": "结果", "codeName": "NPN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "LBORRES"}]}, {"tid": "REPF", "tname": "肺功能检测", "fieldList": [{"innerId": "v001", "fieldName": "检查日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "REDAT"}, {"innerId": "v002", "fieldName": "临床意义", "codeName": "CLSIG", "format": "", "fieldType": "select", "defValue": "", "fieldId": "RECLSIG"}, {"innerId": "t001", "fieldName": "异常描述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "REDESC"}]}, {"tid": "REPF_SUB", "tname": "肺功能检测明细", "fieldList": [{"innerId": "v001", "fieldName": "检查项", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "RETEST"}, {"innerId": "v002", "fieldName": "结果", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "REORRES"}, {"innerId": "v003", "fieldName": "单位", "codeName": "REORRESU", "format": "", "fieldType": "select", "defValue": "", "fieldId": "REORRESU"}]}, {"tid": "DSENROLL", "tname": "入组信息", "fieldList": [{"innerId": "v008", "fieldName": "试验分期", "codeName": "PHASE", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PHASE"}, {"innerId": "v010", "fieldName": "试验阶段", "codeName": "STAGE", "format": "", "fieldType": "select", "defValue": "", "fieldId": "STAGE"}, {"innerId": "v012", "fieldName": "筛选号", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "SCREENID"}, {"innerId": "v001", "fieldName": "筛选时使用的方案版本号", "codeName": "TIVERS", "format": "", "fieldType": "select", "defValue": "", "fieldId": "TIVERS"}, {"innerId": "v002", "fieldName": "筛选结果", "codeName": "DSCAT_SCREEN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "DSCAT"}, {"innerId": "v003", "fieldName": "入组日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "DSSTDAT"}, {"innerId": "v011", "fieldName": "给药方案", "codeName": "REGIMEN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "REGIMEN"}, {"innerId": "v009", "fieldName": "剂量水平", "codeName": "DOSELVL", "format": "", "fieldType": "select", "defValue": "", "fieldId": "DOSELVL"}, {"innerId": "v005", "fieldName": "筛选失败主要原因", "codeName": "DSDECOD_RAND", "format": "1", "fieldType": "select", "defValue": "", "fieldId": "DSDECOD"}, {"innerId": "t001", "fieldName": "其他原因", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "DSTERM"}]}, {"tid": "DSENROLL_SUB", "tname": "入组信息明细", "fieldList": [{"innerId": "v001", "fieldName": "入排标准类型", "codeName": "IECAT", "format": "", "fieldType": "select", "defValue": "", "fieldId": "IECAT"}, {"innerId": "v002", "fieldName": "不符合的入选标准/符合的排除标准序号", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "IETESTCD"}]}, {"tid": "DSRAND", "tname": "随机", "fieldList": [{"innerId": "v001", "fieldName": "是否随机", "codeName": "RANDTRIG", "format": "", "fieldType": "select", "defValue": "", "fieldId": "RANDTRIG"}, {"innerId": "v006", "fieldName": "组别", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "REGIME_NAME"}, {"innerId": "v002", "fieldName": "随机号", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "RAND_ID"}, {"innerId": "v005", "fieldName": "随机日期(UTC)", "codeName": "", "format": "mm", "fieldType": "datetime", "defValue": "", "fieldId": "RANDOMIZED_AT"}, {"innerId": "v004", "fieldName": "随机日期", "codeName": "", "format": "mm", "fieldType": "datetime", "defValue": "", "fieldId": "RANDDTC"}]}, {"tid": "AE", "tname": "不良事件", "fieldList": [{"innerId": "t001", "fieldName": "不良事件名称", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "AETERM"}, {"innerId": "v001", "fieldName": "开始日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "AESTDAT"}, {"innerId": "v002", "fieldName": "开始时间", "codeName": "", "format": "hh:mm", "fieldType": "partialtime", "defValue": "", "fieldId": "AESTTIM"}, {"innerId": "v003", "fieldName": "AE转归", "codeName": "AEOUT", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AEOUT"}, {"innerId": "v004", "fieldName": "转归日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "AEENDAT"}, {"innerId": "v005", "fieldName": "转归时间", "codeName": "", "format": "hh:mm", "fieldType": "partialtime", "defValue": "", "fieldId": "AEENTIM"}, {"innerId": "v006", "fieldName": "对试验用药品采取措施", "codeName": "AEACN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AEACN"}, {"innerId": "v007", "fieldName": "CTCAE分级", "codeName": "AETOXGR", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AETOXGR"}, {"innerId": "v009", "fieldName": "是否有纠正治疗", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AECONTRT"}, {"innerId": "v010", "fieldName": "与试验用药品的关系", "codeName": "REL", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AEREL"}, {"innerId": "v011", "fieldName": "是否是DLT", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AEDLT"}, {"innerId": "v023", "fieldName": "是否是特别关注的医学事件", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AEMIE"}, {"innerId": "v014", "fieldName": "是否是特别关注的不良事件", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESI"}, {"innerId": "v015", "fieldName": "是否是严重不良事件", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESER"}, {"innerId": "v016", "fieldName": "导致死亡", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESDTH"}, {"innerId": "v017", "fieldName": "危及生命", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESLIFE"}, {"innerId": "v018", "fieldName": "需要住院治疗或延长住院时间", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESHOSP"}, {"innerId": "v019", "fieldName": "导致永久或严重的残疾/功能丧失", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESDISAB"}, {"innerId": "v020", "fieldName": "先天异常/出生缺陷", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESCONG"}, {"innerId": "v021", "fieldName": "其他重要医学事件", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESMIE"}, {"innerId": "t002", "fieldName": "其他重要医学事件详述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "AESOSP"}, {"innerId": "v022", "fieldName": "该不良事件是否导致退出研究", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AEDIS"}]}, {"tid": "CM", "tname": "既往及合并用药", "fieldList": [{"innerId": "t001", "fieldName": "药物名称", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CMTRT"}, {"innerId": "v001", "fieldName": "开始日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "CMSTDAT"}, {"innerId": "v010", "fieldName": "开始时间", "codeName": "", "format": "hh:mm", "fieldType": "partialtime", "defValue": "", "fieldId": "CMSTTIM"}, {"innerId": "v002", "fieldName": "是否持续", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CMONGO"}, {"innerId": "v003", "fieldName": "结束日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "CMENDAT"}, {"innerId": "v011", "fieldName": "结束时间", "codeName": "", "format": "hh:mm", "fieldType": "partialtime", "defValue": "", "fieldId": "CMENTIM"}, {"innerId": "v004", "fieldName": "用药原因", "codeName": "CMREAS", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CMREAS"}, {"innerId": "t002", "fieldName": "用药原因详述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CMINDC"}, {"innerId": "t003", "fieldName": "相关既往病史序号", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CMMHNO"}, {"innerId": "t004", "fieldName": "相关AE序号", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CMAENO"}, {"innerId": "v005", "fieldName": "每次剂量", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "CMDSTXT"}, {"innerId": "v006", "fieldName": "剂量单位", "codeName": "CMDOSU", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CMDOSU"}, {"innerId": "t005", "fieldName": "其他单位", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CMDOSUO"}, {"innerId": "v007", "fieldName": "剂型", "codeName": "CMDOSFRM", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CMDOSFRM"}, {"innerId": "t006", "fieldName": "其他剂型", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CMDSFMO"}, {"innerId": "v008", "fieldName": "给药途径", "codeName": "CMROUTE", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CMROUTE"}, {"innerId": "t007", "fieldName": "其他途径", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CMROUTEO"}, {"innerId": "v009", "fieldName": "给药频率", "codeName": "CMDOSFRQ", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CMDOSFRQ"}, {"innerId": "t008", "fieldName": "其他频率", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CMDSFQO"}]}, {"tid": "PRCND", "tname": "既往及合并非药物治疗/操作", "fieldList": [{"innerId": "t001", "fieldName": "治疗名称", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "PRTRT"}, {"innerId": "v001", "fieldName": "开始日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "PRSTDAT"}, {"innerId": "v005", "fieldName": "开始时间", "codeName": "", "format": "hh:mm:ss", "fieldType": "partialtime", "defValue": "", "fieldId": "PRSTTIM"}, {"innerId": "v002", "fieldName": "是否持续", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PRONGO"}, {"innerId": "v003", "fieldName": "结束日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "PRENDAT"}, {"innerId": "v006", "fieldName": "结束时间", "codeName": "", "format": "hh:mm:ss", "fieldType": "partialtime", "defValue": "", "fieldId": "PRENTIM"}, {"innerId": "v004", "fieldName": "治疗原因", "codeName": "PRREAS_CND", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PRREAS"}, {"innerId": "t002", "fieldName": "治疗原因详述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "PRINDC"}, {"innerId": "t003", "fieldName": "相关既往病史序号", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "PRMHNO"}, {"innerId": "t004", "fieldName": "相关AE序号", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "PRAENO"}]}, {"tid": "DSEOT", "tname": "治疗结束页", "fieldList": [{"innerId": "v001", "fieldName": "药物名称", "codeName": "DSSCAT", "format": "", "fieldType": "select", "defValue": "SHR1210", "fieldId": "DSSCAT"}, {"innerId": "v002", "fieldName": "治疗结束日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "DSSTDAT"}, {"innerId": "v003", "fieldName": "治疗结束主要原因", "codeName": "DSDECOD_EOT", "format": "1", "fieldType": "select", "defValue": "", "fieldId": "DSDECOD"}, {"innerId": "t001", "fieldName": "治疗结束主要原因详述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "DSTERM"}]}, {"tid": "RPPFU", "tname": "妊娠报告/随访", "fieldList": [{"innerId": "v001", "fieldName": "孕/产妇是否为男性受试者的女性伴侣", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "RPCWHO"}, {"innerId": "v002", "fieldName": "末次月经日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "RPSTDAT"}, {"innerId": "v003", "fieldName": "妊娠结果", "codeName": "RPTERM", "format": "1", "fieldType": "select", "defValue": "", "fieldId": "RPTERM"}, {"innerId": "v004", "fieldName": "分娩日期/妊娠终止日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "RPENDAT"}, {"innerId": "t001", "fieldName": "胎儿异常详述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "RPDESC"}, {"innerId": "v005", "fieldName": "是否是严重不良事件", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESER"}]}, {"tid": "DSEOS", "tname": "研究总结页", "fieldList": [{"innerId": "v001", "fieldName": "随访结束日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "DSSTDAT"}, {"innerId": "v002", "fieldName": "随访结束原因", "codeName": "DSDECOD_EOS", "format": "1", "fieldType": "select", "defValue": "", "fieldId": "DSDECOD"}, {"innerId": "t001", "fieldName": "随访结束原因详述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "DSTERM"}, {"innerId": "v003", "fieldName": "死亡原因", "codeName": "DTHREAS", "format": "", "fieldType": "select", "defValue": "", "fieldId": "DTHREAS"}, {"innerId": "t002", "fieldName": "其他死亡原因", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "DTHREASO"}, {"innerId": "v004", "fieldName": "死亡日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "DTHDAT"}, {"innerId": "v005", "fieldName": "最后获知受试者生存日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "DSLOSDAT"}]}, {"tid": "MOXC", "tname": "肺功能检测", "fieldList": [{"innerId": "v001", "fieldName": "检查日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "MODAT"}, {"innerId": "v003", "fieldName": "检查部位", "codeName": "MOLOC", "format": "", "fieldType": "mselect", "defValue": "", "fieldId": "MOLOC"}, {"innerId": "t002", "fieldName": "其他部位", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "MOLOCO"}, {"innerId": "v002", "fieldName": "临床意义", "codeName": "CLSIG", "format": "", "fieldType": "select", "defValue": "", "fieldId": "MOCLSIG"}, {"innerId": "t001", "fieldName": "异常描述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "MODESC"}]}, {"tid": "MOULSVA", "tname": "经阴道/腹部超声", "fieldList": [{"innerId": "v001", "fieldName": "检查日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "MODAT"}, {"innerId": "v010", "fieldName": "检查途径", "codeName": "MOMETHODB", "format": "", "fieldType": "select", "defValue": "", "fieldId": "MOVMTHOD"}, {"innerId": "v003", "fieldName": "子宫内膜厚度", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "MOORRES"}, {"innerId": "v004", "fieldName": "单位", "codeName": "MOORRESU_A", "format": "", "fieldType": "select", "defValue": "MM", "fieldId": "MOORRESU"}, {"innerId": "v005", "fieldName": "回声类型", "codeName": "MOMETHOD", "format": "", "fieldType": "select", "defValue": "", "fieldId": "MOMETHOD"}, {"innerId": "v006", "fieldName": "临床意义", "codeName": "CLSIG", "format": "", "fieldType": "select", "defValue": "", "fieldId": "MOCLSIG"}, {"innerId": "t001", "fieldName": "异常描述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "MODESC"}, {"innerId": "t002", "fieldName": "三个最大卵泡的直径", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "MOORRESM"}, {"innerId": "v007", "fieldName": "单位", "codeName": "MOORRESU_A", "format": "", "fieldType": "select", "defValue": "MM", "fieldId": "MOVORESU"}, {"innerId": "v008", "fieldName": "是否满足hCG注射条件", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "MOHCGYN"}, {"innerId": "t003", "fieldName": "满足hCG注射条件触发详述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "MOHCGDES"}, {"innerId": "v002", "fieldName": "是否发生自发排卵", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "MOSORRES"}, {"innerId": "v013", "fieldName": "宫内是否有存活胎儿", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "MOVORRES"}, {"innerId": "v011", "fieldName": "存活胎儿数量", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "MONUM"}, {"innerId": "v012", "fieldName": "单位", "codeName": "MOORRESU_B", "format": "", "fieldType": "select", "defValue": "UNIT", "fieldId": "MONUMU"}]}, {"tid": "MOULSVA_SUB", "tname": "经阴道/腹部超声明细-卵巢大小", "fieldList": [{"innerId": "v001", "fieldName": "检查部位", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "MOLOC"}, {"innerId": "v002", "fieldName": "长", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "MOOORRES"}, {"innerId": "v003", "fieldName": "单位", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "MOOORESU"}, {"innerId": "v004", "fieldName": "宽", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "MOWORRES"}, {"innerId": "v005", "fieldName": "单位", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "MOAORESU"}, {"innerId": "v006", "fieldName": "高", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "MOHORRES"}, {"innerId": "v007", "fieldName": "单位", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "MOBORESU"}, {"innerId": "v008", "fieldName": "临床意义", "codeName": "CLSIG", "format": "", "fieldType": "select", "defValue": "", "fieldId": "MOOCLSIG"}, {"innerId": "t001", "fieldName": "异常描述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "MOODESC"}, {"innerId": "v009", "fieldName": "检查部位", "codeName": "MOLOC", "format": "", "fieldType": "select", "defValue": "", "fieldId": "MOFLOC"}, {"innerId": "v010", "fieldName": "卵泡大小", "codeName": "MOSORRES", "format": "", "fieldType": "select", "defValue": "", "fieldId": "MOSORRES"}, {"innerId": "v011", "fieldName": "卵泡数量", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "MONORRES"}, {"innerId": "v012", "fieldName": "单位", "codeName": "MOORRESU_B", "format": "", "fieldType": "select", "defValue": "UNIT", "fieldId": "MOFORESU"}, {"innerId": "v013", "fieldName": "孕囊数量", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "MOGORRES"}, {"innerId": "v014", "fieldName": "单位", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "MOGORESU"}, {"innerId": "t002", "fieldName": "详情", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "MOGDESC"}]}]}}