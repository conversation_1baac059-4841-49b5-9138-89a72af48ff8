@echo off
REM =====================================================
REM OPTIMIZED SCHEMA COMPARISON BATCH SCRIPT
REM Same Database Instance - Different Schemas
REM Source Schema: CDTMS_PILOT
REM Target Schema: CDTMS_TEMP
REM =====================================================

echo =====================================================
echo Oracle Schema Comparison Tool
echo Same Database Instance - Schema to Schema
echo =====================================================
echo.

REM Set default values for same database comparison
set SOURCE_SCHEMA=CDTMS_PILOT
set TARGET_SCHEMA=CDTMS_TEMP

echo Default Configuration:
echo Source Schema: %SOURCE_SCHEMA%
echo Target Schema: %TARGET_SCHEMA%
echo.

REM Ask user if they want to use default schemas or customize
set /p USE_DEFAULTS="Use default schemas (Y/N)? [Y]: "
if "%USE_DEFAULTS%"=="" set USE_DEFAULTS=Y

if /i "%USE_DEFAULTS%"=="N" (
    set /p SOURCE_SCHEMA="Enter source schema name [%SOURCE_SCHEMA%]: "
    set /p TARGET_SCHEMA="Enter target schema name [%TARGET_SCHEMA%]: "
)

echo.
echo Final Configuration:
echo Source Schema: %SOURCE_SCHEMA%
echo Target Schema: %TARGET_SCHEMA%
echo.

REM Get database connection details (same for both schemas)
set /p DB_USER="Enter database username (with access to both schemas): "
set /p DB_PASS="Enter database password: "
set /p DB_HOST="Enter database host (or leave blank for local): "
set /p DB_SERVICE="Enter database service name: "

echo.
echo =====================================================
echo Creating optimized schema comparison script...
echo =====================================================

REM Create temporary SQL script with user inputs
echo -- Auto-generated schema comparison script > temp_schema_comparison.sql
echo -- Generated on %date% %time% >> temp_schema_comparison.sql
echo -- Source Schema: %SOURCE_SCHEMA% >> temp_schema_comparison.sql
echo -- Target Schema: %TARGET_SCHEMA% >> temp_schema_comparison.sql
echo. >> temp_schema_comparison.sql

echo -- Set schema variables >> temp_schema_comparison.sql
echo DEFINE SOURCE_SCHEMA = '%SOURCE_SCHEMA%' >> temp_schema_comparison.sql
echo DEFINE TARGET_SCHEMA = '%TARGET_SCHEMA%' >> temp_schema_comparison.sql
echo. >> temp_schema_comparison.sql

REM Add quick verification queries first
echo -- Quick verification of schema access >> temp_schema_comparison.sql
echo PROMPT Verifying access to schemas... >> temp_schema_comparison.sql
echo SELECT 'Source schema %SOURCE_SCHEMA% - Tables: ' ^|^| COUNT(*) as verification >> temp_schema_comparison.sql
echo FROM all_tables WHERE owner = '%SOURCE_SCHEMA%'; >> temp_schema_comparison.sql
echo. >> temp_schema_comparison.sql
echo SELECT 'Target schema %TARGET_SCHEMA% - Tables: ' ^|^| COUNT(*) as verification >> temp_schema_comparison.sql
echo FROM all_tables WHERE owner = '%TARGET_SCHEMA%'; >> temp_schema_comparison.sql
echo. >> temp_schema_comparison.sql

REM Append the main comparison script
type same_db_schema_comparison.sql >> temp_schema_comparison.sql

echo. >> temp_schema_comparison.sql
echo -- Export results to spool file >> temp_schema_comparison.sql
echo SPOOL OFF; >> temp_schema_comparison.sql

echo =====================================================
echo Running schema comparison...
echo =====================================================

REM Determine connection string
if "%DB_HOST%"=="" (
    set DB_CONN=%DB_USER%/%DB_PASS%@%DB_SERVICE%
) else (
    set DB_CONN=%DB_USER%/%DB_PASS%@%DB_HOST%:1521/%DB_SERVICE%
)

REM Create output filename with timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

set OUTPUT_FILE=schema_comparison_%SOURCE_SCHEMA%_vs_%TARGET_SCHEMA%_%timestamp%.txt

echo Connecting to database and running comparison...
echo Output will be saved to: %OUTPUT_FILE%

REM Run the comparison with spooling
(
echo SPOOL %OUTPUT_FILE%
echo SET PAGESIZE 1000
echo SET LINESIZE 200
echo SET TRIMSPOOL ON
echo SET ECHO OFF
echo SET FEEDBACK ON
echo.
type temp_schema_comparison.sql
echo SPOOL OFF
echo EXIT
) | sqlplus -S %DB_CONN%

if %ERRORLEVEL% EQU 0 (
    echo.
    echo =====================================================
    echo Schema comparison completed successfully!
    echo =====================================================
    echo Results saved to: %OUTPUT_FILE%
    echo.
    
    REM Show quick summary from the output file
    echo Quick Summary:
    echo --------------
    findstr /C:"Total Items" /C:"Matches" /C:"Mismatches" %OUTPUT_FILE% 2>nul
    echo.
    
    REM Ask if user wants to view the full results
    set /p VIEW_RESULTS="Open full results file? (Y/N) [Y]: "
    if "%VIEW_RESULTS%"=="" set VIEW_RESULTS=Y
    
    if /i "%VIEW_RESULTS%"=="Y" (
        echo Opening results file...
        start notepad %OUTPUT_FILE%
    )
    
    echo.
    echo Additional files created:
    echo - schema_comparison_summary table (in database)
    echo - schema_comparison_details table (in database)
    echo.
    echo You can query these tables later for detailed analysis:
    echo   SELECT * FROM schema_comparison_summary;
    echo   SELECT * FROM schema_comparison_details WHERE match_status = 'MISMATCH';
    
) else (
    echo.
    echo =====================================================
    echo Error occurred during comparison!
    echo =====================================================
    echo Common issues:
    echo - Incorrect database credentials
    echo - Insufficient privileges to access schemas
    echo - Schema names don't exist
    echo - Network connectivity problems
    echo.
    echo Check the output file for error details: %OUTPUT_FILE%
    pause
)

REM Cleanup temporary files
if exist temp_schema_comparison.sql del temp_schema_comparison.sql

echo.
echo =====================================================
echo Additional Quick Checks Available:
echo =====================================================
echo.
echo To run quick manual checks, connect to database and run:
echo.
echo 1. Quick table count comparison:
echo    SELECT '%SOURCE_SCHEMA%' as schema, COUNT(*) as tables FROM all_tables WHERE owner='%SOURCE_SCHEMA%'
echo    UNION ALL
echo    SELECT '%TARGET_SCHEMA%' as schema, COUNT(*) as tables FROM all_tables WHERE owner='%TARGET_SCHEMA%';
echo.
echo 2. Quick row count for specific table:
echo    SELECT COUNT(*) FROM %SOURCE_SCHEMA%.your_table_name;
echo    SELECT COUNT(*) FROM %TARGET_SCHEMA%.your_table_name;
echo.
echo 3. Check specific table from comparison scope:
echo    SELECT COUNT(*) FROM %SOURCE_SCHEMA%.tbl_attachment;
echo    SELECT COUNT(*) FROM %TARGET_SCHEMA%.tbl_attachment;
echo.

echo =====================================================
echo Script completed. Press any key to exit.
echo =====================================================
pause
