﻿
/*%let studyid=HRS-4357-101;*/
/*%let studyid=HRS9531-201;*/
/*%let lang=CH;*/
/*%let studyid=HRS-8427-302;*/
/*%let studyid=SHR-A1811-212;*/
%global system studyid root version dir lang m_minio repdat;
proc datasets lib=work nolist kill; quit;%let dir=%sysfunc(getoption(work));

x "cd &dir";x "mkdir ./pgm";x 'mc find minios3/pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';
%include "&dir/pgm/*.sas";
option mprint symbolgen validvarname=v7;

%let m_minio=minios3;
%let repdat=&sysdate.;

%let studyid=&studyid.;

%let lang="&lang.";
/*%put &lang.;*/

%m_post2s3(studyid=&studyid.);
%m_gets3data(studyid=&studyid.,data=@);


/*x "cd &dir";*/
/*x "mkdir ./dpgm";*/
/*x "mc cp  minios3-t/datamanagement/pgm/mispgm/m_met_mis_HRS4357101.sas  &dir/dpgm/m_met_mis_HRS4357101.sas ";*/
/**/
/*%include "&dir/dpgm/m_met_mis_HRS4357101.sas";*/


/*批量上线后需拆分读入和输出参数程序*/
/*读入程序*/

/*缺失程序*/
%macro metreyn;

data _null_;
tar=cat('m_met_mis_',compress("&studyid.",'','kda'));
call symputx('tar',tar);
run;

%put &tar.;


filename ps_tags pipe "mc tag list  --versions minios3/pgm/&tar..sas";

		data tags;
			infile ps_tags LINESIZE=30000 dlmstr='09'x firstobs=1 dsd;
			length tag $ 200;
			input tag;
			retain n;
		/* 不同版本的tag标序号 */
		    if find(tag,"Name") then n+1;
		run;
		proc sql noprint;select count(*) into:mnum from tags;quit;
		%put  mnum="&mnum.";


%if &mnum. ne 0 %then %do;
	/*根据项目获得tar缺失程序*/
	%&tar.;
%end;

%else %if &mnum. eq 0 %then %do;
	%m_met_mis_common.;
%end;
%mend;

%metreyn;

/*合并程序*/
