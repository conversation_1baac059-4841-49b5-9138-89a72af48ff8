SELECT
	user_id,
	object_id,
	user_name,
	email,
	operateType,
	object_type,
	object_name,
	compound,
	STATUS,
	operate_time 
FROM
	(
SELECT
	user_id,
	object_id,
	user_info.user_name,
	user_mail AS email,
	operation_type AS operateType,
	object_type,
	object_name,
	file_upload_record.compound_folder AS compound,
	STATUS,
	DATE_FORMAT( operate_time, '%Y-%m-%d %H:%i:%s' ) AS operate_time 
FROM
	user_operation_log
	LEFT JOIN file_upload_record ON object_id = file_upload_record.id
	LEFT JOIN user_info ON user_id = user_info.id 
WHERE
	object_type = '文件' 
	AND ( object_name LIKE concat( '%', '江苏恒瑞医药股份有限公司', '%' ) OR user_info.user_name LIKE concat( '%', '江苏恒瑞医药股份有限公司', '%' ) ) UNION
SELECT
	user_id,
	object_id,
	user_info.user_name,
	user_mail AS email,
	operation_type AS operateType,
	object_type,
	object_name,
	file_store_folder.compound_folder AS compound,
	STATUS,
	DATE_FORMAT( operate_time, '%Y-%m-%d %H:%i:%s' ) AS operate_time 
FROM
	user_operation_log
	LEFT JOIN file_store_folder ON object_id = file_store_folder.id
	LEFT JOIN user_info ON user_id = user_info.id 
WHERE
	object_type = '文件夹' 
	AND ( object_name LIKE concat( '%', '江苏恒瑞医药股份有限公司', '%' ) OR user_info.user_name LIKE concat( '%', '江苏恒瑞医药股份有限公司', '%' ) ) 
	) AS result 
WHERE
	1 = 1