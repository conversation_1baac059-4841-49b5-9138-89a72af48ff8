package net.bioknow.cdtms.studyClose;

import net.bioknow.mvc.ioc.AppStartFace;
import net.bioknow.passport.uiframe.FuncUtil;
import net.bioknow.passport.validate.PageIgnore;
import net.bioknow.services.uap.dbdatamng.function.FuncFactoryNew;
import net.bioknow.uap.dbdatamng.function.FuncFactory;
import net.bioknow.uapplug.AdminMenuHandler;
import net.bioknow.webplug.configdb.ConfigDbUtil;

import javax.servlet.ServletConfig;

public class EHAppStart implements AppStartFace{

    public void onStart(ServletConfig arg) throws Exception {


        FuncFactory.addTableFunc(new DTRFstudyClose());
        FuncFactory.addTableFunc(new DTRFstudyOpen());
        FuncFactoryNew.addTableFunc(new DTRFstudyCloseVue());
        FuncFactoryNew.addTableFunc(new DTRFstudyOpenVue());
        FuncUtil.addFunction("prjfunc", "studyClose", "项目关闭", (String)null, (String)null, "configdb.listcfg.do?xmlpath=/prjplug/studyclose/configform.xml");

        //ConfigDbUtil.registerCFGFile(DAOstudyCloseIntegrate.xmlpath);

    }

}
