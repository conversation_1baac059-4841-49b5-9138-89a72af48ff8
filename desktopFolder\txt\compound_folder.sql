/*
 Navicat MySQL Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80032 (8.0.32)
 Source Host           : localhost:3306
 Source Schema         : susar_auto_mail

 Target Server Type    : MySQL
 Target Server Version : 80032 (8.0.32)
 File Encoding         : 65001

 Date: 16/02/2023 12:34:27
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for compound_folder
-- ----------------------------
DROP TABLE IF EXISTS `compound_folder`;
CREATE TABLE `compound_folder`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `folder_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY <PERSON>EY (`id`) USING BTREE,
  UNIQUE INDEX `id`(`id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of compound_folder
-- ----------------------------
INSERT INTO `compound_folder` VALUES (1, 'SUSAR-1316', '2023-02-16 11:28:12');
INSERT INTO `compound_folder` VALUES (2, 'SUSAR-1317', '2023-02-16 11:28:12');
INSERT INTO `compound_folder` VALUES (3, 'SUSAR-1318', '2023-02-16 11:28:12');
INSERT INTO `compound_folder` VALUES (4, 'SUSAR-1219', '2023-02-16 11:28:12');
INSERT INTO `compound_folder` VALUES (5, 'SUSAR-2140', '2023-02-16 11:28:12');
INSERT INTO `compound_folder` VALUES (6, 'SUSAR-1122', '2023-02-16 11:28:12');
INSERT INTO `compound_folder` VALUES (7, 'SUSAR-2131', '2023-02-16 11:28:12');

SET FOREIGN_KEY_CHECKS = 1;
