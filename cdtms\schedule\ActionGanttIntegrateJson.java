package net.bioknow.cdtms.schedule;

import net.bioknow.passport.datamng.PassportCacheUtil;
import net.bioknow.services.core.ResultAction;
import net.bioknow.services.uap.schemaplug.BeanSchemaParams;
import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbcore.dbmng.CNT_DbMng;
import net.bioknow.uap.dbcore.schema.CNT_Schema;
import net.bioknow.uap.dbcore.schema.Schema;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.entitymng.EntityUtil;
import net.bioknow.uap.schemaplug.dtref.DtrefDAO;
import net.bioknow.uapplug.reportform.DAOReportform;
import net.bioknow.webio.restful.Restful;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang.StringUtils;
import org.nlpcn.commons.lang.util.StringUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Restful("/schedule")
public class ActionGanttIntegrateJson extends ResultAction {


    private String formatRadioValue(DAODbApi apidao, String fieldid, EntityScheduleTemplate tem) {
        String result = "";
        Map mapF = apidao.getMapField(EntityUtil.getTableId(EntityScheduleTemplate.class), fieldid);
        Schema schema = apidao.getFieldType(EntityUtil.getTableId(EntityScheduleTemplate.class), fieldid);
        if (fieldid.equals("scheduletype")) {
            result = schema.formatToOutput(EntityUtil.getTableId(EntityScheduleTemplate.class), mapF, EntityUtil.tranToMap(tem));
        } else if (fieldid.equals("preschedule")) {
            result = schema.formatToOutput(EntityUtil.getTableId(EntityScheduleTemplate.class), mapF, EntityUtil.tranToMap(tem));
        }
        return result;
    }

    private List<EntityScheduleTemplate> getScheduleBytime(String projectId, String timestr) {
        DAODbApi apidao = new DAODbApi(projectId);
        String[] arr = timestr.split(";");
        Map mapTime = new HashMap();
        List<String> listTime = new ArrayList();
        for (String str : arr) {
            String[] ar = str.split(",");
            mapTime.put(ar[0], ar[1]);
            listTime.add(ar[1]);
        }
        List<EntityScheduleTemplate> listSchedule = EntityUtil.listEntity(projectId, "", "obj.num asc", 1000, 1, EntityScheduleTemplate.class);
        int i=0;
        for(EntityScheduleTemplate schedule : listSchedule){
            String type = schedule.scheduletype;
            if (StringUtils.equals("milestone", type)) {

                schedule.schedulestart = listTime.get(i);
                schedule.scheduleend = listTime.get(i);
                String end = listTime.size() > i+1?listTime.get(i+1):listTime.get(i);
                try {
                    setstagetime(schedule.schedulestart, end, schedule, listSchedule);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                i ++;
            }
        }
        return listSchedule;
    }

    private void setstagetime(String start, String end, EntityScheduleTemplate ps, List<EntityScheduleTemplate> listSchedule) throws Exception {
        String projectid = SessUtil.getSessInfo().getProjectid();
        DAODbApi apidao = new DAODbApi(projectid);
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        List<EntityScheduleTemplate> list = listSchedule.stream().filter(k -> ("," + k.preschedule + ",").contains("," + ps.num + ",")).collect(Collectors.toList());
        for (EntityScheduleTemplate tm : list) {
            if ("milestone".equals(tm.scheduletype)) continue;
            tm.schedulestart = ps.scheduleend;
            try {
                Date st = sf.parse(start);
                Date et = sf.parse(end);
                long days = (et.getTime() - st.getTime()) / 1000 / 24 / 3600;
                long l = new Double(days * Double.parseDouble(tm.scheduleper)).longValue();
                tm.scheduleend = sf.format(new Date(sf.parse(tm.schedulestart).getTime() + l * 24 * 3600 * 1000));
                List<EntityScheduleTemplate> list2 = listSchedule.stream().filter(k -> ("," + k.preschedule + ",").contains("," + tm.num + ",")).collect(Collectors.toList());
                if (list2.size() > 0) {
                    setstagetime(start, end, tm, listSchedule);
                }
            } catch (ParseException parseException) {
                parseException.printStackTrace();
            }
        }
    }

    @Restful(value = "/finishSchedule")
    public void finishSchedule(HttpServletRequest request, HttpServletResponse response) {
        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            String time = request.getParameter("times");
            String per = request.getParameter("per");
            String respo = request.getParameter("respo");
            String studyid = request.getParameter("studyId");
            List<EntityScheduleTemplate> listSchedule = getScheduleBytime(projectId, time);

            Map mapper = new HashMap();
            String[] arr = per.split(";");
            for (String str : arr) {
                String[] ar = str.split(",");
                mapper.put(ar[0], ar[1]);
            }

            Map maprespo = new HashMap();
            String[] arr2 = respo.split(";");
            for (String str : arr2) {
                String[] ar = str.split(",");
                if(ar==null || ar.length <2) continue;
                maprespo.put(ar[0], ar[1]);
            }

            DAODataMng dmdao = new DAODataMng(projectId);
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            dmdao.delRecord("schedule", "obj.study_id='" + studyid + "'");
            dmdao.delRecord("schedule_links", "obj.study_id='" + studyid + "'");
            //Long kk = 0L;

            Map mapStudy = dmdao.getRecord("xsht", Long.parseLong(studyid));
            Map mapStudySchedule = new HashMap();
            mapStudySchedule.put("study_id", Long.parseLong(studyid));
            mapStudySchedule.put("name", mapStudy.get("studyid"));

            mapStudySchedule.put("start_date", sf.parse(listSchedule.get(0).schedulestart));
            mapStudySchedule.put("end_date", sf.parse(listSchedule.get(listSchedule.size()-1).scheduleend));
            mapStudySchedule.put("planned_start_date", sf.parse(listSchedule.get(0).schedulestart));
            mapStudySchedule.put("planned_end_date", sf.parse(listSchedule.get(listSchedule.size()-1).scheduleend));
            mapStudySchedule.put("text", "");
            mapStudySchedule.put("progress", Double.parseDouble("0.0"));
            mapStudySchedule.put("duration", 0L);
            mapStudySchedule.put("planned_duration", 0L);
            mapStudySchedule.put("sortorder", 0L);
            mapStudySchedule.put("tableid", "");
            mapStudySchedule.put("is_cycle", "0");
            mapStudySchedule.put("parent_schedule_id", null);
            mapStudySchedule.put("type", "project");

            dmdao.saveRecord("schedule", mapStudySchedule);

            //阶段任务
            LinkedHashMap mapStage = new LinkedHashMap();
            for (EntityScheduleTemplate tem : listSchedule) {
                String stage = tem.schedulestage;
                String start = tem.schedulestart;
                String end = tem.scheduleend;
                Map map = new HashMap();
                if(mapStage.containsKey(stage)){
                    map = (Map)mapStage.get(stage);
                }else{
                    mapStage.put(stage, map);
                }
                String s = (String)map.get("start");
                if(StringUtils.isEmpty(s)){
                    map.put("start", start);
                }
                map.put("end", end);
            }

            Iterator ite = mapStage.keySet().iterator();
            List listTosSaveStage = new ArrayList();
            Long ii = 1L;
            while(ite.hasNext()){
                String key = (String)ite.next();
                Map map = (Map)mapStage.get(key);
                Map mapStageSchedule = new HashMap();
                listTosSaveStage.add(mapStageSchedule);
                mapStageSchedule.put("study_id", Long.parseLong(studyid));
                mapStageSchedule.put("name", key);

                mapStageSchedule.put("start_date", sf.parse((String)map.get("start")));
                mapStageSchedule.put("end_date", sf.parse((String)map.get("end")));
                mapStageSchedule.put("planned_start_date", sf.parse((String)map.get("start")));
                mapStageSchedule.put("planned_end_date", sf.parse((String)map.get("end")));
                mapStageSchedule.put("text", "");
                mapStageSchedule.put("progress", Double.parseDouble("0.0"));
                mapStageSchedule.put("duration", 0L);
                mapStageSchedule.put("planned_duration", 0L);
                mapStageSchedule.put("sortorder", ii++);
                mapStageSchedule.put("tableid", "");
                mapStageSchedule.put("is_cycle", "0");
                mapStageSchedule.put("parent_schedule_id", mapStudySchedule.get(CNT_DbMng.id));
                mapStageSchedule.put("type", "project");
            }
            String userid = SessUtil.getSessInfo().getUserid();
            String unitid = PassportCacheUtil.getUnitIdByUserid(projectId, userid);
            dmdao.saveBatchWithoutEH("schedule", listTosSaveStage, Long.parseLong(userid), unitid == null ? null : Long.parseLong(unitid));

            Map mapstage = new HashMap();
            listTosSaveStage.stream().forEach(k -> {
                Map m = (Map)k;
                mapstage.put(m.get("name"), m.get(CNT_DbMng.id));
            });

            List listtosave = new ArrayList();
            for (EntityScheduleTemplate tem : listSchedule) {
                Map maptosave = new HashMap();
                maptosave.put("study_id", Long.parseLong(studyid));
                maptosave.put("name", tem.schedulename);
                maptosave.put("start_date", sf.parse(tem.schedulestart));
                maptosave.put("end_date", sf.parse(tem.scheduleend));
                maptosave.put("planned_start_date", sf.parse(tem.schedulestart));
                maptosave.put("planned_end_date", sf.parse(tem.scheduleend));
                //maptosave.put("owner", tem.num);
                maptosave.put("text", tem.preschedule);
                maptosave.put("progress", Double.parseDouble("0.0"));
                maptosave.put("duration", (sf.parse(tem.scheduleend).getTime() - sf.parse(tem.schedulestart).getTime()) / 1000);
                maptosave.put("planned_duration", (sf.parse(tem.scheduleend).getTime() - sf.parse(tem.schedulestart).getTime()) / 1000);
                maptosave.put("sortorder", Long.parseLong(tem.sn));
                maptosave.put("tableid", tem.tableid);
                maptosave.put("menuid", tem.menuid);
                if (mapper.containsKey(tem.num)) {
                    maptosave.put("is_cycle", "1");
                    maptosave.put("cycle", mapper.get(tem.num));
                } else {
                    maptosave.put("is_cycle", "0");
                }

                maptosave.put("type", tem.scheduletype);
                if (maprespo.containsKey(tem.num)) {
                    maptosave.put("owner", maprespo.get(tem.num));
                }
                //maptosave.put("parent_schedule_id", mapStudySchedule.get(CNT_DbMng.id));
                maptosave.put("parent_schedule_id", mapstage.get(tem.schedulestage));
                //maptosave.put("parent_schedule_id", mapStudySchedule.get(CNT_DbMng.id));
                maptosave.put("markfield", tem.nodefieldid);
                maptosave.put("nodefield", tem.markid);
                listtosave.add(maptosave);
            }
            dmdao.saveBatchWithoutEH("schedule", listtosave, Long.parseLong(userid), unitid == null ? null : Long.parseLong(unitid));
            Map mapc = new HashMap();
            for (int i = 0; i < listtosave.size(); i++) {
                Map map = (Map) listtosave.get(i);
                Long rid = (Long) map.get(CNT_DbMng.id);
                String num = (String) map.get("owner");
                mapc.put("," + num + ",", rid);
            }

            List linkList = new ArrayList();
            for (int i = 0; i < listtosave.size(); i++) {
                Map mapV = (Map) listtosave.get(i);
                String num = (String) mapV.get("text");

                Map map = new HashMap();
                linkList.add(map);
                map.put("type", "0");
                map.put("study_id", Long.parseLong(studyid));
                Long numid = (Long) mapc.get(num);
                if (numid != null) {
                    map.put("schedule_id", numid);
                } else {
                    continue;
                }
                map.put("target_schedule_id", mapV.get(CNT_DbMng.id));
            }
            dmdao.saveBatch("schedule_links", linkList, Long.parseLong(userid), unitid == null ? null : Long.parseLong(unitid));

            //处理历史数据
            DAOReportform rfdao = new DAOReportform(projectId);
            DtrefDAO refdao = new DtrefDAO(projectId);
            List listsub = rfdao.listAllTableFirst("xsht");
            listsub.forEach(x -> {
                String tid = (String)x;
                String reffid = refdao.getRefField("xsht", tid);
                try {
                    List sub = dmdao.listRecord(tid, "obj."+reffid+"='"+studyid+"'", null, 1);
                    if(sub != null && sub.size() >0 ){
                        Map map = (Map)sub.get(0);
                        TaskGeneratorUtil.setScheduleFinishTime(projectId, tid, map);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

            });

            response(response, "ok", "/scheduleGantt.show.do?studyId="+studyid+"&entrytid=xsht");
        } catch (Exception e) {
            Log.error(e.getMessage(), e);
        }
    }

    @Restful(value = "/getRespon")
    public void getRespon(HttpServletRequest request, HttpServletResponse response) {
        try {
            String studyid = request.getParameter("studyId");
            String projectId = SessUtil.getSessInfo().getProjectid();
            String time = request.getParameter("times");
            DAODbApi apidao = new DAODbApi(projectId);
            List<EntityScheduleTemplate> listSchedule = getScheduleBytime(projectId, time);
            for(EntityScheduleTemplate schedule : listSchedule){
                schedule.scheduletype = formatRadioValue(apidao, "scheduletype", schedule);
                schedule.preschedule = formatRadioValue(apidao, "preschedule", schedule);
            }
            DAODataMng dmdao = new DAODataMng(projectId);
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            String tableid = EntityUtil.getTableId(EntityScheduleTemplate.class);
            String[] listfields = {"schedulename", "_respon", "schedulestart", "scheduleend","_day"};
            List listcolumns = new ArrayList();
            for(String str : listfields){
                Map mapcolumns = new HashMap();
                listcolumns.add(mapcolumns);
                if(str.equals("_day")){
                    mapcolumns.put("prop", "_day");
                    mapcolumns.put("label", "计划时长（自然日）");
                    mapcolumns.put("sortable", "custom");
                }else if(str.equals("_respon")){
                    mapcolumns.put("prop", "_respon");
                    mapcolumns.put("label", "负责人");
                    mapcolumns.put("sortable", "custom");
                    mapcolumns.put("slotName", "_respon");
                }else{
                    Map mapF = apidao.getMapField(tableid, str);
                    mapcolumns.put("prop", mapF.get(CNT_Schema.id));
                    mapcolumns.put("label", mapF.get(CNT_Schema.name));
                    mapcolumns.put("sortable", "custom");
                }
            }

            List<Map> listuser = dmdao.listRecord("ryjbzl", "", "", 1000);
            Map mapUser = new HashMap();
            for (Map map : listuser) {
                mapUser.put(String.valueOf(map.get(CNT_DbMng.id)), map.get("xm"));
            }

            List<Map> listroles = dmdao.listRecord("roles", "obj.studyid='" + studyid + "' and obj.active ='1'", null, 1000);

            Map maprole = new HashMap();
            for (Map map : listroles) {
                String projectRole = (String) map.get("limitnum");
                Long roleid = (Long) map.get("member");
                if (StringUtils.isNotEmpty(projectRole)) {
                    String[] str = projectRole.split(",");
                    for (String s : str) {
                        if (maprole.containsKey(s)) {
                            String roleids = (String) maprole.get(s);
                            roleids += "," + roleid;
                            maprole.put(s, roleids);
                        } else {
                            maprole.put(s, roleid + "");
                        }
                    }
                }
            }

            List listrst = EntityUtil.tranToMap(listSchedule);
            for(int i=0;i<listrst.size();i++){
                Map map = (Map)listrst.get(i);
                String studyrole = (String)map.get("studyrole");
                if (maprole.containsKey(studyrole)) {
                    String roleids = (String) maprole.get(studyrole);
                    String[] roles = roleids.split(",");
                    List listrole = new ArrayList();
                    for(String role : roles){
                        Map mapRole = new HashMap();
                        mapRole.put("codedesp", role);
                        mapRole.put("codevalue", mapUser.get(role));
                        listrole.add(mapRole);
                    }
                    map.put("selector", listrole);
                }
                Long diff = (sf.parse((String)map.get("scheduleend")).getTime()-sf.parse((String)map.get("schedulestart")).getTime())/24/3600/1000;
                map.put("_day", diff);
            }
            String html = "";
//            for (EntityScheduleTemplate tem : listSchedule) {
//                html += "<tr style='text-align:center'><td></td><td>" + tem.schedulename + "</td><td>";
//
//                if (maprole.containsKey(tem.studyrole)) {
//                    String roleids = (String) maprole.get(tem.studyrole);
//                    String[] roles = roleids.split(",");
//                    html += "<div >";
//                    for (String role : roles) {
//                        html += "<input id=" + tem.num + " class=role type=checkbox value=" + role + " checked>" + mapUser.get(role) + "</option>";
//                    }
//                    html += "</div>";
//                }
//                html += "</td><td>" + tem.schedulestart + "</td><td>" + tem.scheduleend + "</td><td>";
//                Long diff = (sf.parse(tem.scheduleend).getTime()-sf.parse(tem.schedulestart).getTime())/24/3600/1000;
//                html += (diff) + "</td></tr>";
//            }

            Map maprst = new HashMap();
            maprst.put("columns", listcolumns);
            maprst.put("list", listrst);
            response(response, "ok", maprst);
        } catch (Exception e) {
            Log.error(e.getMessage(), e);
        }
    }
    @Restful(value = "/getScheduleTime")
    public void getScheduleTime(HttpServletRequest request, HttpServletResponse response) {
        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            String time = request.getParameter("times");
            DAODbApi apidao = new DAODbApi(projectId);
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            List<EntityScheduleTemplate> listSchedule = getScheduleBytime(projectId, time);
            for(EntityScheduleTemplate schedule : listSchedule){
                schedule.scheduletype = formatRadioValue(apidao, "scheduletype", schedule);
                schedule.preschedule = formatRadioValue(apidao, "preschedule", schedule);
            }
            String tableid = EntityUtil.getTableId(EntityScheduleTemplate.class);
            String[] listfields = {"schedulename", "scheduletype", "preschedule", "schedulestart", "scheduleend","_day","schedulefre"};
            List listcolumns = new ArrayList();
            for(String str : listfields){
                Map mapcolumns = new HashMap();
                listcolumns.add(mapcolumns);
                if(str.equals("_day")){
                    mapcolumns.put("prop", "_day");
                    mapcolumns.put("label", "计划时长（自然日）");
                    mapcolumns.put("sortable", "custom");
                }else{
                    Map mapF = apidao.getMapField(tableid, str);
                    mapcolumns.put("prop", mapF.get(CNT_Schema.id));
                    mapcolumns.put("label", mapF.get(CNT_Schema.name));
                    mapcolumns.put("sortable", "custom");
                }

                if(str.equals("schedulefre")){
                    mapcolumns.put("slotName", "schedulefre");
                    Map mapF = apidao.getMapField(tableid, "schedulefre");
                    String selector = (String)mapF.get(CNT_Schema.selector);
                    List selectors = new ArrayList();
                    if (StringUtil.isNotBlank(selector)) {
                        String[] sels = selector.trim().replaceAll("\\r\\n", "").split(";");
                        for (String sel : sels) {
                            Map mapselector = new HashMap();
                            selectors.add(mapselector);
                            if (StringUtil.isNotBlank(sel)) {
                                int indexOf = sel.indexOf(",");
                                if (indexOf == -1) continue;
                                mapselector.put("codevalue", sel.substring(0, indexOf));
                                mapselector.put("codedesp", sel.substring(indexOf + 1));
                            }
                        }
                    }
                    mapcolumns.put("selector", selectors);
                }
            }

            List listrst = EntityUtil.tranToMap(listSchedule);
            for(int i=0;i<listrst.size();i++){
                Map map = (Map)listrst.get(i);
                Long diff = (sf.parse((String)map.get("scheduleend")).getTime()-sf.parse((String)map.get("schedulestart")).getTime())/24/3600/1000;
                map.put("_day", diff);
            }

            Map maprst = new HashMap();
            maprst.put("columns", listcolumns);
            maprst.put("list", listrst);
            response(response, "ok", maprst);
        } catch (Exception e) {
            Log.error(e.getMessage(), e);
        }
    }


    @Restful(value = "/init")
    public void initSchedulePage(HttpServletRequest request, HttpServletResponse response) {
        try {
            String studyId = request.getParameter("studyId");
            String projectId = SessUtil.getSessInfo().getProjectid();
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");

//            DAODataMng dmdao = new DAODataMng(projectId);
//            List list = dmdao.listRecord("schedule", "obj.study_id='"+studyId+"' and obj.type ='milestone'", null, 100);
//            Map mapname = new HashMap();
//            list.stream().forEach(k -> {
//                Map map = (Map)k;
//                mapname.put(map.get("name"), map.get("planned_start_date"));
//            });

            List<EntityScheduleTemplate> listSchedule = EntityUtil.listEntity(projectId, "obj.scheduletype='milestone'", "obj.sn asc", EntityScheduleTemplate.class);
            List listrst = new ArrayList();
            Map mapForm = new HashMap();
            Map mapValue = new HashMap();
            listSchedule.stream().forEach(k -> {
                Map map = new HashMap();
                map.put("name", k.schedulename);
                //if(mapname.containsKey(k.schedulename)) map.put("planstarttime", sf.format((Date)mapname.get(k.schedulename)));
                map.put("num", k.num);
                listrst.add(map);
                BeanSchemaParams bean = new BeanSchemaParams();
                bean.setId(k.num);
                bean.setType("datetime");
                bean.setName(k.schedulename);
                bean.setDatetimeAccuracy("d");
                bean.setNotNull(true);
                mapForm.put(k.num, bean);
                Map mapV = new HashMap();
                mapV.put("value", "");
                mapV.put("type", "datetime");
                mapValue.put(k.num, mapV);
            });
            Map map = new HashMap();
            map.put("schedulenamelist", listSchedule);
            map.put("studyid", studyId);


            String tableId = EntityUtil.getTableId(EntityScheduleTemplate.class);
            Map maprst = new HashMap();
            maprst.put("subtable", mapForm);
            maprst.put("value", mapValue);
            maprst.put("studyid", studyId);

            response(response, "ok", maprst);
        } catch (Exception e) {
            Log.error("", e);
        }
    }


    public void show(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            String studyId = request.getParameter("studyId");

            String prrojectid = SessUtil.getSessInfo().getProjectid();
            DAODataMng dmdao = new DAODataMng(prrojectid);
            List listRecord = dmdao.listRecord("schedule", "obj.study_id='" + studyId + "' and obj.type='project'", null, 1);
            Long pid = null;
            if(listRecord != null && listRecord.size() >0 ){
                Map mapRecord = (Map)listRecord.get(0);
                pid = (Long)mapRecord.get(CNT_DbMng.id);
            }
            String where =  "obj.study_id='" + studyId + "'";
            if(pid !=null) where += " and obj.parent_schedule_id='"+pid+"'";
            int count = dmdao.count("schedule", where);
            int count2 = dmdao.count(EntityUtil.getTableId(EntityScheduleTemplate.class), null);
            if (StringUtils.isNotEmpty(studyId)&&count == 0 || count < count2) {
                this.redirectByUri(request, response, "/scheduleGantt.initSchedulePage.do?studyId=" + studyId);
                return;
            }

            String currRoleName = (String) SessUtil.getSessInfo().getUser().get("ryjs");

            Long userid = Long.valueOf(SessUtil.getSessInfo().getUserid());

            if (userid <= 2) {

                currRoleName = "MDM";
            }

            if (StringUtils.isNotEmpty(studyId)) {
                request.setAttribute("studyId", request.getParameter("studyId"));
                request.setAttribute("currRoleName", currRoleName);
            }
            this.forward(request, response, "show");
        } catch (Exception e) {
            Log.error("", e);
        }
    }
}
