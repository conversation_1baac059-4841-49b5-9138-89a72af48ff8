﻿%global system studyid root version dir lang m_minio;
proc datasets lib=work nolist kill; quit;
%let dir=%sysfunc(getoption(work));
x "cd &dir";
%put &dir;

/*初始化minio别名*/

x "mkdir ./pgm";x 'mc find minios3/pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';%include "&dir/pgm/*.sas";

x 'mc find minios3/pgm/ --name "M_std_uatpre.sas" --exec "mc cp {} ./pgm/"';
x 'mc find minios3/pgm/ --name "m_uat_varchack.sas" --exec "mc cp {} ./pgm/"';

x 'mc find minios3/pgm/ --name "m_create_hrtaubuild.sas" --exec "mc cp {} ./pgm/"';
x 'mc find minios3/pgm/ --name "m_derive_hrtaubuild.sas" --exec "mc cp {} ./pgm/"';
x 'mc find minios3/pgm/ --name "m_create_hrtauproj.sas" --exec "mc cp {} ./pgm/"';
x 'mc find minios3/pgm/ --name "m_gets3data_folder.sas" --exec "mc cp {} ./pgm/"';


/*指定minio宏参数*/

%let m_minio=minios3;


x "mkdir ./pgm";x 'mc find &m_minio./pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';%include "&dir/pgm/*.sas";

%let studyid=&studyid.;

/*%let studyid=SHR-4597-101;*/
/*HR7056-302*/
/*HRS-4357-101*/
/*SHR-4597-101*/

%let env=UAT;
%let type=edcserver/sas;
option mprint symbolgen validvarname=v7;

/*语言能否获得*/
/*%let lang="CH";*/

/*%m_post2s3(studyid=&studyid.);*/
/*%m_gets3data(studyid=&studyid.,data=@);*/



/*%let jsonPath=minios3/uat/json/CDTMS手册优化测试项目_UAT_dbafeeabf7dfb071.json;*/

%M_std_uatpre;

/*%M_std_uatpre(json_path=&jsonPath.);*/


/*sas.submit('''x "nohup mc alias set minios3 http://10.10.14.28:9000 bio_liaw Bioknow##2023";''')*/
