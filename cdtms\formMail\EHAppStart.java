package net.bioknow.cdtms.formMail;

import net.bioknow.mvc.ioc.AppStartFace;
import net.bioknow.services.uap.dbdatamng.function.FuncFactoryNew;
import net.bioknow.uap.dbdatamng.function.FuncFactory;

import javax.servlet.ServletConfig;

public class EHAppStart implements AppStartFace{

    public void onStart(ServletConfig arg) throws Exception {
        FuncFactory.addRecordFunc(new DTRFSendMail());
        FuncFactoryNew.addRecordFunc(new DTRFSendMailVue());
//        FuncFactory.addRecordFunc(new DTRFlightpdfSignView());
//        PageIgnore.addIgnore("/lightpdfSign.callback.do");
//        PageIgnore.addIgnore("/lightpdfSign.checkIsLogin.do");

//        FuncUtil.addFunction("sysfunc", "sysfunc.param", "系统参数设定", "Login Parameters Set", "doc.gif", "authparam.list.do");

//        FuncUtil.addFunction(FuncUtil.FuncFolder_prjfunc,"LightpdfSign","LightpdfSign集成",null,null,"configdb.listcfg.do?xmlpath="+ DAOLightpdfSignIntegrate.xmlpath);
////        ConfigDbUtil.registerCFGFile(DAOeSignIntegrate.xmlpath);
//









    }

}
