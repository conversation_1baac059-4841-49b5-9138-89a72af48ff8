<template>
  <div>
    <div class="header-menu">
      <el-container>
        <el-header style="margin-top: 20px; height: 40px;">
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item label="选择环境：">
              <el-select v-model="environment" placeholder="请选择环境" @change="getProject(environment)" width="200px">
                <el-option label="Production" value="1"></el-option>
                <el-option label="Sandbox" value="0"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="选择项目：">
              <el-select v-model="project.route" placeholder="请选择项目" @change="getFileList(project.route)" width="300px">
                <el-option v-for="item in projectlist" :key="item.route" :value="item.route" :label="item.projectname">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </el-header>
      </el-container>

    </div>
    <!-- 数据显示 -->
    <el-table :data="Filelist" ref="myTable" size="small" :stripe="true" :highlight-current-row="true"
      @selection-change="handleSelectionChange">
      <el-table-column prop="filename" label="参数文件名"></el-table-column>
      <el-table-column prop="create_time" label="创建时间"></el-table-column>
      <el-table-column prop="filepath" label="操作">
        <!-- <template slot-scope="scope">
          <a :href="scope.row.filepath" target="_blank" class="buttonText">{{ scope.row.filepath }}</a>
          <el-link :href="scope.row.filepath" type="primary">{{ scope.row.filepath }}</el-link>
        </template> -->
        <template slot-scope="scope">
          <a :href="filepath" download="" type="button"
            style="text-decoration:none;font-size:20px;color: #409EFF;margin-left: 10px"
            @click="downloadFile(scope.row)">
            保存本地</a>
        </template>
      </el-table-column>


      <!-- <el-table-column prop="operation" label="操作" fixed="right" width="200px">
        <template slot-scope="scope">
          <el-button icon="el-icon-download" type="primary" @click="handleDownload(scope.$index, scope.row)"
            v-permission="['ADMINISTRATOR', 'SYSTEMADMIN']">下载
          </el-button> -->
          <!-- <el-button type="primary" size="small" icon="下载" @click="downloadFile(scope.$index, scope.row)">下载
          </el-button> -->
        <!-- </template> -->
        <!-- <template slot-scope="scope">
          <a :href="filepath" download="" type="button"
            style="text-decoration:none; font-size:20px; color: #409EFF; margin-left: 10px"
            @click="downloadFile(scope.row.filepath)">
            保存本地</a>
        </template> -->
      <!-- </el-table-column> -->
    </el-table>


    <!-- 上一页，当前页，下一页 -->
    <!-- <div class="footer-button">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page.sync="page"
        :page-size="pageSize" layout="total, prev, pager, next" :total="total">
      </el-pagination>
    </div> -->
  </div>
</template>

<script>

import { getProject } from '@/api/router'
import { query, download } from '@/api/filelist'


export default {
  name: "FileList",
  data() {
    return {
      environment: '',
      project: {
        route: '',
      },
      Filelist: [],
      projectlist: [],
      keyword: "",
      page: 1,
      pageSize: 10,
      total: 0,
    };
  },

  mounted() {
    // this.allParameter();
    // this.getProject();
  },

  methods: {
    getProject(environment) {
      console.log(this.environment);
      getProject(environment).then(res => {
        this.projectlist = []
        this.projectlist = res.data
        // console.log(this.projectlist);
        // this.$message({message:'查询成功', type: 'success'})
      }).catch(error => {
        this.$message.error("查询项目列表失败")
      });
    },

    getFileList(route) {
      query(route).then(res => {
        this.Filelist = res.data
        this.$message({ message: '查询成功', type: 'success' })
      }).catch(error => {
        this.$message.error("查询列表失败")
      });
    },

    download(index, row) {
      // console.log(11111);
      // console.log(row.filepath);
      // let url = row.filepath.replace('V:\Randmizaition_Generator\Production\Projects','\zip')
      // console.log(url);
      // window.open(url)

      let url = row.filepath
      console.log(url);
      const a = document.createElement('a')
      a.href = url
      a.download = row.fileName // 下载后文件名
      a.style.display = 'none'
      document.body.appendChild(a)
      a.click() // 点击下载
      document.body.removeChild(a)

      // window.location.href = row.filepath
    },

    // downloadFile(path) {
    //   download(path)
    // },

    downloadFile(row) {
      let path = row.filepath
      let filename = row.filename + ".zip"
      download(path).then(res => {
        this.downloadhandler(res,filename)
      }).catch(error => {
      });



      // console.log(path);
      // // const url = '导出链接'; //记得拼接参数
      // const xhr = new XMLHttpRequest();
      // xhr.open('GET', download(path), true); // 也可以使用POST方式，根据接口
      // // xhr.setRequestHeader('token', sessionStorage.getItem('token')); // 设置token
      // xhr.setRequestHeader('Content-Type', 'application/zip; charset=utf-8');
      // xhr.responseType = 'blob'; // 返回类型blob
      // xhr.onload = function (e) {
      //   if (this.status === 200) {
      //     const blob = this.response;
      //     const reader = new FileReader();
      //     reader.readAsDataURL(blob); // 转换为base64，可以直接放入a标签href
      //     reader.onload = function (e) {
      //       console.log("step1");
      //       const a = document.createElement('a');
      //       console.log("step2");
      //       a.download = 'test.zip';
      //       a.href = e.target.result;
      //       document.documentElement.appendChild(a);
      //       a.click();
      //       console.log("step3");
      //       a.remove(); // 等价于document.documentElement.removeChild(a);
      //     };
      //   }
      // };
      // xhr.send(); // 发送ajax请求

      // var flag = true;
      // searchFrom.form('submit', {
      //   url: "/filelist/download",
      //   onSubmit: function (path) {
      //     $.messager.show({
      //       title: '提示',
      //       msg: '导出成功',
      //       timeout: 2000
      //     });
      //     return flag;
      //   },
      // });

    },

    async handleDownload(index, row) {
      try {
        const res = await getBaseTemplate();
        exportExcel(res, "用户导入模板.xlsx");
      } catch (error) {
        console.log(error);
        this.$message.error("下载失败");
      }
    },

    exportfile(res, name) {
      console.log(res, "导出文件");
      const blob = new Blob([res]);
      const link = document.createElement("a");
      // 非ie
      if ("download" in link) {
        link.download = name;
        link.style.display = "none";
        link.href = URL.createObjectURL(blob);
        document.body.appendChild(link);
        link.click();
        URL.revokeObjectURL(link.href);
        document.body.removeChild(link);
      } else {
        navigator.msSaveBlob(blob, name);
      }
    },

    downloadhandler(res,fileName) {
      const blob = new Blob([res], {type: 'application/json'})
      // 通过fileReader读取这个blob
      const reader = new FileReader()
      // 将blob对象以文本的方式读出，读出完成后将会执行onload方法
      reader.readAsText(blob)
      reader.onload = e => {
        const textRes = e.target.result
        // 此处对fileReader读出的结果进行JSON解析
        // 可能会出现错误，需要进行捕获
        try {
          const json = JSON.parse(textRes)
          if (json) {
            // 解析成功说明后端导出出错，进行导出失败的操作 例如展示后端返回的失败提示
            alert(json.errMsg)
            return
          }
        } catch (err) {
          // 该异常无法将字符串转为json
          // 说明返回数据是一个流文件
          // 不需要处理该异常
        }
        // 如果代码能够执行到这里，说明后端给的是一个流文件
        // 这里可以获取header中的cotent-type设置对应的type即可下载不同类型的文件
        let blob = new Blob([res], {type: 'application/ms-excel'}) // excel
        if (window.navigator.msSaveOrOpenBlob) {
          // 兼容IE & Edge
          navigator.msSaveOrOpenBlob(blob,fileName)
        } else {
          // 兼容不同浏览器的URL对象
          const url = window.URL || window.webkitURL || window.moxURL
          // 创建下载链接
          const downloadHref = url.createObjectURL(blob)
          // 创建a标签并为其添加属性
          let donwnloadLink = document.createElement('a')
          donwnloadLink.style.display = 'none'
          donwnloadLink.href = downloadHref
          donwnloadLink.download = fileName
          // 触发点击事件执行下载
          // 火狐浏览器上a标签点击导出无效。解决办法：需要先将a标签添加到当前页面上，在执行click，之后再移除该节点，而不能直接执行click
          document.body.appendChild(donwnloadLink)
          donwnloadLink.click()
          // 下载完成进行释放
          url.revokeObjectURL(donwnloadLink.href)
          document.body.removeChild(donwnloadLink)
        }
      }
    }

  }
};
</script>
  
<style lang="scss" scoped>
// .el-icon-my-delete {
//   background: url('@/icons/svg/delete.svg');
// }
.el-input-group {
  width: 40%;
}

.header-menu {
  margin-bottom: 5px;
  padding: 5px;
  text-align: left;
}

.footer-button {
  margin-top: 10px;
}

.addbtn {
  margin-left: 5px;
}

.el-table .warning-row {
  background: oldlace;
}

.el-table .success-row {
  background: #f4f8f2;
}

[v-cloak] {
  display: none;
}

.el-scrollbar__wrap {
  margin-right: 40px !important;
  margin-left: 20px;
}

.inline-block {
  display: inline-block;
}

.demo-form-inline {
  height: 40px;
}

.form-x {
  & .el-form-item__label {
    float: none; // 取消浮动
    word-break: break-word; // 支持单词截断换行
  }
}

.applyDialog {
  & .el-form-item__label {
    text-align: left;
    //加下面两条就行
    float: none; // 取消浮动
    word-break: break-word; // 支持单词截断换行
  }
}

// .el-table__header-wrapper  .el-checkbox{
//     //找到表头那一行，然后把里面的复选框隐藏掉
// 	display:none
// }

.el-table .disabledSelection .cell .el-checkbox__inner {
  display: none;
  position: relative;
}
</style>