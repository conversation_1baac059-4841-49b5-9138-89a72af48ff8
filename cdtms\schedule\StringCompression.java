package net.bioknow.cdtms.schedule;

import org.apache.commons.codec.binary.Base64;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

public class StringCompression {
    public static byte[] compress(String str) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        GZIPOutputStream gzipOut = new GZIPOutputStream(baos);
        gzipOut.write(str.getBytes());
        gzipOut.finish();
        return baos.toByteArray();
    }

    public static String decompress(byte[] compressed) throws IOException {
        ByteArrayInputStream bais = new ByteArrayInputStream(compressed);
        GZIPInputStream gzipIn = new GZIPInputStream(bais);
        byte[] buffer = new byte[1024];
        StringBuilder sb = new StringBuilder();
        int len;
        while ((len = gzipIn.read(buffer)) > 0) {
            sb.append(new String(buffer, 0, len));
        }
        return sb.toString();
    }




    public static void main(String[] args) throws IOException {
        String longStr = "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce dignissim elit a elit dapibus dignissim. Nam in sapien hendrerit, bibendum felis non, eleifend enim. Vivamus consectetur, dolor vitae bibendum scelerisque, sapien sapien aliquet nibh, vitae semper justo erat id ante. Donec tristique sagittis erat, vel commodo lacus bibendum eget. Praesent vel blandit ipsum. In hac habitasse platea dictumst. Nullam bibendum ipsum auctor augue ornare, id aliquet nisl fringilla. Donec fermentum, leo vel facilisis bibendum, velit nisl finibus nibh, vitae vehicula metus arcu vel ante. Quisque a mi nec lectus euismod consequat. Nulla rutrum justo nunc, sit amet efficitur est suscipit ac. Aliquam eget risus mauris. Donec quis lectus tellus. Duis lacinia vestibulum nibh, in volutpat est pretium nec.";
        byte[] compressed = compress(longStr);
        String decompressed = decompress(compressed);
        System.out.println("Original string: " + longStr);
        System.out.println("Compressed string: " + new String(compressed));
        System.out.println("Decompressed string: " + decompressed);
    }
}
