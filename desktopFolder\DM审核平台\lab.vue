<template>
  <div class="app-container" :style="{color : '#606266'}">
    <el-row>
      <el-col :span="16" style="width: 70%;">
        <div v-show="showTable" id="main" style="width: 100%;height: 150px" />
      </el-col>
    </el-row>
    <el-row>
      <el-dialog title="审核记录" :visible.sync="viewConfirmDataForm" width="60%">
        <el-button
          icon="el-icon-document-copy"
          plain
          size="mini"
          type="primary"
          @click="exportCommentCSV('审核')"
        >导出记录</el-button>
        <el-table :data="viewConfirmData" size="mini" :stripe="true" :highlight-current-row="true">
          <el-table-column prop="operate_user" label="操作人" />
          <el-table-column prop="operateType" label="操作" />
          <el-table-column prop="create_time" label="操作时间" />

          <el-table-column label="链接" align="center">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="info"
                icon="el-icon-link"
                plain
                circle
                @click="ClickTableLocation(scope.row.lab_data_id)"
              />
            </template>
          </el-table-column>
          <div slot="footer" class="dialog-footer">
            <el-button @click="viewConfirmDataForm = false">关闭页面</el-button>
          </div>
        </el-table>
        <div class="footer-button">
          <el-pagination
            :current-page="currentPage2"
            :page-size="pageSize2"
            :page-sizes="pageSizes2"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount2"
            align="center"
            style="margin-top: 5px; margin-bottom: 5px;"
            :pager-count="7"
            @size-change="handleSizeChange2"
            @current-change="handleCurrentChange2"
          />
        </div>
      </el-dialog>
    </el-row>
    <el-row>
      <el-dialog title="批注记录" :visible.sync="viewCommmentDataForm" width="70%">
        <el-button
          v-if="onlyread"
          icon="el-icon-circle-plus-outline"
          plain
          size="mini"
          type="primary"
          @click="showInnerAddcommit()"
        >添加批注</el-button>
        <el-table :data="viewCommmentData2" size="mini" :stripe="true" :highlight-current-row="true">
          <el-table-column prop="center_code" label="中心编号" width="auto" />
          <el-table-column prop="tester_code" label="受试者代码" width="auto" />
          <el-table-column prop="lab_comment" label="批注内容" width="auto" />
          <el-table-column prop="operate_user" label="操作者" width="auto" />
          <el-table-column prop="create_time" label="操作时间" width="auto" />
          <el-table-column label="" width="60">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="info"
                icon="el-icon-link"
                plain
                circle
                @click="ClickTableLocation(scope.row.lab_data_id)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="auto">
            <template slot-scope="scope">
              <el-button
                v-if="isOwner(scope.row.operate_user)"
                circle
                icon="el-icon-circle-close"
                plain
                size="mini"
                type="text"
                @click="deleteComment(scope.row.id)"
              />
            </template>
          </el-table-column>
        </el-table>

        <div class="footer-button">
          <el-pagination
            :current-page="currentPage2"
            :page-size="pageSize2"
            :page-sizes="pageSizes2"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount2"
            align="center"
            style="margin-top: 5px; margin-bottom: 5px;"
            :pager-count="7"
            @size-change="handleSizeChange2"
            @current-change="handleCurrentChange2"
          />
        </div>

        <el-form
          v-if="isshowaddcommit"
          ref="commentdata"
          :model="commentdata"
          size="mini"
          :stripe="true"
          :highlight-current-row="true"
          style="padding-top: 20px; padding-bottom: 10px;"
        >
          <el-form-item label="请选择批注的级别" prop="levelId">
            <el-select
              v-model="commentdata.levelId"
              placeholder="请选择批注的级别"
              clearable
              @change="levelIdChange(commentdata.levelId)"
            >
              <el-option label="中心层面" value="1" />
              <el-option label="受试者层面" value="2" />
              <el-option label="记录层面" value="3" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="isChose1" label="请选择中心代码" prop="levelvalue1">
            <el-select v-model="commentdata.levelvalue1" placeholder="请选择对应代码" clearable>
              <el-option v-for="item in valuelist1" :key="item" :value="item" :label="item" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="isChose2" label="请选择受试者代码" prop="levelvalue2">
            <el-select v-model="commentdata.levelvalue2" placeholder="请选择对应代码" clearable>
              <el-option v-for="item in valuelist2" :key="item" :value="item" :label="item" />
            </el-select>
          </el-form-item>
          <el-form-item label="请填写批注：" prop="comments">
            <el-input v-model="commentdata.comments" placeholder="请填写批注：" clearable type="textarea" :rows="5" />
          </el-form-item>
        </el-form>
        <div v-if="isshowaddcommit" slot="footer">
          <el-button type="info" size="mini" @click="cancelInnerAddcommit()">取消添加</el-button>
          <el-button type="primary" size="mini" @click="addcommit()">添加批注</el-button>
        </div>

      </el-dialog>
    </el-row>
    <el-row>
      <el-dialog title="批注记录" :visible.sync="viewCommmentDataForm2" width="70%">
        <el-button
          icon="el-icon-document-copy"
          plain
          size="mini"
          type="primary"
          @click="exportCommentCSV('批注')"
        >导出记录</el-button>
        <el-table
          :data="viewCommmentData2"
          size="mini"
          :stripe="true"
          :highlight-current-row="true"
          @filter-change="filterChange2"
        >

          <el-table-column
            prop="comment_level"
            label="层级"
            width="80"
            :filters="[{ text: '中心层面', value: '1' }, { text: '受试者层面', value: '2' }, { text: '记录层面', value: '3' }]"
            column-key="filterlevelId"
            :filter-multiple="false"
          />
          <el-table-column prop="center_code" label="中心编号" width="auto" />
          <el-table-column prop="tester_code" label="受试者代码" width="auto" />
          <el-table-column prop="lab_comment" label="批注内容" width="auto" />
          <el-table-column prop="operate_user" label="操作者" width="auto" />
          <el-table-column prop="create_time" label="操作时间" width="auto" />
          <el-table-column label="" width="60">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="info"
                icon="el-icon-link"
                plain
                circle
                @click="ClickTableLocation(scope.row.lab_data_id)"
              />
            </template>
          </el-table-column>
          <div slot="footer" class="dialog-footer">
            <el-button @click="viewCommmentDataForm2 = false">关闭页面</el-button>
          </div>
        </el-table>
        <div class="footer-button">
          <el-pagination
            :current-page="currentPage2"
            :page-size="pageSize2"
            :page-sizes="pageSizes2"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount2"
            align="center"
            style="margin-top: 5px; margin-bottom: 5px;"
            :pager-count="7"
            @size-change="handleSizeChange2"
            @current-change="handleCurrentChange2"
          />
        </div>
      </el-dialog>
    </el-row>
    <el-row>

      <el-dialog title="添加批注" :visible.sync="visibleForm" :before-close="cancelhandler">
        <el-form ref="commentdata" :model="commentdata" size="mini" :stripe="true" :highlight-current-row="true">
          <el-form-item label="请选择批注的级别" prop="levelId">
            <el-select
              v-model="commentdata.levelId"
              placeholder="请选择批注的级别"
              clearable
              @change="levelIdChange(commentdata.levelId)"
            >
              <el-option label="中心层面" value="1" />
              <el-option label="受试者层面" value="2" />
              <el-option label="记录层面" value="3" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="isChose1" label="请选择中心代码" prop="levelvalue1">
            <el-select v-model="commentdata.levelvalue1" placeholder="请选择对应代码" clearable>
              <el-option v-for="item in valuelist1" :key="item" :value="item" :label="item" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="isChose2" label="请选择受试者代码" prop="levelvalue2">
            <el-select v-model="commentdata.levelvalue2" placeholder="请选择对应代码" clearable>
              <el-option v-for="item in valuelist2" :key="item" :value="item" :label="item" />
            </el-select>
          </el-form-item>
          <el-form-item label="请填写批注：" prop="comments">
            <el-input v-model="commentdata.comments" placeholder="请填写批注：" clearable type="textarea" :rows="5" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelhandler()">关闭页面</el-button>
          <el-button type="primary" @click="addcommit()">提 交</el-button>
        </div>
      </el-dialog>
    </el-row>
    <el-row>
      <el-row style="margin-bottom: 1vh;">
        <el-button style="margin-left: 1vw;" size="mini" type="primary" @click="reviewClick('batch')">审核</el-button>
        <el-button style="margin-left: 1vw;" size="mini" type="primary" @click="unreviewClick('batch')">取消审核</el-button>
        <el-button style="margin-left: 1vw;" size="mini" type="primary" @click="commentsClick('batch')">批注</el-button>

        <el-button
          style="margin-left: 1vw;"
          size="mini"
          plain
          type="danger"
          icon="el-icon-search"
          @click="getReviewInfoClick('0')"
        >未审核：{{ NumUnconfirmed }}</el-button>
        <el-button style="margin-left: 1vw;" size="mini" plain type="success" icon="el-icon-finished" @click="getReviewInfoClick('1')">已审核：{{ NumConfirmed }}</el-button>
        <el-button style="margin-left: 1vw;" size="mini" plain type="warning" icon="el-icon-edit" @click="getReviewInfoClick('2')">已批注：{{ NumMarked }}</el-button>
        <el-button style="margin-left: 1vw;" size="mini" plain type="primary" icon="el-icon-magic-stick" @click="oneClickReview()">一键审核</el-button>
        <!-- 弹出框 -->
        <el-popover placement="bottom" trigger="click" style="margin-top: 5px; margin-left: 10px; margin-bottom: 5px; ">
          <div>
            <el-popover placement="left-end" trigger="click" width="500" style="padding: 0;">
              <el-select v-model="hideColumns" multiple>
                <el-option
                  v-for="item in tableColumns"
                  :key="item.prop"
                  :label="item.label"
                  :value="item.prop"
                /></el-select>
              <el-button slot="reference" size="mini" style="margin-top: 5px;">隐藏列设置</el-button>
            </el-popover>
            <br>
            <el-button size="mini" style="margin-top: 5px;" @click="viewcomment('')">批注历史</el-button>
            <br>
            <el-button size="mini" style="margin-top: 5px;" @click="viewConfirms()">审核历史</el-button>
          </div>
          <el-button slot="reference" circle icon="el-icon-more" size="mini" />
        </el-popover>
        <el-button
          circle
          icon="el-icon-caret-top"
          size="mini"
          style="float: right; margin-left: 10px;margin-right: 1.5vw;"
          @click="invisiableTable()"
        />
        <el-button
          type="info"
          plain
          icon="el-icon-refresh-left"
          circle
          size="mini"
          style="float: right; margin-right: 10px;margin-left: 5px;"
          @click="refreshTable()"
        />

      </el-row>

      <el-table
        v-show="tableShow"
        ref="multipleTable"
        :height="tableHight"
        :row-class-name="tableRowClassName"
        :data="tableData"
        :border="true"
        style="width: 95vw;text-align: center;
        margin-left: 1vw;"
        table-layout="auto"
        @header-click="headerClick"
        @selection-change="handleSelectionChange"
        @filter-change="filterChange"
        @sort-change="changeTableSort"
      >
        <template slot="empty">
          <el-empty :image-size="100" description="暂无数据" />
        </template>
        <el-table-column
          type="selection"
          width="55"
        />
        <el-table-column fixed label="批注">
          <template slot-scope="scope">
            <el-button
              v-show="scope.row.lab_comment?true:false"
              circle
              icon="el-icon-s-comment"
              plain
              size="mini"
              type="text"
              width=""
              @click="viewcomment(scope.row)"
            />

            <el-tooltip placement="top" effect="light" enterable:true>
              <div
                v-if="!scope.row.level_content"
                slot="content"
                v-html="toBreak(scope.row.lab_comment)"
              />
              <div
                v-if="scope.row.level_content"
                slot="content"
                v-html="toBreak(scope.row.level_content+','+scope.row.lab_comment)"
              />
              <span v-if="scope.row.level_content" class="overSP">
                {{ scope.row.level_content+' '+scope.row.lab_comment }}
              </span>
              <span v-if="!scope.row.level_content" class="overSP">
                {{ scope.row.lab_comment }}
              </span>

            </el-tooltip>

          </template>
        </el-table-column>

        <el-table-column
          v-for="(column, index) in tableColumns"
          v-show="!hideColumns.includes(column.prop)"
          :key="index"
          width="max-content"
          :column-key="column.label"
          :prop="column.prop"
          :sortable="column.label === '检查结果' ? 'custom' : false"
          :render-header="labelHead"
          :label="column.label"
          :filters="columnFilterItems"
          :filter-method="filterHandler"
          :fixed="[0, 1, 2, tableColumns.length - 2].includes(index)"
        />

        <el-table-column
          fixed="right"
          label="操作"
          width="100"
        >
          <template slot-scope="scope">
            <el-button v-show="scope.row.is_viewed==='0'||scope.row.is_viewed===''" type="text" size="small" @click="reviewClick(scope.row)">审核</el-button>
            <el-button v-show="scope.row.is_viewed==='1'" type="text" size="small" @click="unreviewClick(scope.row)">取消审核</el-button>
            <el-button type="text" size="small" @click="commentsClick(scope.row)">批注</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center;margin-top: 30px; margin-right: 13.5%;" class="block">
        <el-pagination
          background
          layout="total, sizes,prev, pager, next,jumper"
          :total="total"
          :current-page.sync="currentPage"
          :page-sizes="[50, 100, 150, 200]"
          :page-size="pageSize"
          @size-change="handleSizeChange"
          @current-change="current_change"
        />
      </div></el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons')
import { getReviewInfo, getReviewHistory, getColumnValue, exportOperateCSV, getCommentHistory, deleteCommentById, getStaticCount, getLabTableColumns, getQuestionCount, setReview, updateReview, setComment, getReviewInfoID } from '@/api/dmplatform'
import resize from '@/components/mixins/resize'
export default {
  mixins: [resize],
  data() {
    return {
      // 下拉筛选的列
      filterColumn: '',
      // 一键审核的labId
      labDataIds: [],
      filterColumnName: [],
      // 下拉表头筛选项
      columnFilterItems: [],
      // 选中的筛选项
      selectedColumnFilterItems: [],
      tableHight: 0,
      // 2. 声明表格高度的变量
      historyFlag: 0,
      isshowaddcommit: false,
      filterLevel: '',
      currentPage2: 1, // 默认显示第几页
      totalCount2: 1, // 总条数
      pageSize2: 5, // 每页数据的数量
      pageSizes2: [5, 10, 15], // 选择每页的数据量
      commentScope: '0',
      userName: '',
      tableShow: false,
      queryDataType: '0',
      isChose1: false,
      isChose2: false,
      onlyread: true,
      hideColumns: [],
      NumUnconfirmed: 15,
      NumConfirmed: 20,
      NumMarked: 25,
      visibleForm: false,
      currentPage: 1,
      pageSize: 50,
      total: 50,
      tableData: [],
      viewConfirmData: [],
      viewCommmentData2: [],
      viewConfirmDataForm: false,
      viewCommmentDataForm: false,
      viewCommmentDataForm2: false,
      sortColumn: '检查结果',
      sortOrder: 'asc',
      rowLabDataID: '',
      valuelist1: [],
      valuelist2: [],
      flag: '',
      multipleSelection: [],
      centerCode: '',
      showTable: true,
      tableColumns: [],
      xData: [],
      yData: [],
      commentdata: {
        levelId: '3',
        levelvalue1: '',
        levelvalue2: '',
        comments: ''
      }

    }
  },
  async created() {
    const height = await this.hightHead()
    this.tableHight = height

    // await this.labelHead()
  },

  mounted() {
    // 获取路由中的项目名和文件名
    var projectName = ''
    var fileName = ''
    // this.userName = localStorage.getItem('user')
    this.userName = '周辉'
    projectName = 'HR070803-301'
    fileName = 'HR070803-301_LAB_AE.csv'
    // projectName = this.$route.params.projectName
    // fileName = this.$route.params.fileName
    if (projectName && fileName) {
      sessionStorage.setItem('projectName', projectName)
      sessionStorage.setItem('fileName', fileName)
    }
    this.processColumns()
    this.centerCode = ''
    this.initPage()
    getQuestionCount(sessionStorage.getItem('fileName')).then(res => {
      if (res.code === 200) {
        this.xData = res.data.map(item => item.name)
        console.log(this.xData)
        this.yData = res.data.map(item => item.count)
        console.log(this.yData)
        this.initChart()
      }
    })
  },
  methods: {
    showInnerAddcommit() {
      this.isshowaddcommit = true
    },
    // 链接跳转
    ClickTableLocation(pid) {
      this.currentPage = 1
      this.labDataId = pid
      this.getReviewInfoClick('3')
      this.viewConfirmDataForm = false
      this.viewCommmentDataForm = false
      this.viewCommmentDataForm2 = false
    },
    // 判断批注删除权限
    isOwner(creater) {
      if (creater === this.userName) {
        return true
      } else {
        return false
      }
    },
    // 导出批注记录
    exportCommentCSV(val) {
      var param = {}
      param.filterColumn = val
      param.fileName = sessionStorage.getItem('fileName')
      param.pageNum = 1
      param.type = this.filterLevel
      param.pageSize = this.pageSize2
      param.centerCode = ''
      param.sortOrder = ''
      param.sortColumn = ''
      const loading = this.$loading({
        lock: true,
        text: '记录导出中，请稍等',
        background: 'rgba(0,0,0,0.5)'
      })
      exportOperateCSV(param).then(res => {
        this.downloadFile(res, param.fileName, val)
        loading.close()
      }).catch((e) => {
        this.$message.error('记录导出失败！')
        loading.close()
      })
      console.log(val)
    },
    // 一键审核
    oneClickReview() {
      this.$confirm('确认一键审核所有数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = []
        this.labDataIds.forEach(element => {
          var param = {}
          param.labDataId = element
          param.fileName = sessionStorage.getItem('fileName')
          param.operateUser = this.userName
          param.status = '1'
          params.push(param)
        })

        setReview(params).then(res => {
          if (res.data === 'success') {
            this.$message.success('审核成功')
            this.queryDataType = '0'
            this.initPage()
          } else if (res.data === 'warning') {
            this.$message.warning('请先取消审核已选择的数据，再审核！')
          } else {
            this.$message.error(res.data)
          }
        })
      })
    },
    // 表头排序
    changeTableSort(column) {
      console.log('排序的表头', column)
      if (column.order === 'descending') {
        this.sortOrder = 'desc'
      } else if (column.order === 'ascending') {
        this.sortOrder = 'asc'
      }
      if (column.prop) {
        if (column.prop === '检查结果') {
          this.sortColumn = '检查结果-S'
        } else {
          this.sortColumn = column.prop
        }
      }
      this.initPage()
    },
    filterHandler(value, row, column) { // 该方法会对每个row执行
      const property = column['property']
      console.log(property)

      return row // 返回所有数据  注销前端筛选
    },
    // 表头点击
    headerClick(column, event) {
      if (column.property !== '批注' && column.property !== '操作') {
        var param = {}
        param.fileName = sessionStorage.getItem('fileName')
        param.centerCode = this.centerCode
        param.pageNum = this.currentPage
        param.pageSize = this.pageSize
        param.sortOrder = this.sortOrder
        param.sortColumn = this.sortColumn
        param.type = this.queryDataType
        this.filterColumnName.push(column.property)
        this.filterColumnName = Array.from(new Set(this.filterColumnName))
        // 获取对应列的筛选项目
        param.columnName = this.filterColumnName
        param.filterItem = this.selectedColumnFilterItems
        param.filterColumn = column.property
        getColumnValue(param).then(res => {
          if (res.code === 200) {
            this.columnFilterItems = res.data
            this.columnFilterItems.forEach(obj => {
              Object.entries(obj).forEach(([key, value]) => {
                if (value === '\u001D') {
                  obj[key] = null
                }
              })
            })
          }
        })
      }
    },
    // 表头下拉筛选
    filterChange(filters) {
      debugger
      Object.entries(filters).forEach(([key, value]) => {
        // key是属性名，value是对应的值（数组）
        if (value.length > 0) {
          console.log(value)
          // 如果数组非空，处理筛选项
          this.selectedColumnFilterItems.push(...value) // 使用扩展运算符将所有值添加到selectedColumnFilterItems
        } else if (value.length === 0) {
          this.filterColumnName = []
          this.columnFilterItems = []
          this.selectedColumnFilterItems = []
          this.initPage()
        }
      })
      this.selectedColumnFilterItems = Array.from(new Set(this.selectedColumnFilterItems))

      this.initPage()

      // console.log('筛选项', filters)
      // if (filters.prop.length === 0) {
      //   this.filterColumnName = []
      //   this.columnFilterItems = []
      //   this.selectedColumnFilterItems = []
      //   this.initPage()
      // } else {
      //   // 将keyWord作为参数，column作为筛选项传递会后端，支持筛选
      //   filters.prop.forEach(element => {
      //     this.selectedColumnFilterItems.push(element)
      //   })
      // 去重
      // this.selectedColumnFilterItems = Array.from(new Set(this.selectedColumnFilterItems))

      // this.initPage()
      // }
    },

    // 批注层级筛选
    filterChange2(filterObj) {
      console.log('输出的筛选项:', filterObj)
      this.filterFlag3 = 1
      this.filterLevel = filterObj.filterlevelId[0]
      this.viewCommmentDataForm2 = true
      var param = {}
      param.fileName = sessionStorage.getItem('fileName')
      param.pageNum = 1
      param.pageSize = this.pageSize2
      param.centerCode = ''
      param.sortOrder = ''
      param.sortColumn = ''
      param.type = this.filterLevel
      getCommentHistory(param).then(res => {
        this.viewCommmentData2 = res.data.records
        this.totalCount2 = res.data.total
      })
    },
    // 表头自适应高度
    async hightHead() {
    // 计算表格高度
    // 3. 动态计算表格高度
      const windowHeight = document.documentElement.clientHeight || document.body.clientHeight
      // 此处减去100即为当前屏幕内除了表格高度以外其它内容的总高度，

      return windowHeight - 80
    },
    // 表头宽度自适应
    labelHead: function(h, { column }) {
      let l = column.label.length // 表头label长度
      if (column.label === '质疑') {
        l = 10
      } else if (column.label.search('日期') !== -1) {
        l = 3.5
      }
      const f = 18 // 根据需要定义标尺，直接使用字体大小确定就行，也可以根据需要定义
      column.minWidth = f * (l + 1.5) // 加上一个文字长度
      this.tableShow = true
      return h('div', { class: 'table-head', style: { width: '100%' }}, [column.label])
    },

    // 处理表头
    processColumns() {
      getLabTableColumns(sessionStorage.getItem('fileName')).then(res => {
        if (res.code === 200) {
          var tempArray = [{}]
          tempArray = this.dynamicOrder(res.data)
          this.tableColumns = tempArray.flatMap(obj => Object.values(obj).filter(value => value !== '检查结果-S').map(value => {
            const labelValue = value.includes('-D') ? value.split('-D')[0] : value
            return {
              prop: value === '﻿\"质疑\"' ? '质疑' : value,
              label: labelValue === '﻿\"质疑\"' ? '质疑' : labelValue
            }
          }))
          console.log('这是表头处理后的数据：', this.tableColumns)
        }
      })
    },

    toBreak(val) {
      if (val) {
        return val.split(',').join('<br/>')
      }
    },
    // 隐藏chart
    invisiableTable() {
      if (this.showTable === true) {
        this.showTable = false
      } else if (this.showTable === false) {
        this.showTable = true
      }
    },
    cancelhandler() {
      this.$refs['commentdata'].resetFields()
      this.commentdata.levelvalue1 = ''
      this.commentdata.levelvalue2 = ''
      this.visibleForm = false
      this.isChose1 = false
      this.isChose2 = false
      this.viewCommmentDataForm = false
    },
    // 调整页码
    current_change(currentPage) {
      this.currentPage = currentPage
      this.initPage()
    },
    handleCurrentChange2(currentPage) {
      this.currentPage2 = currentPage
      if (this.historyFlag === 0) {
        this.getConfirmData()
      } else if (this.historyFlag === 1) {
        this.getCommentData('')
      } else if (this.historyFlag === 2) {
        // 查询
        this.getCommentData(this.labDataId)
      }
    },
    // 调整大小
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      // 改变每页显示的条数
      this.pageSize = val
      // 注意：在改变每页显示的条数时，要将页码显示到第一页
      this.initPage() // pageSize改变是重新请求数据
    },
    handleSizeChange2(val) {
      this.pageSize2 = val
      if (this.historyFlag === 0) {
        this.getConfirmData()
      } else if (this.historyFlag === 1) {
        this.getCommentData('')
      } else if (this.historyFlag === 2) {
        // 查询
        this.getCommentData(this.labDataId)
      }
    },
    // 获取未审核数据
    getReviewInfoClick(type) {
      this.currentPage = 1
      this.queryDataType = type
      var param = {}
      // 获取对应列的筛选项目
      param.filterItem = this.selectedColumnFilterItems
      param.columnName = this.filterColumnName
      param.fileName = sessionStorage.getItem('fileName')
      param.centerCode = this.centerCode
      param.pageNum = this.currentPage
      param.pageSize = this.pageSize
      param.sortOrder = this.sortOrder
      param.sortColumn = this.sortColumn
      param.type = this.queryDataType
      if (type === '2') {
        param.labAeId = ''
      } else {
        param.labAeId = this.labDataId
      }

      this.listLoading = false
      // const projectName = sessionStorage.getItem('projectName')
      getReviewInfo(param).then(res => {
        if (res.code === 200) {
          this.tableData = res.data.records
          this.tableData.forEach(obj => {
            Object.entries(obj).forEach(([key, value]) => {
              if (value === '\u001D') {
                obj[key] = ''
              }
            })
          })
          this.total = res.data.total
        }
      })
      getReviewInfoID(param).then(res => {
        if (res.code === 200) {
          this.labDataIds = res.data
        }
      })
    },

    initPage() {
      var param = {}
      param.fileName = sessionStorage.getItem('fileName')
      param.centerCode = this.centerCode
      param.pageNum = this.currentPage
      param.pageSize = this.pageSize
      param.sortOrder = this.sortOrder
      param.sortColumn = this.sortColumn
      param.type = this.queryDataType
      param.filterItem = this.selectedColumnFilterItems
      param.columnName = this.filterColumnName
      this.listLoading = false
      // const projectName = sessionStorage.getItem('projectName')
      getReviewInfo(param).then(res => {
        if (res.code === 200) {
          this.tableData = res.data.records
          this.tableData.forEach(obj => {
            Object.entries(obj).forEach(([key, value]) => {
              if (value === '\u001D') {
                obj[key] = ''
              }
            })
          })
          this.total = res.data.total
        }
      })
      getReviewInfoID(param).then(res => {
        if (res.code === 200) {
          this.labDataIds = res.data
        }
      })
      getStaticCount(param).then(res => {
        if (res.code === 200) {
          this.NumConfirmed = res.data.reviewedCount
          this.NumUnconfirmed = res.data.unreviewCount
          this.NumMarked = res.data.commentCount
        }
      })
    },
    // 批注级别变更
    levelIdChange(levelId) {
      if (levelId === '1') {
        this.isChose1 = true
        this.isChose2 = false
      } else if (levelId === '2') {
        this.isChose1 = false
        this.isChose2 = true
      } else if (levelId === '3') {
        this.isChose1 = false
        this.isChose2 = false
      }
    },
    // 数据着色
    tableRowClassName({ row, rowIndex }) {
      if (row.is_viewed === '1') {
        return 'success-row'
      }
      if (row.comment_level === 2) {
        return 'comment-center-row'
      } else if (row.comment_level === 1) {
        return 'comment-tester-row'
      }
      return ''
    },
    // 刷新表格数据
    refreshTable() {
      this.fileName = sessionStorage.getItem('fileName')
      this.centerCode = ''
      this.pageNum = 1
      this.pageSize = 50
      this.sortOrder = 'asc'
      this.sortColumn = '检查结果'
      this.listLoading = false
      this.filterColumnName = []
      this.selectedColumnFilterItems = []
      this.initPage()
    },
    // 审核历史数据
    getConfirmData() {
      var param = {}
      param.fileName = sessionStorage.getItem('fileName')
      param.pageNum = this.currentPage2
      param.pageSize = this.pageSize2
      param.centerCode = ''
      param.sortOrder = ''
      param.sortColumn = ''
      param.type = ''
      getReviewHistory(param).then(res => {
        this.viewConfirmData = res.data.records
        this.totalCount2 = res.data.total
      })
    },
    // 查看审核历史
    viewConfirms() {
      this.currentPage2 = 1
      this.pageSize2 = 10
      this.viewConfirmDataForm = true
      this.historyFlag = 0
      this.getConfirmData()
    },
    // 获取批注历史
    getCommentData(labDataId) {
      debugger
      var param = {}
      param.fileName = sessionStorage.getItem('fileName')
      param.pageNum = this.currentPage2
      param.pageSize = this.pageSize2
      param.centerCode = ''
      param.sortOrder = ''
      param.sortColumn = ''
      param.type = ''
      param.labAeId = labDataId
      debugger
      getCommentHistory(param).then(res => {
        debugger
        this.viewCommmentData2 = res.data.records
        this.totalCount2 = res.data.total
      })
    },
    // 查看批注历史
    viewcomment(row) {
      debugger
      this.currentPage2 = 1
      this.pageSize2 = 10
      if (row) {
        this.viewCommmentDataForm = true
        this.viewCommmentDataForm2 = false
        var scope = {}
        scope.lab_data_id = row.lab_data_id
        this.rowLabDataID = row.lab_data_id
        this.commentsClick(scope)
        this.visibleForm = false
        var tempList1 = []
        var tempList2 = []
        tempList1.push(row.中心)
        tempList2.push(row.受试者代码)
        this.valuelist1 = tempList1
        this.valuelist2 = tempList2
      } else {
        this.filterLevel = null
        this.viewCommmentDataForm = false
        this.viewCommmentDataForm2 = true
      }

      if (row) {
        this.historyFlag = 2
        this.getCommentData(row.lab_data_id)
        this.labDataId = row.lab_data_id
      } else {
        this.historyFlag = 1
        this.getCommentData('')
      }
    },

    // 审核多选
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row)
          console.log('这是点击审核之后的数据', row)
        })
      } else {
        this.$refs.multipleTable.clearSelection()
      }
    },
    // 审核
    reviewClick(scope) {
      this.$confirm('确认审核已选数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = []
        if (scope === 'batch') {
          var that = this
          if (that.multipleSelection.length > 0) {
            that.multipleSelection.forEach(element => {
              var param = {}

              param.labDataId = element.lab_data_id
              param.fileName = sessionStorage.getItem('fileName')
              param.operateUser = this.userName
              param.status = '1'
              params.push(param)
            })
          } else {
            return this.$message.warning('请选择要审核的数据')
          }
        } else {
          var param = {}
          param.labDataId = scope.lab_data_id
          param.fileName = sessionStorage.getItem('fileName')
          param.operateUser = this.userName
          param.status = '1'
          params.push(param)
        }

        setReview(params).then(res => {
          if (res.data === 'success') {
            this.$message.success('审核成功')
            this.queryDataType = '0'
            this.initPage()
          } else if (res.data === 'warning') {
            this.$message.warning('请先取消审核已选择的数据，再审核！')
          } else {
            this.$message.error(res.data)
          }
        })
      })
    },
    // 取消审核
    unreviewClick(scope) {
      this.$confirm('确认取消审核已选数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = []
        if (scope === 'batch') {
          var that = this
          if (that.multipleSelection.length > 0) {
            this.multipleSelection.forEach(element => {
              var param = {}
              param.labDataId = element.lab_data_id
              param.fileName = sessionStorage.getItem('fileName')
              param.operateUser = that.userName
              param.status = '0'
              params.push(param)
            })
          } else {
            return this.$message.warning('请选择要取消审核的数据')
          }
        } else {
          var param = {}
          param.labDataId = scope.lab_data_id
          param.fileName = sessionStorage.getItem('fileName')
          param.operateUser = this.userName
          param.status = '0'
          params.push(param)
        }

        updateReview(params).then(res => {
          if (res.data === 'success') {
            this.$message.success('取消审核成功')
            this.queryDataType = '1'
            this.initPage()
          } else if (res.data === 'warning') {
            this.$message.warning('请先审核已选择的数据，再取消！')
          } else {
            this.$message.error(res.data)
          }
        })
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log(val)
    },
    // 文件导出
    downloadFile(data, fileName, exportType) {
      var currentTime = this.getTimeStr()
      debugger
      const blob = new Blob([data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a') // 创建a标签
      link.href = url
      link.download = sessionStorage.getItem('projectName') + '-' + exportType + '-' + currentTime + '.csv'
      link.click()
      URL.revokeObjectURL(url) // 释放内存
    },
    addcommit() {
      var params = []

      if (this.commentScope === '0') {
        // 批量
        this.multipleSelection.forEach(element => {
          var param = {}
          param.labDataId = element.lab_data_id
          param.fileName = sessionStorage.getItem('fileName')
          param.operateUser = this.userName
          param.comment = this.commentdata.comments
          param.commentLevel = parseInt(this.commentdata.levelId)
          param.levelContent = this.commentdata.levelId === '1' ? this.commentdata.levelvalue1 : this.commentdata.levelvalue2
          params.push(param)
        })
      } else if (this.commentScope === '1') {
        // 单个
        var param = {}
        param.labDataId = this.rowLabDataID
        param.fileName = sessionStorage.getItem('fileName')
        param.operateUser = this.userName
        param.comment = this.commentdata.comments
        param.commentLevel = parseInt(this.commentdata.levelId)
        param.levelContent = this.commentdata.levelId === '1' ? this.commentdata.levelvalue1 : this.commentdata.levelvalue2

        params.push(param)
      }
      setComment(params).then(res => {
        if (res.data === 'success') {
          this.$message.success('批注成功')
          this.cancelhandler()
          this.queryDataType = '0'
          this.initPage()
        } else if (res.data === 'warning') {
          this.$message.warning('请先选择的数据，再批注！')
        } else {
          this.cancelhandler()
          this.$message.error(res.data)
        }
      })
    },
    // 批注
    commentsClick(scope) {
      var that = this
      if (scope === 'batch') {
        this.commentScope = '0'
        if (that.multipleSelection.length > 0) {
          that.visibleForm = true
          console.log('批注选择的数据为:', this.multipleSelection)
          // 定义两个数组，中心和受试者代码
          var tempSet1 = new Set()
          var tempSet2 = new Set()
          this.multipleSelection.forEach(element => {
            tempSet1.add(element.中心)
            tempSet2.add(element.受试者代码)
          })
          this.valuelist1 = Array.from(tempSet1)
          this.valuelist2 = Array.from(tempSet2)
        } else {
          return this.$message.warning('请选择要批注的数据')
        }
      } else {
        debugger
        this.commentScope = '1'
        this.rowLabDataID = scope.lab_data_id
        that.visibleForm = true
        that.commentdata.levelvalue1 = scope.中心
        that.commentdata.levelvalue2 = scope.受试者代码
      }
    },

    // 图表初始化
    initChart() {
      this.chart = echarts.init(document.getElementById('main'), 'macarons')
      this.chart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'line' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          top: 10,
          left: '2%',
          right: '2%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data: this.xData,

          axisTick: {
            alignWithLabel: true
          }
        }],
        yAxis: [{
          type: 'value',
          axisTick: {
            show: false
          }
        }],
        series: [{
          name: '有质疑',
          type: 'bar',
          stack: 'vistors',
          barWidth: '40%',
          data: this.yData,
          itemStyle: {
            normal: {
              color: '#3497FF'
            }
          }
          // animationDuration
        }]
      })
      var that = this
      this.chart.on('click', function(params) {
        that.centerCode = params.name
        that.queryDataType = '0'
        that.initPage()
      })
    },
    // 取消添加批注
    cancelInnerAddcommit() {
      this.isshowaddcommit = false
    },
    // 删除批注
    deleteComment(id) {
      debugger
      this.$confirm('确认删除已选中的批注', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteCommentById(id).then(res => {
          if (res.code === 200 && res.data === 'success') {
            this.$message.success('删除批注成功')
            this.queryDataType = '0'
            this.getCommentData(this.labDataId)
            this.cancelInnerAddcommit()
            this.initPage()
          } else {
            this.$message.error('删除批注失败')
          }
        })
      })
    },
    getTimeStr() {
      const now = new Date()
      const yy = now.getFullYear() // 年
      const mm = String(now.getMonth() + 1).padStart(2, '0') // 月
      const dd = String(now.getDate()).padStart(2, '0') // 日
      const hh = String(now.getHours()).padStart(2, '0') // 时
      const ii = String(now.getMinutes()).padStart(2, '0') // 分
      const ss = String(now.getSeconds()).padStart(2, '0') // 秒
      const time = `${yy}-${mm}-${dd} ${hh}/${ii}/${ss}`
      console.log(time) // 获取当前日期 2019-04-17 10:27:27

      return time
    },

    // 查看
    handleClick(row) {
      console.log(row)
    },
    // 处理表头列名顺序
    dynamicOrder(jsonData) {
      const orderedData = []

      Object.entries(jsonData[0])
        .sort((a, b) => {
          const keyA = parseInt(a[0].replace('c', ''))
          const keyB = parseInt(b[0].replace('c', ''))
          return keyA - keyB
        })
        .forEach(([key, value]) => {
          const obj = {}
          obj[key] = value
          orderedData.push(obj)
        })

      return orderedData
    }
  }
}
</script>

<style>
#main > div:nth-child(1) > canvas{
  width: 100% !important;
}
.el-table .success-row {
  background: #f0f9eb;
}
.el-table th.el-table__cell>.cell {
  display: flex;
}

.el-table .comment-center-row {
  background: #e0d26ad1;
}
.el-table .comment-tester-row {
  background: #99d9e6;
}

.el-table__header{
      table-layout: auto;
    }
    .el-table__body{
      table-layout: auto!important;
    }
    .el-table--border th, .el-table--border td{
      border-right: 1px solid #ebeef5;
    }
    .el-table th, .el-table td{
      padding: 0px;
      word-wrap: normal!important;
      width: auto!important;
      word-break: break-all!important;
    }
    .el-table .cell{
      padding-right: 0px;
    }

    .overSP {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1; /*可以显示的行数，超出部分用...表示 */
      -webkit-box-orient: vertical;
      max-width: 100%;
}
.el-table__fixed {
    height:auto !important;
    bottom:17px;
    position: absolute;top: 0;left: 0;
}
.el-table--scrollable-x .el-table__body-wrapper {
    z-index: 1;
  }

</style>
