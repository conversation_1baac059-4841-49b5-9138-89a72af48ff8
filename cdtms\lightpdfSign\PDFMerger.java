package net.bioknow.cdtms.lightpdfSign;


import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import net.bioknow.mvc.tools.WebPath;
import net.bioknow.webutil.fileup.DAOFileup;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;

import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;

public class PDFMerger {


    public static String PDFMerger(String sourcePdfUri, String insertPdf, String fileName) {
        try {
            PDFMergerUtility mergePdf = new PDFMergerUtility();
            String Savepath = WebPath.getRootPath() + DAOFileup.tempfolder + "/"+fileName;
//            String Savepath = "C:\\Users\\<USER>\\Desktop\\" +fileName;
            mergePdf.addSource(insertPdf);
            mergePdf.addSource(sourcePdfUri);
            mergePdf.setDestinationFileName(Savepath);
            mergePdf.mergeDocuments(MemoryUsageSetting.setupMainMemoryOnly());
            return Savepath;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static void main(String[] args) {
        PDFMerger("C:\\Users\\<USER>\\Desktop\\HR19006-301_医学编码计划_20231020_V3.0.pdf","C:\\Users\\<USER>\\Desktop\\1111111.pdf","222222.pdf");
    }

    public static String PDFMerger2(String sourcePdfUri, String insertPdf, String fileName) {
            try {
                // 打开源PDF文件
                PdfReader sourcePdfReader = new PdfReader(sourcePdfUri);
                int sourcePageCount = sourcePdfReader.getNumberOfPages();

                // 打开要插入的PDF文件
                PdfReader insertPdfReader = new PdfReader(insertPdf);
                int insertPageCount = insertPdfReader.getNumberOfPages();

                // 创建输出PDF文件
                Document document = new Document();
                String Savepath = WebPath.getRootPath() + DAOFileup.tempfolder + "/"+fileName;
                PdfCopy copy = new PdfCopy(document, new FileOutputStream(Savepath));
                document.open();

                // 将源PDF文件的第一页复制到输出PDF文件
                PdfImportedPage importedPage = copy.getImportedPage(sourcePdfReader, 1);
                copy.addPage(importedPage);

                for (int i = 1; i <= insertPageCount; i++) {
                    // 将要插入的PDF文件的第一页添加到输出PDF文件
                    importedPage = copy.getImportedPage(insertPdfReader, 1);
                    copy.addPage(importedPage);
                }
                // 将源PDF文件的剩余页面添加到输出PDF文件
                for (int i = 2; i <= sourcePageCount; i++) {
                    importedPage = copy.getImportedPage(sourcePdfReader, i);
                    copy.addPage(importedPage);
                }
                // 关闭资源
                document.close();
                sourcePdfReader.close();
                insertPdfReader.close();
                return Savepath;
            } catch (Exception e) {
                e.printStackTrace();
            }
            return sourcePdfUri;
        }






    }
