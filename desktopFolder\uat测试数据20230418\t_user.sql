/*
 Navicat MySQL Data Transfer

 Source Server         : 测试环境
 Source Server Type    : MySQL
 Source Server Version : 80028 (8.0.28)
 Source Host           : ***********:3306
 Source Schema         : susar_auto_mail

 Target Server Type    : MySQL
 Target Server Version : 80028 (8.0.28)
 File Encoding         : 65001

 Date: 18/04/2023 23:02:20
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_user
-- ----------------------------
DROP TABLE IF EXISTS `t_user`;
CREATE TABLE `t_user`  (
  `ID` bigint NULL DEFAULT NULL,
  `site_user_role` bigint NULL DEFAULT NULL,
  `site_user_email` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `site_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_user
-- ----------------------------
INSERT INTO `t_user` VALUES (368, 2, '<EMAIL>', 'SHR-6390-III-301CN001');
INSERT INTO `t_user` VALUES (369, 2, '<EMAIL>', 'SHR-6390-III-301CN001');
INSERT INTO `t_user` VALUES (370, 2, '<EMAIL>', 'SHR-6390-III-301CN001');
INSERT INTO `t_user` VALUES (371, 2, '<EMAIL>', 'SHR-6390-III-301CN001');
INSERT INTO `t_user` VALUES (372, 1, '<EMAIL>', 'SHR-6390-III-301CN001');
INSERT INTO `t_user` VALUES (373, 2, '<EMAIL>', 'SHR-6390-III-301CN001');
INSERT INTO `t_user` VALUES (374, 2, '<EMAIL>', 'SHR-6390-III-301CN001');
INSERT INTO `t_user` VALUES (375, 1, '<EMAIL>', 'SHR-6390-III-301CN001');
INSERT INTO `t_user` VALUES (376, 2, '<EMAIL>', 'SHR-6390-III-301CN007');
INSERT INTO `t_user` VALUES (378, 2, '<EMAIL>', 'SHR4640-301CN001');
INSERT INTO `t_user` VALUES (379, 1, '<EMAIL>', 'SHR4640-301CN001');
INSERT INTO `t_user` VALUES (380, 2, '<EMAIL>', 'SHR4640-301CN048');
INSERT INTO `t_user` VALUES (381, 2, '<EMAIL>', 'SHR4640-301CN048');
INSERT INTO `t_user` VALUES (390, 2, '<EMAIL>', 'HRS-1358-I-101CN001');
INSERT INTO `t_user` VALUES (391, 2, '<EMAIL>', 'HRS-1358-I-101CN001');
INSERT INTO `t_user` VALUES (392, 1, '<EMAIL>', 'HRS-1358-I-101CN002');
INSERT INTO `t_user` VALUES (393, 2, '<EMAIL>', 'HRS-1358-I-101CN002');
INSERT INTO `t_user` VALUES (394, 2, '<EMAIL>', 'HRS9531-102CN001');
INSERT INTO `t_user` VALUES (395, 1, '<EMAIL>', 'HRS9531-102CN001');
INSERT INTO `t_user` VALUES (396, 2, '<EMAIL>', 'HRS9531-102CN004');
INSERT INTO `t_user` VALUES (397, 1, '<EMAIL>', 'HRS9531-102CN004');
INSERT INTO `t_user` VALUES (398, 2, '<EMAIL>', 'INS068-301CN001');
INSERT INTO `t_user` VALUES (399, 1, '<EMAIL>', 'INS068-301CN001');
INSERT INTO `t_user` VALUES (400, 2, '<EMAIL>', 'INS068-301CN007');
INSERT INTO `t_user` VALUES (401, 1, '<EMAIL>', 'INS068-301CN007');
INSERT INTO `t_user` VALUES (402, 1, '<EMAIL>', 'SHR-A1811-II-203CN001');
INSERT INTO `t_user` VALUES (403, 2, '<EMAIL>', 'SHR-A1811-II-203CN001');
INSERT INTO `t_user` VALUES (404, 2, '<EMAIL>', 'SHR-A1811-II-203CN003');
INSERT INTO `t_user` VALUES (405, 2, '<EMAIL>', 'SHR-A1811-II-203CN003');
INSERT INTO `t_user` VALUES (406, 2, '<EMAIL>', 'SHR-1701-209CN001');
INSERT INTO `t_user` VALUES (407, 2, '<EMAIL>', 'SHR-1701-209CN001');
INSERT INTO `t_user` VALUES (408, 1, '<EMAIL>', 'SHR-1701-209CN002');
INSERT INTO `t_user` VALUES (409, 2, '<EMAIL>', 'SHR-1701-209CN002');
INSERT INTO `t_user` VALUES (410, 2, '<EMAIL>', 'SHR-1316-322CN001');
INSERT INTO `t_user` VALUES (411, 1, '<EMAIL>', 'SHR-1316-322CN001');

SET FOREIGN_KEY_CHECKS = 1;
