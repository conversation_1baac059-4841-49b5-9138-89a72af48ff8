package net.bioknow.cdtms.extdatabind;

import net.bioknow.services.uapplug.datasync.DaoDataSync;
import net.bioknow.uap.dbcore.schema.EditUrlParam;
import net.bioknow.uap.dbcore.schema.FaceInputAssist;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EHIASelectEDM implements FaceInputAssist {
    @Override
    public String getId() {
        return "EHIASelectEDM";
    }

    @Override
    public String getName() {
        return "EDM Select";
    }

    @Override
    public EditUrlParam getEditUrl(String tableId, String fieldId, Map<String, Object> data) {
        try {

            EditUrlParam editUrlParam = new EditUrlParam();
            String projectid = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectid);
            Long studyid = Long.valueOf((String) data.get("studyid"));

            Map studyMap = daoDataMng.getRecord("xsht", studyid);
            String studycode = (String) studyMap.get("studyid");
            editUrlParam.setUrl("/cdtms/extdatabind/selectEdm/selectEdm.html?tableid="+tableId+"&studycode=" + studycode + "&studyid="+studyid+"&id="+ data.get("id")+"&type="+ data.get("zq"));
            editUrlParam.setHeight(540);
            editUrlParam.setWidth(500);
            return editUrlParam;
        } catch (Exception e) {
            Log.error("",e);
        }
        return null;
    }

    @Override
    public String getShowValue(String tableId, String fieldId, Map<String, Object> data) {
        return data.get(fieldId) == null ?"":data.get(fieldId).toString();
    }

    @Override
    public Map<String, Map<String, Object>> getOtherShowValues(String tableId, String fieldId, Map<String, Object> data) {
        Map<String, Map<String, Object>>  map = new HashMap<>();
        Map<String, Object> map1 =  new HashMap<String,Object>();
        map1.put("value",data.get("outboard_data_department"));
        map1.put("showValue",data.get("outboard_data_department"));

        Map<String, Object> map2 =  new HashMap<String,Object>();
        map2.put("value",data.get("receive_date"));
        map2.put("showValue",data.get("receive_date"));

        Map<String, Object> map3 =  new HashMap<String,Object>();
        map3.put("value",data.get("edm_id"));
        map3.put("showValue",data.get("edm_id"));
        if (StringUtils.equals(tableId,"outboard_data_manager")) {

        map.put("outboard_data_department",map1);

        }
        if (StringUtils.equals(tableId,"blindedtransfer")) {

        map.put("lab_org",map1);
        }

        if (StringUtils.equals(tableId,"dta_bind_method")) {

            map.put("lab_org",map1);
        }


        map.put("receive_date",map2);
        map.put("edm_id",map3);




        return map;
    }
}