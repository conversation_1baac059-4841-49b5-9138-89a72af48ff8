#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Complete solution for translating all 377 records
"""

import json
import codecs
import re

def create_comprehensive_translation_map():
    """
    Comprehensive translation mapping based on the translation report
    """
    translation_map = {
        # Basic system functions
        "Website Content": "网站内容",
        "Link Settings": "链接设置", 
        "Statistics Settings": "统计设置",
        "Export Configuration Definition": "导出配置定义",
        
        # Workflow related
        "Workflow Definitions": "工作流定义",
        "Workflow Definition Details": "工作流定义详情",
        "Workflow Status Codelists": "工作流状态代码列表",
        "Workflow Records": "工作流记录",
        "Workflow Details": "工作流详情",
        
        # Role and user management
        "Role": "角色",
        "Role Data": "角色数据",
        "System Role Mapping": "系统角色映射",
        "Cross-Function Role Setting": "跨功能角色设置",
        "Data Center Managers": "数据中心管理员",
        "Other Team Members": "其他团队成员",
        
        # Clinical research
        "Clinical Studies": "临床研究",
        "EDC": "电子数据采集",
        "Protocol": "试验方案",
        "RTSM": "随机化试验供应管理",
        "Medical Coding Plan": "医学编码计划",
        "Site and Investigator": "研究中心和研究者",
        "Safety Data Recocilliation": "安全数据核对",
        "Coding Dictionary & System": "编码字典和系统",
        "CDSC SOPs": "CDSC标准操作程序",
        
        # Communication and project management
        "Communications": "沟通交流",
        "Communication Plan": "沟通计划",
        "Task Transition": "任务交接",
        "Project Plan": "项目计划",
        "Extention Application": "延期申请",
        "External Data Management": "外部数据管理",
        
        # Data management specific
        "Data Management Plan": "数据管理计划",
        "Data Management Report": "数据管理报告",
        "Data Management Summary": "数据管理总结",
        "Data Management Review Plan (DMRP)": "数据管理审查计划",
        "Data Management Discrepancy": "数据管理差异",
        "Data Management Flie History": "数据管理文件历史",
        "Data QA": "数据质量保证",
        "Data Transfer": "数据传输",
        "Data Transfer Agreement": "数据传输协议",
        "Data Validation Plan": "数据验证计划",
        "Data Validation Report": "数据验证报告",
        "Data Extract": "数据提取",
        
        # Database related
        "Database QC": "数据库质量控制",
        "Database QC History": "数据库质量控制历史",
        "Database Unlock": "数据库解锁",
        "Database amendment": "数据库修正",
        "Database Go-Live Approval History": "数据库上线批准历史",
        "DB Lock Data QC": "数据库锁定数据质量控制",
        "DB Unlock Request": "数据库解锁请求",
        
        # EDC specific
        "EDC Account Management": "EDC账户管理",
        "EDC Account Management History": "EDC账户管理历史",
        "EDC Go-Live Notification": "EDC上线通知",
        "EDC Manual": "EDC手册",
        "EDC Production Use": "EDC生产使用",
        "EDC System Recovery": "EDC系统恢复",
        "EDC User History": "EDC用户历史",
        "EDC Version": "EDC版本",
        "EDC Version Upgrade": "EDC版本升级",
        
        # RTSM specific
        "RTSM Account Management": "RTSM账户管理",
        "RTSM Account Management History": "RTSM账户管理历史",
        "RTSM Production Environment": "RTSM生产环境",
        "RTSM User Training": "RTSM用户培训",
        "Randomization Application": "随机化申请",
        "Randomization Application History": "随机化申请历史",
        "Randomization Reports": "随机化报告",
        "Randomization Scheme": "随机化方案",
        "Randomization Scheme Version History": "随机化方案版本历史",
        "Rand Setting History": "随机设置历史",
        
        # Medical coding
        "Medical Coding": "医学编码",
        "Medical Coding Management  History": "医学编码管理历史",
        "Codelists": "代码列表",
        
        # CRF related
        "CRF Completion Guideline": "CRF完成指南",
        "CRF Design": "CRF设计",
        "CRF Design Hisitory": "CRF设计历史",
        
        # Documents and SOPs
        "DM Document Archives": "数据管理文档档案",
        "DM Issue and Resolution": "数据管理问题和解决方案",
        "DM Report History": "数据管理报告历史",
        "DM SOP Lists": "数据管理SOP列表",
        "DM Technical Document History": "数据管理技术文档历史",
        "DM Technical Documents": "数据管理技术文档",
        "DM Working Documents": "数据管理工作文档",
        "DMP History": "数据管理计划历史",
        "DVP History": "数据验证计划历史",
        
        # Analysis and reporting
        "Interim Analysis": "中期分析",
        "Interim Analysis Plan": "中期分析计划",
        "Monthly Summary": "月度总结",
        "Progress Summary Category": "进度总结类别",
        "Production Report History": "生产报告历史",
        
        # Blinding and unblinding
        "Drug Blinding": "药物盲法",
        "Emergency Unblinding": "紧急揭盲",
        "Interim Unblinding  History": "中期揭盲历史",
        "(Blinded) Data Review Meeting": "（盲法）数据审查会议",
        
        # Training and meetings
        "Investigator Meeting": "研究者会议",
        "My Trainings": "我的培训",
        
        # System and technical
        "Disaster Recovery Process": "灾难恢复流程",
        "Functional Specification": "功能规范",
        "Edit Check Programming/Testing": "编辑检查编程/测试",
        "Manual Review": "手工审查",
        "Manual Review History": "手工审查历史",
        
        # Administrative
        "Announcements": "公告",
        "Help": "帮助",
        "Information Sharing": "信息共享",
        "Publishing": "发布",
        "Regulations": "法规",
        "Policies And Regulations": "政策和法规",
        
        # HR and organization
        "CVs and JDs": "简历和职位描述",
        "Department": "部门",
        "Employee": "员工",
        "Organization Chart": "组织架构图",
        "Orgnization": "组织",
        "Leaves": "请假",
        "Overtime": "加班",
        
        # Other
        "Other": "其他",
        "Public Templates": "公共模板",
        "Completed data cleanings": "已完成的数据清理",
        "External Data Checking": "外部数据检查",
        "Lab Reference Range": "实验室参考范围",
        "Lab Reference Range History": "实验室参考范围历史",
        "Protocol Deviation Definition": "方案偏离定义",
        "Protocol Deviation Version History": "方案偏离版本历史",
        "Protocol or Database Amendment": "方案或数据库修正",
        "Request EDC Instances": "请求EDC实例",
        "Pushing Study Definition": "推送研究定义",
        "QC & QA": "质量控制和质量保证",
        
        # Technical terms
        "DTA Field Dictionary": "DTA字段字典",
        "DTA Type": "DTA类型",
        "DTA Type Template": "DTA类型模板",
        "EDM Account Management": "EDM账户管理",
        "EC Hisitory": "伦理委员会历史",
        "IRC Data transfer": "IRC数据传输",
        "Investigator Site File Category": "研究者研究中心文件类别",
        "Investigator e-Signature Statement": "研究者电子签名声明",
        "NON External Data Management": "非外部数据管理",
        "Risk Management and Contingency Plan": "风险管理和应急计划",
        "Comminication records": "沟通记录",
        
        # Common system terms
        "attachment": "附件",
        "log_event": "日志事件",
        "modify_history": "修改历史",
        "esign_engine": "电子签名引擎",
        "esign_log": "电子签名日志",
        "esign_account": "电子签名账户",
        
        # Empty or system names
        "": "",
    }
    return translation_map

def apply_comprehensive_translation(input_file, output_file=None):
    """
    Apply comprehensive translation to all records
    """
    if output_file is None:
        output_file = input_file.replace('.json', '_fully_translated.json')
    
    try:
        # Load the data
        with codecs.open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        translation_map = create_comprehensive_translation_map()
        
        print(f"Processing {len(data)} records with comprehensive translation...")
        
        fixed_count = 0
        for i, record in enumerate(data):
            original_name = record.get('name', '')
            english_name = record.get('nameen', '')
            
            # Apply translation if English name exists in our map
            if english_name and english_name in translation_map:
                record['name'] = translation_map[english_name]
                fixed_count += 1
            
            # Progress indicator
            if (i + 1) % 50 == 0:
                print(f"Processed {i + 1}/{len(data)} records...")
        
        # Save the result
        with codecs.open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ Comprehensive translation complete!")
        print(f"📊 Fixed {fixed_count} records with proper Chinese translations")
        print(f"💾 Saved to: {output_file}")
        
        # Generate summary
        remaining_garbled = 0
        for record in data:
            name = record.get('name', '')
            if name and any(char in name for char in ['閸', '閺', '缂', '妞', '鐠', '闁']):
                remaining_garbled += 1
        
        print(f"📈 Summary:")
        print(f"   - Total records: {len(data)}")
        print(f"   - Successfully translated: {fixed_count}")
        print(f"   - Still garbled: {remaining_garbled}")
        print(f"   - Success rate: {(fixed_count/len(data)*100):.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    input_file = "json_simple_fixed.json"
    apply_comprehensive_translation(input_file)
