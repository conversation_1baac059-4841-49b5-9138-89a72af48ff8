<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>PDF坐标查看器</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.min.js"></script>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            display: flex;
            font-family: Arial, sans-serif;
        }

        /* 左侧边栏样式 */
        .sidebar {
            width: 320px;
            height: 100vh;
            background-color: #f5f5f5;
            border-right: 1px solid #ddd;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* 用户信息区域 */
        .user-info {
            padding: 12px;
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
        }

        .user-info .user-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #e1e1e1;
            border-radius: 50%;
        }

        .user-info .user-name {
            font-size: 14px;
            color: #555;
        }

        /* 左侧两列布局 */
        .sidebar-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        /* 签名按钮列 */
        .sidebar-left {
            width: 130px;
            border-right: 1px solid #ddd;
            padding: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #f8f8f8;
        }

        /* 签字按钮样式 */
        .sign-button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 15px;
            margin: 10px 0;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-align: center;
            width: 100px;
        }

        /* 文件预览区域样式 */
        .sidebar-right {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .thumbnail {
            width: 100%;
            margin-bottom: 10px;
            border: 2px solid transparent;
            box-sizing: border-box;
            cursor: pointer;
            aspect-ratio: 0.8; /* 保持与截图中一致的比例 */
        }

        .thumbnail.active {
            border-color: #1890ff;
        }

        .thumbnail-container {
            position: relative;
            margin-bottom: 10px;
            height: auto;
        }

        .page-number {
            position: absolute;
            bottom: 5px;
            left: 5px;
            background: rgba(0,0,0,0.5);
            color: white;
            padding: 2px 5px;
            font-size: 12px;
            border-radius: 3px;
        }

        /* 主要内容区域样式 */
        .main-content {
            flex: 1;
            height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
            background-color: #e6e6e6;
        }

        .toolbar {
            display: flex;
            align-items: center;
            padding: 5px 10px;
            background-color: #f0f0f0;
            border-bottom: 1px solid #ddd;
        }

        #file-input {
            margin-right: 10px;
        }

        #coordinates {
            position: fixed;
            top: 50px;
            right: 10px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border-radius: 5px;
            z-index: 100;
            max-width: 450px;
        }

        #pdf-info {
            position: fixed;
            top: 10px;
            left: 330px;
            padding: 5px 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border-radius: 5px;
            z-index: 100;
            font-size: 12px;
        }

        #pdf-container {
            flex: 1;
            overflow: auto;
            background-color: #e6e6e6;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding: 20px;
        }

        #pdf-canvas {
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        #coordinate-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }
        
        #coordinate-table th, #coordinate-table td {
            padding: 3px 5px;
            text-align: left;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .pdf-coordinates {
            font-weight: bold;
            color: #4CAF50;
        }
        
        .scroll-info {
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
        }
        
        #copyStatus {
            display: none;
            position: fixed;
            top: 150px;
            right: 10px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border-radius: 5px;
            z-index: 100;
        }

        /* 顶部工具栏样式 */
        .header {
            height: 50px;
            background-color: #f8f8f8;
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
            padding: 0 15px;
        }

        /* 为了匹配截图，模拟PowerPoint图标 */
        .document-icon {
            display: flex;
            align-items: center;
            margin-right: 15px;
        }

        .document-icon img {
            height: 40px;
            width: 40px;
        }

        /* 邮件图标样式 */
        .mail-icon {
            position: fixed;
            top: 15px;
            right: 15px;
            font-size: 24px;
            color: #666;
            z-index: 100;
        }
        
        /* 同步图标样式 */
        .sync-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 5px;
            font-size: 18px;
            color: #666;
        }
    </style>
</head>
<body>
    <!-- 左侧边栏 -->
    <div class="sidebar">
        <!-- 用户信息显示 -->
        <div class="user-info">
            <div class="user-icon">1:</div>
            <div class="user-name">hui.zhou...</div>
        </div>
        
        <!-- 两列布局的内容区 -->
        <div class="sidebar-content">
            <!-- 左侧签名按钮区 -->
            <div class="sidebar-left">
                <button class="sign-button">添加签名</button>
                <div class="sync-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="23 4 23 10 17 10"></polyline>
                        <polyline points="1 20 1 14 7 14"></polyline>
                        <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                    </svg>
                </div>
            </div>
            
            <!-- 右侧缩略图预览区 -->
            <div class="sidebar-right" id="thumbnails-container">
                <!-- 缩略图将在这里动态生成 -->
            </div>
        </div>
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="document-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="#d83b01">
                <path d="M19.3 2H7c-.6 0-1 .4-1 1v26c0 .6.4 1 1 1h18c.6 0 1-.4 1-1V8.7L19.3 2z"/>
                <path fill="#ffffff" d="M19 9h6v1h-6z"/>
                <path fill="#ffffff" d="M19 13h6v1h-6z"/>
                <path fill="#ffffff" d="M19 17h6v1h-6z"/>
                <path fill="#ffffff" d="M7 13h10v1H7z"/>
                <path fill="#ffffff" d="M7 17h10v1H7z"/>
                <path fill="#ffffff" d="M7 21h18v1H7z"/>
                <path fill="#ffffff" d="M7 25h18v1H7z"/>
            </svg>
        </div>
        
        <div class="mail-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
        </div>

        <input type="file" id="file-input" accept=".pdf" style="position: absolute; top: 15px; right: 50px; z-index: 100;" />
        
        <div id="coordinates">
            <h3 style="margin-top: 0;">坐标信息 <small>(点击自动复制PDF坐标)</small></h3>
            <table id="coordinate-table">
                <tr>
                    <th>坐标类型</th>
                    <th>X</th>
                    <th>Y</th>
                    <th>页码</th>
                </tr>
                <tr>
                    <td>屏幕坐标</td>
                    <td id="screen-x">0</td>
                    <td id="screen-y">0</td>
                    <td rowspan="3" id="page-num">1</td>
                </tr>
                <tr class="pdf-coordinates">
                    <td>PDF坐标</td>
                    <td id="pdf-x">0</td>
                    <td id="pdf-y">0</td>
                </tr>
                <tr>
                    <td>index.js坐标</td>
                    <td id="index-x">0</td>
                    <td id="index-y">0</td>
                </tr>
                <tr>
                    <td>旋转角度</td>
                    <td colspan="3" id="rotation">0°</td>
                </tr>
            </table>
            <div class="scroll-info">滚动已包含在计算中</div>
        </div>
        <div id="pdf-info"></div>
        <div id="copyStatus">PDF坐标已复制到剪贴板: <span id="copied-text"></span></div>
        <div id="pdf-container">
            <canvas id="pdf-canvas"></canvas>
        </div>
    </div>

    <script>
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.worker.min.js';

        const fileInput = document.getElementById('file-input');
        const pdfContainer = document.getElementById('pdf-container');
        const canvas = document.getElementById('pdf-canvas');
        const ctx = canvas.getContext('2d');
        const coordinatesDisplay = document.getElementById('coordinates');
        const copyStatus = document.getElementById('copyStatus');
        const pdfInfo = document.getElementById('pdf-info');
        const thumbnailsContainer = document.getElementById('thumbnails-container');
        
        // 获取坐标表格元素
        const screenXEl = document.getElementById('screen-x');
        const screenYEl = document.getElementById('screen-y');
        const pdfXEl = document.getElementById('pdf-x');
        const pdfYEl = document.getElementById('pdf-y');
        const indexXEl = document.getElementById('index-x');
        const indexYEl = document.getElementById('index-y');
        const rotationEl = document.getElementById('rotation');
        
        let pdfDoc = null;
        let pageNum = 1;
        let currentScale = 1.2; // 调整缩放比例以匹配截图
        let pdfPageView = null;
        let pageRotation = 0;
        let pdfScale = 96.0 / 72.0; // 从index.js采用的比例因子
        let pageRotateMap = {}; // 存储页面旋转信息

        // 签名按钮点击事件
        document.querySelector('.sign-button').addEventListener('click', function() {
            alert('添加签名功能');
        });

        // 加载PDF文件
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file && file.type === 'application/pdf') {
                const fileReader = new FileReader();
                
                fileReader.onload = function() {
                    const typedarray = new Uint8Array(this.result);
                    loadPDF(typedarray);
                };
                
                fileReader.readAsArrayBuffer(file);
            }
        });

        // 使用PDF.js加载PDF
        function loadPDF(data) {
            pdfjsLib.getDocument({data: data}).promise.then(function(pdf) {
                pdfDoc = pdf;
                updatePdfInfo(`PDF页数: ${pdf.numPages}`);
                renderPage(pageNum);
                generateThumbnails(pdf);
            });
        }

        // 生成PDF缩略图
        function generateThumbnails(pdf) {
            thumbnailsContainer.innerHTML = '';
            
            for (let i = 1; i <= pdf.numPages; i++) {
                pdf.getPage(i).then(function(page) {
                    const viewport = page.getViewport({scale: 0.2});
                    const thumbnailCanvas = document.createElement('canvas');
                    thumbnailCanvas.width = viewport.width;
                    thumbnailCanvas.height = viewport.height;
                    
                    const thumbnailContext = thumbnailCanvas.getContext('2d');
                    
                    // 创建缩略图容器
                    const thumbnailContainer = document.createElement('div');
                    thumbnailContainer.className = 'thumbnail-container';
                    
                    // 创建页码标签
                    const pageNumberLabel = document.createElement('div');
                    pageNumberLabel.className = 'page-number';
                    pageNumberLabel.textContent = i;
                    
                    // 渲染缩略图
                    page.render({
                        canvasContext: thumbnailContext,
                        viewport: viewport
                    }).promise.then(() => {
                        thumbnailCanvas.className = 'thumbnail' + (i === pageNum ? ' active' : '');
                        thumbnailCanvas.dataset.pageNum = i;
                        
                        thumbnailCanvas.addEventListener('click', function() {
                            pageNum = parseInt(this.dataset.pageNum);
                            
                            // 更新活动缩略图样式
                            document.querySelectorAll('.thumbnail').forEach(thumb => {
                                thumb.classList.remove('active');
                            });
                            this.classList.add('active');
                            
                            renderPage(pageNum);
                        });
                        
                        thumbnailContainer.appendChild(thumbnailCanvas);
                        thumbnailContainer.appendChild(pageNumberLabel);
                        thumbnailsContainer.appendChild(thumbnailContainer);
                    });
                });
            }
        }

        // 更新PDF信息显示
        function updatePdfInfo(info) {
            pdfInfo.textContent = info;
        }

        // 获取页码显示元素
        const pageNumEl = document.getElementById('page-num');
        const copiedTextEl = document.getElementById('copied-text');

        // 渲染PDF页面
        function renderPage(num) {
            pdfDoc.getPage(num).then(function(page) {
                pageRotation = page.rotate || 0;
                pageRotateMap[num] = pageRotation; // 存储页面旋转信息
                
                updatePdfInfo(`PDF页数: ${pdfDoc.numPages} | 当前页: ${num} | 旋转: ${pageRotation}°`);
                rotationEl.textContent = `${pageRotation}°`;
                pageNumEl.textContent = num; // 更新页码显示
                
                const viewport = page.getViewport({scale: currentScale});
                canvas.height = viewport.height;
                canvas.width = viewport.width;
                
                const renderContext = {
                    canvasContext: ctx,
                    viewport: viewport
                };
                
                page.render(renderContext);
                pdfPageView = {page: page, viewport: viewport};
                
                // 添加鼠标事件
                setupMouseListeners();
                
                // 更新活动缩略图
                document.querySelectorAll('.thumbnail').forEach(thumb => {
                    if (parseInt(thumb.dataset.pageNum) === num) {
                        thumb.classList.add('active');
                    } else {
                        thumb.classList.remove('active');
                    }
                });
            });
        }

        // 设置鼠标事件监听器
        function setupMouseListeners() {
            // 移除现有事件监听器
            pdfContainer.removeEventListener('mousemove', handleMouseMove);
            pdfContainer.removeEventListener('click', handleMouseClick);
            
            // 添加新事件监听器
            pdfContainer.addEventListener('mousemove', handleMouseMove);
            pdfContainer.addEventListener('click', handleMouseClick);
        }

        // 存储最后的鼠标事件
        let lastMouseEvent = null;

        // 获取屏幕坐标，考虑滚动位置
        function getScreenCoordinates(event) {
            lastMouseEvent = event;
            
            const rect = canvas.getBoundingClientRect();
            const scrollLeft = pdfContainer.scrollLeft;
            const scrollTop = pdfContainer.scrollTop;
            
            return {
                x: event.clientX - rect.left + scrollLeft,
                y: event.clientY - rect.top + scrollTop
            };
        }

        // 获取PDF坐标（转换屏幕坐标到PDF坐标系）
        function getPdfCoordinates(screenCoords) {
            const rotate = pageRotateMap[pageNum] || 0;
            let pdfX, pdfY;
            
            // 注意PDF坐标系与屏幕坐标系的转换
            switch(rotate) {
                case 0:
                    pdfX = screenCoords.x / pdfScale;
                    pdfY = (canvas.height - screenCoords.y) / pdfScale;
                    break;
                case 90:
                    pdfX = screenCoords.y / pdfScale;
                    pdfY = screenCoords.x / pdfScale;
                    break;
                // 其他旋转角度处理...
            }
            
            return {x: pdfX, y: pdfY};
        }

        // index.js中的getPdfPositionX函数的实现
        function getPdfPositionX(item) {
            let rotate = pageRotateMap[pageNum];
            
            const simulatedItem = {
                left: item.x * currentScale,
                top: item.y * currentScale,
                width: 0,
                height: 0,
                pdfWidth: canvas.width,
                pdfHeight: canvas.height,
                page: pageNum
            };
            
            switch (rotate) {
                case 0:
                    return simulatedItem.left / pdfScale;
                case 90:
                    return simulatedItem.top / pdfScale;
                case 180:
                    return (simulatedItem.pdfWidth - simulatedItem.left - simulatedItem.width) / pdfScale;
                case 270:
                    return (simulatedItem.pdfHeight - simulatedItem.top - simulatedItem.height) / pdfScale;
                default:
                    return simulatedItem.left / pdfScale;
            }
        }

        // index.js中的getPdfPositionY函数的实现
        function getPdfPositionY(item) {
            let rotate = pageRotateMap[pageNum];
            
            const simulatedItem = {
                left: item.x * currentScale,
                top: (pdfPageView.viewport.height / currentScale - item.y) * currentScale,
                width: 0,
                height: 0,
                pdfWidth: canvas.width,
                pdfHeight: canvas.height,
                page: pageNum
            };
            
            switch (rotate) {
                case 0:
                    return (simulatedItem.pdfHeight - simulatedItem.top - simulatedItem.height) / pdfScale;
                case 90:
                    return simulatedItem.left / pdfScale;
                case 180:
                    return simulatedItem.top / pdfScale;
                case 270:
                    return (simulatedItem.pdfWidth - simulatedItem.left - simulatedItem.width) / pdfScale;
                default:
                    return (simulatedItem.pdfHeight - simulatedItem.top - simulatedItem.height) / pdfScale;
            }
        }

        // ... existing code ...
        // 处理鼠标移动
        function handleMouseMove(event) {
            const screenCoords = getScreenCoordinates(event);
            const pdfCoords = getPdfCoordinates(screenCoords);
            
            // 使用index.js中的坐标转换函数计算最终坐标
            const indexX = getPdfPositionX({x: pdfCoords.x, y: pdfCoords.y});
            const indexY = getPdfPositionY({x: pdfCoords.x, y: pdfCoords.y});
            
            // 更新坐标显示
            screenXEl.textContent = Math.round(screenCoords.x);
            screenYEl.textContent = Math.round(screenCoords.y);
            pdfXEl.textContent = pdfCoords.x.toFixed(2);
            pdfYEl.textContent = pdfCoords.y.toFixed(2);
            indexXEl.textContent = indexX.toFixed(2);
            indexYEl.textContent = indexY.toFixed(2);
            
            // 仅更新坐标显示，不复制
        }

        // 处理鼠标点击
        function handleMouseClick(event) {
            const screenCoords = getScreenCoordinates(event);
            const pdfCoords = getPdfCoordinates(screenCoords);
            
            // 使用index.js中的坐标转换函数计算最终坐标
            const indexX = getPdfPositionX({x: pdfCoords.x, y: pdfCoords.y});
            const indexY = getPdfPositionY({x: pdfCoords.x, y: pdfCoords.y});
            
            // 更新坐标显示
            screenXEl.textContent = Math.round(screenCoords.x);
            screenYEl.textContent = Math.round(screenCoords.y);
            pdfXEl.textContent = pdfCoords.x.toFixed(2);
            pdfYEl.textContent = pdfCoords.y.toFixed(2);
            indexXEl.textContent = indexX.toFixed(2);
            indexYEl.textContent = indexY.toFixed(2);
            
            // 仅在点击时复制坐标
            copyIndexCoordinates();
        }
// ... existing code ...
       // ... existing code ...
        // 复制index.js格式的坐标到剪贴板
        function copyIndexCoordinates() {
            // 修改这里，添加页码信息
            const coordText = `${indexXEl.textContent},${indexYEl.textContent},${pageNum}`;
            copyToClipboard(coordText);
        }
        
        // 复制到剪贴板
        function copyToClipboard(text) {
            // 创建临时文本区域元素
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-9999px';
            textArea.style.top = '-9999px';
            document.body.appendChild(textArea);
            
            // 选择并复制文本
            textArea.select();
            let successful = false;
            
            try {
                successful = document.execCommand('copy');
                if (successful) {
                    copiedTextEl.textContent = text;
                    copyStatus.style.display = 'block';
                    
                    setTimeout(() => {
                        copyStatus.style.display = 'none';
                    }, 2000);
                } else {
                    console.error('execCommand copy failed');
                }
            } catch (err) {
                console.error('无法复制文本: ', err);
                // 尝试使用navigator.clipboard API
                tryClipboardAPI(text);
            }
            
            // 清理
            document.body.removeChild(textArea);
        }
        
        // 尝试使用Clipboard API作为备选方案
        function tryClipboardAPI(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    copiedTextEl.textContent = text;
                    copyStatus.style.display = 'block';
                    
                    setTimeout(() => {
                        copyStatus.style.display = 'none';
                    }, 2000);
                }).catch(err => {
                    console.error('Clipboard API failed: ', err);
                    alert('复制坐标失败，请手动复制坐标信息');
                });
            } else {
                alert('您的浏览器不支持复制功能，请手动复制坐标信息');
            }
        }
// ... existing code ...
        
        // 添加键盘事件用于页面导航
        document.addEventListener('keydown', function(event) {
            if (event.key === 'ArrowLeft' && pageNum > 1) {
                pageNum--;
                renderPage(pageNum);
            } else if (event.key === 'ArrowRight' && pdfDoc && pageNum < pdfDoc.numPages) {
                pageNum++;
                renderPage(pageNum);
            }
        });
    </script>
</body>
</html>