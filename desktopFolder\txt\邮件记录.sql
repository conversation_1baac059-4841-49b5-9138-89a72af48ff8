/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80032
 Source Host           : localhost:3306
 Source Schema         : external_data

 Target Server Type    : MySQL
 Target Server Version : 80032
 File Encoding         : 65001

 Date: 04/12/2023 16:59:26
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ex_blind_email_record
-- ----------------------------
DROP TABLE IF EXISTS `ex_blind_email_record`;
CREATE TABLE `ex_blind_email_record`  (
  `id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `user_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `project_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `file_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `file_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `receive_emails` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP,
  `original_file_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ex_blind_email_record
-- ----------------------------
INSERT INTO `ex_blind_email_record` VALUES ('27ee3ffddf76d145', '周辉', 'SHR-1210', 'test-06.csv', '5fbc77feddfe2233', '<EMAIL>', '2023-12-04 16:41:32', 'f5dd3ffbf4be9f14', '0');
INSERT INTO `ex_blind_email_record` VALUES ('2b8db3f78e0d79e5', '周辉', 'SHR-1210', 'test-06.csv', 'b7dcfbefedfb118d', '<EMAIL>', '2023-12-04 16:29:09', 'f9adf3efd67eca84', '2');
INSERT INTO `ex_blind_email_record` VALUES ('37ec7f77fffeff89', '周辉', 'SHR-1210', 'test-06.csv', 'e1cd7efd3bf7a294', '<EMAIL>', '2023-12-03 19:00:07', '678dffeddb7b4864', '');
INSERT INTO `ex_blind_email_record` VALUES ('39ae7bfefff566ef', '周辉', 'SHR-1210', 'test-06.csv', '5fbc77feddfe2233', '<EMAIL>', '2023-12-04 16:40:41', 'f5dd3ffbf4be9f14', '1');
INSERT INTO `ex_blind_email_record` VALUES ('3f9deff7fbb96296', '周辉', 'SHR-1210', 'test-06.csv', '89ac6fff79ff215b', '<EMAIL>', '2023-12-03 19:35:13', '8bcf6f7ffff3fcbb', '0');
INSERT INTO `ex_blind_email_record` VALUES ('77ec73fbeb77626f', '周辉', 'SHR-1210', 'test-06.csv', '89ac6fff79ff215b', '<EMAIL>,<EMAIL>', '2023-12-04 15:38:14', '8bcf6f7ffff3fcbb', '2');
INSERT INTO `ex_blind_email_record` VALUES ('95adb3f96bf723d3', '周辉', 'SHR-1210', 'test-06.csv', 'b7dcfbefedfb118d', '<EMAIL>', '2023-12-04 16:27:23', 'f9adf3efd67eca84', '0');
INSERT INTO `ex_blind_email_record` VALUES ('97fd2fff7d9df904', '周辉', 'SHR-1210', 'test-06.csv', 'e1cd7efd3bf7a294', '<EMAIL>', '2023-12-03 19:05:58', '678dffeddb7b4864', '');
INSERT INTO `ex_blind_email_record` VALUES ('e9cfff7befabbe61', '周辉', 'SHR-1210', 'test-06.csv', '89ac6fff79ff215b', '<EMAIL>', '2023-12-03 19:32:41', '8bcf6f7ffff3fcbb', '1');
INSERT INTO `ex_blind_email_record` VALUES ('f5cf6f736feb7907', '周辉', 'SHR-1210', 'test-06.csv', 'dbdd6fdfb97d5555', '<EMAIL>', '2023-12-03 19:11:13', '678dffeddb7b4864', '');

SET FOREIGN_KEY_CHECKS = 1;
