/*
 Navicat Premium Data Transfer

 Source Server         : 虚拟机
 Source Server Type    : MySQL
 Source Server Version : 80020
 Source Host           : *************:3307
 Source Schema         : book

 Target Server Type    : MySQL
 Target Server Version : 80020
 File Encoding         : 65001

 Date: 16/02/2023 01:40:09
*/ CREATE DATABASE
IF
	NOT EXISTS `susar_auto_mail` DEFAULT CHARACTER
	SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `susar_auto_mail`;

SET NAMES utf8mb4;

SET FOREIGN_KEY_CHECKS = 0;
-- ----------------------------
-- Table structure for file_upload_record
-- ----------------------------
DROP TABLE
    IF
    EXISTS `file_upload_record`;
CREATE TABLE `file_upload_record` (
                                      `id` INT NOT NULL AUTO_INCREMENT,
                                      `file_name` VARCHA<PERSON> ( 255 ) CHARACTER
                                          SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                                      `user_name` VARCHAR ( 32 ) CHARACTER
                                          SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                                      `compound_folder` VARCHAR ( 200 ) CHARACTER
                                          SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
                                      `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                      PRIMARY KEY ( `id` ) USING BTREE
) ENGINE = INNODB AUTO_INCREMENT = 1 CHARACTER
SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

ALTER TABLE file_upload_record ADD COLUMN file_path VARCHAR ( 255 ) DEFAULT NULL COMMENT '文件下载路径' AFTER compound_folder;-- ----------------------------
-- Table structure for compound_folder
-- ----------------------------
DROP TABLE
    IF
    EXISTS `compound_folder`;
CREATE TABLE `compound_folder` (
                                   `id` INT NOT NULL AUTO_INCREMENT,
                                   `folder_name` VARCHAR ( 255 ) CHARACTER
                                       SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                                   `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                   PRIMARY KEY ( `id` ) USING BTREE,
                                   UNIQUE INDEX `id` ( `id` ASC ) USING BTREE
) ENGINE = INNODB AUTO_INCREMENT = 8 CHARACTER
SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;-- ----------------------------


SET FOREIGN_KEY_CHECKS = 1;-- ----------------------------
-- Table structure for mail_send__record
-- ----------------------------
DROP TABLE
    IF
    EXISTS `mail_send_record`;
CREATE TABLE `mail_send_record` (
                                    `id` INT NOT NULL AUTO_INCREMENT,
                                    `mail_content` VARCHAR ( 255 ) CHARACTER
                                        SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                                    `receiver` VARCHAR ( 32 ) CHARACTER
                                        SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                                    `status` CHAR ( 1 ) NOT NULL DEFAULT 'N',
                                    `file_id` INT NOT NULL,
                                    `send_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                    PRIMARY KEY ( `id` ) USING BTREE
) ENGINE = INNODB AUTO_INCREMENT = 1 CHARACTER
SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
ALTER TABLE file_upload_record ADD COLUMN is_deleted CHAR NOT NULL DEFAULT 'N' AFTER create_time;
ALTER TABLE mail_send_record ADD COLUMN is_deleted CHAR NOT NULL DEFAULT 'N' AFTER send_time;
ALTER TABLE mail_send_record MODIFY COLUMN receiver TEXT;
ALTER TABLE mail_send_record ADD COLUMN file_name TEXT AFTER mail_content;
ALTER TABLE mail_send_record ADD COLUMN sender VARCHAR(255) AFTER file_name;
ALTER TABLE file_upload_record ADD COLUMN folder_path VARCHAR ( 255 ) NULL AFTER file_path;
ALTER TABLE file_upload_record ADD COLUMN is_send CHAR NOT NULL DEFAULT 'N' AFTER create_time;
ALTER TABLE compound_folder ADD COLUMN is_deleted CHAR NOT NULL DEFAULT 'N' AFTER create_time;
ALTER TABLE file_upload_record ADD COLUMN url VARCHAR ( 255 ) NULL AFTER folder_path;
ALTER TABLE mail_send_record CHANGE file_id compound_folder VARCHAR ( 255 ) DEFAULT NULL COMMENT '化合物文件夹名称';
ALTER TABLE mail_send_record ADD COLUMN operate_user VARCHAR ( 255 ) DEFAULT 'system' COMMENT '发送人' AFTER receiver;
DROP TABLE
    IF
    EXISTS `file_store_folder`;
CREATE TABLE `file_store_folder` (
                                     `id` INT NOT NULL AUTO_INCREMENT,
                                     `folder_name` VARCHAR ( 255 ) CHARACTER
                                         SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                                     `folder_path` VARCHAR ( 255 ) CHARACTER
                                         SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                                     `compound_folder` VARCHAR ( 200 ) CHARACTER
                                         SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
                                     `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                     `is_deleted` CHAR NOT NULL DEFAULT 'N',
                                     PRIMARY KEY ( `id` ) USING BTREE
) ENGINE = INNODB AUTO_INCREMENT = 1 CHARACTER
SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

SET NAMES utf8mb4;

SET FOREIGN_KEY_CHECKS = 0;-- ----------------------------
-- Table structure for t_site
-- ----------------------------
DROP TABLE
    IF
    EXISTS `t_site`;
CREATE TABLE `t_site` (
                          `id` VARCHAR ( 255 ) CHARACTER
                              SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
                          `compound_no` VARCHAR ( 255 ) CHARACTER
                              SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '化合物代码',
                          `compound_name` VARCHAR ( 2000 ) CHARACTER
                              SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '化合物名称',
                          `study_no` VARCHAR ( 255 ) CHARACTER
                              SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '研究代码',
                          `study_name` VARCHAR ( 2000 ) CHARACTER
                              SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '研究名称',
                          `site_no` VARCHAR ( 255 ) CHARACTER
                              SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中心编号',
                          `site_name` VARCHAR ( 255 ) CHARACTER
                              SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中心名称',
                          `create_time` DATETIME ( 6 ) NULL DEFAULT NULL COMMENT '创建时间',
                          `update_time` DATETIME ( 6 ) NULL DEFAULT NULL COMMENT '修改时间',
                          PRIMARY KEY ( `id` ) USING BTREE
) ENGINE = INNODB CHARACTER
SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

SET NAMES utf8mb4;

SET FOREIGN_KEY_CHECKS = 0;-- ----------------------------
-- Table structure for t_user
-- ----------------------------
DROP TABLE
    IF
    EXISTS `t_user`;
CREATE TABLE `t_user` (
                          `id` VARCHAR ( 255 ) CHARACTER
                              SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
                          `site_user_role` INT ( 5 ) NULL DEFAULT NULL COMMENT '角色代码，1 代表 Site_PI, 2 代表 Site_INV, 3 代表机构SUSAR人员',
                          `site_user_email` VARCHAR ( 255 ) CHARACTER
                              SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户邮箱',
                          `site_id` VARCHAR ( 255 ) CHARACTER
                              SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外键，关联site表',
                          PRIMARY KEY ( `id` ) USING BTREE
) ENGINE = INNODB CHARACTER
SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for user_info
-- ----------------------------
DROP TABLE IF EXISTS `user_info`;
CREATE TABLE `user_info`  (
                              `id` int NOT NULL AUTO_INCREMENT,
                              `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                              `user_password` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                              `role` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
                              `user_mail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                              `site_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外键，关联site表',
                              `validate_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                              `is_deleted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N',
                              `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                              `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                              `code_generate_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of user_info
-- ----------------------------
INSERT INTO `user_info` VALUES (24, 'admin', '$2a$10$4vhLnvWzdXytw9OE7VVMiOR0l2OrpHT7LOqEh5BSt8GrsVjm/Bwai', 'admin', '<EMAIL>', '1', '716578', 'N', '2023-04-13 09:11:15', '2023-04-13 09:11:15', '2023-04-13 09:11:40');
INSERT INTO `user_info` VALUES (25, 'pv', '$2a$10$1wRgG8uIKcDNqmfEClMSFePESUw4l8iqsrlcogGGJcSmCbqnRFN6.', 'user', '<EMAIL>', '2', '245099', 'N', '2023-04-13 09:11:15', '2023-04-13 09:11:15', '2023-04-13 09:15:24');

SET FOREIGN_KEY_CHECKS = 1;



SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for user_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `user_operation_log`;
CREATE TABLE `user_operation_log`  (
                                       `id` int NOT NULL AUTO_INCREMENT,
                                       `user_id` int NOT NULL,
                                       `object_id` int NULL DEFAULT NULL COMMENT '文件id',
                                       `operation_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '未知' COMMENT '操作类型',
                                       `object_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '对象类型',
                                       `object_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '对象名称',
                                       `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Y',
                                       `operate_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 112 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of user_operation_log
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;


------site清理表----
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_site_clear
-- ----------------------------
DROP TABLE IF EXISTS `t_site_clear`;
CREATE TABLE `t_site_clear`  (
                                 `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 `site_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT 'site表主键',
                                 `compound_name` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '化合物名称',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;