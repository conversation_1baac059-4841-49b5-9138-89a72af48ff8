/*
 Navicat MySQL Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80032 (8.0.32)
 Source Host           : localhost:3306
 Source Schema         : susar_auto_mail

 Target Server Type    : MySQL
 Target Server Version : 80032 (8.0.32)
 File Encoding         : 65001

 Date: 01/03/2023 17:44:42
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for file_upload_record
-- ----------------------------
DROP TABLE IF EXISTS `file_upload_record`;
CREATE TABLE `file_upload_record`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `compound_folder` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '文件下载路径',
  `folder_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_send` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N',
  `is_deleted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 204 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of file_upload_record
-- ----------------------------
INSERT INTO `file_upload_record` VALUES (93, '办梯控卡流程_20220810.docx', 'Marin', 'SUSAR-1316', '121', '123', '2023-02-24 15:15:26', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (94, '个人简历.docx', 'Marin', 'SUSAR-1316', 'C:\\Work\\SusarFolderTest\\SUSAR-1316', NULL, '2023-02-24 15:15:26', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (95, 'OA用户使用手册.doc', 'Marin', 'SUSAR-1316', 'C:\\Work\\SusarFolderTest\\SUSAR-1316', NULL, '2023-02-24 15:32:25', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (96, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SUSAR-1316', 'C:\\Work\\SusarFolderTest\\SUSAR-1316', NULL, '2023-02-24 15:32:26', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (97, '办梯控卡流程_20220810.docx', 'Marin', 'SUSAR-1316', 'C:\\Work\\SusarFolderTest\\SUSAR-1316', NULL, '2023-02-24 15:32:26', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (98, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SUSAR-1317', 'C:\\Work\\SusarFolderTest\\SUSAR-1317', NULL, '2023-02-24 16:29:03', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (99, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\', NULL, '2023-02-24 17:24:02', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (100, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\', NULL, '2023-02-24 17:25:04', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (101, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\\\SUSAR报告平台原型确认沟通会.pdf', NULL, '2023-02-27 11:09:27', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (102, '办梯控卡流程_20220810.docx', 'Marin', 'SusarFolderTest', 'http://localhost:8085C:\\Users\\<USER>\\AppData\\Local\\Temp\\tomcat-docbase.8085.100009016603676161\\upload\\2023/02/27/办梯控卡流程_20220810.docx', NULL, '2023-02-27 11:12:52', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (103, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\\\SUSAR报告平台原型确认沟通会.pdf', NULL, '2023-02-27 11:21:23', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (104, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\\\SUSAR报告平台原型确认沟通会.pdf', NULL, '2023-02-27 13:14:56', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (105, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\\\SUSAR报告平台原型确认沟通会.pdf', NULL, '2023-02-27 13:15:26', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (106, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\\\SUSAR报告平台原型确认沟通会.pdf', NULL, '2023-02-27 13:24:19', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (107, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\\\新员工线下培训计划 V3.0 数据科学中心.pdf', NULL, '2023-02-27 13:26:55', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (108, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\\\SUSAR报告平台原型确认沟通会.pdf', NULL, '2023-02-27 13:30:08', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (109, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\\\新员工线下培训计划 V3.0 数据科学中心.pdf', NULL, '2023-02-27 13:30:14', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (110, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\SUSAR报告平台原型确认沟通会.pdf', NULL, '2023-02-27 13:32:29', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (111, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\新员工线下培训计划 V3.0 数据科学中心.pdf', NULL, '2023-02-27 16:41:19', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (112, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\新员工线下培训计划 V3.0 数据科学中心.pdf', NULL, '2023-02-27 16:52:36', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (113, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\', '2023-02-27 17:10:47', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (114, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SUSAR-1317', 'C:\\Work\\SusarFolderTest\\SUSAR-1317\\新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-1317\\', '2023-02-27 17:35:45', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (115, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SUSAR-1317', 'C:\\Work\\SusarFolderTest\\SUSAR-1317\\SUSAR报告平台原型确认沟通会.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-1317\\', '2023-02-27 17:36:14', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (116, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 08:58:31', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (117, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SusarFolderTest', '', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 09:02:19', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (118, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SusarFolderTest', '', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 09:06:17', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (119, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\12ad8da841934cccb2f08db12091a64c_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 09:32:05', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (120, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\4870e46b93464e45a2a36e8df676c3f0_SUSAR报告平台原型确认沟通会.pdf', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 09:34:43', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (121, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\886b76f73bb94680b014732ff565d4d7_SUSAR报告平台原型确认沟通会.pdf', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 09:51:10', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (122, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\69f39b1355aa4e599f1d4ca8639ac0fb_SUSAR报告平台原型确认沟通会.pdf', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 09:51:34', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (123, '办梯控卡流程_20220810.docx', 'Marin', 'SusarFolderTest', 'http://localhost:8085C:\\Users\\<USER>\\AppData\\Local\\Temp\\tomcat-docbase.8085.5077916124695044143\\/upload//2023/02/28/32e0457491dd46d49fc712c28322e9cf_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 09:51:34', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (124, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\44f5b94494264a458e35655b09e968ba_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 09:51:35', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (125, 'OA用户使用手册.doc', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\65ef02d351714e8d872d47d375d61bf2_OA用户使用手册.doc', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 09:57:41', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (126, '办梯控卡流程_20220810.docx', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\f9a1c7eb07784656ae49d4357f9e7f34_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 09:57:53', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (127, '办梯控卡流程_20220810.docx', 'Marin', 'SUSAR-1316', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\59bf1056840d440dba0fca37b46db1e3_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\', '2023-02-28 09:59:06', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (128, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SUSAR-1316', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\c2066a2610cc405dbca381e9dae3f687_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\', '2023-02-28 09:59:07', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (129, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\5c08c664de6d434f9df00427d6d6334a_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 10:18:40', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (130, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\8d4129629065479883d2872eaf5c35ff_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 10:31:42', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (131, 'CDSC新员工入职指引 （final)（带超链接）_南京_20220804.docx', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\97d8e31e6bf04265bc34b23b6de7c6e5_CDSC新员工入职指引 （final)（带超链接）_南京_20220804.docx', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 10:33:54', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (132, 'dbDetail.doc', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\1e88df5031264566a025e053f8595f15_dbDetail.doc', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 10:33:54', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (133, 'OA用户使用手册.doc', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\755b9cfc644b422bacc554874e8fd1a7_OA用户使用手册.doc', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 10:33:54', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (134, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\0bd9e53d07fc456b8831f11db26d3ccb_SUSAR报告平台原型确认沟通会.pdf', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 10:33:54', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (135, '办梯控卡流程_20220810.docx', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\a99c4424563b400ea9bd76361ef96f3c_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 10:33:54', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (136, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\87c9a9e0d5224ebfa8aa7a1b7bb4d5fe_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 10:33:54', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (137, 'dbDetail.doc', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\822965405e694e328d13f57c5d801e57_dbDetail.doc', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 13:10:49', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (138, 'OA用户使用手册.doc', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\f6d1db7373574425a544fa0f12ed70e7_OA用户使用手册.doc', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 13:10:49', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (139, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\4da938cd8fdc4efa9b8d26555741d7af_SUSAR报告平台原型确认沟通会.pdf', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 13:10:50', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (140, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\ccf4364d71cb49b7a61577b0ba065275_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 13:10:50', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (141, '办梯控卡流程_20220810.docx', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\93efd7f4495045b082ed252b96cc27f1_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 13:11:27', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (142, '办梯控卡流程_20220810.docx', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\bb5c8f36b718480f85188ba0d14512c9_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\', '2023-02-28 13:15:36', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (143, 'CDSC新员工入职指引 （final)（带超链接）_南京_20220804.docx', 'Marin', 'SUSAR-1316', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\a526f8695ffc4737bd77f88c7062ada5_CDSC新员工入职指引 （final)（带超链接）_南京_20220804.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\', '2023-02-28 13:20:15', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (144, 'dbDetail.doc', 'Marin', 'SUSAR-1316', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\741e063276a34f898099d3f228f37e6e_dbDetail.doc', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\', '2023-02-28 13:20:15', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (145, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SUSAR-1316', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\8ac2230e812a4542b1354f6cc19c3154_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\', '2023-02-28 13:20:15', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (146, 'SUSAR平台演示会议纪要.docx', 'Marin', 'SUSAR-1316', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\83b88bba049c4daab8832152ca8085a9_SUSAR平台演示会议纪要.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\', '2023-02-28 13:30:00', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (147, 'SUSAR平台演示会议纪要.docx', 'Marin', 'SUSAR-1316', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\0efca5e7a13948389508d4c87077d6ac_SUSAR平台演示会议纪要.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\', '2023-02-28 13:56:24', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (148, '办梯控卡流程_20220810.docx', 'Marin', 'SUSAR-1316', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\4e6c1b008db14039a5ad8d2228cef58c_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\', '2023-02-28 13:56:24', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (149, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SUSAR-1316', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\acf6368550d4424b8323103aeb0942cf_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-1316\\', '2023-02-28 13:56:24', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (150, 'OA用户使用手册.doc', 'Marin', 'SUSAR-1317', 'C:\\Work\\SusarFolderTest\\SUSAR-1317\\ed844e7e37654d4aa809c13dce2ad005_OA用户使用手册.doc', 'C:\\Work\\SusarFolderTest\\SUSAR-1317\\', '2023-02-28 13:57:44', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (151, '办梯控卡流程_20220810.docx', 'Marin', 'SUSAR-1317', 'C:\\Work\\SusarFolderTest\\SUSAR-1317\\bd5f3a1307e94e8d87d78ecf3f13ca76_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-1317\\', '2023-02-28 13:57:44', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (152, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SUSAR-1317', 'C:\\Work\\SusarFolderTest\\SUSAR-1317\\e7f19ffc13bb469ea7dcfec2076b4dc9_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-1317\\', '2023-02-28 13:57:44', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (153, 'SUSAR平台演示会议纪要.docx', 'Marin', 'SUSAR-2140', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\0321aa95eee242599009c9efb616082d_SUSAR平台演示会议纪要.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\', '2023-02-28 14:20:36', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (154, '办梯控卡流程_20220810.docx', 'Marin', 'SUSAR-2140', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\0cca388488964f1488abc45697efe7c6_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\', '2023-02-28 14:20:36', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (155, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SUSAR-2140', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\3fb87f030470405aaaea11709bdf27cd_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\', '2023-02-28 14:20:36', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (156, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SUSAR-2140', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\4206c86695214be5bb7bc1a9efa4c550_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\', '2023-02-28 15:04:32', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (157, 'SUSAR平台演示会议纪要.docx', 'Marin', 'SUSAR-2140', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\8552e3284228466d86d8ece9c0803434_SUSAR平台演示会议纪要.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\', '2023-02-28 15:17:42', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (158, '办梯控卡流程_20220810.docx', 'Marin', 'SUSAR-2140', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\a8474c67e64a4cbc94fd5a8881f14467_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\', '2023-02-28 15:17:42', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (159, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SUSAR-2140', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\33b935bc16b54244acc6b44d3adbab28_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\', '2023-02-28 15:17:42', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (160, 'dbDetail.doc', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\7a6a411d561e4aaa952d5d6ec23bf365_dbDetail.doc', 'C:\\Work\\SusarFolderTest\\', '2023-03-01 10:36:00', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (161, 'OA用户使用手册.doc', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\6a4f9e622e2e4b1d87bf8c482944a69b_OA用户使用手册.doc', 'C:\\Work\\SusarFolderTest\\', '2023-03-01 10:36:00', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (162, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\3da65970e6094de3a836b849df5d9138_SUSAR报告平台原型确认沟通会.pdf', 'C:\\Work\\SusarFolderTest\\', '2023-03-01 10:36:00', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (163, 'SUSAR平台演示会议纪要.docx', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\5edc6d07ea614aa0860eb09778016861_SUSAR平台演示会议纪要.docx', 'C:\\Work\\SusarFolderTest\\', '2023-03-01 10:36:00', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (164, '办梯控卡流程_20220810.docx', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\571fe5e5045c4801b603397925b25ae6_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\', '2023-03-01 10:36:00', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (165, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\94fd6184f6b64aae9c87b3a631d9c76e_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\', '2023-03-01 10:36:00', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (166, 'dbDetail.doc', 'Marin', 'SUSAR-2131', 'C:\\Work\\SusarFolderTest\\SUSAR-2131\\950cedb2157d469b946fb444c089effd_dbDetail.doc', 'C:\\Work\\SusarFolderTest\\SUSAR-2131\\', '2023-03-01 11:02:11', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (167, 'OA用户使用手册.doc', 'Marin', 'SUSAR-2131', 'C:\\Work\\SusarFolderTest\\SUSAR-2131\\4886c6bac9544d59b35c2fa4c77cfa86_OA用户使用手册.doc', 'C:\\Work\\SusarFolderTest\\SUSAR-2131\\', '2023-03-01 11:02:11', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (168, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SUSAR-2131', 'C:\\Work\\SusarFolderTest\\SUSAR-2131\\7fc6f221725447f8bea40ed39a67767f_SUSAR报告平台原型确认沟通会.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-2131\\', '2023-03-01 11:02:11', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (169, 'SUSAR平台演示会议纪要.docx', 'Marin', 'SUSAR-2131', 'C:\\Work\\SusarFolderTest\\SUSAR-2131\\d4b6585c89c943158e7afca3c9863a13_SUSAR平台演示会议纪要.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-2131\\', '2023-03-01 11:02:11', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (170, '办梯控卡流程_20220810.docx', 'Marin', 'SUSAR-2131', 'C:\\Work\\SusarFolderTest\\SUSAR-2131\\5167fcefdf3b4d6e99d1a6fb37add1f5_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-2131\\', '2023-03-01 11:02:11', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (171, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SUSAR-2131', 'C:\\Work\\SusarFolderTest\\SUSAR-2131\\9e67823c687e411a87c4861d4379686a_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-2131\\', '2023-03-01 11:02:11', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (172, 'dbDetail.doc', 'Marin', 'SUSAR-1318', 'C:\\Work\\SusarFolderTest\\SUSAR-1318\\da9943616c774ed58847ff5cfd76fa5a_dbDetail.doc', 'C:\\Work\\SusarFolderTest\\SUSAR-1318\\', '2023-03-01 11:05:05', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (173, 'OA用户使用手册.doc', 'Marin', 'SUSAR-1318', 'C:\\Work\\SusarFolderTest\\SUSAR-1318\\def28ebff3134e928cb13b7eb3fe52fa_OA用户使用手册.doc', 'C:\\Work\\SusarFolderTest\\SUSAR-1318\\', '2023-03-01 11:05:05', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (174, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SUSAR-1318', 'C:\\Work\\SusarFolderTest\\SUSAR-1318\\f91422c538464e8e8e77753302cf6162_SUSAR报告平台原型确认沟通会.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-1318\\', '2023-03-01 11:05:06', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (175, 'SUSAR平台演示会议纪要.docx', 'Marin', 'SUSAR-1318', 'C:\\Work\\SusarFolderTest\\SUSAR-1318\\a24893d7b94a41f3b503461313d4f44e_SUSAR平台演示会议纪要.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-1318\\', '2023-03-01 11:05:06', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (176, '办梯控卡流程_20220810.docx', 'Marin', 'SUSAR-1318', 'C:\\Work\\SusarFolderTest\\SUSAR-1318\\4bf5122ab10b411b912ea89d42c1ea06_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-1318\\', '2023-03-01 11:05:06', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (177, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SUSAR-1318', 'C:\\Work\\SusarFolderTest\\SUSAR-1318\\c18d999a583a449789ed1e65f656d199_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-1318\\', '2023-03-01 11:05:06', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (178, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SUSAR-1122', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\ec7ffe4fd820422ba87e9516fe0eed10_SUSAR报告平台原型确认沟通会.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\', '2023-03-01 11:35:56', 'Y', 'Y');
INSERT INTO `file_upload_record` VALUES (179, 'SUSAR平台演示会议纪要.docx', 'Marin', 'SUSAR-1122', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\41c6ebba2f754085b2732de06748c952_SUSAR平台演示会议纪要.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\', '2023-03-01 11:35:56', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (180, '办梯控卡流程_20220810.docx', 'Marin', 'SUSAR-1122', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\407cbd08439b4f0d9f3355de6d766a77_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\', '2023-03-01 11:35:56', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (181, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SUSAR-1122', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\f9062ec80b8f4c0d861a1e1d7e2a18b0_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\', '2023-03-01 11:35:56', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (182, '办梯控卡流程_20220810.docx', 'Marin', 'SUSAR-1122', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\f1f5aa3e4d4d48a2a1ffd171993a5fc2_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\', '2023-03-01 13:11:01', 'Y', 'Y');
INSERT INTO `file_upload_record` VALUES (183, 'dbDetail.doc', 'Marin', 'SUSAR-1122', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\2023-03\\45202a185b5e4389851b142ae2c61c30_dbDetail.doc', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\2023-03', '2023-03-01 13:28:30', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (184, 'OA用户使用手册.doc', 'Marin', 'SUSAR-1122', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\2023-03\\3716e330cd2b41c2a78bd54e48e36233_OA用户使用手册.doc', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\2023-03', '2023-03-01 13:28:30', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (185, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SUSAR-1122', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\2023-03\\f1024d03de604fbcb426213dc345a999_SUSAR报告平台原型确认沟通会.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\2023-03', '2023-03-01 13:28:30', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (186, 'SUSAR平台演示会议纪要.docx', 'Marin', 'SUSAR-1122', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\2023-03\\54089786a4a64282b9c249c6712b6eb5_SUSAR平台演示会议纪要.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\2023-03', '2023-03-01 13:28:30', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (187, '办梯控卡流程_20220810.docx', 'Marin', 'SUSAR-1122', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\2023-03\\3f313dc4660b43728b09366cb21ac102_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\2023-03', '2023-03-01 13:28:30', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (188, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SUSAR-1122', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\2023-03\\db9fbce91d464d5b82ba47f72b142521_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-1122\\2023-03', '2023-03-01 13:28:30', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (189, 'dbDetail.doc', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\2023-03\\a70d920d118a4a868696c2a6d0f65c75_dbDetail.doc', 'C:\\Work\\SusarFolderTest\\2023-03', '2023-03-01 14:15:38', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (190, 'OA用户使用手册.doc', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\2023-03\\aeefc60a36884d4ca240c78e8af2af93_OA用户使用手册.doc', 'C:\\Work\\SusarFolderTest\\2023-03', '2023-03-01 14:15:38', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (191, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\2023-03\\6b1aebf723844243a1a33d13a61c4ac7_SUSAR报告平台原型确认沟通会.pdf', 'C:\\Work\\SusarFolderTest\\2023-03', '2023-03-01 14:15:38', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (192, 'SUSAR平台演示会议纪要.docx', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\2023-03\\9a35e4e1ad6e4da9adfde6e6d07c1463_SUSAR平台演示会议纪要.docx', 'C:\\Work\\SusarFolderTest\\2023-03', '2023-03-01 14:15:38', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (193, '办梯控卡流程_20220810.docx', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\2023-03\\590da5df37e74b248d346470ea97aeff_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\2023-03', '2023-03-01 14:15:38', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (194, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\2023-03\\ac6af1e004fb4f5da0ceb337751c3a0e_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\2023-03', '2023-03-01 14:15:38', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (195, 'dbDetail.doc', 'Marin', 'SUSAR-2140', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\2023-03\\390dea4fce5e4f318cb1fa37e22f90cf_dbDetail.doc', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\2023-03', '2023-03-01 14:31:25', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (196, 'OA用户使用手册.doc', 'Marin', 'SUSAR-2140', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\2023-03\\e0ba486ae24f494c8115c893a341cb4f_OA用户使用手册.doc', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\2023-03', '2023-03-01 14:31:25', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (197, 'SUSAR报告平台原型确认沟通会.pdf', 'Marin', 'SUSAR-2140', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\2023-03\\c2541dc7e1d8401cb4c5611f4ee29a09_SUSAR报告平台原型确认沟通会.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\2023-03', '2023-03-01 14:31:25', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (198, 'SUSAR平台演示会议纪要.docx', 'Marin', 'SUSAR-2140', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\2023-03\\ee517cd106444521b950ae795056549c_SUSAR平台演示会议纪要.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\2023-03', '2023-03-01 14:31:25', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (199, '办梯控卡流程_20220810.docx', 'Marin', 'SUSAR-2140', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\2023-03\\01d47168e1f742dfb2ff21b5f76a4e69_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\2023-03', '2023-03-01 14:31:25', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (200, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SUSAR-2140', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\2023-03\\38f0c50cb387449c943bb1b99f037908_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\SUSAR-2140\\2023-03', '2023-03-01 14:31:25', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (201, 'SUSAR平台演示会议纪要.docx', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\2023-03\\94dd8af5627d4c969c57008a1072dcf5_SUSAR平台演示会议纪要.docx', 'C:\\Work\\SusarFolderTest\\2023-03', '2023-03-01 15:19:25', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (202, '办梯控卡流程_20220810.docx', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\2023-03\\72284256d7494acd861d3d50754281bf_办梯控卡流程_20220810.docx', 'C:\\Work\\SusarFolderTest\\2023-03', '2023-03-01 15:19:25', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (203, '新员工线下培训计划 V3.0 数据科学中心.pdf', 'Marin', 'SusarFolderTest', 'C:\\Work\\SusarFolderTest\\2023-03\\f3e359b3a10c438d82e456f3df024214_新员工线下培训计划 V3.0 数据科学中心.pdf', 'C:\\Work\\SusarFolderTest\\2023-03', '2023-03-01 15:19:25', 'Y', 'N');

SET FOREIGN_KEY_CHECKS = 1;
