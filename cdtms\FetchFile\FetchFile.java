package net.bioknow.cdtms.FetchFile;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import net.bioknow.dbplug.reportformattachout.FaceRFAttachout;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.webutil.tools.Log;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FetchFile implements FaceRFAttachout {

    @Override
    public boolean canUse(String tableid, String projectId) {
        if (tableid.equals("dm_doc_archive")) {
            return true;
        }else {
            return false;
        }

    }

    @Override
    public void appendFile(String tableid, String projectId, Map map, File file) {

        String edmTokenUrl = "https://meduap-tst.hengrui.com:1818";
        String edmRecordUrl = "https://meduap-tst.hengrui.com:1818";
        String edmDownloadUrl = "https://meduap-tst.hengrui.com:1818";
        String secret = "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8";

        //edm文件
        if (map.get("med_archiv_type").equals("2")){
            try {
                DAODataMng daoDataMng = new DAODataMng(projectId);
                List<Map> recordMap = daoDataMng.listRecord("outboard_data_manager");

                List<String> edmFilename = new ArrayList<>();
                for (Map m : recordMap) {
                    if (m.get("zp").equals("6")){
                        edmFilename.add(String.valueOf(m.get("chk_finding")));
                    }
                }

                List<Map> recordMap2 = daoDataMng.listRecord("ext_data_final");
                for (Map m : recordMap2) {
                    edmFilename.add(String.valueOf(m.get("chk_finding")));
                }

                for (String f:edmFilename) {
                    String conditions = "obj.studyid=(select obj2.id from Xsht as obj2 where obj2.studyid='"+projectId+"')" +
                            "and outboard_data_doc like '%" + f + "%' and obj.status != 20";

                    String token = this.getCdtmsToken(edmTokenUrl+"",projectId,secret);
                    String ufn = this.getCdtmsColumnValue(edmRecordUrl+"",token,"outboard_data_manager",conditions,"outboard_data_doc");
                    String filename = ufn.substring(1,ufn.lastIndexOf("*"));
                    ufn = ufn.substring(ufn.lastIndexOf('*')+1,ufn.lastIndexOf('|'));

                    Map<String,String> paramsMap = new HashMap<>();
                    paramsMap.put("token",token);
                    paramsMap.put("tableid", tableid);
                    paramsMap.put("ufn",ufn);

                    HttpURLConnection conn = (HttpURLConnection) this.connectRemoteApi(edmDownloadUrl+"","GET",paramsMap,null);
                    InputStream in = conn.getInputStream();
                    String subPath = "10.02.06 外部数据及相关数据（项目）\\外部数据集";
                    saveInputStreamToFile(in, file, subPath, filename);
                }

            } catch (Exception e) {
                Log.info("打包文件出错");
            }

        }else if (map.get("med_archiv_type").equals("7")) {

        }


    }

    public String getCdtmsToken(String tokenUrl, String projectid, String secret) throws IOException {
        Map<String,String> paramsMap = new HashMap<>();
        paramsMap.put("projectid",projectid);
        paramsMap.put("secret", secret);

        HttpURLConnection conn = (HttpURLConnection) this.connectRemoteApi(tokenUrl,"GET",paramsMap,null);
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(conn.getInputStream());
        String token = jsonNode.get("token").asText();
        return token;
    }

    //连接外部api
    public Object connectRemoteApi(String apiUrl,String type, Map<String,String> params, Map<String,String> headers) throws IOException {
        // 创建URL对象
        String url0 = "";
        if (params != null){
            int index = 0;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (index == 0){
                    url0 = apiUrl + "?" + entry.getKey() + "=" + entry.getValue();
                }else {
                    url0 = url0 + "&" + entry.getKey() + "=" + entry.getValue();
                }
                index++;
            }
        }

        System.out.println(url0);
        URL url = new URL(url0);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod(type);
        conn.setConnectTimeout(60*1000); // 设置连接超时时间
        conn.setReadTimeout(60*1000); // 设置读取超时时间

        if (headers != null){
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                conn.setRequestProperty(entry.getKey(), entry.getValue());
            }
        }

        return conn;

    }

    public String getCdtmsColumnValue(String url, String token, String tableId, String conditions, String column) throws IOException {
        Map<String,String> paramsMap = new HashMap<>();
        paramsMap.put("token",token);
        paramsMap.put("tableid",tableId);
        paramsMap.put("where", URLEncoder.encode(conditions, "UTF-8").replace("+", "%20"));
        paramsMap.put("pagesize", String.valueOf(1));
        paramsMap.put("type","edit");
//        paramsMap.put("pagecount",null);
//        paramsMap.put("username",null);


        HttpURLConnection conn = (HttpURLConnection) this.connectRemoteApi(url,"GET",paramsMap,null);
        BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        StringBuilder stringBuilder = new StringBuilder();

        String line;
        while ((line = reader.readLine()) != null) {
            stringBuilder.append(line);
        }

        JsonParser parser = new JsonParser();
        JsonArray jsonArray = parser.parse(stringBuilder.toString()).getAsJsonArray();
        JsonObject jsonObject = jsonArray.get(0).getAsJsonObject();
        String value = jsonObject.get(column).toString();
        return value;
    }

    public static void saveInputStreamToFile(InputStream inputStream, File baseDir, String subPath, String fileName) throws IOException {
        // 构建目标路径
        Path targetDir = baseDir.toPath().resolve(subPath);
        Path targetFile = targetDir.resolve(fileName);

        // 创建所有必要的父目录
        Files.createDirectories(targetDir);

        // 将输入流写入文件，替换已存在的文件
        Files.copy(inputStream, targetFile, StandardCopyOption.REPLACE_EXISTING);
    }

}
