from airflow import DAG
from airflow.operators.python import PythonOperator
import os
import urllib.parse
from datetime import datetime,timedelta 
# DAG参数

# 下载文件函数 
def download_file(**kwargs):
    url = kwargs['url']
    url_split = urllib.parse.urlsplit(url)
    query_params = urllib.parse.parse_qs(url_split.query)
    filename = query_params['studyCode'][0]
    wget_cmd = f'wget -O /tmp/hsperfdata_root/sas_download_data/{filename}.zip "{url}"'
    os.system(wget_cmd)
    return filename

# 上传文件函数
def upload_file(**kwargs):
    local_file = kwargs['local_file']
    filename = kwargs['filename']
    local_file = local_file.format(filename=filename)
    # 获取minios3/raw目录下的文件列表
    list_cmd = 'mc ls minios3/raw'
    result = os.popen(list_cmd).read()

    # 检查是否有同名文件
    if filename in result:
        # 有同名文件,获取本地文件和MinIO上的文件md5
        local_md5_cmd = f'md5sum {local_file}'
        local_md5 = os.popen(local_md5_cmd).read().split(' ')[0]
        
        remote_md5_cmd = f'mc cat minios3/raw/{filename} | md5sum' 
        remote_md5 = os.popen(remote_md5_cmd).read().split(' ')[0]

        # 比较md5,如果不同则上传并覆盖
        if local_md5 != remote_md5:
            upload_cmd = f'mc cp {local_file} minios3/raw --tags "key1=HRTAU&key2={filename}"'
            os.system(upload_cmd)
    else:
        # 无同名文件,直接上传
        upload_cmd = f'mc cp {local_file} minios3/raw --tags "key1=HRTAU&key2={filename}"'
        os.system(upload_cmd)

# 定义DAG  
with DAG('upload_sas_data',schedule_interval='@daily',start_date=datetime(2023, 5, 17)) as dag:
# 任务1:下载文件
	task1 = PythonOperator(
		task_id ='download_file', 
		python_callable = download_file,
		op_kwargs={
			'url': "https://clinical.hengruipharma.com:88/report/report/getRemoteFile?studyCode=HRS9531-102&environment=pro&loginId=<EMAIL>&pwd=Hr123456&filename=HRS9531-102-Daily"
		},
		dag=dag
)

# 任务2:上传文件到服务器 
	filename = task1.execute_callable()
	task2 = PythonOperator(
		task_id ='upload_file',
		python_callable = upload_file, 
		op_kwargs={
			'local_file': '/tmp/hsperfdata_root/sas_download_data/{filename}.zip',
			'filename': filename
		},
		dag=dag 
	)

# 设置任务顺序
task1 >> task2
