from datetime import timedelta, datetime
import airflow
from airflow import DAG
from airflow.contrib.operators.ssh_operator import SSHOperator
default_args = {
    'owner': 'zhouhui',
    'depends_on_past': False,
    'start_date': datetime.now() - timedelta(minutes=20)
}
dag = DAG(dag_id='testing_ssh_conn',
          default_args=default_args,
          schedule_interval="* 0/1 * * * ?"

t1_bash = """
mkdir /home/<USER>
"""
task1 = SSHOperator(
    ssh_conn_id='10.10.5.51',
    task_id='test_ssh_operator',
    command=t1_bash,
    dag=dag)
      
task1