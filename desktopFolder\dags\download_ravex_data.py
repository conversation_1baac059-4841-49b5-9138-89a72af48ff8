# 获取建库端数据
import os
import time
import datetime
import xmltodict
import json
from rwslib import RWSConnection
from rwslib.rws_requests import StudyVersionsRequest, StudyVersionRequest 
from rwslib.rws_requests.biostats_gateway import FormDataRequest 
# =============================================================================
# 创建连接
# =============================================================================
def creatConn(username, password):
    conn = RWSConnection('hengruimedicine-ravex', username, password)
    return conn
def load_json(xml_path):
    #获取xml文件
    xml_file = open(xml_path, 'r', encoding='utf_8')
    #读取xml文件内容
    xml_str = xml_file.read()
    #将读取的xml内容转为json
    json = xmltodict.parse(xml_str)
    return json
class UTF8FormDataRequest(FormDataRequest):
    def result(self, response):
        response.encoding = 'utf-8-mul'
        return response.text 
# =============================================================================
# 主函数
# =============================================================================
def mainFunction():
    # 创建连接
    conn = creatConn('RWSQUERY', 'Hryy8999')
#     study_list = ['FZPL-III-302', 'FZPL-III-303', 'HR-BLTN-III-NSCLC', 'HRS9531-101', 'HR-SP2086-304' 
#                     , 'HR-TPO-CIT-III', 'SHR0302-301', 'SHR0302-302', 'SHR0302-303', 'SHR-1209-301' 
#                     , 'SHR-1209-302', 'SHR-1209-303', 'SHR-1210-II-217', 'SHR-1210-III-322' 
#                     , 'SHR-1210-III-323', 'SHR-1210-III-324', 'SHR-1210-III-329', 'SHR-1314-204' 
#                     , 'SHR-1314-205', 'SHR-1314-301', 'SHR-1314-302', 'SHR-1316-III-302' 
#                     , 'SHR-1316-III-302-EN', 'SHR-1316-III-303-EN', 'SHR-1701-II-203', 'SHR-1701-II-207' 
#                     , 'SHR-1701-III-301', 'SHR-1701-III-307', 'SHR-1701-III-308', 'SHR-1701-III-309' 
#                     , 'SHR3162-I-113', 'SHR3162-II-202', 'SHR3162-III-305', 'SHR3680-III-302' 
#                     , 'SHR3824-SP2086-MET-301', 'SHR4640-303', 'SHR6390-III-303', 'SHR7280-104', 'SHR-A1811-I-101' 
#                     , 'SHR-A1811-I-102', 'SHR-A1811-I-103', 'SHR-A1811-II-201', 'SHR-A1811-II-202' 
#                     , 'SHR-A1811-II-203', 'SHR-A1811-III-301', 'SHR-A1904-I-101', 'SHR-A1904-I-102' 
#                     , 'SHR-A2009-I-101', 'SHR-A2009-I-102']
    study_list = ['SHR-1701-001AUS'] 
#     study_list = ['FZPL-III-302']
    start_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
    print(start_time)
    for study in study_list:
        t1 = time.perf_counter()
        versions = conn.send_request(StudyVersionsRequest(study))
        versions_len = len(versions)
        version_first = versions[0]
        resp_xml = conn.send_request(StudyVersionRequest(study, int(version_first.oid)))
        with open('/tmp/hsperfdata_root/api_download_data/architect_xml/' + study + ".xml", "w", encoding='utf_8') as f:
            f.write(resp_xml)
            f.close()  

        resp_json = load_json('/tmp/hsperfdata_root/api_download_data/architect_xml/' + study + ".xml")
        with open('/tmp/hsperfdata_root/api_download_data/architect_json/' + study + ".json", 'w', encoding='utf_8') as write_f:
            json.dump(resp_json, write_f, indent=4, ensure_ascii=True)  

        with open('/tmp/hsperfdata_root/api_download_data/architect_json/' + study + ".json") as read_json:
            json_content = read_json.read()
            parsed_json = json.loads(json_content)
            project_forms_json = parsed_json['ODM']['Study']['MetaDataVersion']['FormDef']
            project_forms = []
            if len(project_forms_json) > 0:
                for project_form_json in project_forms_json:  
                    project_forms.append(project_form_json['@OID'])  

            if not os.path.exists("/tmp/hsperfdata_root/api_download_data/temp/" + study + "/"):
                os.makedirs("/tmp/hsperfdata_root/api_download_data/temp/" + study + "/")  

            for form_oid in project_forms:
                if form_oid.upper() != 'DUMMY':
                    form_data = conn.send_request(UTF8FormDataRequest(study, 'Prod', 'REGULAR', form_oid, dataset_format="csv"))  
                    with open("/tmp/hsperfdata_root/api_download_data/temp/" + study + "/" + form_oid + ".csv", 'w', encoding='utf_8', newline='') as f:
                        f.write(form_data)  
                        f.close()
        t2 =time.perf_counter()
        print(study + "(" + version_first.oid + '):项目程序运行时间:%s毫秒' % ((t2 - t1)))  

    end_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
    print(end_time)  
mainFunction()