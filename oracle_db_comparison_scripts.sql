-- =====================================================
-- Oracle Database Comparison Scripts
-- After Data Synchronization Validation
-- =====================================================

-- =====================================================
-- 1. SETUP: Create Database Link to Source Database
-- =====================================================
-- Replace with your actual connection details
/*
CREATE DATABASE LINK source_db_link
CONNECT TO your_username IDENTIFIED BY your_password
USING '(DESCRIPTION=
    (ADDRESS=(PROTOCOL=TCP)(HOST=source_host)(PORT=1521))
    (CONNECT_DATA=(SERVICE_NAME=source_service_name))
)';
*/

-- =====================================================
-- 2. CREATE COMPARISON RESULT TABLES
-- =====================================================

-- Table to store comparison results
CREATE TABLE db_comparison_results (
    comparison_id NUMBER GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    comparison_date DATE DEFAULT SYSDATE,
    table_name VARCHAR2(128),
    comparison_type VARCHAR2(50), -- 'ROW_COUNT', 'CHECKSUM', 'STRUCTURE'
    source_value VARCHAR2(4000),
    target_value VARCHAR2(4000),
    match_status VARCHAR2(10), -- 'MATCH', 'MISMATCH'
    difference_details CLOB,
    created_date DATE DEFAULT SYSDATE
);

-- Table to log comparison process
CREATE TABLE db_comparison_log (
    log_id NUMBER GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    log_date DATE DEFAULT SYSDATE,
    activity VARCHAR2(200),
    status VARCHAR2(20), -- 'SUCCESS', 'ERROR', 'WARNING'
    details CLOB
);

-- =====================================================
-- 3. ROW COUNT COMPARISON
-- =====================================================

-- Compare row counts for all tables
INSERT INTO db_comparison_results (table_name, comparison_type, source_value, target_value, match_status)
WITH source_counts AS (
    SELECT table_name, num_rows as row_count
    FROM user_tables@source_db_link
    WHERE table_name NOT LIKE 'BIN$%'
),
target_counts AS (
    SELECT table_name, num_rows as row_count
    FROM user_tables
    WHERE table_name NOT LIKE 'BIN$%'
)
SELECT 
    COALESCE(s.table_name, t.table_name) as table_name,
    'ROW_COUNT' as comparison_type,
    NVL(TO_CHAR(s.row_count), 'TABLE_MISSING') as source_value,
    NVL(TO_CHAR(t.row_count), 'TABLE_MISSING') as target_value,
    CASE 
        WHEN s.row_count = t.row_count THEN 'MATCH'
        WHEN s.row_count IS NULL THEN 'SOURCE_MISSING'
        WHEN t.row_count IS NULL THEN 'TARGET_MISSING'
        ELSE 'MISMATCH'
    END as match_status
FROM source_counts s
FULL OUTER JOIN target_counts t ON s.table_name = t.table_name;

-- =====================================================
-- 4. ACTUAL ROW COUNT COMPARISON (More Accurate)
-- =====================================================

-- For critical tables, get actual counts (replace 'EMPLOYEES' with your table names)
DECLARE
    v_source_count NUMBER;
    v_target_count NUMBER;
    v_table_name VARCHAR2(128);
    
    CURSOR table_cursor IS
        SELECT table_name FROM user_tables 
        WHERE table_name IN ('EMPLOYEES', 'DEPARTMENTS', 'CUSTOMERS') -- Add your critical tables
        ORDER BY table_name;
BEGIN
    FOR rec IN table_cursor LOOP
        v_table_name := rec.table_name;
        
        -- Get source count
        EXECUTE IMMEDIATE 'SELECT COUNT(*) FROM ' || v_table_name || '@source_db_link' 
        INTO v_source_count;
        
        -- Get target count
        EXECUTE IMMEDIATE 'SELECT COUNT(*) FROM ' || v_table_name 
        INTO v_target_count;
        
        -- Insert comparison result
        INSERT INTO db_comparison_results (
            table_name, comparison_type, source_value, target_value, match_status
        ) VALUES (
            v_table_name,
            'ACTUAL_ROW_COUNT',
            TO_CHAR(v_source_count),
            TO_CHAR(v_target_count),
            CASE WHEN v_source_count = v_target_count THEN 'MATCH' ELSE 'MISMATCH' END
        );
        
        -- Log progress
        INSERT INTO db_comparison_log (activity, status, details)
        VALUES ('Row count comparison', 'SUCCESS', 
                'Table: ' || v_table_name || ', Source: ' || v_source_count || ', Target: ' || v_target_count);
    END LOOP;
    
    COMMIT;
END;
/

-- =====================================================
-- 5. SCHEMA STRUCTURE COMPARISON
-- =====================================================

-- Compare table structures (columns, data types, lengths)
INSERT INTO db_comparison_results (table_name, comparison_type, source_value, target_value, match_status, difference_details)
WITH structure_diff AS (
    SELECT 
        COALESCE(s.table_name, t.table_name) as table_name,
        COALESCE(s.column_name, t.column_name) as column_name,
        s.data_type || '(' || NVL(TO_CHAR(s.data_length), 'NULL') || ')' as source_structure,
        t.data_type || '(' || NVL(TO_CHAR(t.data_length), 'NULL') || ')' as target_structure,
        CASE 
            WHEN s.column_name IS NULL THEN 'TARGET_ONLY'
            WHEN t.column_name IS NULL THEN 'SOURCE_ONLY'
            WHEN s.data_type != t.data_type OR s.data_length != t.data_length THEN 'DIFFERENT'
            ELSE 'MATCH'
        END as diff_type
    FROM user_tab_columns@source_db_link s
    FULL OUTER JOIN user_tab_columns t 
        ON s.table_name = t.table_name AND s.column_name = t.column_name
    WHERE s.table_name NOT LIKE 'BIN$%' OR t.table_name NOT LIKE 'BIN$%'
)
SELECT 
    table_name,
    'STRUCTURE' as comparison_type,
    source_structure as source_value,
    target_structure as target_value,
    CASE WHEN diff_type = 'MATCH' THEN 'MATCH' ELSE 'MISMATCH' END as match_status,
    'Column: ' || column_name || ', Difference: ' || diff_type as difference_details
FROM structure_diff
WHERE diff_type != 'MATCH';

-- =====================================================
-- 6. DATA CHECKSUM COMPARISON (For Specific Tables)
-- =====================================================

-- Example for EMPLOYEES table - modify for your tables
DECLARE
    v_source_checksum NUMBER;
    v_target_checksum NUMBER;
    v_table_name VARCHAR2(128) := 'EMPLOYEES'; -- Change to your table name
BEGIN
    -- Calculate source checksum (modify column list for your table)
    EXECUTE IMMEDIATE 
        'SELECT SUM(ORA_HASH(employee_id||'||'''|'''||'||
         'NVL(first_name,''NULL'')||'||'''|'''||'||
         'NVL(last_name,''NULL'')||'||'''|'''||'||
         'NVL(email,''NULL'')||'||'''|'''||'||
         'NVL(TO_CHAR(hire_date,''YYYY-MM-DD''),''NULL''))) 
         FROM ' || v_table_name || '@source_db_link'
    INTO v_source_checksum;
    
    -- Calculate target checksum
    EXECUTE IMMEDIATE 
        'SELECT SUM(ORA_HASH(employee_id||'||'''|'''||'||
         'NVL(first_name,''NULL'')||'||'''|'''||'||
         'NVL(last_name,''NULL'')||'||'''|'''||'||
         'NVL(email,''NULL'')||'||'''|'''||'||
         'NVL(TO_CHAR(hire_date,''YYYY-MM-DD''),''NULL''))) 
         FROM ' || v_table_name
    INTO v_target_checksum;
    
    -- Insert comparison result
    INSERT INTO db_comparison_results (
        table_name, comparison_type, source_value, target_value, match_status
    ) VALUES (
        v_table_name,
        'DATA_CHECKSUM',
        TO_CHAR(v_source_checksum),
        TO_CHAR(v_target_checksum),
        CASE WHEN v_source_checksum = v_target_checksum THEN 'MATCH' ELSE 'MISMATCH' END
    );
    
    COMMIT;
END;
/

-- =====================================================
-- 7. FIND MISSING RECORDS (Primary Key Based)
-- =====================================================

-- Example for EMPLOYEES table - find records that exist in one DB but not the other
INSERT INTO db_comparison_results (table_name, comparison_type, source_value, target_value, match_status, difference_details)
WITH missing_records AS (
    SELECT 
        'EMPLOYEES' as table_name,
        employee_id,
        'SOURCE_ONLY' as location
    FROM employees@source_db_link
    WHERE employee_id NOT IN (SELECT employee_id FROM employees WHERE employee_id IS NOT NULL)
    
    UNION ALL
    
    SELECT 
        'EMPLOYEES' as table_name,
        employee_id,
        'TARGET_ONLY' as location
    FROM employees
    WHERE employee_id NOT IN (SELECT employee_id FROM employees@source_db_link WHERE employee_id IS NOT NULL)
)
SELECT 
    table_name,
    'MISSING_RECORDS' as comparison_type,
    CASE WHEN location = 'SOURCE_ONLY' THEN TO_CHAR(employee_id) ELSE 'N/A' END as source_value,
    CASE WHEN location = 'TARGET_ONLY' THEN TO_CHAR(employee_id) ELSE 'N/A' END as target_value,
    'MISMATCH' as match_status,
    'Employee ID ' || employee_id || ' exists only in ' || location as difference_details
FROM missing_records;

-- =====================================================
-- 8. STATISTICAL COMPARISON
-- =====================================================

-- Compare basic statistics for numeric columns
INSERT INTO db_comparison_results (table_name, comparison_type, source_value, target_value, match_status, difference_details)
WITH stats_comparison AS (
    SELECT 
        'EMPLOYEES' as table_name,
        'MIN_SALARY' as stat_type,
        (SELECT TO_CHAR(MIN(salary)) FROM employees@source_db_link) as source_stat,
        (SELECT TO_CHAR(MIN(salary)) FROM employees) as target_stat
    FROM dual
    
    UNION ALL
    
    SELECT 
        'EMPLOYEES' as table_name,
        'MAX_SALARY' as stat_type,
        (SELECT TO_CHAR(MAX(salary)) FROM employees@source_db_link) as source_stat,
        (SELECT TO_CHAR(MAX(salary)) FROM employees) as target_stat
    FROM dual
    
    UNION ALL
    
    SELECT 
        'EMPLOYEES' as table_name,
        'AVG_SALARY' as stat_type,
        (SELECT TO_CHAR(ROUND(AVG(salary), 2)) FROM employees@source_db_link) as source_stat,
        (SELECT TO_CHAR(ROUND(AVG(salary), 2)) FROM employees) as target_stat
    FROM dual
)
SELECT 
    table_name,
    'STATISTICS' as comparison_type,
    source_stat as source_value,
    target_stat as target_value,
    CASE WHEN source_stat = target_stat THEN 'MATCH' ELSE 'MISMATCH' END as match_status,
    'Statistic: ' || stat_type as difference_details
FROM stats_comparison;

-- =====================================================
-- 9. VIEW COMPARISON RESULTS
-- =====================================================

-- Summary of all comparisons
SELECT 
    comparison_type,
    match_status,
    COUNT(*) as count_of_differences
FROM db_comparison_results
GROUP BY comparison_type, match_status
ORDER BY comparison_type, match_status;

-- Detailed mismatches
SELECT 
    table_name,
    comparison_type,
    source_value,
    target_value,
    difference_details,
    comparison_date
FROM db_comparison_results
WHERE match_status != 'MATCH'
ORDER BY table_name, comparison_type;

-- Tables with issues
SELECT DISTINCT 
    table_name,
    COUNT(*) as issue_count
FROM db_comparison_results
WHERE match_status != 'MATCH'
GROUP BY table_name
ORDER BY issue_count DESC;

-- =====================================================
-- 10. CLEANUP (Optional)
-- =====================================================

-- Drop comparison tables if needed
-- DROP TABLE db_comparison_results;
-- DROP TABLE db_comparison_log;

-- Drop database link if needed
-- DROP DATABASE LINK source_db_link;

-- =====================================================
-- 11. PERFORMANCE MONITORING QUERY
-- =====================================================

-- Check comparison performance
SELECT 
    activity,
    status,
    COUNT(*) as occurrence_count,
    MIN(log_date) as first_occurrence,
    MAX(log_date) as last_occurrence
FROM db_comparison_log
GROUP BY activity, status
ORDER BY activity, status;
