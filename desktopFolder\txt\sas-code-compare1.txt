sas.submit('''%global system studyid root version dir lang;''')
	sas.submit('''proc datasets lib=work nolist kill; run;quit;%let dir=%sysfunc(getoption(work));x "cd &dir";x "mkdir ./pgm";x 'mc find minios3/pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';%include "&dir/pgm/*.sas";''')
	sas.submit('''%let studyid=SHR-1802-II-202;''')
	sas.submit('''%m_post2s3(studyid=&studyid.);''')
	sas.submit('''%m_gets3data(studyid=&studyid.,data=@);''')
	sas.submit('''%let lang="CH";''')
	sas.submit('''option mprint symbolgen validvarname=v7;''')
	sas.submit('%m_medical_joinaecm;')
	sas.submit('%clin_consistency;')
	sas.submit('''%m_medical_aedrugeq;''')
	sas.submit('''%include "./pgm/m_out2s3.sas);''')
	sas.submit('''%m_out2s3(studyid=&studyid.);''')
	sas.submit('''%m_med_formfie;''')
	sas.submit('''%m_med_stadm;''')
	sas.submit('''%m_med_starand;''')
	sas.submit('''%m_med_stasub;''')
	sas.submit('''%m_med_staae;''')
	sas.submit('''%m_med_stalb;''')
	sas.submit('''%m_create_hrtaubuild_lab;''')
	sas.submit('''%m_derive_hrtaubuild(folder=csvdata_lab);''')
	sas.submit('''%m_create_hrtau_lab(is_als=N);''')
	sas.submit('''%M_std_dvs_labreview;''')
	sas.submit('''%m_out2s3T(studyid=&studyid.,suffix=labctc);''')
	sas.submit('''%m_med_stavs;''')
	sas.submit('''%M_std_dvs_vsview;''')
	sas.submit('''%m_out2s3T(studyid=&studyid.,suffix=vsview);''')
	sas.submit('''%m_med_staeg;''')
	sas.submit('''%M_std_dvs_egview;''')
	sas.submit('''%m_out2s3T(studyid=&studyid.,suffix=egview);''')
	sas.submit('''%let studyid = SHR-1802-II-202;''')
	sas.submitLOG('''%M_gen_pro_coding(formoid=ae,folder=,output=Y);''')
	sas.submit('''%readin_coding;''')
	sas.submit('''%m_med_dseos_pre_process(dseos=dseos,dsstdat =dsstdat);''')
	sas.submit('''%m_med_hrtau_dseot_pre_process(dseot=dseot, dsstdat = dsstdat,dsdecod = dsdecod,dsterm = dsterm);''')

	sas.submit('''%m_med_dsrand_pre_process(dsrand=exi,regime = extrt);''')
	sas.submit('''proc sort nodupkey; by _all_; run;''')
	sas.submit('''%m_med_hrtau_ae_pre_process(aerel = aerel, ctcae = aetoxgr);''')
	sas.submit('''%m_med_hrtau_ex_pre_process(data = exi,expdost=expdose,exdosadj=);''')
	sas.submit('''%ae_ex_process;''')
	sas.submit('''%label_ae_ex;''')
	sas.submit('''data ae_ex_med0; set out.ae_ex; run;''')
	sas.submit('''data ex_med0; set out.ex; run;''')

	sas.submit('''%m_med_dsrand_pre_process(dsrand=exi1,regime = extrt);''')
	sas.submit('''proc sort nodupkey; by _all_; run;''')
	sas.submit('''%m_med_hrtau_ae_pre_process(aerel = aerel1, ctcae = aetoxgr);''')
	sas.submit('''%m_med_hrtau_ex_pre_process(data = exi1,expdost=expdose,exdosadj=);''')
	sas.submit('''%ae_ex_process;''')
	sas.submit('''%label_ae_ex;''')
	sas.submit('''data ae_ex_med1; set out.ae_ex; run;''')
	sas.submit('''data ex_med1; set out.ex; run;''')

	sas.submit('''%m_med_dsrand_pre_process(dsrand=exi2,regime = extrt);''')
	sas.submit('''proc sort nodupkey; by _all_; run;''')
	sas.submit('''%m_med_hrtau_ae_pre_process(aerel = aerel2, ctcae = aetoxgr);''')
	sas.submit('''%m_med_hrtau_ex_pre_process(data = exi2,expdost=expdose,exdosadj=exdosadj);''')
	sas.submit('''%ae_ex_process;''')
	sas.submit('''%label_ae_ex;''')
	sas.submit('''data ae_ex_med2; set out.ae_ex; run;''')
	sas.submit('''data ex_med2; set out.ex; run;''')

	sas.submit('''%m_med_dsrand_pre_process(dsrand=exi3,regime = extrt);''')
	sas.submit('''proc sort nodupkey; by _all_; run;''')
	sas.submit('''%m_med_hrtau_ae_pre_process(aerel = aerel3, ctcae = aetoxgr);''')
	sas.submit('''%m_med_hrtau_ex_pre_process(data = exi3,expdost=expdose,exdosadj=exdosadj);''')
	sas.submit('''%ae_ex_process;''')
	sas.submit('''%label_ae_ex;''')
	sas.submit('''data ae_ex_med3; set out.ae_ex; run;''')
	sas.submit('''data ex_med3; set out.ex; run;''')

	sas.submit('''%m_med_dsrand_pre_process(dsrand=exi4,regime = extrt);''')
	sas.submit('''proc sort nodupkey; by _all_; run;''')
	sas.submit('''%m_med_hrtau_ae_pre_process(aerel = aerel4, ctcae = aetoxgr);''')
	sas.submit('''%m_med_hrtau_ex_pre_process(data = exi4,expdost=expdose,exdosadj=exdosadj);''')
	sas.submit('''%ae_ex_process;''')
	sas.submit('''%label_ae_ex;''')
	sas.submit('''data ae_ex_med4; set out.ae_ex; run;''')
	sas.submit('''data ex_med4; set out.ex; run;''')

	sas.submit('''%m_med_dsrand_pre_process(dsrand=exi5,regime = extrt);''')
	sas.submit('''proc sort nodupkey; by _all_; run;''')
	sas.submit('''%m_med_hrtau_ae_pre_process(aerel = aerel5, ctcae = aetoxgr);''')
	sas.submit('''%m_med_hrtau_ex_pre_process(data = exi5,expdost=expdose,exdosadj=exdosadj);''')
	sas.submit('''%ae_ex_process;''')
	sas.submit('''%label_ae_ex;''')
	sas.submit('''data ae_ex_med5; set out.ae_ex; run;''')
	sas.submit('''data ex_med5; set out.ex; run;''')

	sas.submit('''%m_med_dsrand_pre_process(dsrand=exi6,regime = extrt);''')
	sas.submit('''proc sort nodupkey; by _all_; run;''')
	sas.submit('''%m_med_hrtau_ae_pre_process(aerel = aerel6, ctcae = aetoxgr);''')
	sas.submit('''%m_med_hrtau_ex_pre_process(data = exi6,expdost=expdose,exdosadj=exdosadj);''')
	sas.submit('''%ae_ex_process;''')
	sas.submit('''%label_ae_ex;''')
	sas.submit('''data ae_ex_med6; set out.ae_ex; run;''')
	sas.submit('''data ex_med6; set out.ex; run;''')


	sas.submit('''data out.ae_ex(label='AE_EX');set ae_ex_med:; run;''')
	sas.submit('''data out.ex(label='EX');set ex_med:; run;''')

	sas.submitLOG('''%m_out2s3T(studyid=&studyid.,suffix=AEEX);''')	
	sas.endsas()   