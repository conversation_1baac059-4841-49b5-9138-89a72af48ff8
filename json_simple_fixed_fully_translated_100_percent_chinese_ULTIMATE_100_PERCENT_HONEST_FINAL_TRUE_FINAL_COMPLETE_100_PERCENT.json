[{"delinnerdata": "false", "id": "nr", "lastmodifytime": "1502269394790", "modifyreason": "false", "name": "网站内容", "nameen": "Website Content", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "F83BFBB9776551042444011F3BCAD39F"}, {"delinnerdata": "false", "id": "ljszb", "lastmodifytime": "1502269538201", "modifyreason": "false", "name": "链接设置", "nameen": "<PERSON>s", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "F73258355BA17A935A82449590CF7BCA"}, {"delinnerdata": "false", "id": "tjszb", "lastmodifytime": "1502269266995", "modifyreason": "false", "name": "统计设置", "nameen": "Statistics Settings", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "85D6C25D3DFE8C60A7D3421611324F47"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "gzlgzb", "lastmodifytime": "1504008486281", "modifyreason": "false", "name": "工作流定义", "nameen": "Workflow Definitions", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showreturnbtn": "false", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "DD0F380E968566AA6F0B897EDC906A9A"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "gzlmxb", "lastmodifytime": "1503471964906", "modifyreason": "false", "name": "工作流定义详情", "nameen": "Workflow Definition Details", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showreturnbtn": "false", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "B45ED0302888F265E61E12AF4A9DE674"}, {"delinnerdata": "false", "id": "kbmdrjssdb", "lastmodifytime": "1502269634500", "modifyreason": "false", "name": "跨功能角色设置", "nameen": "Cross-Function Role Setting", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "E02EEE2567D3BF5AC8720517D87FB502"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "ztzd", "lastmodifytime": "1502183803063", "modifyreason": "false", "name": "工作流状态代码列表", "nameen": "Workflow Status Codelists", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showreturnbtn": "false", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "994EFF372DAE0F18DC2093FA5503C7D6"}, {"delinnerdata": "false", "id": "spjlb", "lastmodifytime": "1502178034451", "modifyreason": "false", "name": "工作流记录", "nameen": "Workflow Records", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showreturnbtn": "false", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "B2A288694DB8E1B5BAC0AA430EDBFE51"}, {"delinnerdata": "false", "id": "spmxb", "lastmodifytime": "1502178503543", "modifyreason": "false", "name": "工作流详情", "nameen": "Workflow Details", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showreturnbtn": "false", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "3BDC2D826DCCEEEAB05C436CBBFB4811"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "xmjs", "lastmodifytime": "1502270065169", "modifyreason": "false", "name": "角色", "nameen": "Role", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showreturnbtn": "false", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "false", "uuid": "5D1371BA632D9438DC246019E82A4BBF"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "jssj", "lastmodifytime": "1502269863913", "modifyreason": "false", "name": "角色数据", "nameen": "Role Data", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "73574C441995A446627C9BDCD884F7AC"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "ggsyr", "lastmodifytime": "1502269970914", "modifyreason": "false", "name": "系统角色映射", "nameen": "System Role Mapping", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showreturnbtn": "false", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "191CE29C8F0407C1E50C92AFC78173C7"}, {"delinnerdata": "false", "id": "dcpzb", "lastmodifytime": "1502173758333", "modifyreason": "false", "name": "导出配置定义", "nameen": "Export Configuration Definition", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "499E056FAF88E74483E61DF51ABE73AA"}, {"id": "switch_button_show", "name": "开关按钮显示", "note": "", "uuid": "51672BD6293E405A82F7D5FBFFEA4839"}, {"id": "field_setting", "name": "閹绘劗銇氱�涙顔岀拋鎯х暰", "note": "", "uuid": "24300F5D434F42C086DD8347C981EC8D"}, {"id": "content_setting", "name": "内容设置", "note": "", "uuid": "69FFA5E94D4349D8993A0FDB7F8AAA8F"}, {"delinnerdata": "false", "id": "view_filter_set", "modifyreason": "false", "name": "视图过滤设置", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "false", "uuid": "0C20A8B874294381A35D8269046DB756"}, {"id": "work_his_m", "name": "工作历史记录", "note": "", "uuid": "8B0CDC93AB844317BF82239FB16041A5"}, {"id": "work_detail", "name": "工作详情", "note": "", "uuid": "C1B6970069194BC781BD8BEB7D29186E"}, {"delinnerdata": "false", "id": "log_event", "lastmodifytime": "1435654585185", "menuinfo": "aaa", "modifyreason": "true", "name": "缁崵绮洪弮銉ョ箶", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "B170466ABD7B4703B7651DB5ECCC7953"}, {"delinnerdata": "false", "id": "log_event_2021_06", "lastmodifytime": "1435654585185", "menuinfo": "aaa", "modifyreason": "true", "name": "缁崵绮洪弮銉ョ箶", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "6D608CC86EE24ABEA80F8A53BFBB8135"}, {"delinnerdata": "false", "id": "modify_history", "lastmodifytime": "1516848443608", "modifyreason": "false", "name": "修改历史", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "E144C033B3BB4CB7A4E5D0198FED2D63"}, {"id": "wf_signature", "name": "工作流签名", "note": "", "uuid": "FDF8573DFEE5478288B141178B92EE5E"}, {"id": "entry_guide", "name": "入口指南", "note": "", "uuid": "839E345D142044F8A9AFB0645DB9619F"}, {"delinnerdata": "false", "id": "log_event_2022_05", "lastmodifytime": "1435654585185", "menuinfo": "aaa", "modifyreason": "true", "name": "缁崵绮洪弮銉ョ箶", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "6C6CAE8C56924F82B51A5651FBE41612"}, {"delinnerdata": "false", "id": "log_event_2022_07", "lastmodifytime": "1435654585185", "menuinfo": "aaa", "modifyreason": "true", "name": "缁崵绮洪弮銉ョ箶", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "C56C90AE8B4F43BAB1419EB32D15155E"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "", "id": "mdbook", "modifyreason": "false", "name": "医学词典", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "F4D334419A8A435385723D1F5A3E9EE8"}, {"delinnerdata": "false", "id": "attachment", "modifyreason": "false", "name": "附件", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "37D6D494C65B4F2C8B80F206C37E81A0"}, {"id": "mdchapter_menuid", "name": "mdchapter_menuid", "note": "", "uuid": "AAF28B9FDE4641EE8E52B45C9BE4163A"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "mdchapter", "modifyreason": "false", "name": "医学章节", "nameen": "", "newtoedit": "true", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "2676F9AD28294FE8838EBFBA394B727F"}, {"delinnerdata": "false", "id": "log_event_2023_06", "lastmodifytime": "1435654585185", "menuinfo": "aaa", "modifyreason": "true", "name": "缁崵绮洪弮銉ョ箶", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "1F105B15153444F19489C5A66FA486EF"}, {"id": "eclinichistory", "name": "电子临床历史记录", "note": "", "uuid": "0A561C4B47A046B09F3672848879E46C"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "esign_engine", "modifyreason": "false", "name": "esign_engine", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "3A4807B062DD43A483455BD546CCB1CA"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "esign_instance", "modifyreason": "false", "name": "电子签名实例", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "false", "uuid": "74328B6ECDE447DB9937E07F1C2241C5"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "", "id": "esign_file", "modifyreason": "false", "name": "电子签名文件", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "B34352EBC1984DFE90771599334A2C47"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "esign_signer", "modifyreason": "false", "name": "电子签名签署者", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "C9F4FAD06A3448EEB3265462EEE5946D"}, {"id": "esign_log", "name": "esign_log", "note": "", "uuid": "********************************"}, {"id": "attach_history_log", "name": "附件历史日志", "note": "", "uuid": "24B685666B854FEC854FB0FADC6D7F7E"}, {"id": "esign_account", "name": "esign_account", "note": "", "uuid": "39D36B704BB54EB1BCF70491B17782C0"}, {"id": "sign_online_auth", "name": "在线签名认证", "note": "", "uuid": "9190D866614A4F01B8D5D8C8F09B1502"}, {"allowreply": "false", "counthits": "false", "delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "xsht", "lastmodifytime": "*************", "modifylock": "false", "modifyreason": "true", "name": "临床研究", "nameen": "Clinical Studies", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "0", "saveas_copyattach": "true", "showaddbtn": "true", "showreturnbtn": "false", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "F8055C2A607F17B718EEE32381D082C4"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "edc_name", "lastmodifytime": "*************", "menuinfo": "", "modifyreason": "true", "name": "电子数据采集", "nameen": "EDC", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "6148E3D5747E5B656699FDC8C3932C43"}, {"delinnerdata": "true", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "coding_name", "lastmodifytime": "1505452647041", "menuinfo": "", "modifyreason": "true", "name": "编码字典和系统", "nameen": "Coding Dictionary & System", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "87844ADB6222E40C7B89118371E78B8A"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "dmsop", "lastmodifytime": "1503227667500", "menuinfo": "", "modifyreason": "true", "name": "CDSC标准操作程序", "nameen": "CDSC SOPs", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "false", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "87AEB650216F51F1AF68E535545D80AE"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "sae_check", "lastmodifytime": "1503227351422", "menuinfo": "", "modifyreason": "true", "name": "安全数据核对", "nameen": "Safety Data Recocilliation", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "41DDCE11833A40E4510672243EA6BA5F"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "xshtbbxx", "lastmodifytime": "1505204055547", "menuinfo": "", "modifyreason": "true", "name": "试验方案", "nameen": "Protocol", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "C2300963BA9191373E82762FBD934FED"}, {"delinnerdata": "false", "id": "rand_blind", "lastmodifytime": "1510623974313", "menuinfo": "", "modifyreason": "true", "name": "随机化试验供应管理", "nameen": "RTSM", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "40518A081D7245EBA1F17096B21F1B28"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "study_coding_plan", "modifylock": "false", "modifyreason": "true", "name": "医学编码计划", "nameen": "Medical Coding Plan", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "80%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "C8BE7FBF8D2042A19A419FC47F733590"}, {"id": "study_visit_set", "name": "研究访问设置", "note": "", "uuid": "4D2391966E594191B998AD866328EFFB"}, {"id": "study_visit_table", "name": "研究访问表", "note": "", "uuid": "6A6F02F927384C7682EC6FEEADE9A680"}, {"id": "study_crf_visit", "name": "研究访问设置", "note": "", "uuid": "5FE8F3574C3C429580F5B6DEF6601F4C"}, {"delinnerdata": "false", "id": "study_crf_visit_table", "modifyreason": "false", "name": "研究CRF访问表", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "1E4A06287DC1478CA1D123996C3B72C5"}, {"id": "study_crf_table_pctpt", "name": "研究CRF表患者", "note": "", "uuid": "C5409E492D3D489195994D958C1E5ADD"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "", "id": "study_coding_ver_update", "modifylock": "false", "modifyreason": "false", "name": "MedDRA编码历史", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "94EE9A94AE3C49DF9A85E9E81D91C189"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "wysjlx", "lastmodifytime": "1503227327234", "menuinfo": "", "modifyreason": "true", "name": "外部数据管理", "nameen": "External Data Management", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "64490B23CD2B4A37B79D510502276C2F"}, {"delinnerdata": "true", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "study_data_blind_plan", "modifylock": "false", "modifyreason": "false", "name": "数据验证规范", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "60%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "1F8D249DAB5244489E39A67DFDEBB628"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "", "id": "study_coding_ver_update1", "modifylock": "false", "modifyreason": "false", "name": "WHODrug编码历史", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "767783774DC0480DA9C8D832ED5A348E"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "siteinf", "lastmodifytime": "1503669472797", "modifylock": "false", "modifyreason": "true", "name": "研究中心和研究者", "nameen": "Site and Investigator", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "C82EBD8B210E2F0649113CEBE7F71F3E"}, {"delinnerdata": "true", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "roles", "lastmodifytime": "1505288779196", "modifyreason": "true", "name": "数据中心管理员", "nameen": "Data Center Managers", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "80%", "saveCloseWindow": "", "showaddbtn": "false", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "809E632000ED0B854E6FB25568393764"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "xmgt", "lastmodifytime": "1503909169297", "modifyreason": "true", "name": "沟通交流", "nameen": "Communications", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "BAC60BC42FAE4ED2C4CC558DB5DB918D"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "communication_plan", "lastmodifytime": "1503229981437", "modifyreason": "true", "name": "沟通计划", "nameen": "Communication Plan", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "ED99B9CDCD30F668D09BE375E8E9E9D4"}, {"delinnerdata": "true", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "data_management_transfer", "lastmodifytime": "1503909207704", "modifylock": "false", "modifyreason": "true", "name": "任务交接", "nameen": "Task Transition", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "70%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "AA985E8EC7CC76F16E862AF221967137"}, {"id": "crocont", "name": "CRO娴滃搫鎲�", "note": "", "uuid": "36A4DDCA2E8D48E1B1149E5BCE65DA88"}, {"delinnerdata": "true", "id": "study_partner", "lastmodifytime": "1515404009014", "modifyreason": "false", "name": "CRO", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "D3BC11C4C33440CFBC564F73C1A86A48"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "work_transfer_subject", "lastmodifytime": "1503909207704", "modifyreason": "true", "name": "任务交接", "nameen": "Task Transition", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "CC5D64D0A533436AB2576BC1CA37E64E"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "study_partner_contacts", "modifylock": "false", "modifyreason": "true", "name": "研究合作伙伴联系人", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "60%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "F861C200856944BC9C5FDF6172C049BA"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "cra", "lastmodifytime": "1503550946062", "modifyreason": "true", "name": "其他团队成员", "nameen": "Other Team Members", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "75FB1F7B6DD146A18C3D5BA194EAE910"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "study_user_responsibility", "modifyreason": "false", "name": "娴犺濮熼崚鍡椾紣", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "AC73AFCD9D9648C99FC785FF85571904"}, {"delinnerdata": "false", "id": "proj_plan", "lastmodifytime": "1503230700625", "modifyreason": "true", "name": "项目计划", "nameen": "Project Plan", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "CF2706EB86A62D1638CDAF577564837C"}, {"delinnerdata": "false", "id": "yqsq", "lastmodifytime": "1503232980062", "modifyreason": "true", "name": "延期申请", "nameen": "Extention Application", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "2F39D12F8EBF513F0892F55A631BCC8A"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "risk_management", "lastmodifytime": "1503230117593", "modifyreason": "true", "name": "风险管理和应急计划", "nameen": "Risk Management and Contingency Plan", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "CA5BBD64545037B046491A952270984A"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "project_month_update", "lastmodifytime": "1503230117593", "modifylock": "false", "modifyreason": "true", "name": "风险管理和应急计划", "nameen": "Risk Management and Contingency Plan", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "39EA5F8023B544ADA7367C97CAB1EB7F"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "pro_rand_monthly_progress", "lastmodifytime": "1503230117593", "modifylock": "false", "modifyreason": "true", "name": "数据管理计划模板", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "170190E208D349D9A384F8E296E56F75"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "study_task", "modifyreason": "false", "name": "鏉╂稑瀹崇拋鈥冲灊", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "273F1D1E25E343BB8F17D463C29839CE"}, {"id": "schedule_template", "name": "schedule_Template", "note": "", "uuid": "0E66AB5A8DDA4A12882765D12975D59D"}, {"id": "schedule_links", "name": "schedule_links", "note": "", "uuid": "72B385B794E24B80A2A69373DFF3A15E"}, {"id": "schedule_work_date_cnf", "name": "schedule_work_Date_cnf", "note": "", "uuid": "0953EE82763E41CAA8D2824EE389ECFB"}, {"id": "schedule_work_rename", "name": "schedule_complete_name", "note": "", "uuid": "D31EA03D4004474FBACA093BC70CAC96"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "sysq_management", "lastmodifytime": "1503912125203", "modifylock": "false", "modifyreason": "true", "name": "沟通记录", "nameen": "Comminication records", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "60%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "A220DAB90F9148DCAE5D238324601CF9"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "study_qc", "modifyreason": "false", "name": "研究质量控制", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "691970BDE246454498D72BB95096238C"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "study_regular_review", "modifyreason": "true", "name": "研究定期审查", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "D8FC4BDF97A94B389AE25E64BDE5CEF7"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "iframe", "id": "scheduletemplate", "modifyreason": "false", "name": "娴犺濮熷Ο鈩冩緲鐞涳拷", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "8A1A824DBACB4D48B6F745B9FC7F54F9"}, {"id": "schedule", "name": "瀹搞儰缍旂拋鈥冲灊", "note": "", "uuid": "C7195753EEED4EF29A408EE6B94B937E"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "manual_rev_prog", "lastmodifytime": "1503987082859", "modifylock": "false", "modifyreason": "true", "name": "SAS审查", "nameen": "SAS Review", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "60%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "C78EE75C41B049DF831ADDBFEE1230C4"}, {"delinnerdata": "false", "id": "crf_design", "lastmodifytime": "1503912338890", "modifyreason": "true", "name": "CRF设计", "nameen": "CRF Design", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "6F95A161EBDB4A8FAF0BE6DCF42A15B4"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "middle", "id": "ecrf_build", "lastmodifytime": "1527747625826", "modifylock": "false", "modifyreason": "false", "name": "eCRF设计和设置", "nameen": "eCRF Design and Setup", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "75%", "saveCloseWindow": "", "saveas_copyattach": "true", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "7E538B6A5D8340369FCDA5BFCE6FDC73"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "edit_check_plan", "lastmodifytime": "*************", "modifylock": "false", "modifyreason": "true", "name": "数据管理审查计划", "nameen": "Data Management Review Plan (DMRP)", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "false", "uuid": "FAD97371E8324AFEAC0BCFB610666606"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "edit_check_prog", "lastmodifytime": "1503986846390", "modifylock": "false", "modifyreason": "true", "name": "编辑检查编程/测试", "nameen": "Edit Check Programming/Testing", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "true", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "false", "uuid": "66C499A630A14E47AB34F7F6EFC77A98"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "uta", "lastmodifytime": "1503987428328", "modifylock": "false", "modifyreason": "true", "name": "UAT", "nameen": "Team UAT", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "09EB280530CB4B0AB5C6FD7B064C26C8"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "pv", "lastmodifytime": "1503986914468", "modifyreason": "true", "name": "方案偏离定义", "nameen": "Protocol Deviation Definition", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "A20B51BB2BCB457F902CF02BB43BD47A"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "edc_go_live", "lastmodifytime": "1503285897234", "modifylock": "false", "modifyreason": "true", "name": "eCRF发布和备份计划", "nameen": "eCRF Publishing and Backup Plan", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "5368DD9DB82D4C179ECDD3865B25F56C"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "edc_account", "lastmodifytime": "*************", "modifyreason": "true", "name": "EDC账户管理", "nameen": "EDC Account Management", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "60%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "********************************"}, {"delinnerdata": "false", "id": "manual_rev_plan", "lastmodifytime": "*************", "modifyreason": "true", "name": "手工审查", "nameen": "Manual Review", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "810C03AAEF9143A58351B15FD25D5B0F"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "lnr", "lastmodifytime": "*************", "modifylock": "false", "modifyreason": "true", "name": "实验室参考范围", "nameen": "Lab Reference Range", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "45B8509A49A54C3EA5F0A51F2CD748A2"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "user_train", "lastmodifytime": "1503286096109", "modifyreason": "true", "name": "閻€劍鍩涢崺纭咁唲", "nameen": "User Training", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "60%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "9A6383EF2DD04BCCBBDCE79AD592D7DB"}, {"delinnerdata": "true", "delrefdata": "true", "delreflink": "false", "editpopuptype": "right", "id": "ext_data", "lastmodifytime": "1503276823234", "modifylock": "false", "modifyreason": "true", "name": "数据传输协议", "nameen": "Data Transfer Agreement", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "50E6811C6B5A4F9DADB84F33898445B6"}, {"delinnerdata": "false", "id": "im", "lastmodifytime": "1503286633953", "modifyreason": "true", "name": "研究者会议", "nameen": "Investigator Meeting", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "7928D3AD7E404731B420D5012B573679"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "ecrf_build1", "lastmodifytime": "1503992114656", "modifylock": "false", "modifyreason": "true", "name": "CRF完成指南", "nameen": "CRF Completion Guideline", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "true", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "9AAC8D29457C4BCFA930AEAAE39C4246"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "ecrf_build2", "lastmodifytime": "1503285559515", "modifylock": "false", "modifyreason": "true", "name": "EDC发布审批", "nameen": "eCRF Publishing Approval", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "C88BA4190F8645778EB30A4F2C494F61"}, {"delinnerdata": "false", "id": "esignature_statement", "lastmodifytime": "1503286145718", "modifyreason": "true", "name": "研究者电子签名声明", "nameen": "Investigator e-Signature Statement", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "8EEE113DEEB04ABE80557C518B3B58AC"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "edc_permission_his", "lastmodifytime": "1503383469250", "modifyreason": "false", "name": "EDC用户历史", "nameen": "EDC User History", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "425F1066569042D8BC532C9DA600031A"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "sjk", "lastmodifytime": "1503285112890", "modifyreason": "true", "name": "数据库质量控制", "nameen": "Database QC", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "DC097562124E475787DB65EC808A5004"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "interim_analysis", "lastmodifytime": "1503227593250", "modifyreason": "true", "name": "中期分析计划", "nameen": "Interim Analysis Plan", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "********************************"}, {"delinnerdata": "false", "id": "xm_view", "lastmodifytime": "*************", "modifyreason": "true", "name": "视图定义", "nameen": "View Definition", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "18C97E291F67470FBAAC60FB497CD230"}, {"delinnerdata": "false", "id": "edm_account", "lastmodifytime": "*************", "modifyreason": "false", "name": "EDM账户管理", "nameen": "EDM Account Management", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "084304D09BFC4FD6B9947F7FE761F0E7"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "study_dvs", "lastmodifytime": "*************", "modifyreason": "true", "name": "数据验证规范(DVS)", "nameen": "Data Validation Specification 閿涘湒VS閿涳拷", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "49DDA0D79B484817ACDD8465DE0F5B40"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "study_edm_plan", "lastmodifytime": "*************", "modifyreason": "true", "name": "数据管理计划", "nameen": "Data Management Plan", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "D080103CFFA84645BFC4F51DA9694B66"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "study_ext_test_data", "lastmodifytime": "1503813986140", "modifyreason": "true", "name": "外部数据管理", "nameen": "External Data Management", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "59D8D4DDF388474291D0DF4C5AF940C6"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "", "id": "sqcshj", "lastmodifytime": "1531985980035", "modifylock": "false", "modifyreason": "true", "name": "请求EDC实例", "nameen": "Request EDC Instances", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "600px", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "16A4995052E14DB288B77E171A6F3B27"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "dta_bind_method", "modifylock": "false", "modifyreason": "false", "name": "数据验证规范设计", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "EA99E057F3E3419B8FCB9B4FE9FD03C4"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "prot_workflow", "modifyreason": "true", "name": "协议工作流", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "60%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "79745AEB2A004FBFB9944DFD185294A6"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "ed<PERSON><PERSON>d", "modifyreason": "false", "name": "EDC盲法", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "60%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "D39112FA5D304E2ABD93C4339F483B34"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "prod_report", "lastmodifytime": "1503270300453", "modifyreason": "true", "name": "EDC生产使用", "nameen": "EDC Production Use", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "407AFF05580B4F44B6F8B44477649476"}, {"delinnerdata": "false", "id": "fsfv", "lastmodifytime": "1503226576265", "modifyreason": "true", "name": "FSFV", "nameen": "FSFV", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "E0A45773267A4A0096BCC7E751FDEE8B"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "project_report", "lastmodifytime": "1503227662328", "modifyreason": "true", "name": "已完成的数据清理", "nameen": "Completed data cleanings", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "3963923E9BEF90C0A87B24611632A2B1"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "outboard_data_manager", "lastmodifytime": "1503813986140", "modifylock": "false", "modifyreason": "true", "name": "外部数据检查", "nameen": "External Data Checking", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "240C46F8EC66FA1CD6907D594BA46134"}, {"delinnerdata": "false", "id": "dm_report", "lastmodifytime": "1502258908673", "modifyreason": "true", "name": "数据管理报告", "nameen": "Data Management Report", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "false", "uuid": "3ECF71AE86A6F0463A780F78B9F5D479"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "coding", "lastmodifytime": "1504248685000", "modifyreason": "true", "name": "医学编码", "nameen": "Medical Coding", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "14C1258745DAB7858FE555EA2A87DEB5"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "q_management", "lastmodifytime": "1503240139765", "modifyreason": "true", "name": "数据管理问题和解决方案", "nameen": "DM Issue and Resolution", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "90962A5686B2C1E025D45A5EA3233DA3"}, {"delinnerdata": "false", "id": "lslv", "lastmodifytime": "1503226404109", "modifyreason": "true", "name": "LSLV", "nameen": "LSLV", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "3F51F0E1245E432C3FA284DB3C9F5B44"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "plan_revision", "lastmodifytime": "1503239651937", "modifyreason": "true", "name": "EDC娣囶喛顓圭拋鈥冲灊", "nameen": "eCRF Amendment or EDC Upgrade", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "136F6C325A091F92086392F44D1D9CF7"}, {"delinnerdata": "false", "id": "fahsjkxd", "lastmodifytime": "1502174921920", "modifyreason": "false", "name": "方案或数据库修正", "nameen": "Protocol or Database Amendment", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "B47A38DA6DA19D1618E3B9149EFC1449"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "dm_disc_log", "lastmodifytime": "1503814022187", "modifyreason": "true", "name": "数据管理差异", "nameen": "Data Management Discrepancy", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "56E4C2DD329940A8FEEA8ADA158563A5"}, {"delinnerdata": "false", "id": "xtsj", "lastmodifytime": "1502154949150", "modifyreason": "false", "name": "EDC版本升级", "nameen": "EDC Version Upgrade", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "26E14BC0F8F3C06E40FE1F68751CA991"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "yzxhc", "lastmodifytime": "1503237362593", "modifyreason": "true", "name": "严重不良事件核对", "nameen": "SAE Recociliation", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "068EED36207569BCDAAAC9B41FC2FBDB"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "interim_analyplan", "lastmodifytime": "1503236985515", "modifyreason": "true", "name": "中期分析", "nameen": "Interim Analysis", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "E877B084AB0B9193EB5E1632EB4AE9A1"}, {"delinnerdata": "false", "id": "non_outboard_data_manager", "lastmodifytime": "1503813986140", "modifyreason": "false", "name": "非外部数据管理", "nameen": "NON External Data Management", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "89D63168046D449C8B73AF8B8DBF7A91"}, {"delinnerdata": "false", "id": "external_data_dta_check", "lastmodifytime": "1503813986140", "modifyreason": "false", "name": "外部数据DTA检查", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "E8BF766A91D24F82858FCABB8B44EE43"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "study_rand_consistency", "lastmodifytime": "1503237362593", "modifylock": "false", "modifyreason": "true", "name": "实验室数据核对", "nameen": "闂呭繑婧�閸栵拷 Recociliation", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "60%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "CE2901ADB84742BEA12FAF012549EF77"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "study_db_revise_assess", "lastmodifytime": "1503239651937", "modifyreason": "true", "name": "数据库修正", "nameen": "Database amendment", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "632E1FA41DAB440C915DBAE183F204B4"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "study_db_revise_report", "lastmodifytime": "1503239651937", "modifyreason": "true", "name": "EDC娣囶喛顓归幎銉ユ啞", "nameen": "eCRF Amendment or EDC Upgrade", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "60%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "2B7FB78D51874C7BB1338557F559CE74"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "", "id": "ext_data_bind", "modifyreason": "false", "name": "外部数据绑定", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "85E0053878C543FE837E7319364F968A"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "helphtml": "", "id": "sas_ver_pro", "lastmodifytime": "1726022170384", "modifylock": "false", "modifyreason": "false", "name": "IRC数据传输", "nameen": "IRC Data transfer", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "60%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "A506176BE34147CBB3483A925BC0029E"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "sascheck", "modifyreason": "false", "name": "SAS检查", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "C75BE828E9DE4F6089A00643969DC4EB"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "blindedtransfer", "modifylock": "false", "modifyreason": "false", "name": "数据验证规范查询", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "45%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "E3DBCDA1E342402EB19A2DAC365CEF56"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "edcblindtransfer", "modifyreason": "false", "name": "EDC鐎涙顔岀拋鍓ф锤娑撳簼绱舵潏锟�", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "60%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "874892EAB2FB4BE0813E91F79EB4C110"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "sas_check_run", "lastmodifytime": "1503987082859", "modifylock": "false", "modifyreason": "true", "name": "SAS审查", "nameen": "SAS Review", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "155A75E4BBA34E77A11ED389CFD1C1DC"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "db_lock_proc", "lastmodifytime": "1503272644234", "modifyreason": "true", "name": "eCRF锁定检查清单", "nameen": "eCRF Lock CheckList", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "0572237DBCDFEBAF6CBF32D2F7BC2A1B"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "data_eval", "lastmodifytime": "1503371928453", "modifyreason": "true", "name": "数据库锁定数据质量控制", "nameen": "DB Lock Data QC ", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "ADB2A4A16DA6D5E87511333C5F51C7E9"}, {"delinnerdata": "true", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "db_lock", "lastmodifytime": "1503380002859", "modifylock": "false", "modifyreason": "true", "name": "eCRF快照", "nameen": "eCRF Snapshot", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "3CA8BAB97B690AF2812741386B41ADD8"}, {"delinnerdata": "false", "id": "project_oreport", "lastmodifytime": "1503297361125", "modifyreason": "true", "name": "数据管理总结", "nameen": "Data Management Summary", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "B3BBD8531AFCE366E57933BE54DBD8A4"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "db_unlock", "lastmodifytime": "1503380284062", "modifylock": "false", "modifyreason": "true", "name": "eCRF鐟欙綁鏀�", "nameen": "eCRF Unlock", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "4B5C1D5BED7BD98DC6772140985A37EF"}, {"delinnerdata": "false", "id": "blinding_export", "lastmodifytime": "1503323300469", "modifyreason": "true", "name": "数据提取", "nameen": "Data Extract", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "03AD48CD1F5FD3EBFC81A8342081B099"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "data_submission", "lastmodifytime": "1503814079359", "modifylock": "false", "modifyreason": "true", "name": "数据传输", "nameen": "Data Transfer", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "3F0C756EAA7EA1AC98C254A026D057E6"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "blinding_meeting", "lastmodifytime": "1505300411149", "modifyreason": "true", "name": "（盲法）数据审查会议", "nameen": "(Blinded) Data Review Meeting", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "1FC5487C1972CBBA0D4EBA4FB9C29439"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "dmp_gen", "lastmodifytime": "*************", "modifyreason": "true", "name": "数据管理计划", "nameen": "Data Management Plan", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "0F9A473B990DBA7593624498F4F953DD"}, {"delinnerdata": "false", "id": "final_manage_report", "lastmodifytime": "1503999580468", "modifyreason": "true", "name": "数据管理报告", "nameen": "Data Management Report", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "E273E2D4D5C394DF47F7BFD8BF267204"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "dm_doc_archive", "lastmodifytime": "1503998396828", "modifyreason": "true", "name": "数据管理文档档案", "nameen": "DM Document Archives", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "32CA21B1F546EC7C6AAE66B2888A9258"}, {"delinnerdata": "false", "id": "db_archive", "lastmodifytime": "1502268048900", "modifyreason": "false", "name": "数据库解锁", "nameen": "Database Unlock", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "A48D3FE4C3AADF249D9721612EFF0F96"}, {"delinnerdata": "false", "id": "db_unlock_application", "lastmodifytime": "1503300172093", "modifyreason": "true", "name": "数据库解锁请求", "nameen": "DB Unlock Request", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "false", "uuid": "F2E284E6A8A014D3DDEED50EE35DA4B9"}, {"delinnerdata": "false", "id": "verification_pr_data", "lastmodifytime": "1503300266047", "modifyreason": "true", "name": "数据验证计划", "nameen": "Data Validation Plan", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "false", "uuid": "A0E7CFCF69E4EE53F769D26930434EB5"}, {"delinnerdata": "false", "id": "verification_report_data", "lastmodifytime": "1503300327812", "modifyreason": "true", "name": "数据验证报告", "nameen": "Data Validation Report", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "false", "uuid": "728353A06697BD3A3A7B0161FD0B4504"}, {"delinnerdata": "false", "id": "sjzlkz", "lastmodifytime": "1503300450015", "modifyreason": "true", "name": "质量控制和质量保证", "nameen": "QC & QA", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "false", "uuid": "C85126CD38AD03F184B8F69CA96DCCB1"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "sjzlbz", "lastmodifytime": "1505300005281", "modifyreason": "true", "name": "数据质量保证", "nameen": "Data QA", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "01272A4F76F9D9F20689B524C6692980"}, {"delinnerdata": "false", "id": "zxjsqrh", "lastmodifytime": "1503386164172", "modifyreason": "true", "name": "研究中心确认eCRF接收", "nameen": "Site Confirmation eCRF Receiving", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "BE24261D309294F4E54FF97B64EB9CA6"}, {"delinnerdata": "false", "id": "approval_unblinding", "lastmodifytime": "1503300390234", "modifyreason": "true", "name": "揭盲申请和审批", "nameen": "Unblinding Application & Approval", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "false", "uuid": "5FB8EDE2B9B0429381DF365AD27A42BA"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "crf_handover", "lastmodifytime": "1503386164172", "modifyreason": "true", "name": "研究中心确认eCRF接收", "nameen": "Site Confirmation eCRF Receiving", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "5B355B5191D7433FADEC2CF7ADF2BB2D"}, {"delinnerdata": "true", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "db_lock_plan", "lastmodifytime": "1503380002859", "modifyreason": "true", "name": "eCRF快照", "nameen": "eCRF Snapshot", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "524B8FFE342544E197F8FB61431BDFC9"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "", "id": "db_lock_plan_item", "modifyreason": "false", "name": "快照/数据库锁定检查清单", "nameen": "Snapshot/DB lock checklist", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "992ECFFB9CC748FE94A10318C54BE1AC"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "", "id": "db_lock_item", "modifyreason": "false", "name": "数据库锁定项目", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "2042EA427F9E46EC882E3A52173E94B5"}, {"delinnerdata": "true", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "db_lock_data_qc", "modifyreason": "false", "name": "数据库锁定数据质量控制", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "9502D1EADB2F4990B8F975AB127C6E05"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "ext_data_final", "modifylock": "false", "modifyreason": "false", "name": "外部数据最终版", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "60%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "50CFFBA7B6DA4954B0D60347F17E2AFA"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "zzjg", "lastmodifytime": "1503827326265", "modifyreason": "true", "name": "组织架构图", "nameen": "Organization Chart", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "9CB4E34931596BBD749CA0E22E2B6B7C"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "ryjl", "lastmodifytime": "1513219178347", "modifyreason": "true", "name": "员工", "nameen": "Employee", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "368E02066E5C83E6B2D45432060F37CD"}, {"delinnerdata": "false", "id": "employe_probation_report", "modifyreason": "true", "name": "员工试用期报告", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "05001C4D783D478F92D7CE04AB818C7A"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "", "id": "resume", "modifyreason": "false", "name": "简历和职位描述", "nameen": "CVs and JDs", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "DD0FA55E9C234A12951EB4690AE80008"}, {"id": "employe_work_experience", "name": "瀹搞儰缍旂紒蹇撳坊", "note": "", "uuid": "A01D47879FD34D48B8D78E5AD6AA9E0B"}, {"id": "employe_growth", "name": "员工成长", "note": "", "uuid": "2031BE29FA674493B61BBF7690229BBB"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "rewards", "modifylock": "false", "modifyreason": "true", "name": "奖励", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "false", "showsubtable": "true", "uuid": "A21EA0ED550D421F8C07929AE2E72660"}, {"delinnerdata": "true", "id": "pxjl", "lastmodifytime": "1503831471265", "modifyreason": "true", "name": "eLearning记录", "nameen": "eLearning Records", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "false", "uuid": "D041C9CD259286F877D4894C7258E4F1"}, {"delinnerdata": "false", "id": "pxjlmx", "lastmodifytime": "1503832373109", "modifyreason": "false", "name": "培训记录详情", "nameen": "Training Record Details", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "31FFA637E566C030E2286BFB29859D28"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "rwzz", "lastmodifytime": "1503301061187", "modifyreason": "false", "name": "娴犺濮熼懕宀冪煑", "nameen": "Roles & Responsibilities", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "A5D880637EF5ECBC3F24EBB3E834610A"}, {"delinnerdata": "false", "id": "jsxx", "lastmodifytime": "*************", "modifyreason": "false", "name": "鐟欐帟澹婃穱鈩冧紖", "nameen": "Roles", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "2ABE7BFD7FED3BC56054EF1F986F630E"}, {"delinnerdata": "false", "id": "ryjbzl", "lastmodifytime": "*************", "modifyreason": "false", "name": "用户账户", "nameen": "User Account", "newtoedit": "false", "note": "", "recordpwr": "false", "redirecturl": "", "showaddbtn": "true", "showreturnbtn": "false", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "false", "uuid": "917BFF3F144789439C605557F2DB482B"}, {"delinnerdata": "false", "id": "unit_info", "lastmodifytime": "*************", "modifyreason": "false", "name": "组织", "nameen": "Orgnization", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "14BE0CE1113A679F0647251E25718217"}, {"delinnerdata": "false", "id": "dw", "lastmodifytime": "*************", "modifyreason": "false", "name": "部门", "nameen": "Department", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "DD3FF4CB2B5AAE56F085C3B3B64635BF"}, {"delinnerdata": "false", "id": "systemcode", "lastmodifytime": "**********562", "modifyreason": "false", "name": "缁崵绮虹紓鏍垳鐎涙鍚�", "nameen": "System Codelists", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "935D1A2D898ABF0097150D1EED1D17DE"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "codelist", "lastmodifytime": "1505205762547", "modifyreason": "false", "name": "代码列表", "nameen": "Codelists", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "C20E9209C094A9073C309773C25D8644"}, {"delinnerdata": "false", "id": "xmsjjhzd", "lastmodifytime": "1503559762875", "modifyreason": "false", "name": "鏉╂稑瀹崇粻锛勬倞濡剝婢�", "nameen": "Task Management", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showreturnbtn": "false", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "119B946E1DEAD4D93FCBB683A4F3BA9C"}, {"delinnerdata": "false", "id": "jdhzlb", "lastmodifytime": "1502264987696", "modifyreason": "false", "name": "进度总结类别", "nameen": "Progress Summary Category", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "2918ECA275060CCED79FF1D67C34231A"}, {"id": "progress_template", "name": "鏉╂稑瀹崇粻锛勬倞濡紕澧楃悰锟�", "note": "", "uuid": "039F6A7B77FF407DB0424CE387DB315D"}, {"delinnerdata": "false", "id": "sjglzd", "lastmodifytime": "1503563624859", "modifyreason": "false", "name": "数据管理SOP列表", "nameen": "DM SOP Lists", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "A6D75F3C60523E45EB1C6E9B22462413"}, {"delinnerdata": "false", "id": "pxzd", "lastmodifytime": "1503563592218", "modifyreason": "false", "name": "培训分配", "nameen": "Training Assignment", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "BDCBC21A479A1552DF8BE9A7F26B18A1"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "dm_template_file", "lastmodifytime": "1503627903109", "modifyreason": "false", "name": "工作模板", "nameen": "Working Templates", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "235120DED5A1C23C0FD4E2B878C0CD92"}, {"delinnerdata": "false", "id": "jlmb", "lastmodifytime": "1502264201191", "modifyreason": "false", "name": "模板", "nameen": "Templates", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "CFD780E09E3F98A20A46B35458527AC1"}, {"delinnerdata": "false", "id": "wjlb", "lastmodifytime": "1502264006409", "modifyreason": "false", "name": "娑撴潙绨ラ弬鍥︽缁鍩嗙�涙鍚�", "nameen": "Trial File Types", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showreturnbtn": "false", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "8391098D1CE022A7AAC4C4F40EAFA268"}, {"delinnerdata": "false", "id": "yjzwjlbzd", "lastmodifytime": "1502263788072", "modifyreason": "false", "name": "研究者研究中心文件类别", "nameen": "Investigator Site File Category ", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showreturnbtn": "false", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "02FB571188E13287C57C9D5811523561"}, {"delinnerdata": "false", "id": "lcwjlbsjg", "lastmodifytime": "1502264090915", "modifyreason": "false", "name": "试验文件树结构", "nameen": "Trial File Tree Structures", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "6E34E5A436EB81C120E403362B86401F"}, {"delinnerdata": "false", "id": "mksjg", "lastmodifytime": "1502263880346", "modifyreason": "false", "name": "公共模板", "nameen": "Public Templates", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "8DE586C34AAC20B7B53C594BE2C6C06A"}, {"delinnerdata": "false", "id": "help", "lastmodifytime": "1502172079167", "modifyreason": "false", "name": "帮助", "nameen": "Help", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "36C61D2C3F458C3F752180351A0490BA"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "dta_ds", "modifyreason": "true", "name": "DTA类型", "nameen": "DTA Type", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "E4F0B7B6BA6F44C6A8C1FEF97201E63B"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "dta_ds_item_template", "modifyreason": "true", "name": "DTA字段字典", "nameen": "DTA Field Dictionary", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "785C1133882F4188B0305FA95043AEB2"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "dta_ds_item", "modifyreason": "true", "name": "DTA类型模板", "nameen": "DTA Type Template", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "false", "uuid": "BD0A2F6029F54658B7988A02BA8720F9"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "dta_ds_template", "modifyreason": "true", "name": "DTA类型", "nameen": "DTA Type", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "92538E91BEC84299AE598B87FD897C09"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "middle", "id": "email_template", "modifylock": "false", "modifyreason": "false", "name": "鐞涖劌宕熼柇顔绘濡剝婢�", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "D253F0AE8BCD4663AAE18A968A3B0FDB"}, {"id": "db_lock_item_tmpl", "name": "数据库锁定项目模板", "note": "", "uuid": "96E5C4CA5DFF48F1BAEF536A2F21155D"}, {"id": "study_user_resp_tmpl", "name": "study_user_resp_tmpl", "note": "", "uuid": "BB729E4D183842C197A5C9A95C3B5F59"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "middle", "helphtml": "", "id": "xxyjl1", "lastmodifytime": "1524303837890", "menuinfo": "", "modifyreason": "false", "name": "信息共享", "nameen": "Information Sharing", "newtoedit": "false", "note": "Knowledge Base", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "false", "uuid": "25C4E77ABC2A377461E519998130DA7C"}, {"id": "new_post", "menuinfo": "", "modifyreason": "false", "name": "new_post", "note": "", "uuid": "62DBCDF5608C491AB8054FD86460672D"}, {"id": "new_post1", "menuinfo": "", "modifyreason": "false", "name": "new_post1", "note": "", "uuid": "59AA5CF52206439282BA871E29A94209"}, {"id": "study_dm_hour_sum", "menuinfo": "", "modifyreason": "false", "name": "研究DM工作文档历史", "note": "", "uuid": "1EF4A227C0904D878FDEDFC3D79D5F27"}, {"id": "delete_study_listy", "menuinfo": "", "modifyreason": "false", "name": "删除研究列表", "note": "", "uuid": "62D81315E3734EEB965CC48FBCCF6C2A"}, {"id": "roster_lead", "menuinfo": "", "modifyreason": "false", "name": "问题解决", "note": "", "uuid": "51B3ADB7C70B4C17A3CA688BD47D5401"}, {"id": "scp_update", "menuinfo": "", "modifyreason": "false", "name": "scp_update", "note": "", "uuid": "B1A73A7BE67542CB9DEE2A4EB478A6F6"}, {"id": "employee_code", "menuinfo": "", "modifyreason": "false", "name": "闂嗗棗娲熷銉ュ娇", "note": "", "uuid": "61448BA401C24FE6A3BE4D32D55D7244"}, {"id": "study_oa", "menuinfo": "", "modifyreason": "false", "name": "OA办公自动化系统", "note": "", "uuid": "BFECE8862D014CCF92A36753A1AF313D"}, {"delinnerdata": "false", "id": "holiday", "menuinfo": "", "modifyreason": "false", "name": "閼哄倸浜ｉ弮锟�", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "99F4EC19BDC3488194225D64F8491D0A"}, {"id": "special_working_day", "menuinfo": "", "modifyreason": "false", "name": "閻楄鐣╅惃鍕紣娴ｆ粍妫�", "note": "", "uuid": "F95AFBEF174947719369A787BD7C974E"}, {"id": "temp1", "modifyreason": "false", "name": "eCRF模板", "note": "", "uuid": "7602036022A74ECCA6ABA9C9E19D3DE9"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "middle", "id": "jyjl2", "modifyreason": "false", "name": "技术分享", "nameen": "Technical sharing", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "false", "uuid": "2DCDCEFEA3DC440881FB3492388FE8F8"}, {"id": "study_dmp_update", "modifyreason": "false", "name": "study_dmp_update", "note": "", "uuid": "10A486F95B3241D2B14ACF4F1C65B408"}, {"delinnerdata": "false", "id": "knowledge_base_learner", "modifyreason": "false", "name": "培训记录", "nameen": "Training records", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "0F09D258A60B4F289C05C29D94C3C39F"}, {"id": "study_for_diary", "modifyreason": "false", "name": "study_for_Diary", "note": "", "uuid": "647A47DD97894AF690818E569E0CB9D1"}, {"id": "diary_task_template", "modifyreason": "false", "name": "日记任务模板", "note": "", "uuid": "99173A28921E4F4EB9E0000933AFCFE0"}, {"id": "employee_manager_map", "modifyreason": "false", "name": "閻€劍鍩涙稉鑽ゎ吀", "note": "", "uuid": "C32228846F4C4C428986AC7E49F2A37E"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "email_send_log", "modifyreason": "false", "name": "邮件发送日志", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "66981C69F2AE4365AE0406246394FB33"}, {"id": "dm_guide", "modifyreason": "false", "name": "数据管理指南", "note": "", "uuid": "5F1C87DBD6574A94B055B7414246C569"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "middle", "id": "standard_email", "modifyreason": "false", "name": "标准邮件", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "false", "uuid": "9747172E9E744954A35914128C6D7B99"}, {"id": "employee_gifted_plan", "modifyreason": "false", "name": "閼昏鲸澧犵拋鈥冲灊", "note": "", "uuid": "603B0E33C5204CA9B508841E8D8F3362"}, {"id": "study_work_subject", "modifyreason": "false", "name": "娴溿倖甯撮崘鍛啇", "note": "", "uuid": "B65FCB8E946F499587DEC8099111FABC"}, {"id": "data_modify_track", "modifyreason": "false", "name": "data_modify_track", "note": "", "uuid": "D9011AD7B464465EA56AA7A3F813B681"}, {"id": "innovation_team", "modifyreason": "false", "name": "创新团队", "note": "", "uuid": "5BE6F255FB58466C9614522EDAFBE74F"}, {"id": "dvs_edm_plan", "modifyreason": "false", "name": "dvs_edm_plan", "note": "", "uuid": "0ABBD093757D4C30A60CEB4F7B54B2A5"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "wiki_auth", "modifyreason": "false", "name": "wiki_auth", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "947DD73A5F4940A5B6D66D82BD186D4F"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "", "id": "remotebuttontask", "modifyreason": "false", "name": "鏉╂粎鈻兼禒璇插閹稿鎸�", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "C0B0E3DE024E4322B2ABB86BDE9ED31A"}, {"id": "ecrf", "name": "ecrf", "note": "", "uuid": "778B6B738C77491E8A2436CEA9E6BFD1"}, {"id": "ec", "name": "ec", "note": "", "uuid": "51E56CC46E154F3A957121772C9AD278"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "rtmstest", "modifyreason": "false", "name": "RTMS_TEST", "nameen": "rtms", "newtoedit": "true", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "false", "uuid": "A702695F3E5E46B5B92784DC2959B8BE"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "study_training", "modifylock": "false", "modifyreason": "false", "name": "研究内容", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "71DB2F4963034CD8841B2D5B8E9FC05F"}, {"id": "dmrp_map", "name": "DMRP濡剝婢�", "note": "", "uuid": "693FE84EAE1A486DA74D41A39F873D19"}, {"id": "dmrp_map_test", "name": "dmrp_map_test", "note": "", "uuid": "BA02FC86E5A340C4B8967766E2F1564D"}, {"id": "job_duty", "name": "瀹搞儰缍旈懕宀冪煑", "note": "", "uuid": "6FA52DE67CEC4AA48D876117951BA37E"}, {"id": "content_text", "name": "内容文本", "note": "", "uuid": "4D8EAE0D588E4007BB8124018C7DCD57"}, {"delinnerdata": "false", "id": "gzzd", "lastmodifytime": "1503986142422", "modifyreason": "false", "name": "法规", "nameen": "Regulations", "newtoedit": "false", "note": "", "recordpwr": "false", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "43F1C08304101C42A1DE6814109D2BF8"}, {"delinnerdata": "false", "id": "tzhgg", "lastmodifytime": "1503997551375", "modifyreason": "false", "name": "公告", "nameen": "Announcements", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "5F518634E4A6810B6BC213E29847ED66"}, {"delinnerdata": "false", "id": "sop", "lastmodifytime": "1502162477991", "modifyreason": "false", "name": "SOP文件", "nameen": "SOPs", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "A8BB2DB4A81BF4DAFA6CDDB1327C3338"}, {"delinnerdata": "false", "id": "mb", "lastmodifytime": "1502263347434", "modifyreason": "false", "name": "模板", "nameen": "Templates", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "D91C1C478F100D04D5AC68B3778913F4"}, {"delinnerdata": "false", "id": "xxyjl", "lastmodifytime": "1502263158955", "modifyreason": "true", "name": "其他", "nameen": "Other", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "false", "uuid": "E84676A2380DA9830286D09E5DDAD94B"}, {"delinnerdata": "false", "id": "fgzn", "lastmodifytime": "1503985811984", "modifyreason": "true", "name": "政策和法规", "nameen": "Policies And Regulations", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showreturnbtn": "false", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "false", "uuid": "7140CF097EE33678DEC75C7F9715C57E"}, {"delinnerdata": "false", "id": "edc_validation_report", "lastmodifytime": "1502155649309", "modifyreason": "false", "name": "缁崵绮烘宀冪槈閹躲儱鎲�", "nameen": "System Validation Report", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "E9A10B1F5849A71736A2E4572FA9AB11"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "edc_v_name", "lastmodifytime": "1502096169286", "modifyreason": "false", "name": "缁崵绮洪悧鍫熸拱鐎涙鍚�", "nameen": "System Versions", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "F984E7EB1C151FC00D7F36BF866482A8"}, {"delinnerdata": "false", "id": "edc_user_manual", "lastmodifytime": "1502156555076", "modifyreason": "false", "name": "缁崵绮洪悽銊﹀煕閹靛鍞�", "nameen": "System User Manual", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "E773571146F7A24C15D393E1FE2BE736"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "edc_support_agreement", "lastmodifytime": "1502096514592", "modifyreason": "false", "name": "服务级别协议", "nameen": "SLAs", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "4CF733999DF44C3118D8505F0FEFC9DE"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "sjgl", "lastmodifytime": "1502261891876", "modifyreason": "false", "name": "CDSC标准操作程序", "nameen": "CDSC SOPs", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "true", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "27831B18EEED70F86D5A2238C02C4931"}, {"delinnerdata": "false", "id": "dbworkfile", "lastmodifytime": "1503494650703", "modifyreason": "false", "name": "数据管理工作文档", "nameen": "DM Working Documents", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "964D006A2992CAA05CA83C24694BD70F"}, {"delinnerdata": "false", "id": "dbwork<PERSON>le_his", "lastmodifytime": "1503494690750", "modifyreason": "false", "name": "数据管理文件历史", "nameen": "Data Management Flie History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "65C51997F59C248116C533F57E7E41CE"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "dbjs<PERSON>le", "lastmodifytime": "1503494747359", "modifyreason": "false", "name": "数据管理技术文档", "nameen": "DM Technical Documents", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "false", "uuid": "64F2C962B139AAEBB493922C0D06CA09"}, {"delinnerdata": "false", "id": "dbjsfile_his", "lastmodifytime": "1503494770922", "modifyreason": "false", "name": "数据管理技术文档历史", "nameen": "DM Technical Document History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "D195065EE72EBABD2E001D0408748861"}, {"id": "regularattachment", "name": "常规附件", "note": "", "uuid": "08F7E2BFE8F544D09CF448354F85650F"}, {"delinnerdata": "false", "id": "db_backup", "lastmodifytime": "1451464121799", "modifyreason": "false", "name": "数据库备份", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "E051BE999789769A2C928151906F0173"}, {"delinnerdata": "false", "id": "qa_management", "lastmodifytime": "1451464420426", "modifyreason": "false", "name": "系统模板", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "F91F6964CCD05F50FEAB3FB60C767FDA"}, {"delinnerdata": "false", "id": "db_qc", "lastmodifytime": "1434710358988", "modifyreason": "false", "name": "数据管理文档", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "35A92DE0741876633016F73EE5D2337D"}, {"delinnerdata": "false", "id": "ext_data_load", "lastmodifytime": "1451464471913", "modifyreason": "false", "name": "外部数据加载", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "DC90D29CE215CA2BD13091349CC63EB1"}, {"delinnerdata": "false", "id": "final_report", "lastmodifytime": "1430301139790", "modifyreason": "false", "name": "系统配置文档", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "DC93F861D6C6F42E47EB66A70241A997"}, {"id": "db_lock_appr", "lastmodifytime": "1430377235911", "name": "閹电懓鍣弫鐗堝祦鎼存捇鏀ｇ�癸拷", "note": "", "uuid": "FD76AB9783B4CC93608C5A41BBF963EB"}, {"delinnerdata": "false", "id": "bb", "lastmodifytime": "1503301229984", "modifyreason": "false", "name": "EDC版本", "nameen": "EDC Version", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "1C5D60F1BAAF673FDD35CDC6CCEDBF6E"}, {"delinnerdata": "false", "id": "yhxq", "lastmodifytime": "1503301362812", "modifyreason": "false", "name": "閻€劍鍩涢棁鈧Ч锟�", "nameen": "User Requirement", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "40768CE5A569D0586413783D0B341F3E"}, {"delinnerdata": "false", "id": "gngg", "lastmodifytime": "1503301399593", "modifyreason": "false", "name": "功能规范", "nameen": "Functional Specification", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "6EBCC258E17C7B67A9992A13F881795C"}, {"delinnerdata": "false", "id": "yzbg", "lastmodifytime": "1503301433797", "modifyreason": "false", "name": "妤犲矁鐦夐幎銉ユ啞", "nameen": "Validation Report", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "347BC53EF7ED2C1A86697CC73C2A5A2F"}, {"delinnerdata": "false", "id": "sc", "lastmodifytime": "1503301468750", "modifyreason": "false", "name": "EDC手册", "nameen": "EDC Manual", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "F705220757A7C317E38A5E3AF9875F4A"}, {"delinnerdata": "false", "id": "sxtz", "lastmodifytime": "1503301577594", "modifyreason": "false", "name": "EDC上线通知", "nameen": "EDC Go-Live Notification", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "2123DDFFF3E3FC3420B355C7653DF8E7"}, {"delinnerdata": "false", "id": "fwq", "lastmodifytime": "1503301610718", "modifyreason": "false", "name": "服务器", "nameen": "Server", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "81F005629FF8C8724AAFFA0E3564D1DA"}, {"delinnerdata": "false", "id": "xthf", "lastmodifytime": "1503301645906", "modifyreason": "false", "name": "EDC系统恢复", "nameen": "EDC System Recovery", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "A4AD1E40CB7912DD4D09F9EFE055544A"}, {"delinnerdata": "false", "id": "znhfgc", "lastmodifytime": "1503301680843", "modifyreason": "false", "name": "灾难恢复流程", "nameen": "Disaster Recovery Process", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "C892BE3F48E1DAB0E8F3C3068F9B173D"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "study_edc_system_trace", "lastmodifytime": "*************", "menuinfo": "", "modifyreason": "true", "name": "电子数据采集", "nameen": "EDC", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "4B26CC4EA0614E70AE86A06BB2A65F4D"}, {"delinnerdata": "false", "id": "xtwt", "lastmodifytime": "1503301716547", "modifyreason": "false", "name": "缁崵绮洪梻顕�顣�", "nameen": "System Problem", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "41CB60B17142C8A6EB7F69D0D4D9EA15"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "system_verification", "lastmodifytime": "1503841743312", "modifyreason": "false", "name": "缁崵绮�", "nameen": "Systems", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "AEB4E64AAD4FE6C4E4C5860DC0932412"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "system_verification_list", "lastmodifytime": "1503841046765", "modifyreason": "false", "name": "系统验证交付物", "nameen": "System Validation Deliverables", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "437C57225C72E39A635341B677C948C5"}, {"delinnerdata": "false", "id": "verification_list_dict", "lastmodifytime": "1503839311656", "modifyreason": "false", "name": "系统验证交付物", "nameen": "System Validation Deliverables", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "11345378258DFAFF6C2B3C1DA243377C"}, {"delinnerdata": "false", "id": "verification_his", "lastmodifytime": "1503839479890", "modifyreason": "false", "name": "閻楀牊婀伴崢鍡楀蕉", "nameen": "Version History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "false", "uuid": "2B802F84986FED9361B4B704D6634854"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "tool_verification", "lastmodifytime": "1503841743312", "modifyreason": "false", "name": "瀹搞儱鍙�", "nameen": "Systems", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "7EFCEB716FD546CC9A4572D8BDB0CADD"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "tool_verification_list", "lastmodifytime": "1503841046765", "modifyreason": "false", "name": "工具验证交付物", "nameen": "tool Validation Deliverables", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "5CDEF39CA3AA45578B4216CFA1A76258"}, {"delinnerdata": "false", "id": "manual_rev_prog_his", "lastmodifytime": "1503557355140", "modifyreason": "false", "name": "手工审查历史", "nameen": "Manual Review History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "false", "uuid": "6FA38CCA67C80A9EC2A30C222DCB13FB"}, {"delinnerdata": "false", "id": "xm_view_his", "lastmodifytime": "1503557511328", "modifyreason": "true", "name": "视图定义历史", "nameen": "View Definition History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "C0343E4314D17F5EC4EBB9F66B546B59"}, {"delinnerdata": "false", "id": "crf_design_history", "lastmodifytime": "1503557699562", "modifyreason": "false", "name": "CRF设计历史", "nameen": "CRF Design Hisitory", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "42591F27803372C3765EAD8B07F25CF5"}, {"delinnerdata": "false", "id": "ecrf_build_his", "lastmodifytime": "1503557789531", "modifyreason": "false", "name": "eCRF閻楀牊婀伴崢鍡楀蕉", "nameen": "eCRF History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "71FDCA747429628DECBA2C4A0E7F0A5E"}, {"delinnerdata": "false", "id": "edit_check_plan_his", "lastmodifytime": "1503557910859", "modifyreason": "false", "name": "数据验证计划历史", "nameen": "DVP History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "9D5C17D5E736E250759C15AD4E817C21"}, {"delinnerdata": "false", "id": "edit_check_prog_his", "lastmodifytime": "1503558970968", "modifyreason": "false", "name": "伦理委员会历史", "nameen": "EC Hisitory", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "82FF4A4F118A6A5324B2B161328B8A11"}, {"delinnerdata": "false", "id": "uta_his", "lastmodifytime": "1503559093281", "modifyreason": "false", "name": "UAT閻楀牊婀伴崢鍡楀蕉", "nameen": "UAT Version History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "false", "uuid": "2DF5DF8CDBD1F6390F223EE424B29B31"}, {"delinnerdata": "false", "id": "pv_his", "lastmodifytime": "*************", "modifyreason": "false", "name": "方案偏离版本历史", "nameen": "Protocol Deviation Version History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "C3861BA38664B8A15D2197AE64A47EA9"}, {"delinnerdata": "false", "id": "edc_accountd_his", "lastmodifytime": "*************", "modifyreason": "false", "name": "EDC账户管理历史", "nameen": "EDC Account Management History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "B7819B9F4B0A4457B97EF9BB926C8BBD"}, {"delinnerdata": "false", "id": "prod_report_dt", "lastmodifytime": "*************", "modifyreason": "false", "name": "生产报告历史", "nameen": "Production Report History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "62CF23D51BD014D073CD093A450C7754"}, {"delinnerdata": "false", "id": "dmp_gen_his", "lastmodifytime": "*************", "modifyreason": "false", "name": "数据管理计划历史", "nameen": "DMP History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "E329993CD4E8BC880C5E829E07A50CF2"}, {"delinnerdata": "false", "id": "ecrf_build1_his", "lastmodifytime": "1503563087547", "modifyreason": "false", "name": "CRF完成指南", "nameen": "CRF Completion Guideline", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "C0F37DF0C51A57EE7C4188999A8642F5"}, {"delinnerdata": "false", "id": "sjk_his", "lastmodifytime": "1503563169125", "modifyreason": "false", "name": "数据库质量控制历史", "nameen": "Database QC History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "4CE6FDD73A160B6DFA55D170FA94BC88"}, {"delinnerdata": "false", "id": "ecrf_build2_his", "lastmodifytime": "1503563912500", "modifyreason": "false", "name": "数据库上线批准历史", "nameen": "Database Go-Live Approval History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "45F5FDD6FE0F8DFAC1E92A8B970A9E8D"}, {"delinnerdata": "false", "id": "rand_his", "lastmodifytime": "1503563962718", "modifyreason": "false", "name": "随机设置历史", "nameen": "Rand Setting History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "false", "uuid": "F4B19ECFA89029D7BA3DC8D487D6A964"}, {"delinnerdata": "false", "id": "coding_his", "lastmodifytime": "1503564007109", "modifyreason": "false", "name": "医学编码管理历史", "nameen": "Medical Coding Management  History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "ADAD83811C611B0CB270CF113AB25716"}, {"delinnerdata": "false", "id": "xmzqjm_his", "lastmodifytime": "1503564054062", "modifyreason": "false", "name": "中期揭盲历史", "nameen": "Interim Unblinding  History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "F3900B60AE1193068914EA041E22614E"}, {"delinnerdata": "false", "id": "randomized_schemehis", "lastmodifytime": "1503564130922", "modifyreason": "false", "name": "随机化方案版本历史", "nameen": "Randomization Scheme Version History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "false", "uuid": "********************************"}, {"delinnerdata": "false", "id": "rutahis", "lastmodifytime": "*************", "modifyreason": "false", "name": "UAT閻楀牊婀伴崢鍡楀蕉1", "nameen": "UAT Version History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "false", "uuid": "C312A77C006C7B6044D4B4033B9FCC3A"}, {"delinnerdata": "false", "id": "rstm_accounthis", "lastmodifytime": "*************", "modifyreason": "false", "name": "RTSM账户管理历史", "nameen": "RTSM Account Management History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "D300375694686626F142189C6C92B443"}, {"delinnerdata": "false", "id": "rand_prohis", "lastmodifytime": "*************", "modifyreason": "false", "name": "随机化申请历史", "nameen": "Randomization Application History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "B103E5B7815158B353F907B97B79D3A5"}, {"delinnerdata": "false", "id": "final_manage_report_his", "lastmodifytime": "*************", "modifyreason": "false", "name": "数据管理报告历史", "nameen": "DM Report History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "BF7C3BAE9275D4CEA5480A27711CDD11"}, {"delinnerdata": "false", "id": "esignature_statement_his", "lastmodifytime": "1503566149812", "modifyreason": "false", "name": "研究者电子签名声明", "nameen": "Investigator e-Signature Statement", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "20941A6AF3379A1F7A13B01FC15C33BF"}, {"delinnerdata": "false", "id": "lnr_his", "lastmodifytime": "1503924225531", "modifyreason": "false", "name": "实验室参考范围历史", "nameen": "Lab Reference Range History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "ABCD1FECA3F7164D92F8123EA861D544"}, {"delinnerdata": "false", "id": "edc_permission_his_his", "lastmodifytime": "1503925201750", "modifyreason": "false", "name": "EDC用户历史", "nameen": "EDC User History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "13A1FB742FB111C2C229E4105D6DFC18"}, {"delinnerdata": "false", "id": "q_management_his", "lastmodifytime": "1503925731562", "modifyreason": "false", "name": "数据管理问题和解决方案", "nameen": "DM Issue and Resolution", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "CAAA7B44207DC7320D6F0D59A3E981E9"}, {"delinnerdata": "false", "id": "drugnum_create_his", "lastmodifytime": "1510646407693", "modifyreason": "false", "name": "随机化方案版本历史", "nameen": "Randomization Scheme Version History", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "false", "uuid": "72CC1565973549F199B5490B852058E5"}, {"delinnerdata": "true", "delrefdata": "false", "delreflink": "false", "editpopuptype": "middle", "id": "xmrz", "lastmodifytime": "1512374503468", "modifyreason": "true", "name": "研究日记", "nameen": "Study Diary", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "E283D9E5B8DF95B22EF6EB180CF71341"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "xmrznr", "lastmodifytime": "1512374523538", "modifyreason": "false", "name": "研究日记详情", "nameen": "Study Diary Details", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "80DAF844BBB9EFED6B11548D541066E2"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "xjjl", "modifyreason": "false", "name": "请假", "nameen": "Leaves", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "EB346F32C4AA40119CCD8FBB718BB0D1"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "jbsq", "modifyreason": "false", "name": "加班", "nameen": "Overtime", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "562B200F697D4A3896A4C323EB273DAB"}, {"delinnerdata": "true", "delrefdata": "false", "delreflink": "false", "editpopuptype": "middle", "id": "yxbzzj", "lastmodifytime": "1510661701869", "modifyreason": "false", "name": "周总结", "nameen": "Weekly Summary", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "95F509F46FC340F1D5E5393D6ED57444"}, {"delinnerdata": "false", "id": "weekly_summary_list", "lastmodifytime": "1512374523538", "modifyreason": "false", "name": "研究日记详情", "nameen": "Study Diary Details", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "5BC9CE2DB1484670ABD85765F43A4492"}, {"delinnerdata": "true", "delrefdata": "false", "delreflink": "false", "editpopuptype": "middle", "id": "yzj", "lastmodifytime": "1510661708859", "modifyreason": "false", "name": "月度总结", "nameen": "Monthly Summary", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "9CD2096720E6CB49F39DF7275E6C1453"}, {"delinnerdata": "false", "id": "monthly_summary_list", "lastmodifytime": "1512374523538", "modifyreason": "false", "name": "研究日记详情", "nameen": "Study Diary Details", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "true", "showsubtable": "true", "uuid": "971BB846F20844AF9438232210D4A938"}, {"id": "study_daily_monthly_sum", "name": "研究日记月总结", "note": "", "uuid": "6FB2C1FC2C2E4066A6024125A6FFE3A5"}, {"id": "monthly_sort_dict", "name": "月度排序字典", "note": "", "uuid": "ADF526A4D67849DCA9FD631E5CF49089"}, {"id": "studyid_oa_map", "name": "studyid_oa_map", "note": "", "uuid": "3CCBC696335340D59A2FAC15C46ACCA4"}, {"id": "study_phase_daily_sum", "name": "研究阶段日总结", "note": "", "uuid": "4310CC27BFB3400D8B219C193A713915"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "rand_pro", "lastmodifytime": "1510664812384", "modifyreason": "false", "name": "随机化申请", "nameen": "Randomization Application", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "FC77E4C4B0D846B98050E4B717438570"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "randomized_scheme", "lastmodifytime": "1512114553370", "modifyreason": "false", "name": "随机化方案", "nameen": "Randomization Scheme", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "06400D6C9E374AB3AFCED51001C3E697"}, {"delinnerdata": "false", "id": "rtsmsqcshj", "lastmodifytime": "1503234542578", "modifyreason": "false", "name": "閻㈠疇顕ù瀣槸閻滎垰顣�", "nameen": "Test Environment Application", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "1DF790E33A00439D978F76CD7FF77F23"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "ruta", "lastmodifytime": "1510883150798", "modifyreason": "false", "name": "RTSM Team UAT", "nameen": "RTSM Team UAT", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "346BC3E1775D47ED817F5A9CB2542598"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "rtschj", "lastmodifytime": "1510388185539", "modifylock": "false", "modifyreason": "false", "name": "RTSM生产环境", "nameen": "RTSM Production Environment", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "5E4473AB873744EE9E2953C503BD1533"}, {"delinnerdata": "false", "id": "drug_blind", "lastmodifytime": "1510646619085", "modifyreason": "true", "name": "药物盲法", "nameen": "Drug Blinding", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "2A698178C448498E89A32A461CEC3EB5"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "drug_blindtrain", "lastmodifytime": "*************", "modifylock": "false", "modifyreason": "true", "name": "RTSM用户培训", "nameen": "RTSM User Training", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "60%", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "02A323715A3641F2B9DE8ED0273C341E"}, {"delinnerdata": "false", "id": "rstm_account", "lastmodifytime": "*************", "modifyreason": "false", "name": "RTSM账户管理", "nameen": "RTSM Account Management", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "FBEC94A360364DBAB1E97DA612DEB8EA"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "jjjm", "lastmodifytime": "*************", "modifyreason": "false", "name": "紧急揭盲", "nameen": "Emergency Unblinding", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "830445762A58407FA2871FB361B39E8A"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "rand", "lastmodifytime": "*************", "modifyreason": "false", "name": "随机化报告", "nameen": "Randomization Reports", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "66B1F3C7884A410EB698C359E069C9CF"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "xmzqjm", "lastmodifytime": "1503236032937", "modifyreason": "false", "name": "试验揭盲", "nameen": "Trial Unblinding", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "6E925DFA2DA6436E89EA630AE49C7802"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "drugnum_apply", "lastmodifytime": "1510664882634", "modifyreason": "false", "name": "随机化申请", "nameen": "Randomization Application", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "1A4540E1766848A4BCA855B3E8F5B1A0"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "id": "drugnum_create", "lastmodifytime": "1510664601290", "modifyreason": "false", "name": "随机化方案", "nameen": "Randomization Scheme", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "B51FBE8B3DC24F129AFC3A556AEA2DAB"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "mblind_record", "lastmodifytime": "1510798544268", "modifyreason": "false", "name": "随机化申请", "nameen": "Randomization Application", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "F39D02CD369A4822BD73B2B37CE86573"}, {"delinnerdata": "false", "id": "tconfigure_reoort", "lastmodifytime": "1510641451906", "modifyreason": "true", "name": "RTSM用户培训", "nameen": "RTSM User Training", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "A0334A679FC74E5CA2C9A92B8D334B0C"}, {"delinnerdata": "false", "id": "rconfigure_reoort", "lastmodifytime": "1510642259105", "modifyreason": "true", "name": "重新配置报告", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "E2D5691143A94288ACF5EFE0E24D4ECE"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "unblinding", "lastmodifytime": "1510901285406", "modifyreason": "false", "name": "试验揭盲", "nameen": "Trial Unblinding", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "B91C7C04D34047059682912B00E66346"}, {"delinnerdata": "false", "id": "rand_sas_program", "lastmodifytime": "1510664812384", "modifyreason": "false", "name": "閻╂彃绨�", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "C5608560E7284D8AB462D29B019D5367"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "rand_other", "lastmodifytime": "1510664812384", "modifyreason": "false", "name": "总结", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "67BD9E90417A4C66BC82E3077E5562A9"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "rand_protocol_design", "lastmodifytime": "1510664812384", "modifyreason": "false", "name": "实验室数据", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "7FD0AD1199354B8CB144C4257B3BD1DD"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "rtsm_setting_explain", "lastmodifytime": "1510664812384", "modifyreason": "false", "name": "随机化申请", "nameen": "Randomization Application", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "6290FCB9EC17418E98A5DD49B4367C8D"}, {"delinnerdata": "false", "id": "comparison_report", "lastmodifytime": "1510644593831", "modifyreason": "true", "name": "RTSM用户培训", "nameen": "RTSM User Training", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "2211DEF0B30C4783B9FD4A8DB49E632C"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "unbeautiful_plan", "lastmodifytime": "1510664812384", "modifyreason": "false", "name": "揭盲计划", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "8407C3D44C3B48E18B99AE62381CAF76"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "rtsm_plan_version", "modifylock": "false", "modifyreason": "false", "name": "RTSM计划版本", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "630C7071B98C4989AB5B19409BD79ABF"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "rtsm_pg_version", "modifylock": "false", "modifyreason": "false", "name": "RTSM程序版本", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "9EC221B9AEB4487D8137618316C8C56B"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "rtsm_report_version", "modifylock": "false", "modifyreason": "false", "name": "RTSM缁崵绮烘穱顔款吂閹躲儱鎲�", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "1C4156FFC14945D4A06C8243680AA841"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "rtsm_sjk", "modifylock": "false", "modifyreason": "false", "name": "RTSM用户培训", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "A4A0117CD2324CE6A0A3BE46B4820423"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "rtsm_jc", "modifylock": "false", "modifyreason": "false", "name": "随机化应用历史", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "saveas_copyattach": "false", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "true", "uuid": "9085164A6DA3405F9411BFD3EBD8A0DA"}, {"delinnerdata": "false", "id": "site_trainee", "lastmodifytime": "1500372370948", "modifyreason": "false", "name": "研究中心受训者", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "false", "showsubtable": "true", "uuid": "1820202D86EE4D03AF0DCA90535444A0"}, {"delinnerdata": "false", "id": "site_mmcai", "lastmodifytime": "1500514081718", "modifyreason": "false", "name": "员工", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "true", "showsaveas": "false", "showsubtable": "false", "uuid": "9F8675129F5740A2979AE37EB9A3F930"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "right", "id": "site_train", "lastmodifytime": "1499997169651", "modifyreason": "false", "name": "我的培训", "nameen": "My Trainings", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "50%", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "6808E592D0D5487095F81D34B4BDFC8A"}, {"delinnerdata": "false", "id": "item_control", "lastmodifytime": "1524454241852", "modifyreason": "false", "name": "妫版ê绨辩粻锛勬倞", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "1101D8526F3149F39436136884DE1BE7"}, {"delinnerdata": "false", "id": "tpaper_control", "lastmodifytime": "1524454291346", "modifyreason": "false", "name": "员工模板", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "false", "uuid": "C0DAA140F83F46FCB57D8EE4716FA662"}, {"id": "ctgzb", "lastmodifytime": "1485065494152", "name": "閹朵粙顣界憴鍕灟鐞涳拷", "note": "", "uuid": "E65235DE860049598E1FFB30C7224134"}, {"delinnerdata": "false", "id": "detailed_item", "lastmodifytime": "1486450645080", "modifyreason": "false", "name": "组织", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "true", "showsaveas": "false", "showsubtable": "true", "uuid": "66FC757167684947B977994036A2C125"}, {"delinnerdata": "false", "id": "exam_plan", "lastmodifytime": "1499997262097", "modifyreason": "false", "name": "閼板啳鐦�瑰甯�", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "false", "uuid": "75183B168C754A169A56F72695F84774"}, {"delinnerdata": "false", "id": "exam_user", "lastmodifytime": "1486624126968", "modifyreason": "false", "name": "閼板啳鐦禍鍝勬喅", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "true", "showsaveas": "false", "showsubtable": "true", "uuid": "ACB24976179C44AB8D053F7AD6143BDC"}, {"delinnerdata": "false", "id": "scores_info", "lastmodifytime": "1490164271049", "modifyreason": "false", "name": "评分信息", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "prjexam.showreadonly.do?ridgrade={id}", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "A01DC8222A2742D98891FE98B4446F75"}, {"delinnerdata": "false", "id": "detailed_item1", "lastmodifytime": "1496312601420", "modifyreason": "false", "name": "工作记录", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "42547437DD6045CB88BFBAC7F159A3ED"}, {"delinnerdata": "false", "id": "mmcai_control", "lastmodifytime": "1500357317062", "modifyreason": "false", "name": "员工模板", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "false", "showsubtable": "false", "uuid": "B810099C825E43119E06348589A1830A"}, {"delinnerdata": "false", "id": "course_control", "lastmodifytime": "1499997169651", "modifyreason": "false", "name": "课程控制", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "false", "uuid": "A99483E216024185B5869D68CE3E87FB"}, {"delinnerdata": "false", "id": "trainee", "lastmodifytime": "1500372370948", "modifyreason": "false", "name": "受训者", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "true", "showsaveas": "false", "showsubtable": "true", "uuid": "F121B1174B94428DBCD05E140F9590C1"}, {"delinnerdata": "false", "id": "detailed_mmcai", "lastmodifytime": "1500514081718", "modifyreason": "false", "name": "详细MMCAI", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "false", "showsaveandnew": "true", "showsaveas": "false", "showsubtable": "false", "uuid": "8AC14EBACDEB49949BC84D58C43AA8C8"}, {"delinnerdata": "false", "id": "pxjl1", "lastmodifytime": "1500514108247", "modifyreason": "false", "name": "培训记录1", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "8944F9A5920E4033B0524D50FB048085"}, {"delinnerdata": "false", "id": "llscb", "modifyreason": "false", "name": "培训记录表", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "E0634EFEDB004C54959006A54179B2BD"}, {"delinnerdata": "false", "id": "pxsm", "lastmodifytime": "1467872571183", "modifyreason": "false", "name": "培训状态", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsubtable": "true", "uuid": "29E46E643D5346FCADDFB7CB0963AB6D"}, {"delinnerdata": "false", "id": "zzxx", "lastmodifytime": "1475034278230", "modifyreason": "false", "name": "閼奉亙瀵岀�涳缚绡�", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "6501FDCA9839483495063B6FC14E4444"}, {"delinnerdata": "false", "id": "train_credential", "lastmodifytime": "1500372370948", "modifyreason": "false", "name": "培训凭证", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "false", "showsubtable": "true", "uuid": "E87F506006874067ADD628BE0198579F"}, {"delinnerdata": "false", "id": "credential", "modifyreason": "false", "name": "凭证模板", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "4472306A70244A02BEFD4A38726A675B"}, {"id": "dm_quantity", "name": "数量", "note": "", "uuid": "E4A18F9C51B84CD99678CCDA8F7538D5"}, {"id": "dm_dur", "name": "持续时间", "note": "", "uuid": "BC638EB579394EB7BE5B239250F307C6"}, {"id": "dm_efficiency", "name": "效率", "note": "", "uuid": "69279709D332418BB91B8FC8022A4DDD"}, {"id": "dm_cost", "name": "成本", "note": "", "uuid": "BEB6A0D55D184173868197EE584A0410"}, {"delinnerdata": "false", "id": "conpub", "lastmodifytime": "1518427755878", "modifyreason": "false", "name": "内容发布", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "55A7BAFF62FC4DE5A31785B9531D8AAB"}, {"delinnerdata": "false", "id": "pubhis", "lastmodifytime": "1520591523512", "modifyreason": "false", "name": "推送研究定义", "nameen": "Pushing Study Definition ", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "206FD074285F4A5AB4DC1721FCD7EFFD"}, {"delinnerdata": "false", "id": "contable", "lastmodifytime": "1520583493116", "modifyreason": "false", "name": "发布", "nameen": "Publishing", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsubtable": "true", "uuid": "8DCD2AC84A4140509616398984215CA5"}, {"delinnerdata": "false", "id": "study_file", "lastmodifytime": "1503998396828", "modifyreason": "true", "name": "数据管理文档档案", "nameen": "DM Document Archives", "newtoedit": "false", "note": "", "redirecturl": "", "showaddbtn": "true", "showsaveandnew": "false", "showsaveas": "false", "showsearchbtn": "true", "showsubtable": "false", "uuid": "E215838B93CF474BBF829E524638C18D"}, {"id": "langmanage", "name": "婢舵俺顕㈢懛鈧粻锛勬倞", "note": "", "uuid": "63CCD66D839C45A2AA6BCCF2DB68B5F3"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "", "id": "online_template", "modifyreason": "false", "name": "在线模板", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "3717840516E144948758F49594A77832"}, {"id": "emaildistribute", "name": "邮件分发", "note": "", "uuid": "D6EF87B926CA401D95EA6AFD5ACC2450"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "", "id": "online_review", "modifyreason": "false", "name": "在线审查", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "false", "uuid": "5D240165CDF6431F9B66779730031507"}, {"id": "review_detail", "name": "审查详情", "note": "", "uuid": "80B0D7783CED4414A58FBF38FB093DDE"}, {"delinnerdata": "false", "delrefdata": "false", "delreflink": "false", "editpopuptype": "", "id": "tableid", "modifyreason": "false", "name": "tableid_ch_en", "nameen": "", "newtoedit": "false", "note": "", "redirecturl": "", "rightPopWidth": "", "saveCloseWindow": "", "showaddbtn": "true", "showsaveandnew": "true", "showsaveas": "true", "showsearchbtn": "true", "showsubtable": "true", "uuid": "56C387D4E7B04DF6A8E7FC517264D21E"}, {"id": "datamerge", "name": "数据合并", "note": "", "uuid": "EB59F7AC7BED4DE78FF6054720FA1628"}, {"id": "datamerge1", "name": "数据合并", "note": "", "uuid": "E4E5C157A9604572B3BD7AE7E8DB1E0E"}, {"id": "datamergelog", "name": "数据合并日志", "note": "", "uuid": "D52C92BD0B514FB9B4F4978DE3B62CE5"}]