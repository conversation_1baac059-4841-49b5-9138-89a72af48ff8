package net.bioknow.cdtms.edmFileOutput;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;

public class TestUpload {
	
	public static void main(String[] args) {
		try {
			String url = "https://meduap-tst.hengrui.com:8085/usersyn/upload?"
					+ "token=8FD33CAD5E6C478F9C1566BBD6ACE9E3"
					+ "&formid=AE99921E8286422CAB79B365CBC8F831"
					+ "&tableid=data_submission"
					+ "&recordid=3815145472"
					+ "&fid=subject_crf"
					+ "&fn=" + URLEncoder.encode("HRS-4357-101_PRO_受试者病例报告表_20241108_154413_无稽查历史", "UTF8")+".zip";

			String url2="https://meduap-tst.hengrui.com:8085/remoteButtonTask/upload?taskId=3377037315&formId=C76463A0287E4E0A989EBD040E314A04&fid=file&fn="+URLEncoder.encode("SAS-Validation-duplicaterecord_UAT_4A8D087A05E54B378493EE56F3C1AF74_a", "UTF8")+".zip"+"&projectId=cdtmsen_val";
			File f = new File("d:/HRS-4357-101_PRO_6bff3dffb7ffc825_a.zip");
			FileInputStream fis = new FileInputStream(f);
			String ret = postContent(url,fis);
			System.out.println(ret);
		}catch(Exception e) {
			e.printStackTrace();
		}
	}
	
	public static String postContent(String urlStr, InputStream inputstream) throws Exception {
		URL url = new URL(urlStr);
		HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
		httpConn.setDoInput(true);
		httpConn.setDoOutput(true);
		httpConn.setRequestMethod("POST");
		httpConn.setConnectTimeout(3000);
		httpConn.setReadTimeout(3000);

		httpConn.setRequestProperty("content-type", "application/octet-stream");

		httpConn.connect();
		int len = 0;
		byte[] byteA = new byte[4096];
		OutputStream outStream = httpConn.getOutputStream();
		while ((len = inputstream.read(byteA)) > 0) {
			outStream.write(byteA, 0, len);
		}
		httpConn.getOutputStream().flush();
		httpConn.getOutputStream().close();

		OutputStream os = new ByteArrayOutputStream();

		InputStream is = httpConn.getInputStream();
		byte[] bA = new byte[40960];
		len = is.read(bA);
		while (len > 0) {
			os.write(bA, 0, len);
			len = is.read(bA);
		}
		os.close();
		is.close();

		return os.toString();
	}
}
