﻿<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=utf-8" %>
<%@ page language="java" pageEncoding="utf-8" %>


<html lang="en">
<head>
    <meta charset="utf-8">
    <title>签署信息</title>

    <meta http-equiv="X-UA-Compatible" content="chrome=1">
    <meta name="description" content="Table-to-json : jQuery plugin to serialize HTML tables into easy-to-use JSON objects.">
    <link rel="stylesheet" href="/public/bootstrap/css/bootstrap.min.css">
    <%--    <script src="/public/bootstrap/js/bootstrap.bundle.min.js"></script>--%>
    <script src="/cdtms/esign/js/jquery-3.4.1.min.js"></script>
    <script src="/cdtms/esign/js/bootstrap.min.js"></script>
    <script src="/cdtms/esign/js/jquery.tabletojson.min.js"></script>
    <link rel="stylesheet" href="/cdtms/esign/js/bootstrap-datetimepicker.min.css">
    <script src="/cdtms/esign/js/bootstrap-datetimepicker.min.js"></script>
    <script src="/cdtms/esign/js/xm-select.js"></script>
    <script src="/cdtms/LightpdfSign/js/flatpickr.js"></script>
    <link rel="stylesheet" href="/cdtms/LightpdfSign/js/flatpickr.min.css">

<%--    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">--%>
<%--    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>--%>




    <style>
        .center {
            float: none;
            margin-left: auto;
            margin-right: auto;
        }

        xmp {
            margin: 0;
        }
        #convert-table {
            margin-bottom: 1.5em;
        }

    </style>

</head>



<body>








<div class="container">
    <div class="row center span10">
        <h2>${esignInstanceMap.edition_name}</h2>
        <div class="w-100 p-2">
            <form class="form-horizontal" id="eSign">
                <div class="form-group">
                    <label>主题<span style="color: red;">*</span></label>
                    <input class="form-control" id="subject" name="subject" autocomplete="off" placeholder="主题" value="${esignInstanceMap.edition_name}  ${esignInstanceMap.edition_date}  签字"  required>                </div>

                <div class="form-group">
                    <label>签署截止日期</label>
                    <input type="text" class="form-control"  autocomplete="off"  id="signFlowExpireTime" name="signFlowExpireTime" placeholder="签署截止日期" required>
                </div>


                <div class="form-group">
                    <label>语种</label>
                    <select name="signLanguage" id="signLanguage"  class="form-control">
                        <option value="cn" selected>中文</option>
                        <option value="en">英文</option>
                    </select>
                </div>




                <div class="form-group">
                    <label>备注</label>
                    <textarea class="form-control" name="remark" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <div  >待签署文件：</div>
                <c:forEach items="${eSignFileList}" var="eSignFileList" varStatus="eSignFileListStatus">

                    <div>

<%--                        ${eSignFileList.file}--%>
    <input type="radio" name="signfile" value="${eSignFileList.filename}*${eSignFileList.file_uuid}"

    <c:if test="${eSignFileListStatus.first}">
           checked
    </c:if>
    >  <a target="_blank" href="ff_showpdf2swf.show.do?tableid=${tableid}&fieldid=file&filename=${eSignFileList.file_uuid}&ap=true">${eSignFileList.filename}</a>
<%--                            <a title="下载" target="_blank" href="ftattach.download.do?tableid=esign_file&amp;ufn=${eSignFileList.file_uuid}&amp;fn=${eSignFileList.filename}"><span>(下载)</span></a><br>--%>
                    </div>


                </c:forEach>
                </div>
                <c:if test="${not empty signPageFileList}">
                <div class="form-group">
                <div  >签字页：</div>


                    <c:forEach items="${signPageFileList}" var="signPageFileList" varStatus="signPageFileListStatus">

                        <div>

                                <%--                        ${eSignFileList.file}--%>
                            <input type="radio" name="signfile" value="${signPageFileList.filename}*${signPageFileList.file_uuid}"

                            <c:if test="${signPageFileListStatus.first}">
                                   checked
                            </c:if>
                            >  <a target="_blank" href="ff_showpdf2swf.show.do?tableid=${tableid}&fieldid=file&filename=${signPageFileList.file_uuid}&ap=true">${signPageFileList.filename}</a>
                                <%--                            <a title="下载" target="_blank" href="ftattach.download.do?tableid=esign_file&amp;ufn=${eSignFileList.file_uuid}&amp;fn=${eSignFileList.filename}"><span>(下载)</span></a><br>--%>
                        </div>


                    </c:forEach>



<%--                <a target="_blank" href="ff_showpdf2swf.show.do?tableid=${tableid}&fieldid=file&filename=${signPageFileMap.file_uuid}&ap=true">${signPageFileMap.filename}</a>--%>
                </div>
            </c:if>

                <div class="form-group">
                    <div  >签署人:</div>
                    <div id="signers"></div>
                </div>




                <div style="text-align: center">
                        <button   onclick="return false;" value="Submit" id="submit" class="btn btn-primary">提交签署</button>
                </div>


    </div>

        </div>

</div>

</div>



<script>
    $('#convert-table').click( function() {
        var table = $('#example-table').tableToJSON();

    });
</script>


<script>


  var signers = xmSelect.render({
          el: '#signers',
          language: 'zn',
          searchTips: '搜索',
            name:'signers',
          filterable: true,
          data: [
            ${stuydUser}
          ]
      })


</script>



<script type="text/javascript">
    var threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);

    const signFlowExpireTime = flatpickr("#signFlowExpireTime", {
        enableTime: true,
        dateFormat: "Y-m-d H:i:S",
        minDate: "today",
        locale: "zh",
        defaultDate: threeDaysFromNow



    });


</script>



<script type="text/javascript">



    //发送表单ajax请求
    $('#submit').on('click',function(){
        $(this).prop('disabled', true);

        var signFlowExpireTime = $("#signFlowExpireTime");
        if (!signFlowExpireTime.val()) {
            alert("签署截止日期不能为空！");

            $(this).prop('disabled', false);

            return;
        }



        // alert(JSON.stringify(signers));
        $.ajax({
            url:"LightpdfSignIntergrate.eSignRegister.do",
            type:"POST",
            data:{
                eSigndata:JSON.stringify($('#eSign').serializeObject()),
                // signersJson:JSON.stringify(signers),
                tableid:'${param.tableid}',
                recordid:'${param.recordid}',
<%--                <c:if test="${not empty signPageFileMap}">--%>
<%--                &lt;%&ndash;signPage:"${signPageFileMap.filename}*${signPageFileMap.file_uuid}",&ndash;%&gt;--%>

<%--                </c:if>--%>
                signersJson:JSON.stringify(signers.getValue(), null, 2)
            },
            async: false,
            success:function(data){
                $(this).prop('disabled', false);

                if(data.status==200){

                    window.open(data.data.setUrl);

                    window.location.href="/LightpdfSignIntergrate.View.do?recordid=${param.recordid}&tableid=${param.tableid}";
                    return;
                }else{

                    alert(data);
                }

            }
        });
    });

    /**
     * 自动将form表单封装成json对象
     */
    $.fn.serializeObject = function() {
        var o = {};
        var a = this.serializeArray();
        $.each(a, function() {
            if (o[this.name]) {
                if (!o[this.name].push) {
                    o[this.name] = [ o[this.name] ];
                }
                o[this.name].push(this.value || '');
            } else {
                o[this.name] = this.value || '';
            }
        });
        return o;
    };
</script>

</body>
</html>
