文件下载逻辑：
    public void download(String filesUUID, HttpServletResponse response) {
        try {
            //根据文件的唯一标识码获取文件
            File uploadFile = new File(filesUploadPath + filesUUID);

            //读取文件的字节流
            FileInputStream fileInputStream = new FileInputStream(uploadFile);
            //将文件写入输入流
            InputStream inputStream = new BufferedInputStream(fileInputStream);

            byte[] buffer = new byte[inputStream.available()];
            inputStream.read(buffer);
            inputStream.close();


            //attachment表示以附件方式下载 inline表示在线打开 "Content-Disposition: inline; filename=文件名.png"
            //filename表示文件的默认名称，因为网络传输只支持URL编码的相关支付，因此需要将文件名URL编码后进行传输,前端收到后需要反编码才能获取到真正的名称
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filesUUID, "UTF-8"));
            response.setContentType("application/octet-stream");

            //设置输出流的格式
            ServletOutputStream os = response.getOutputStream();
            os.write(buffer);
            
            //关闭
            fileInputStream.close();
            os.flush();
            os.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    
    
    
    
         <el-pagination
        background
        layout="total, sizes,prev, pager, next,jumper"
        :total="total"
        :current-page.sync="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        @size-change="handleSizeChange"
        @current-change="current_change"
      />
    
    
    
    
 
    
    
 //批量下载文件   
    
  /**
 * 
 * response 返回体
 * fileInfoList 文件信息集合
 */
 public void downForZip(String filePath,HttpServletResponse response) {
       File file = new File(filePath);
        File[] tempList = file.listFiles();
        // 创建临时文件
        File zipFile = null;
        FileInputStream fis = null;
        BufferedInputStream buff = null;
        try {
            //临时文件名称
            zipFile = File.createTempFile("test", ".zip");
            FileOutputStream fot = new FileOutputStream(zipFile);
            // 为任何OutputStream产生校验，第一个参数是制定产生校验和的输出流，第二个参数是指定Checksum的类型 (Adler32(较快)和CRC32两种)
            CheckedOutputStream cos = new CheckedOutputStream(fot, new Adler32());
            // 用于将数据压缩成Zip文件格式
            ZipOutputStream zos = new ZipOutputStream(cos);
           for (File file: tempList) {
                 //读取文件的字节流
                FileInputStream fileInputStream = new FileInputStream(file);
                //将文件写入输入流
                InputStream inputStream = new BufferedInputStream(fileInputStream);
                if (null == inputStream) {
                    break;
                }
                // 对于每一个要被存放到压缩包的文件，都必须调用ZipOutputStream对象的putNextEntry()方法，确保压缩包里面文件不同名
                //多个文件名称重复时zos.putNextEntry()会报错！！ 可以再文件名称后面加编号等操作
                zos.putNextEntry(new ZipEntry(file.getFileName()));
                int bytesRead = 0;
                // 向压缩文件中输出数据
                while ((bytesRead = inputStream.read()) != -1) {
                    zos.write(bytesRead);
                }
                inputStream.close();
                // 当前文件写完，写入下一个文件
                zos.closeEntry();
            }
            zos.close();
            ServletOutputStream os = response.getOutputStream();
            //下载文件,使用spring框架中的FileCopyUtils工具
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            //设置响应头,attachment表示以附件的形式下载，inline表示在线打开
   response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(tempList[i].getName(), "UTF-8"));
            fis = new FileInputStream(zipFile);
            buff = new BufferedInputStream(fis);
            FileCopyUtils.copy(buff, os);
        } catch (Exception e1) {
            throw new BusinessException("批量下载失败");
            // 关闭流
        } finally {
            try {
                if (null != fis) {
                    fis.close();
                }
                if (null != buff) {
                    buff.close();
                }
            } catch (IOException e) {
                log.error( "流关闭异常");
            }
            // 删除临时文件
            if (null != zipFile) {
                zipFile.delete();
            }
        }
    }



reportTable(val) {
      this.$axios.service({
        method: "post",
        url: '/test/downloadZip',
        data: {reportAllotMonthId:val.id,name:val.reportFormName,month:val.month},
        headers: {
          "content-type": "application/json; charset=utf-8",
          token: sessionStorage.getItem("cmsUser")
            ? JSON.parse(sessionStorage.getItem("cmsUser")).tokenId
            : ""
        },
        responseType: "blob"
      })
      .then(res => {
        this.downloadFile(res.data);
      })
      .catch(res => {
        this.downloadFile(res.data);
      });
    },
    // 导出
    downloadFile(data) {
      let blob = new Blob([data], {type: 'application/zip'})
      let url = window.URL.createObjectURL(blob)
      const link = document.createElement('a') // 创建a标签
      link.href = url
      link.download = `导出${utils.getTimeString(new Date())}` // 重命名文件
      link.click()
      URL.revokeObjectURL(url) // 释放内存
    },   
const res = response.data
const respContentType = response.headers['content-type']
// 如果响应类型是 zip 包，则执行下载文件逻辑
if (respContentType === 'application/zip') {
  if (!res) {
    return
  }
  const url = window.URL.createObjectURL(new Blob([res], {
    type: 'application/zip'
  }))
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  link.setAttribute('download', 'appIconZip.zip')
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  return
}




month-file-list

接收方：
   if (this.$route.query.param) {
        this.param = this.$route.query.param
      }
      
参数传递方:
toUploadPage(index, row) {
      this.$router.push({
        path:'/month-file-list',
        query: {
          param: row.compoundFolder // 保证每次点击路由的query项都是不一样的，确保会重新刷新view
        }
      })
    }
根据compoundFolder + 月度包名 查询该包名下的所有文件



localhost:8085/susarAuto/getMonthFolderFiles



pdf文件预览接口

	//后台controller代码，根据前端传入的fileName到指定目录读取pdf文件，进行展示
	@RequestMapping(value = "/preview", method = RequestMethod.GET)
	public void prePDF(String fileName, HttpServletRequest request, HttpServletResponse response) {
		logger.info("文件名：" + fileName);
		File file = new File("E:/pdf/" + fileName);
		if (file.exists()) {
			byte[] data = null;
			try {
				FileInputStream input = new FileInputStream(file);
				data = new byte[input.available()];
				input.read(data);
				response.getOutputStream().write(data);
				input.close();
			} catch (Exception e) {
				logger.info("pdf文件处理异常...");
			}
		}



export function downloadMothFile(data) {
  return request({
    url: 'susarAuto/downloadMothFile',
    method: 'post',
    data,
    headers: { 'content-type': 'application/octet-stream' },
    responseType: 'blob'
  })
}



 if (isOnLine) { // 在线打开方式
            URL u = null;
            try {
                u = new URL("file:///" + entity.getFilePath());
            } catch (MalformedURLException e) {
                throw new RuntimeException(e);
            }
            try {
                response.setContentType(u.openConnection().getContentType());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            response.setCharacterEncoding("UTF-8");
            response.setContentLength(new Long(f.length()).intValue());
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "inline; filename=" + fileName);
            // 文件名应该编码成UTF-8
        } else { // 纯下载方式
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-msdownload");
            response.setContentLength(new Long(f.length()).intValue());
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
        }

'application/octet-stream;charset=UTF-8'


  const blob = new Blob([data], { type: 'application/zip' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a') // 创建a标签
      link.href = url
      if (this.param === '') {
        this.param = 'SusarFolderTest'
      }
      link.download = this.param + '-' + name // 重命名文件
      link.click()
      URL.revokeObjectURL(url) // 释放内存
      
      
      
      //预览
                const blob = new Blob([res.data], { type: 'application/pdf;charset=UTF-8' })
          this.pdfSrc = window.URL.createObjectURL(blob)
          debugger
          window.open(this.pdfSrc) // 新窗口打开，借用浏览器去打印
          
          
          
          
          
          
         //ids 从库里获取地址，然后获取相应的文件
          
        File file = new File(filePath);
        File[] tempList = file.listFiles();
        // 创建临时文件
        File zipFile = null;
        FileInputStream fis = null;
        BufferedInputStream buff = null;
        try {
            //临时文件名称
            zipFile = File.createTempFile("test", ".zip");
            FileOutputStream fot = new FileOutputStream(zipFile);
            // 为任何OutputStream产生校验，第一个参数是制定产生校验和的输出流，第二个参数是指定Checksum的类型 (Adler32(较快)和CRC32两种)
            CheckedOutputStream cos = new CheckedOutputStream(fot, new Adler32());
            // 用于将数据压缩成Zip文件格式
            ZipOutputStream zos = new ZipOutputStream(cos);
            for (File readFile: tempList) {
                //读取文件的字节流
                FileInputStream fileInputStream = new FileInputStream(readFile);
                //将文件写入输入流
                InputStream inputStream = new BufferedInputStream(fileInputStream);
                if (null == inputStream) {
                    break;
                }
                // 对于每一个要被存放到压缩包的文件，都必须调用ZipOutputStream对象的putNextEntry()方法，确保压缩包里面文件不同名
                //多个文件名称重复时zos.putNextEntry()会报错！！ 可以再文件名称后面加编号等操作
                zos.putNextEntry(new ZipEntry(readFile.getName()));
                int bytesRead = 0;
                // 向压缩文件中输出数据
                while ((bytesRead = inputStream.read()) != -1) {
                    zos.write(bytesRead);
                }
                inputStream.close();
                // 当前文件写完，写入下一个文件
                zos.closeEntry();
            }
            zos.close();
            ServletOutputStream os = response.getOutputStream();
            //下载文件,使用spring框架中的FileCopyUtils工具
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            //设置响应头,attachment表示以附件的形式下载，inline表示在线打开

            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("Susar-1231-2023-01", "UTF-8"));
            fis = new FileInputStream(zipFile);
            buff = new BufferedInputStream(fis);
            FileCopyUtils.copy(buff, os);
        } catch (Exception e1) {
            try {
                throw new BusinessException("批量下载失败");
            } catch (BusinessException e) {
                throw new RuntimeException(e);
            }
            // 关闭流
        } finally {
            try {
                if (null != fis) {
                    fis.close();
                }
                if (null != buff) {
                    buff.close();
                }
            } catch (IOException e) {
                log.error( "流关闭异常");
            }
            // 删除临时文件
            if (null != zipFile) {
                zipFile.delete();
            }
        }
        
        
        
        const blob = new Blob([data], { type: 'application/zip' })

    