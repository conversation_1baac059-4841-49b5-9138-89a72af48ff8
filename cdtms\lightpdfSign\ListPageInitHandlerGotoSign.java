package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.mvc.tools.Language;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.face.ListPageInitFace;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ListPageInitHandlerGotoSign implements ListPageInitFace {
	private Map gotoSign = null;
	@Override
	public boolean canUse(int auth, String tableid, String projectId) {
        try {



			if (StringUtils.equals(tableid,"esign_instance")) {
				return true;
			}
        }
        catch (Exception e){
            Log.error("",e);
        }
        return false;
    }
	@Override
	public String getColName() {
		Language lang = Language.getInstance(this.getClass());
		return lang.get("签署");
	}
	@Override
    public String getJsInc() {

		return "";
    }

	@Override
	public Map getValueMap() {
		return this.gotoSign;
	}
	@Override
	public void init(List listV,String tableid,String projectId) {
		try {

			DAODataMng daoDataMng = new DAODataMng(projectId);
			Map currentUserMap = SessUtil.getSessInfo().getUser();
			List<Map<String, String>> listR = DAOLightpdfSignIntegrate.listRule(projectId, "1");
			String esignUrl = listR.get(0).get(DAOLightpdfSignIntegrate.esignUrl);
			this.gotoSign=new HashMap<>();
			for (Object dataObj : listV) {
				Map dataMap = (Map) dataObj;
				Long signId = (Long) dataMap.get("id");
				String signFlowId = (String) dataMap.get("sign_flow_id");

				List<Map> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id=" + signId +" and obj.user_code='"+(String) currentUserMap.get("email")+"'", null, 1000);

				String gotoSignHtml="";
				if (CollectionUtils.isNotEmpty(esignSignerList)) {


					Map esignSignerMap = esignSignerList.get(0);

					String status = (String) esignSignerMap.get("status");


					if (StringUtils.equals(status,"1")){
						String redirectUrl = esignUrl +  "?task_id=" + signFlowId + "&email=" +new String(Base64.getEncoder().encode( String.valueOf(esignSignerMap.get("user_code")).getBytes("UTF-8"))) + "&verification_code=" + esignSignerMap.get("verification_code");

						gotoSignHtml = "<a target=\"_blank\" href=\""+redirectUrl+"\">去签署</a>";

					}else {


						gotoSignHtml ="我已签署";
					}



				}else {
					gotoSignHtml = "";
				}


				
				this.gotoSign.put("rid" + signId, gotoSignHtml);
			}
		} catch (Exception e) {
			Log.error("",e);
		}
	}

}
