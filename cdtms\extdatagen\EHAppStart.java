package net.bioknow.cdtms.extdatagen;

import net.bioknow.cdtms.lightpdfSign.DTTFActionSign;
import net.bioknow.cdtms.lightpdfSign.*;
import net.bioknow.mvc.ioc.AppStartFace;
import net.bioknow.passport.uiframe.FuncUtil;
import net.bioknow.passport.validate.PageIgnore;
import net.bioknow.uap.dbdatamng.function.FuncFactory;

import javax.servlet.ServletConfig;

public class EHAppStart implements AppStartFace{

    public void onStart(ServletConfig arg) throws Exception {
                FuncFactory.addTableFunc(new DTTFActionExtdatagen());

    }

}
