zhouh36
<EMAIL>
HR9cf3cbd8

pop设置：
服务器名称: mail.hengrui.com
端口: 995
加密方法: SSL


IMAP设置：
服务器名称: mail.hengrui.com
端口: 993
加密方法: SSL

SMTP设置：
服务器名称: mail.hengrui.com
端口: 587
加密方法: TLS

uap系统账号：
<EMAIL>
Zh123456


mayp5
asdqwe123`


无线局域网适配器 本地连接* 1:

   媒体状态  . . . . . . . . . . . . : 媒体已断开连接
   连接特定的 DNS 后缀 . . . . . . . :

无线局域网适配器 本地连接* 2:

   媒体状态  . . . . . . . . . . . . : 媒体已断开连接
   连接特定的 DNS 后缀 . . . . . . . :

无线局域网适配器 WLAN:

   连接特定的 DNS 后缀 . . . . . . . : localdomain
   本地链接 IPv6 地址. . . . . . . . : fe80::4d01:b28e:f52f:f8c4%2
   IPv4 地址 . . . . . . . . . . . . : ************
   子网掩码  . . . . . . . . . . . . : *************
   默认网关. . . . . . . . . . . . . : ***********

以太网适配器 以太网:

   媒体状态  . . . . . . . . . . . . : 媒体已断开连接
   连接特定的 DNS 后缀 . . . . . . . :
   
   
   
   
   this.$router.push({
        path: '/example/upload-excel',
        query: {
          param: row.compoundFolder // 保证每次点击路由的query项都是不一样的，确保会重新刷新view
        }
      })







[图片]
tomcat:
/data/tomcat 
启动及关闭脚本(已设置开机自启动)
/opt/tomcat-start.sh
/opt/tomcat-stop.sh

nginx
/data/nginx
systemd服务
systemctl start/stop/restart nginx.service

mysql
systemd服务
systemctl start/stop/restart mysqld.service
账户密码root/Hr@mysql1024
备份脚本/opt/mysql_backup.sh(每天自动备份至/data/backup)
目录
/var/lib/mysql                     mysql数据文件存放路径
/etc/my.cnf                  配置文件路径
/usr/lib64/mysql                 mysql库文件路径
/usr/bin/mysql*                     mysql二进制可执行文件路径
/etc/rc.d/init.d/mysqld             mysql服务管理脚本地址
/var/log/mysqld.log                 mysql日志文件路径      
      
      