[
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "nr",
        "lastmodifytime": "1502269394790",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缂冩垹鐝崘鍛啇",
        "nameen": "Website Content",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "F83BFBB9776551042444011F3BCAD39F"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "ljszb",
        "lastmodifytime": "1502269538201",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "闁剧偓甯寸拋鍓х枂鐞涳拷",
        "nameen": "<PERSON> Settings",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "F73258355BA17A935A82449590CF7BCA"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "tjszb",
        "lastmodifytime": "1502269266995",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缂佺喕顓哥拋鍓х枂鐞涳拷",
        "nameen": "Statistics Settings",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "85D6C25D3DFE8C60A7D3421611324F47"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "gzlgzb",
        "lastmodifytime": "1504008486281",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "瀹搞儰缍斿ù浣筋啎鐠伮ゃ��",
        "nameen": "Workflow Definitions",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showreturnbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "DD0F380E968566AA6F0B897EDC906A9A"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "gzlmxb",
        "lastmodifytime": "1503471964906",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "瀹搞儰缍斿ù浣筋啎鐠佲剝妲戠紒鍡氥��",
        "nameen": "Workflow Definition Details",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showreturnbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "B45ED0302888F265E61E12AF4A9DE674"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "kbmdrjssdb",
        "lastmodifytime": "1502269634500",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐠恒劑鍎撮梻銊﹀娴犳槒顫楅懝鑼额啎鐎规俺銆�",
        "nameen": "Cross-Function Role Setting",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "E02EEE2567D3BF5AC8720517D87FB502"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "ztzd",
        "lastmodifytime": "1502183803063",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "瀹搞儰缍斿ù浣哄Ц閹礁鐡ч崗锟�",
        "nameen": "Workflow Status Codelists",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showreturnbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "994EFF372DAE0F18DC2093FA5503C7D6"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "spjlb",
        "lastmodifytime": "1502178034451",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "瀹搞儰缍斿ù浣筋唶瑜版洝銆�",
        "nameen": "Workflow Records",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showreturnbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "B2A288694DB8E1B5BAC0AA430EDBFE51"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "spmxb",
        "lastmodifytime": "1502178503543",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "瀹搞儰缍斿ù浣规缂佸棜銆�",
        "nameen": "Workflow Details",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showreturnbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "3BDC2D826DCCEEEAB05C436CBBFB4811"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "xmjs",
        "lastmodifytime": "1502270065169",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "妞ゅ湱娲扮憴鎺曞",
        "nameen": "Role",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showreturnbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "5D1371BA632D9438DC246019E82A4BBF"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "jssj",
        "lastmodifytime": "1502269863913",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐟欐帟澹婇弫鐗堝祦",
        "nameen": "Role Data",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "73574C441995A446627C9BDCD884F7AC"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "ggsyr",
        "lastmodifytime": "1502269970914",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缁崵绮虹憴鎺曞閺勭姴鐨犵悰锟�",
        "nameen": "System Role Mapping",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showreturnbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "191CE29C8F0407C1E50C92AFC78173C7"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "dcpzb",
        "lastmodifytime": "1502173758333",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐎电厧鍤柊宥囩枂鐞涳拷",
        "nameen": "Export Configuration Definition",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "499E056FAF88E74483E61DF51ABE73AA"
    },
    {
        "id": "switch_button_show",
        "name": "瀵偓閸忚櫕瀵滈柦顔芥▔缁�锟�",
        "note": "",
        "uuid": "51672BD6293E405A82F7D5FBFFEA4839"
    },
    {
        "id": "field_setting",
        "name": "閹绘劗銇氱�涙顔岀拋鎯х暰",
        "note": "",
        "uuid": "24300F5D434F42C086DD8347C981EC8D"
    },
    {
        "id": "content_setting",
        "name": "閹绘劗銇氶崘鍛啇鐠佹儳鐣�",
        "note": "",
        "uuid": "69FFA5E94D4349D8993A0FDB7F8AAA8F"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "view_filter_set",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺佺増宓佺粵娑⑩偓锟�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "false",
        "uuid": "0C20A8B874294381A35D8269046DB756"
    },
    {
        "id": "work_his_m",
        "name": "瀹搞儰缍旈崢鍡楀蕉鐞涳拷",
        "note": "",
        "uuid": "8B0CDC93AB844317BF82239FB16041A5"
    },
    {
        "id": "work_detail",
        "name": "瀹搞儰缍旈惄顔肩秿",
        "note": "",
        "uuid": "C1B6970069194BC781BD8BEB7D29186E"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "log_event",
        "lastmodifytime": "1435654585185",
        "listeditforminitjs": "",
        "menuinfo": "aaa",
        "modifyreason": "true",
        "name": "缁崵绮洪弮銉ョ箶",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "B170466ABD7B4703B7651DB5ECCC7953"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "log_event_2021_06",
        "lastmodifytime": "1435654585185",
        "listeditforminitjs": "",
        "menuinfo": "aaa",
        "modifyreason": "true",
        "name": "缁崵绮洪弮銉ョ箶",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "6D608CC86EE24ABEA80F8A53BFBB8135"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "modify_history",
        "lastmodifytime": "1516848443608",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "娣囶喗鏁奸崢鍡楀蕉",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "E144C033B3BB4CB7A4E5D0198FED2D63"
    },
    {
        "id": "wf_signature",
        "name": "缁涙儳鐡х拋鏉跨秿",
        "note": "",
        "uuid": "FDF8573DFEE5478288B141178B92EE5E"
    },
    {
        "id": "entry_guide",
        "name": "婵夘偄鍟撻幐鍥у础",
        "note": "",
        "uuid": "839E345D142044F8A9AFB0645DB9619F"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "log_event_2022_05",
        "lastmodifytime": "1435654585185",
        "listeditforminitjs": "",
        "menuinfo": "aaa",
        "modifyreason": "true",
        "name": "缁崵绮洪弮銉ョ箶",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "6C6CAE8C56924F82B51A5651FBE41612"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "log_event_2022_07",
        "lastmodifytime": "1435654585185",
        "listeditforminitjs": "",
        "menuinfo": "aaa",
        "modifyreason": "true",
        "name": "缁崵绮洪弮銉ョ箶",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "C56C90AE8B4F43BAB1419EB32D15155E"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "",
        "forminitjs": "",
        "formvaljs": "",
        "id": "mdbook",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閻㈤潧鐡欐稊锟�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "F4D334419A8A435385723D1F5A3E9EE8"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "attachment",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "闂勫嫪娆�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "37D6D494C65B4F2C8B80F206C37E81A0"
    },
    {
        "id": "mdchapter_menuid",
        "name": "mdchapter_menuid",
        "note": "",
        "uuid": "AAF28B9FDE4641EE8E52B45C9BE4163A"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "mdchapter",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缁旂姾濡�",
        "nameen": "",
        "newtoedit": "true",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "2676F9AD28294FE8838EBFBA394B727F"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "log_event_2023_06",
        "lastmodifytime": "1435654585185",
        "listeditforminitjs": "",
        "menuinfo": "aaa",
        "modifyreason": "true",
        "name": "缁崵绮洪弮銉ョ箶",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "1F105B15153444F19489C5A66FA486EF"
    },
    {
        "id": "eclinichistory",
        "name": "娑撯偓娴ｆ挸瀵查弫鐗堝祦閸氬本顒為弮銉ョ箶",
        "note": "",
        "uuid": "0A561C4B47A046B09F3672848879E46C"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "esign_engine",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "esign_engine",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "3A4807B062DD43A483455BD546CCB1CA"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "esign_instance",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缁惧じ绗傜粵鎯х摟",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "74328B6ECDE447DB9937E07F1C2241C5"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "",
        "forminitjs": "var status = va_getLinkValue('esign_instance','$status','obj.id='+va_getValue('esign_instance_id'),'obj.id asc');
$(%2#showdiv_signed_file[@title=娑撳娴嘳%2).remove();
",
        "formvaljs": "",
        "id": "esign_file",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺傚洣娆�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "B34352EBC1984DFE90771599334A2C47"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "esign_signer",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缁涙儳鐡ф禍锟�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "C9F4FAD06A3448EEB3265462EEE5946D"
    },
    {
        "id": "esign_log",
        "name": "esign_log",
        "note": "",
        "uuid": "********************************"
    },
    {
        "id": "attach_history_log",
        "name": "闂勫嫪娆㈤崢鍡楀蕉鐠佹澘缍嶇悰锟�",
        "note": "",
        "uuid": "24B685666B854FEC854FB0FADC6D7F7E"
    },
    {
        "id": "esign_account",
        "name": "esign_account",
        "note": "",
        "uuid": "39D36B704BB54EB1BCF70491B17782C0"
    },
    {
        "id": "sign_online_auth",
        "name": "閸︺劎鍤庣粵鎯х摟閺夊啴妾�",
        "note": "",
        "uuid": "9190D866614A4F01B8D5D8C8F09B1502"
    },
    {
        "allowreply": "false",
        "appendhtml": "",
        "counthits": "false",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "xsht",
        "lastmodifytime": "*************",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "娑撴潙绨ョ拠鏇㈢崣妞ゅ湱娲�",
        "nameen": "Clinical Studies",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "0",
        "saveas_copyattach": "true",
        "showaddbtn": "true",
        "showreturnbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "F8055C2A607F17B718EEE32381D082C4"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "edc_name",
        "lastmodifytime": "1511331053749",
        "listeditforminitjs": "",
        "menuinfo": "",
        "modifyreason": "true",
        "name": "EDC缁崵绮�",
        "nameen": "EDC",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "6148E3D5747E5B656699FDC8C3932C43"
    },
    {
        "appendhtml": "<script src=%2https://libs.baidu.com/jquery/1.11.1/jquery.min.js%2></script>

<script type=%2text/javascript%2>
    var jQuery_1_11_1 = $.noConflict(true);
</script>",
        "delinnerdata": "true",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "//$(%2#showdiv_change_log%2).css(%2height%2,%2500px%2);
//$(%2#showdiv_change_log .form_table_right%2).html($(%2.uiSpecial-childTable-main%2).html());




jQuery_1_11_1(function($){

$(%2#showdiv_change_log .form_table_right:first%2).replaceWith($(%2#uispeciallistedit_study_coding_ver_update%2));

});

$(%2#uispeciallistedit_study_coding_ver_update%2).parent().css(%2height%2, %2300px%2);
$(%2#uispeciallistedit_study_coding_ver_update%2).parent().css(%2width%2, %2350px%2);
$(%2#fhead_change_log%2).parent().parent().attr(%2valign%2, %2top%2);
$(%2#showdiv_change_log%2).parent().attr(%2rowspan%2, %25%2);
$(%2#showdiv_bz%2).parent().css(%2height%2, %2300px%2);


let globalValue= va_getValue('code_dict');
localStorage.setItem('globalValue',globalValue);



",
        "formvaljs": "",
        "id": "coding_name",
        "lastmodifytime": "1505452647041",
        "listeditforminitjs": "",
        "menuinfo": "",
        "modifyreason": "true",
        "name": "缂傛牜鐖滅�涙鍚�娑撳海閮寸紒锟�",
        "nameen": "Coding Dictionary & System",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "87844ADB6222E40C7B89118371E78B8A"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "dmsop",
        "lastmodifytime": "1503227667500",
        "listeditforminitjs": "",
        "menuinfo": "",
        "modifyreason": "true",
        "name": "CDSC SOPs",
        "nameen": "CDSC SOPs",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "false",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "87AEB650216F51F1AF68E535545D80AE"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "sae_check",
        "lastmodifytime": "1503227351422",
        "listeditforminitjs": "",
        "menuinfo": "",
        "modifyreason": "true",
        "name": "閼筋垳澧跨�瑰鍙忛弫鐗堝祦娑撯偓閼峰瓨鈧勭槷鐎碉拷",
        "nameen": "Safety Data Recocilliation",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "41DDCE11833A40E4510672243EA6BA5F"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
if(eVersion == '' || eVersion == null ){
var note=%2鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!%2
var noteen=%2Please select the current version!%2
var var1= {en:noteen,zh:note};
alert(getLocStr(var1,g_locale));
}
else{
var version=projectNum+%2_%2+eVersion;
va_setValue('xsht_version',version);
}
}
</script>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "var uap_ver=va_getValue('uap_ver');
var bbh=va_getValue('bbh');


if(uap_ver && !bbh){

va_setValue('bbh','V'+uap_ver)
}



if(!g_recordid){

var stuyid=va_getValue('studyid');
var getid =%2obj.id='%2+stuyid+%2'%2;
var projectNum= va_getLinkValue('xsht','studyid',getid,'obj.createtime desc');



var note=%2閸掕泛鐣鹃張顒傤吀閻炲棜顓搁崚鎺旀畱閻╊喚娈戦弰顖氭躬闁潧鐣ч幋鎴濇禇濞夋洝顫夋稉宥–P鐟曚焦鐪�, 閸︹問CH GCP閻ㄥ嫬甯崚娆忕唨绾偓娑撳绱濋弽瑙勫祦閸忣剙寰冮弫鐗堝祦缁狅紕鎮奡OP閿涘苯顕�%2+projectNum+%2妞ゅ湱娲伴惃鍕鎼村﹨鐦灞炬殶閹诡喚顓搁悶鍡欐畱鏉╁洨鈻兼稉搴㈡煙濞夋洖浠涢崗铚傜秼閻ㄥ嫯顓搁崚鎺炵礉楠炶泛寮烽弮鎯邦唶瑜版洘鏆熼幑顔绢吀閻炲棜绻冪粙瀣╄厬閻ㄥ嫰鍣哥憰浣稿綁閺囪揪绱濈憴鍕瘱閺佺増宓佺粻锛勬倞鏉╁洨鈻奸敍宀�鈥樻穱婵嬫敚鐎规艾鎮楅惃鍕殶閹诡喚娈戦崣顖涘嚱濠ф劖鈧佲偓浣稿讲闂冨懓顕伴幀褋鈧礁鐤勯弮鑸碘偓褋鈧礁甯慨瀣偓褍鎷伴崙鍡欌�橀幀褝绱濇稉铏圭埠鐠佲�冲瀻閺嬫劖濮ら崨濠佷簰閸欏﹣澶嶆惔濠勭埡缁岃埖濮ら崨濠冨絹娓氭盯鐝拹銊╁櫤閻ㄥ嫭鏆熼幑顔衡偓锟�%2
var note1=%2The purpose of having the Data Management Plan (DMP) is to be compliant with GCP as well as the regulatory requirement by China health authority. Based on GCP and the SOP of the company, DMP defines the process and methodology of clinical data management in study %2+projectNum+%2, and records all major modifications in a timely manner during the study conduct. DMP standardizes the data management process and ensure the data traceability, legibility, timeliness, completeness, and accuracy, in order to deliver quality data for statistical analysis and clinical research report.%2




var var1= {en:note1,zh:note};
var vv1=getLocStr(var1,g_locale);

va_setValue('dmp_goal',vv1);

}

",
        "formvaljs": "",
        "id": "xshtbbxx",
        "lastmodifytime": "1505204055547",
        "listeditforminitjs": "",
        "menuinfo": "",
        "modifyreason": "true",
        "name": "閻梻鈹掗弬瑙勵攳",
        "nameen": "Protocol",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "C2300963BA9191373E82762FBD934FED"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rand_blind",
        "lastmodifytime": "1510623974313",
        "listeditforminitjs": "",
        "menuinfo": "",
        "modifyreason": "true",
        "name": "闂呭繑婧�閸栨牔绗岄懡顖滃⒖缁狅紕鎮婄化鑽ょ埠",
        "nameen": "RTSM",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "40518A081D7245EBA1F17096B21F1B28"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>

",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "study_coding_plan",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "閸栬顒熺紓鏍垳鐠佲�冲灊",
        "nameen": "Medical Coding Plan",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "80%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "C8BE7FBF8D2042A19A419FC47F733590"
    },
    {
        "id": "study_visit_set",
        "name": "鐠佽儻顫嬮幎銉ユ啞",
        "note": "",
        "uuid": "4D2391966E594191B998AD866328EFFB"
    },
    {
        "id": "study_visit_table",
        "name": "鐠佽儻顫嬬悰銊啎缂冿拷",
        "note": "",
        "uuid": "6A6F02F927384C7682EC6FEEADE9A680"
    },
    {
        "id": "study_crf_visit",
        "name": "鐠佽儻顫嬮幎銉ユ啞",
        "note": "",
        "uuid": "5FE8F3574C3C429580F5B6DEF6601F4C"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "study_crf_visit_table",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐠佽儻顫嬬紓鏍垳",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "1E4A06287DC1478CA1D123996C3B72C5"
    },
    {
        "id": "study_crf_table_pctpt",
        "name": "鐠佽儻顫嬬悰銊╁櫚閺嶇柉顓搁崚锟�",
        "note": "",
        "uuid": "C5409E492D3D489195994D958C1E5ADD"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "",
        "forminitjs": "",
        "formvaljs": "",
        "id": "study_coding_ver_update",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "false",
        "name": "MedDRA閸欐ɑ娲块崢鍡楀蕉",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "94EE9A94AE3C49DF9A85E9E81D91C189"
    },
    {
        "appendhtml": "	<script language=%2javascript%2>


	function mailSendHmtl(){

var href=%2actionsendmail.getemail.do?tableid=%2+g_tableid+%2&recordid=%2+g_recordid+%2&studyid=%2+va_getValue('studyid')+%2&where=1=1%2;

window.top.webmask_show_modalbyurl(%2闁喕娆�%2,encodeURI(href),1000,600);

}
	</script>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "	if(g_recordid){
if(va_getValue('version_zt')!='1')$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:mailSendHmtl();return false;' title='閸欐垿鈧線鍋栨禒绂眜0027>閸欐垿鈧線鍋栨禒绂眜003c/button>%2);
}",
        "formvaljs": "",
        "id": "wysjlx",
        "lastmodifytime": "1503227327234",
        "listeditforminitjs": "",
        "menuinfo": "",
        "modifyreason": "true",
        "name": "婢舵牠鍎撮弫鐗堝祦缁狅紕鎮婄化鑽ょ埠",
        "nameen": "External Data Management",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "64490B23CD2B4A37B79D510502276C2F"
    },
    {
        "appendhtml": "",
        "delinnerdata": "true",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "study_data_blind_plan",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "false",
        "name": "閺佺増宓佺拋鍓ф锤鐠佲�冲灊",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "60%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "1F8D249DAB5244489E39A67DFDEBB628"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "",
        "forminitjs": "",
        "formvaljs": "",
        "id": "study_coding_ver_update1",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "false",
        "name": "WHODrug閸欐ɑ娲块崢鍡楀蕉",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "767783774DC0480DA9C8D832ED5A348E"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "siteinf",
        "lastmodifytime": "1503669472797",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "娑擃厼绺炬稉搴ｇ埡缁屾儼鈧拷",
        "nameen": "Site and Investigator",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "C82EBD8B210E2F0649113CEBE7F71F3E"
    },
    {
        "appendhtml": "<script type=%2text/javascript%2>



function asstAfterSelect(fid){
if(fid=='limitnum'){

var role=','+va_getValue('limitnum')+',';

if(role.includes(%2,TDM,%2)||role.includes(%2,DM,%2)||role.includes(%2,CRO_DM,%2)||role.includes(%2,CRO_TDM,%2)){

$(%2#showdiv_blinding%2).show();
hideFields = hideFields.replace(%2,blinding,%2,%2,%2);

}else{

hideFields += %2blinding,%2;
 
$(%2#showdiv_blinding%2).hide();

va_setValue('blinding','');

}



}


}

</script>",
        "delinnerdata": "true",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "var role=','+va_getValue('limitnum')+',';

if(role.includes(%2,TDM,%2)||role.includes(%2,DM,%2)||role.includes(%2,CRO_DM,%2)||role.includes(%2,CRO_TDM,%2)){


}else{
 
$(%2#showdiv_blinding%2).hide();

va_setValue('blinding','');
hideFields += %2blinding,%2;

}",
        "formvaljs": "",
        "id": "roles",
        "lastmodifytime": "1505288779196",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓佹稉顓炵妇娴滃搫鎲�",
        "nameen": "Data Center Managers",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "80%",
        "saveCloseWindow": "",
        "showaddbtn": "false",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "809E632000ED0B854E6FB25568393764"
    },
    {
        "appendhtml": "<style type=%2text/css%2>
  /* 閹绘劗銇氶崘鍛啇閺嶅嘲绱� */
  .tip{ 

    position:absolute;
    padding: 6px 5px; 
    background-color:#dddddd; 
    border-radius: 4px;
      font-size: 12px;
      position: absolute;
      top: -10%1;
margin-right:80px;
      left: 0%1;
      border: 2px solid #ffffff;
      border-radius: 8px;
      box-shadow: 2px 4px 5px #eeeaea;


  }
  .content{
    cursor:pointer;
    }
  </style>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": " //鐞涖劌宕熼崚婵嗩潗閸栨牞鍓奸張顒婄窗
$(document).ready(function(){ // document.ready閺勵垱瀵歞om閸旂姾娴囩�瑰本鍨氱亸鍗炲讲娴犮儻绱濇稉宥夋付鐟曚礁娴橀悧鍥ф嫲閸忔湹绮挧鍕爱閸旂姾娴囩�瑰苯姘ㄩ崣顖欎簰閹笛嗩攽
  $('.content').click(
   function(event){
     event.stopPropagation();
      $('.tip').fadeIn('slow'); // 濞ｂ�冲弳
   },function(){
	  $('.tip').hide();
	}
  );

  $(document).click(function(){
  $('.tip').fadeOut('slow'); 
  });

  $(%2.tip%2).click(function(event){
  event.stopPropagation();
  });

 $('.content').click(function(e){ // e娑撳搫缍嬮崜宥夌炊閺嶅洣缍旈悽鈺爋m閸忓啰绀岀�电钖�
   var top = e.pageY-100;
   var left = e.pageX;
   $('.tip').css({
    'top' : top+'px',
    'left': left+'px',
    'z-index': '99999'
   });
  });

  // $('.content').mouseout(function(){
  //    $('.tip').hide();
  // });
 });",
        "formvaljs": "",
        "id": "xmgt",
        "lastmodifytime": "1503909169297",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "濞岀喖鈧俺顔囪ぐ锟�",
        "nameen": "Communications",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "BAC60BC42FAE4ED2C4CC558DB5DB918D"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "communication_plan",
        "lastmodifytime": "1503229981437",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "濞岀喖鈧俺顓搁崚锟�",
        "nameen": "Communication Plan",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "ED99B9CDCD30F668D09BE375E8E9E9D4"
    },
    {
        "appendhtml": "<script type=%2text/javascript%2>


function changeContent(){

$(%2#field_classify%2).find(%2.form_table_right%2).html(%2%2);
var transfer_content= va_getValue('transfer_content');

var str1 = transfer_content.substr(1); //閸掔娀娅庢＃鏍х摟缁楊泜r
var str2 =%2'%2+str1.substring(0,str1.length-1).replace(/,/g,%2','%2)+%2'%2;

if(transfer_content){
	var getFlListWhere=%2obj.active='1' and obj.codelist_group='jjmx' and (obj.notes1='%2+va_getValue('js')+%2'  or obj.notes1 is null or obj.code_value in (%2+str2+%2))%2;

}else{

	var getFlListWhere=%2obj.active='1' and obj.codelist_group='jjmx' and (obj.notes1='%2+va_getValue('js')+%2'  or obj.notes1 is null)%2;
	
	}

	var flList=va_getLinkValueStr('codelist','code_value,code_cdescription',getFlListWhere,'obj.code_value asc','all');


	
	
	var flMap = flList.split(',|,');
	var str = '';
	var fl1Value = '';

	for (let i=0; i < flMap.length; i++) {
		
		
		var fl = flMap[i].split(',;,');
		if(i==0){
		fl1Value=fl[0];
		}
str += %2<label class=\%2mselectlabel\%2 for=\%2transfer_content_%2+i+%2\%2><input class=\%2bio-input-checkbox\%2 type=\%2checkbox\%2 id=\%2transfer_content_%2+i+%2\%2 name=\%2transfer_content\%2 value=\%2%2+fl[0]+%2\%2 onclick=\%2javascript:$.pageLogicByField('transfer_content');\%2 onkeydown=\%2pressTab();\%2>%2+fl[1]+%2<\/label>%2;
		
	if((i+1) %1 5 == 0){
str +=%2<br>%2

}
	
		
	
	}

	$(%2#showdiv_transfer_content%2).find(%2.form_table_right%2).html(str);
	va_setValue('transfer_content',transfer_content);



}






function asstAfterSelect(fid){


$(%2#showdiv_transfer_content%2).find(%2.form_table_right%2).html(%2%2);


if(fid=='jc'){
//uispeciallistedit_work_transfer_subject.window.template_reload();

localStorage.setItem('globalValue_role',va_getValue('js'));
localStorage.setItem('globalValue_studyid',va_getValue('studyid'));


changeContent()

}

}






</script>",
        "delinnerdata": "true",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "var treason= va_getValue('treason');

if(treason!=4){
 $(%2#treason_3%2).next().remove();
 $(%2#treason_3%2).remove();
}


if(!g_recordid){


var managername=va_getLinkValue('ryjl','managername','obj.email=\''+g_loginid +'\'','obj.id desc');

va_setValue('leader',managername);


}

if(va_getValue('transfer_content')){
 $('#tr_id1493905546304_556_7').hide()
}else{

 $('#tr_id1493905546304_556_8').hide()
 $('#tr_id1493905546304_556_10').hide()
 $('#tr_id1493905546304_556_11').hide()



}



changeContent();



localStorage.setItem('globalValue_role',va_getValue('js'));
localStorage.setItem('globalValue_studyid',va_getValue('studyid'));

",
        "formvaljs": "var managername=va_getLinkValue('ryjl','managername',%2obj.email='%2+g_loginid +%2'%2,'obj.createtime desc');
var work_group=va_getLinkValue('ryjl','work_group',%2obj.email='%2+g_loginid+%2'%2,'obj.createtime desc');
if(managername=='妫版粌纾搾鍖紆0027){
var manager=va_getLinkValue('ryjl','ry',%2obj.work_group='%2+work_group+%2' and obj.email!='%2+g_loginid+%2' and obj.ylz='2' and managername='妫版粌纾搾鍖紆0027%2,'obj.createtime desc');

va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+manager+%2'%2,'obj.createtime desc'));

}else{
 if(managername.indexOf(%2,%2) < 0){
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+managername+%2'%2,'obj.createtime desc'));
 }else{
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+work_group.split(',').pop()+%2'%2,'obj.createtime desc'));
 }

}",
        "id": "data_management_transfer",
        "lastmodifytime": "1503909207704",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "瀹搞儰缍旀禍銈嗗复鐞涳拷",
        "nameen": "Task Transition",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "70%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "AA985E8EC7CC76F16E862AF221967137"
    },
    {
        "id": "crocont",
        "name": "CRO娴滃搫鎲�",
        "note": "",
        "uuid": "36A4DDCA2E8D48E1B1149E5BCE65DA88"
    },
    {
        "appendhtml": "",
        "delinnerdata": "true",
        "forminitjs": "",
        "formvaljs": "",
        "id": "study_partner",
        "lastmodifytime": "1515404009014",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "CRO",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "D3BC11C4C33440CFBC564F73C1A86A48"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "work_transfer_subject",
        "lastmodifytime": "1503909207704",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "瀹搞儰缍旀禍銈嗗复閸愬懎顔�",
        "nameen": "Task Transition",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "CC5D64D0A533436AB2576BC1CA37E64E"
    },
    {
        "appendhtml": "<script language=%2javascript%2>


function mailSendHmtl(){

var href=%2actionsendmail.getemail.do?tableid=%2+g_tableid+%2&receiver=%2+va_getValue('name')+%2&recordid=%2+g_recordid+%2&studyid=%2+va_getValue('study_id')+%2&where=1=1%2;

window.top.webmask_show_modalbyurl(%2闁喕娆�%2,encodeURI(href),1000,600);

}
</script>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "if(g_recordid){
$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:mailSendHmtl();return false;' title='閸欐垿鈧線鍋栨禒绂眜0027>閸欐垿鈧線鍋栨禒绂眜003c/button>%2);
}",
        "formvaljs": "",
        "id": "study_partner_contacts",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "婢舵牠鍎撮弫鐗堝祦娓氭稑绨查崯锟�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "60%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "F861C200856944BC9C5FDF6172C049BA"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "var studyid=va_getValue('studyid');
var tdm=va_getLinkValue('roles','$member',%2obj.limitnum='TDM' and obj.studyid='%2+studyid+%2' and obj.active='1'%2,'obj.createtime desc');
var loginid=va_getLinkValue('ryjbzl','loginid',%2obj.id='%2+tdm+%2' and obj.zt='1'%2,'obj.createtime desc');


var managername=va_getLinkValue('ryjl','managername',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
var work_group=va_getLinkValue('ryjl','work_group',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
if(managername=='妫版粌纾搾鍖紆0027){
var manager=va_getLinkValue('ryjl','ry',%2obj.work_group='%2+work_group+%2' and obj.email!='%2+loginid+%2' and obj.ylz='2' and managername='妫版粌纾搾鍖紆0027%2,'obj.createtime desc');

va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+manager+%2'%2,'obj.createtime desc'));

}else{
 if(managername.indexOf(%2,%2) < 0){
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+managername+%2'%2,'obj.createtime desc'));
 }else{
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+work_group.split(',').pop()+%2'%2,'obj.createtime desc'));
 }

}",
        "id": "cra",
        "lastmodifytime": "1503550946062",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "妞ゅ湱娲扮紒鍕眽閸涳拷",
        "nameen": "Other Team Members",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "75FB1F7B6DD146A18C3D5BA194EAE910"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "study_user_responsibility",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "娴犺濮熼崚鍡椾紣",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "AC73AFCD9D9648C99FC785FF85571904"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var projectNum=va_getLinkValue('xsht','project_number','obj.id='+va_getValue('project_number'),'');
alert(projectNum);
var now=new Date();
var month=now.getMonth()+1;
var nowtime=(now.getYear())+(now.getMonth()+1)+now.getDate();
if(month<=9)
{
var nowtime=(now.getYear())+'0'+(now.getMonth()+1)+now.getDate();
}
alert(nowtime);
var projVersion=va_getLinkValue('xsht','proj_version','obj.id='+va_getValue('project_number'),'');
var version=projectNum+%2_eCRF_%2+nowtime+%2_%2+projVersion;
alert(projVersion);
alert(version);
va_setValue('crf_version',version);
}
</script>
",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "proj_plan",
        "lastmodifytime": "1503230700625",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "妞ゅ湱娲扮拋鈥冲灊",
        "nameen": "Project Plan",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "CF2706EB86A62D1638CDAF577564837C"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "yqsq",
        "lastmodifytime": "1503232980062",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "瀵よ埖婀￠悽瀹狀嚞",
        "nameen": "Extention Application",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "2F39D12F8EBF513F0892F55A631BCC8A"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "risk_management",
        "lastmodifytime": "1503230117593",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "妞嬪酣娅撶粻锛勬倞娑撳骸绨查幀銉吀閸掞拷",
        "nameen": "Risk Management and Contingency Plan",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "CA5BBD64545037B046491A952270984A"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "if(!g_recordid){
var notes = $(%2#field_notes%2).val();
var studyUserStr = va_getLinkValue('roles','member','obj.studyid='+va_getValue('studyid')+' and obj.active=1 and obj.limitnum in (\'TDM\',\'DM\',\'TDM(QC)\')','','all');
var studyUserMap = studyUserStr.split(',|,');
for (let i=0; i < studyUserMap.length; i++) {notes += %2\n@%2+studyUserMap[i]+%2@%2;}
$(%2#field_notes%2).text(notes);
}",
        "formvaljs": "",
        "id": "project_month_update",
        "lastmodifytime": "1503230117593",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "閺堝牆瀹矰M鏉╂稑鐫�",
        "nameen": "Risk Management and Contingency Plan",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "39EA5F8023B544ADA7367C97CAB1EB7F"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "pro_rand_monthly_progress",
        "lastmodifytime": "1503230117593",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "閺堝牆瀹抽梾蹇旀簚閸栨牞绻樼仦锟�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "170190E208D349D9A384F8E296E56F75"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "",
        "formvaljs": "var jh = va_timeDiff(%2csjhksrq%2,%2csjhjsrq%2);
if(jh>0 && jh!=null) return %2鐠佲�冲灊鐎瑰本鍨氶弮銉︽埂鎼存梹娅勬禍搴ゎ吀閸掓帒绱戞慨瀣）閺堢噦绱�%2;
var sj = va_timeDiff(%2sjqdsj%2,%2sjjssj%2);
if(sj>0 && sj!=null) return %2鐎圭偤妾�瑰本鍨氶弮銉︽埂鎼存梹娅勬禍搴＄杽闂勫懎绱戞慨瀣）閺堢噦绱�%2;


var sjks=va_getValue('sjqdsj')
var sjjs=va_getValue('sjjssj')
if(!sjks && sjjs){
return %2鐎圭偤妾鈧慨瀣）閺堢喍璐熺粚鐑樻閿涘奔绗夐崗浣筋啅閻╁瓨甯存繅顐㈠晸鐎圭偤妾�瑰本鍨氶弮銉︽埂閿涳拷%2;
}


if(va_timeDiff(%2sjjssj%2,%2csjhjsrq%2)>0 && !va_getValue('bz')) return %2鐎圭偤妾紒鎾存将閺冦儲婀￠弲姘艾鐠佲�冲灊缂佹挻娼弮銉︽埂閿涘矁顕繅顐㈠晸婢跺洦鏁炵�涙顔�%2;

",
        "id": "study_task",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鏉╂稑瀹崇拋鈥冲灊",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "273F1D1E25E343BB8F17D463C29839CE"
    },
    {
        "id": "schedule_template",
        "name": "schedule_Template",
        "note": "",
        "uuid": "0E66AB5A8DDA4A12882765D12975D59D"
    },
    {
        "id": "schedule_links",
        "name": "schedule_links",
        "note": "",
        "uuid": "72B385B794E24B80A2A69373DFF3A15E"
    },
    {
        "id": "schedule_work_date_cnf",
        "name": "schedule_work_Date_cnf",
        "note": "",
        "uuid": "0953EE82763E41CAA8D2824EE389ECFB"
    },
    {
        "id": "schedule_work_rename",
        "name": "schedule_complete_name",
        "note": "",
        "uuid": "D31EA03D4004474FBACA093BC70CAC96"
    },
    {
        "appendhtml": "<script>
function asstAfterSelect(fid){



if(fid=='resolved_by'){



var resolved_by=va_getValue('resolved_by');
var resolved_by_Arr=resolved_by.split(%2,%2);
var studyUserRole= va_getLinkValue('cra','limitnum','obj.id='+resolved_by_Arr[resolved_by_Arr.length-1],'obj.id desc');
resolved_by=resolved_by.replace(','+resolved_by_Arr[resolved_by_Arr.length-1],'('+studyUserRole+')');
va_setValue('resolved_by',resolved_by);


}


}



</script>


",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "sysq_management",
        "lastmodifytime": "1503912125203",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "妞ゅ湱娲伴梻顕�顣芥稉搴㈢煛闁拷",
        "nameen": "Comminication records",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "60%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "A220DAB90F9148DCAE5D238324601CF9"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){


var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('study_id'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}
window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);


var studyid=va_getValue('study_id');

var tdm=va_getLinkValue('roles','$member,member',%2obj.limitnum='TDM' and obj.studyid='%2+studyid+%2' and obj.active='1'%2,'obj.createtime desc');
var tdmqc=va_getLinkValue('roles','$member,member',%2obj.limitnum='TDM(QC)' and obj.studyid='%2+studyid+%2' and obj.active='1'%2,'obj.createtime desc');

va_setValue('tdm_name',tdm[1]);
va_setValue('tdm_qc_name',tdmqc[1]);
var loginid=va_getLinkValue('ryjbzl','loginid',%2obj.id='%2+tdm[0]+%2' and obj.zt='1'%2,'obj.createtime desc');


va_setValue('mdm_name',va_getLinkValue('ryjl','managername',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc'));",
        "formvaljs": "",
        "id": "study_qc",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "妞ゅ湱娲扮拹銊╁櫤濡偓閺岋拷",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "691970BDE246454498D72BB95096238C"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "study_regular_review",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "鐎规碍婀＄�光剝鐗�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "D8FC4BDF97A94B389AE25E64BDE5CEF7"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "iframe",
        "forminitjs": "",
        "formvaljs": "",
        "id": "scheduletemplate",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "娴犺濮熷Ο鈩冩緲鐞涳拷",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "8A1A824DBACB4D48B6F745B9FC7F54F9"
    },
    {
        "id": "schedule",
        "name": "瀹搞儰缍旂拋鈥冲灊",
        "note": "",
        "uuid": "C7195753EEED4EF29A408EE6B94B937E"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
if(eVersion == '' || eVersion == null ){
var note=%2鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!%2
var noteen=%2Please select the current version!%2
var var1= {en:noteen,zh:note};
alert(getLocStr(var1,g_locale));

}else{
var version=projectNum+%2_MR%2+eVersion;
va_setValue('manual_rev_prog_version',version);
}
}

function asstAfterSelect(fid){

var va_idStr=va_getValue('$studyid');

var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');

if(fid=='bbh'){

var version=projectNum+%2_MR%2+va_getValue('bbh');
va_setValue('manual_rev_prog_version',version);

}


}






function downloadFormwork(){

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}
window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);

if(!g_recordid ){
 var change_control_code =va_getLinkValue('plan_revision','change_control_code','obj.studyid='+va_getValue('studyid'),'obj.change_control_code desc');
 va_setValue('change_control_code',change_control_code?change_control_code:'NA')
 }",
        "formvaljs": "",
        "id": "manual_rev_prog",
        "lastmodifytime": "1503987082859",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "SAS閺嶅憡鐓″ù瀣槸",
        "nameen": "SAS Review",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "60%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "C78EE75C41B049DF831ADDBFEE1230C4"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
if(eVersion == '' || eVersion == null ){
var note=%2鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!%2
var noteen=%2Please select the current version!%2
var var1= {en:noteen,zh:note};
alert(getLocStr(var1,g_locale));
}else{
var version=projectNum+%2_CRF鐠佹崘顓�%2+eVersion;
va_setValue('crf_version',version);
}
}
</script>",
        "delinnerdata": "false",
        "forminitjs": "va_setValue('crf_manager',g_curruser);
var date = new Date();
var mydate=date.getFullYear()+%2-%2+(date.getMonth()+1)+%2-%2+date.getDate();

va_setValue('rq',mydate);

",
        "formvaljs": "",
        "id": "crf_design",
        "lastmodifytime": "1503912338890",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "CRF鐠佹崘顓�",
        "nameen": "CRF Design",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "6F95A161EBDB4A8FAF0BE6DCF42A15B4"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function asstAfterSelect(fid){

var va_idStr=va_getValue('$studyid');

var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');

if(fid=='bbh'){

var version=projectNum+%2_eCRF%2+va_getValue('bbh');
va_setValue('ecrf_version',version);

}


}




function downloadFormwork(){

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>








",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "middle",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);



$(document).ready(function(){
if(va_getValue('version_zt')!=3||!va_getValue('version_zt')){
//console.log($(%2#version_zt_2%2));
//console.log($(%2#version_zt_2%2).nextElementSibling);
$(%2#version_zt_2%2).next().remove();
$(%2#version_zt_2%2).remove();
}


});

if(!g_recordid ){
 var change_control_code =va_getLinkValue('plan_revision','change_control_code','obj.studyid='+va_getValue('studyid'),'obj.change_control_code desc');
 va_setValue('change_control_code',change_control_code?change_control_code:'NA')
 }


",
        "formvaljs": "var studyid=va_getValue('studyid');
var tdm=va_getLinkValue('roles','$member',%2obj.limitnum='TDM' and obj.studyid='%2+studyid+%2' and obj.active='1'%2,'obj.createtime desc');
var loginid=va_getLinkValue('ryjbzl','loginid',%2obj.id='%2+tdm+%2' and obj.zt='1'%2,'obj.createtime desc');


var managername=va_getLinkValue('ryjl','managername',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
var work_group=va_getLinkValue('ryjl','work_group',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
if(managername=='妫版粌纾搾鍖紆0027){
var manager=va_getLinkValue('ryjl','ry',%2obj.work_group='%2+work_group+%2' and obj.email!='%2+loginid+%2' and obj.ylz='2' and managername='妫版粌纾搾鍖紆0027%2,'obj.createtime desc');

va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+manager+%2'%2,'obj.createtime desc'));

}else{
 if(managername.indexOf(%2,%2) < 0){
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+managername+%2'%2,'obj.createtime desc'));
 }else{
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+work_group.split(',').pop()+%2'%2,'obj.createtime desc'));
 }

}",
        "id": "ecrf_build",
        "lastmodifytime": "1527747625826",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "false",
        "name": "eCRF鐠佹崘顓告稉搴㈡儗瀵わ拷",
        "nameen": "eCRF Design and Setup",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "75%",
        "saveCloseWindow": "",
        "saveas_copyattach": "true",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "7E538B6A5D8340369FCDA5BFCE6FDC73"
    },
    {
        "appendhtml": "<script language=%2javascript%2>



function asstAfterSelect(fid){

var va_idStr=va_getValue('$studyid');

var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');

if(fid=='bbh'){

var version=projectNum+%2_DMRP%2+va_getValue('bbh');
va_setValue('dvp_version',version);

}


}


function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
if(eVersion == '' || eVersion == null ){


var note=%2鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!%2
var noteen=%2Please select the current version!%2
var var1= {en:noteen,zh:note};
alert(getLocStr(var1,g_locale));


}else{
var version=projectNum+%2_DMRP%2+eVersion;
va_setValue('dvp_version',version);
}
}




function downloadFormwork(){
var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}
</script>




",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);

 if(!g_recordid ){
 var change_control_code =va_getLinkValue('plan_revision','change_control_code','obj.studyid='+va_getValue('studyid'),'obj.change_control_code desc');
 va_setValue('change_control_code',change_control_code?change_control_code:'NA')
 }


",
        "formvaljs": "var studyid=va_getValue('studyid');
var tdm=va_getLinkValue('roles','$member',%2obj.limitnum='TDM' and obj.studyid='%2+studyid+%2' and obj.active='1'%2,'obj.createtime desc');
var loginid=va_getLinkValue('ryjbzl','loginid',%2obj.id='%2+tdm+%2' and obj.zt='1'%2,'obj.createtime desc');


var managername=va_getLinkValue('ryjl','managername',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
var work_group=va_getLinkValue('ryjl','work_group',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
if(managername=='妫版粌纾搾鍖紆0027){
var manager=va_getLinkValue('ryjl','ry',%2obj.work_group='%2+work_group+%2' and obj.email!='%2+loginid+%2' and obj.ylz='2' and managername='妫版粌纾搾鍖紆0027%2,'obj.createtime desc');

va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+manager+%2'%2,'obj.createtime desc'));

}else{
 if(managername.indexOf(%2,%2) < 0){
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+managername+%2'%2,'obj.createtime desc'));
 }else{
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+work_group.split(',').pop()+%2'%2,'obj.createtime desc'));
 }

}",
        "id": "edit_check_plan",
        "lastmodifytime": "1503986834500",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "閺佺増宓佺�光剝鐗崇拋鈥冲灊(DMRP)",
        "nameen": "Data Management Review Plan (DMRP)",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "FAD97371E8324AFEAC0BCFB610666606"
    },
    {
        "appendhtml": "<script language=%2javascript%2>



function asstAfterSelect(fid){

var va_idStr=va_getValue('$studyid');

var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');

if(fid=='bbh'){

var version=projectNum+%2_EC%2+va_getValue('bbh');
va_setValue('edit_check_prog_version',version);

}


}
 
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
if(eVersion == '' || eVersion == null ){
var note=%2鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!%2
var noteen=%2Please select the current version!%2
var var1= {en:noteen,zh:note};
alert(getLocStr(var1,g_locale));
}else{
var version=projectNum+%2_EC%2+eVersion;
va_setValue('edit_check_prog_version',version);
}
}

function downloadFormwork(){

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}


window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}
</script>



<style type=%2text/css%2>
  /* 閹绘劗銇氶崘鍛啇閺嶅嘲绱� */
  .tip{ 

    position:absolute;
    padding: 6px 5px; 
    background-color:#dddddd; 
    border-radius: 4px;
      font-size: 12px;
      position: absolute;
      top: -10%1;
margin-right:80px;
      left: 0%1;
      border: 2px solid #ffffff;
      border-radius: 8px;
      box-shadow: 2px 4px 5px #eeeaea;


  }
  .content{
    cursor:pointer;
    }
  </style>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);

 if(!g_recordid ){
 var change_control_code =va_getLinkValue('plan_revision','change_control_code','obj.studyid='+va_getValue('studyid'),'obj.change_control_code desc');
 va_setValue('change_control_code',change_control_code?change_control_code:'NA')
 }
//鐞涖劌宕熼崚婵嗩潗閸栨牞鍓奸張顒婄窗
$(document).ready(function(){ // document.ready閺勵垱瀵歞om閸旂姾娴囩�瑰本鍨氱亸鍗炲讲娴犮儻绱濇稉宥夋付鐟曚礁娴橀悧鍥ф嫲閸忔湹绮挧鍕爱閸旂姾娴囩�瑰苯姘ㄩ崣顖欎簰閹笛嗩攽
  $('.content').click(
   function(event){
     event.stopPropagation();
      $('.tip').fadeIn('slow'); // 濞ｂ�冲弳
   },function(){
	  $('.tip').hide();
	}
  );

  $(document).click(function(){
  $('.tip').fadeOut('slow'); 
  });

  $(%2.tip%2).click(function(event){
  event.stopPropagation();
  });

 $('.content').click(function(e){ // e娑撳搫缍嬮崜宥夌炊閺嶅洣缍旈悽鈺爋m閸忓啰绀岀�电钖�
   var top = e.pageY-100;
   var left = e.pageX;
   $('.tip').css({
    'top' : top+'px',
    'left': left+'px',
    'z-index': '99999'
   });
  });

  // $('.content').mouseout(function(){
  //    $('.tip').hide();
  // });
 });",
        "formvaljs": "",
        "id": "edit_check_prog",
        "lastmodifytime": "1503986846390",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "闁槒绶弽鍛婄叀缁嬪绨�",
        "nameen": "Edit Check Programming/Testing",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "true",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "66C499A630A14E47AB34F7F6EFC77A98"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('dqbb');
if(eVersion == '' || eVersion == null ){
var note=%2鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!%2
var noteen=%2Please select the current version!%2
var var1= {en:noteen,zh:note};
alert(getLocStr(var1,g_locale));
}else{
var version=projectNum+%2_UAT%2+eVersion;
va_setValue('uat_version',version);
}
}

 
function asstAfterSelect(fid){

var va_idStr=va_getValue('$studyid');

var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');

if(fid=='dqbb'){

var version=projectNum+%2_UAT%2+va_getValue('dqbb');
va_setValue('uat_version',version);

}


}


function downloadFormwork(){
var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>




<script language=%2javascript%2>


	function mailSendHmtl(){

var href=%2actionsendmail.getemail.do?tableid=%2+g_tableid+%2&recordid=%2+g_recordid+%2&studyid=%2+va_getValue('studyid')+%2&where=1=1%2;

window.top.webmask_show_modalbyurl(%2闁喕娆�%2,encodeURI(href),1000,600);

}
	</script>



",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);


 if(!g_recordid ){
 var change_control_code =va_getLinkValue('plan_revision','change_control_code','obj.studyid='+va_getValue('studyid'),'obj.change_control_code desc');
 va_setValue('change_control_code',change_control_code?change_control_code:'NA')
 }
",
        "formvaljs": "var studyid=va_getValue('studyid');
var tdm=va_getLinkValue('roles','$member',%2obj.limitnum='TDM' and obj.studyid='%2+studyid+%2' and obj.active='1'%2,'obj.createtime desc');
var loginid=va_getLinkValue('ryjbzl','loginid',%2obj.id='%2+tdm+%2' and obj.zt='1'%2,'obj.createtime desc');


var managername=va_getLinkValue('ryjl','managername',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
var work_group=va_getLinkValue('ryjl','work_group',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
if(managername=='妫版粌纾搾鍖紆0027){
var manager=va_getLinkValue('ryjl','ry',%2obj.work_group='%2+work_group+%2' and obj.email!='%2+loginid+%2' and obj.ylz='2' and managername='妫版粌纾搾鍖紆0027%2,'obj.createtime desc');

va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+manager+%2'%2,'obj.createtime desc'));

}else{
 if(managername.indexOf(%2,%2) < 0){
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+managername+%2'%2,'obj.createtime desc'));
 }else{
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+work_group.split(',').pop()+%2'%2,'obj.createtime desc'));
 }

}",
        "id": "uta",
        "lastmodifytime": "1503987428328",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "UAT",
        "nameen": "Team UAT",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "09EB280530CB4B0AB5C6FD7B064C26C8"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
if(eVersion == '' || eVersion == null ){
var note=%2鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!%2
var noteen=%2Please select the current version!%2
var var1= {en:noteen,zh:note};
alert(getLocStr(var1,g_locale));
}else{
var version=projectNum+%2_PD%2+eVersion;
va_setValue('pv_version',version);
}
}

function asstAfterSelect(fid){

var va_idStr=va_getValue('$studyid');

var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');

if(fid=='bbh'){

var version=projectNum+%2_PD%2+va_getValue('bbh');
va_setValue('pv_version',version);

}


}

function downloadFormwork(){

var count = va_getLinkCount('xsht','obj.id='+va_getValue('studyid')+' and obj.start_date>=to_date(\'2021-12-15\',\'yyyy-mm-dd\')');
if(count==0){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120obj.version_date%13C%13Dto_date(%1272021-12-15%127%12C%127yyyy-mm-dd%127)';
}else{
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';
}

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}
</script>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);

 if(!g_recordid ){
 var change_control_code =va_getLinkValue('plan_revision','change_control_code','obj.studyid='+va_getValue('studyid'),'obj.change_control_code desc');
 va_setValue('change_control_code',change_control_code?change_control_code:'NA')
 }",
        "formvaljs": "",
        "id": "pv",
        "lastmodifytime": "1503986914468",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺傝顢嶆潻婵婂剹鐎规矮绠�",
        "nameen": "Protocol Deviation Definition",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "A20B51BB2BCB457F902CF02BB43BD47A"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>



<script language=%2javascript%2>


	function mailSendHmtl(){

var href=%2actionsendmail.getemail.do?tableid=%2+g_tableid+%2&recordid=%2+g_recordid+%2&studyid=%2+va_getValue('studyid')+%2&where=1=1%2;

window.top.webmask_show_modalbyurl(%2闁喕娆�%2,encodeURI(href),1000,600);

}
	</script>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": " if(!g_recordid ){
 var change_control_code =va_getLinkValue('plan_revision','change_control_code','obj.studyid='+va_getValue('studyid'),'obj.change_control_code desc');
 va_setValue('change_control_code',change_control_code?change_control_code:'NA')
 }",
        "formvaljs": "",
        "id": "edc_go_live",
        "lastmodifytime": "1503285897234",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "EDC娑撳﹦鍤庢稉搴☆槵娴犲�燁吀閸掞拷",
        "nameen": "eCRF Publishing and Backup Plan",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "5368DD9DB82D4C179ECDD3865B25F56C"
    },
    {
        "appendhtml": "<script language=%2javascript%2>


function downloadFormwork(){


var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}
window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>



<script language=%2javascript%2>


function mailSendHmtl(){
if(va_getValue('type').indexOf('04')!=-1 &&!va_getValue('cljg')){
var href=%2actionsendmail.getemail.do?tableid=%2+g_tableid+%2&recordid=%2+g_recordid+%2&studyid=%2+va_getValue('studyid')+%2&where=obj.type='04'%2;}
else if(!va_getValue('cljg') && va_getValue('system').indexOf('00')!=-1){
var href=%2actionsendmail.getemail.do?tableid=%2+g_tableid+%2&recordid=%2+g_recordid+%2&studyid=%2+va_getValue('studyid')+%2&where=obj.type='042'%2;}
else if(va_getValue('system').indexOf('05')!=-1){

var href=%2actionsendmail.getemail.do?tableid=%2+g_tableid+%2&recordid=%2+g_recordid+%2&studyid=%2+va_getValue('studyid')+%2&where=obj.type='FTP'%2;}


else{

var href=%2actionsendmail.getemail.do?tableid=%2+g_tableid+%2&recordid=%2+g_recordid+%2&studyid=%2+va_getValue('studyid')+%2&where=obj.type='041'%2;}




window.top.webmask_show_modalbyurl(%2闁喕娆�%2,encodeURI(href),1000,600);

}
	</script>


<style type=%2text/css%2>
  /* 閹绘劗銇氶崘鍛啇閺嶅嘲绱� */
  .tip{ 

    position:absolute;
    padding: 6px 5px; 
    background-color:#dddddd; 
    border-radius: 4px;
      font-size: 12px;
      position: absolute;
      top: -10%1;
margin-right:80px;
      left: 0%1;
      border: 2px solid #ffffff;
      border-radius: 8px;
      box-shadow: 2px 4px 5px #eeeaea;


  }
  .content{
    cursor:pointer;
    }
  </style>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "if(g_readonly){

$(%2#tabtr1%2).append(%2<div id='function_btn_tr' class='editdata-btn-tool' needshow='true'><div id='tbl_buttons' class='tool_btn clearfix' style='visibility: visible;'><button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button></div></div>%2);




}else{

$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);
}





//鐞涖劌宕熼崚婵嗩潗閸栨牞鍓奸張顒婄窗
$(document).ready(function(){ // document.ready閺勵垱瀵歞om閸旂姾娴囩�瑰本鍨氱亸鍗炲讲娴犮儻绱濇稉宥夋付鐟曚礁娴橀悧鍥ф嫲閸忔湹绮挧鍕爱閸旂姾娴囩�瑰苯姘ㄩ崣顖欎簰閹笛嗩攽
  $('.content').click(
   function(event){
     event.stopPropagation();
      $('.tip').fadeIn('slow'); // 濞ｂ�冲弳
   },function(){
	  $('.tip').hide();
	}
  );

  $(document).click(function(){
  $('.tip').fadeOut('slow'); 
  });

  $(%2.tip%2).click(function(event){
  event.stopPropagation();
  });

 $('.content').click(function(e){ // e娑撳搫缍嬮崜宥夌炊閺嶅洣缍旈悽鈺爋m閸忓啰绀岀�电钖�
   var top = e.pageY-100;
   var left = e.pageX;
   $('.tip').css({
    'top' : top+'px',
    'left': left+'px',
    'z-index': '99999'
   });
  });

  // $('.content').mouseout(function(){
  //    $('.tip').hide();
  // });
 });",
        "formvaljs": "",
        "id": "edc_account",
        "lastmodifytime": "*************",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "鐠愶附鍩涚粻锛勬倞",
        "nameen": "EDC Account Management",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "60%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "********************************"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "manual_rev_plan",
        "lastmodifytime": "*************",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閹靛浼愰弽鍛婄叀",
        "nameen": "Manual Review",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "810C03AAEF9143A58351B15FD25D5B0F"
    },
    {
        "appendhtml": "<script language=%2javascript%2>


function downloadFormwork(){

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}
window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}


</script>


",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);



",
        "formvaljs": "",
        "id": "lnr",
        "lastmodifytime": "1503650491562",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "鐎圭偤鐛欑�广倕寮懓鍐瘱閸ワ拷",
        "nameen": "Lab Reference Range",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "45B8509A49A54C3EA5F0A51F2CD748A2"
    },
    {
        "appendhtml": "<script language=%2javascript%2>


function downloadFormwork(){

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);
if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}
window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}
</script>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "if(g_readonly){

$(%2#tabtr1%2).append(%2<div id='function_btn_tr' class='editdata-btn-tool' needshow='true'><div id='tbl_buttons' class='tool_btn clearfix' style='visibility: visible;'><button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button></div></div>%2);




}else{

$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);
}
",
        "formvaljs": "",
        "id": "user_train",
        "lastmodifytime": "1503286096109",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閻€劍鍩涢崺纭咁唲",
        "nameen": "User Training",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "60%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "9A6383EF2DD04BCCBBDCE79AD592D7DB"
    },
    {
        "appendhtml": "<script language=%2javascript%2>


function downloadFormwork(){

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);
if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}
window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}
</script>






",
        "delinnerdata": "true",
        "delrefdata": "true",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);


if(!va_getValue('edm_phone')){

va_setValue('edm_phone',va_getLinkValue('ryjl','sjh',%2obj.email='%2+g_loginid+%2'%2));

}

if(va_getLinkCount('ryjbzl',%2obj.loginid = '%2+g_loginid+%2' and obj.ryjs like '%1EDM%1'%2)==0 && g_loginid!='supersa'){

var linkA = $('#showdiv_dta_doc').find('a');
for(var i = 0; i < linkA.length; i++){
    //linkA.eq(i).attr('href', 'javascript:void(0)');
    //linkA.eq(i).attr('target', '');




}
//linkA.css(%2color%2, %2#303133%2);

}


var ext_data_type=va_getValue('ext_data_type');
var ds_type_now=va_getValue('ds_type');

var dtasc=va_getLinkValue('wysjlx','bz','obj.studyid='+va_getValue('studyid'));
dtascList=dtasc.split(';');
var ds_type='';
for (var i=1; i<=dtascList.length; i++){
    var dtasctype = dtascList[i-1].split(':');
   if(dtasctype[0]==ext_data_type){
		ds_type=dtasctype[1];

        break;
   }
}
if(!ds_type_now){
va_setValue('ds_type',ds_type);
}







",
        "formvaljs": "",
        "id": "ext_data",
        "lastmodifytime": "1503276823234",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "閺佺増宓佹导鐘虹翻閸楀繗顔�",
        "nameen": "Data Transfer Agreement",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "50E6811C6B5A4F9DADB84F33898445B6"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "im",
        "lastmodifytime": "1503286633953",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閻梻鈹掗懓鍛窗鐠侊拷",
        "nameen": "Investigator Meeting",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "7928D3AD7E404731B420D5012B573679"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('dqbb');
if(eVersion == '' || eVersion == null ){
var note=%2鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!%2
var noteen=%2Please select the current version!%2
var var1= {en:noteen,zh:note};
alert(getLocStr(var1,g_locale));
}else{
var version=projectNum+%2_eCRF CCG%2+eVersion;
va_setValue('ecrf_version',version);
}
}


function asstAfterSelect(fid){

var va_idStr=va_getValue('$studyid');

var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');

if(fid=='dqbb'){

var version=projectNum+%2_eCRF CCG%2+va_getValue('dqbb');
va_setValue('ecrf_version',version);

}


}


function downloadFormwork(){

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}
window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>



",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);


 
 if(!g_recordid ){
 var change_control_code =va_getLinkValue('plan_revision','change_control_code','obj.studyid='+va_getValue('studyid'),'obj.change_control_code desc');
 va_setValue('change_control_code',change_control_code?change_control_code:'NA')
 }
",
        "formvaljs": "var studyid=va_getValue('studyid');
var tdm=va_getLinkValue('roles','$member',%2obj.limitnum='TDM' and obj.studyid='%2+studyid+%2' and obj.active='1'%2,'obj.createtime desc');
var loginid=va_getLinkValue('ryjbzl','loginid',%2obj.id='%2+tdm+%2' and obj.zt='1'%2,'obj.createtime desc');


var managername=va_getLinkValue('ryjl','managername',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
var work_group=va_getLinkValue('ryjl','work_group',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
if(managername=='妫版粌纾搾鍖紆0027){
var manager=va_getLinkValue('ryjl','ry',%2obj.work_group='%2+work_group+%2' and obj.email!='%2+loginid+%2' and obj.ylz='2' and managername='妫版粌纾搾鍖紆0027%2,'obj.createtime desc');

va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+manager+%2'%2,'obj.createtime desc'));

}else{
 if(managername.indexOf(%2,%2) < 0){
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+managername+%2'%2,'obj.createtime desc'));
 }else{
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+work_group.split(',').pop()+%2'%2,'obj.createtime desc'));
 }

}",
        "id": "ecrf_build1",
        "lastmodifytime": "1503992114656",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "eCRF婵夘偄鍟撻幐鍥у础",
        "nameen": "CRF Completion Guideline",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "true",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "9AAC8D29457C4BCFA930AEAAE39C4246"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
var version=projectNum+%2%2+eVersion;
va_setValue('ecrf_version',version);
}

function downloadFormwork(){


var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>






	<script language=%2javascript%2>


	function mailSendHmtl(){

var href=%2actionsendmail.getemail.do?tableid=%2+g_tableid+%2&recordid=%2+g_recordid+%2&studyid=%2+va_getValue('studyid')+%2&where=1=1%2;

window.top.webmask_show_modalbyurl(%2闁喕娆�%2,encodeURI(href),1000,600);

}
	</script>




",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);

 if(!g_recordid ){
 var change_control_code =va_getLinkValue('plan_revision','change_control_code','obj.studyid='+va_getValue('studyid'),'obj.change_control_code desc');
 va_setValue('change_control_code',change_control_code?change_control_code:'NA')
 }





",
        "formvaljs": "var studyid=va_getValue('studyid');
var tdm=va_getLinkValue('roles','$member',%2obj.limitnum='TDM' and obj.studyid='%2+studyid+%2' and obj.active='1'%2,'obj.createtime desc');
var loginid=va_getLinkValue('ryjbzl','loginid',%2obj.id='%2+tdm+%2' and obj.zt='1'%2,'obj.createtime desc');


var managername=va_getLinkValue('ryjl','managername',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
var work_group=va_getLinkValue('ryjl','work_group',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
if(managername=='妫版粌纾搾鍖紆0027){
var manager=va_getLinkValue('ryjl','ry',%2obj.work_group='%2+work_group+%2' and obj.email!='%2+loginid+%2' and obj.ylz='2' and managername='妫版粌纾搾鍖紆0027%2,'obj.createtime desc');

va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+manager+%2'%2,'obj.createtime desc'));

}else{
 if(managername.indexOf(%2,%2) < 0){
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+managername+%2'%2,'obj.createtime desc'));
 }else{
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+work_group.split(',').pop()+%2'%2,'obj.createtime desc'));
 }

}",
        "id": "ecrf_build2",
        "lastmodifytime": "1503285559515",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "EDC娑撳﹦鍤庣�光剝澹�",
        "nameen": "eCRF Publishing Approval",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "C88BA4190F8645778EB30A4F2C494F61"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "esignature_statement",
        "lastmodifytime": "1503286145718",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閻梻鈹掗懓鍛暩鐎涙劗顒烽崥宥囨畱婢圭増妲�",
        "nameen": "Investigator e-Signature Statement",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "8EEE113DEEB04ABE80557C518B3B58AC"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>

",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "edc_permission_his",
        "lastmodifytime": "1503383469250",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "EDC閻€劍鍩涢弶鍐閸樺棗褰�",
        "nameen": "EDC User History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "425F1066569042D8BC532C9DA600031A"
    },
    {
        "appendhtml": "<script language=%2javascript%2>


function downloadFormwork(){


var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('xmdm'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}


window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>


<script type=%2text/javascript%2>









function changeContent(){

var jcnr= va_getValue('jcnr');

if((jcnr.match(/,/g) || []).length>=3){


$(%2#showdiv_jcnr%2).find(%2.form_table_right%2).html(%2%2);
var str1 = jcnr.substr(1); //閸掔娀娅庢＃鏍х摟缁楊泜r
var str2 =%2'%2+str1.substring(0,str1.length-1).replace(/,/g,%2','%2)+%2'%2;
if(jcnr){
	var getFlListWhere=%2obj.active='1' and obj.codelist_name='DBQC' and (obj.code_value not in ('00','01','02','04','05','06','07')  or obj.code_value in (%2+str2+%2))%2;

}else{

	var getFlListWhere=%2obj.active='1' and obj.codelist_name='DBQC' and obj.code_value not in ('00','01','02','04','05','06','07')%2;
	
}

	var flList=va_getLinkValueStr('codelist','code_value,code_cdescription',getFlListWhere,'obj.code_value asc','all');
	var flMap = flList.split(',|,');
	var str = '';
	for (let i=0; i < flMap.length; i++) {		
	var fl = flMap[i].split(',;,');
	str += %2<label class=\%2mselectlabel\%2 for=\%2jcnr_%2+i+%2\%2><input class=\%2bio-input-checkbox\%2 type=\%2checkbox\%2 id=\%2jcnr_%2+i+%2\%2 name=\%2jcnr\%2 value=\%2%2+fl[0]+%2\%2 onclick=\%2javascript:$.pageLogicByField('jcnr');\%2 onkeydown=\%2pressTab();\%2>%2+fl[1]+%2<\/label>%2;
		
	if((i+1) %1 5 == 0){
		str +=%2<br>%2
	}
	
		
	
	}

	$(%2#showdiv_jcnr%2).find(%2.form_table_right%2).append(str);
	
	va_setValue('jcnr',jcnr);

	
	}else{
	

	$(%2#showdiv_jcnr%2).find(%2.form_table_right%2).hide();

	jcnr=jcnr.replaceAll(%2,%2,%2%2);
	
	var getFlListWhere=%2obj.active='1' and obj.codelist_name='DBQC' and (obj.code_value not in ('00','01','02','04','05','06','07') or  obj.code_value='%2+jcnr+%2')%2;

	var flList=va_getLinkValueStr('codelist','code_value,code_cdescription',getFlListWhere,'obj.code_value asc','all');
	
	
	
	var flMap = flList.split(',|,');
	var str = %2<select id=\%2field_jcnr_select\%2 name=\%2jcnr\%2 onchange=\%2javascript:$.pageLogicByField('jcnr');va_setValue('jcnr',','+$('#field_jcnr_select').val()+',');\%2 onkeydown=\%2var k=event.keyCode;if(k==38||k==37){sendShiftTabKey();return;} if(k==39||k==40||k==13){event.keyCode=9; return;}\%2><option value=\%2\%2></option>%2;

	for (let i=0; i < flMap.length; i++) {
		
	var fl = flMap[i].split(',;,');

	str += %2<option value=\%2%2+fl[0]+%2\%2>%2+fl[1]+%2</option>%2;
	
	}
	
	$(%2#showdiv_jcnr%2).find(%2.form_table_right%2).after(str);
		
$('#field_jcnr_select').val(jcnr);
}



}















</script>



",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);

 
 if(!g_recordid ){
 var change_control_code =va_getLinkValue('plan_revision','change_control_code','obj.studyid='+va_getValue('xmdm'),'obj.change_control_code desc');
 va_setValue('change_control_code',change_control_code?change_control_code:'NA')
 }

changeContent();


",
        "formvaljs": "",
        "id": "sjk",
        "lastmodifytime": "1503285112890",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓佹惔鎾茬瑐缁捐儻宸濋幒锟�",
        "nameen": "Database QC",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "DC097562124E475787DB65EC808A5004"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "interim_analysis",
        "lastmodifytime": "1503227593250",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺堢喍鑵戦崚鍡樼�界拋鈥冲灊",
        "nameen": "Interim Analysis Plan",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "DF78DA3EE84B453EAE7FBDFDA36C9579"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "var date = new Date();
var mydate=date.getFullYear()+%2-%2+(date.getMonth()+1)+%2-%2+date.getDate();
va_setValue('rq',mydate);",
        "formvaljs": "",
        "id": "xm_view",
        "lastmodifytime": "1503286727531",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "妞ゅ湱娲扮憴鍡楁禈鐎规矮绠�",
        "nameen": "View Definition",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "18C97E291F67470FBAAC60FB497CD230"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "var date = new Date();
var mydate=date.getFullYear()+%2-%2+(date.getMonth()+1)+%2-%2+date.getDate();
va_setValue('date',mydate);",
        "formvaljs": "",
        "id": "edm_account",
        "lastmodifytime": "*************",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "EDM鐠愶附鍩涚粻锛勬倞",
        "nameen": "EDM Account Management",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "084304D09BFC4FD6B9947F7FE761F0E7"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function asstAfterSelect(fid){

var va_idStr=va_getValue('$studyid');

var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');

if(fid=='bbh'){

var version=projectNum+%2_DVS%2+va_getValue('bbh');
va_setValue('dvs_version',version);

}


}


function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
if(eVersion == '' || eVersion == null ){


var note=%2鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!%2
var noteen=%2Please select the current version!%2
var var1= {en:noteen,zh:note};
alert(getLocStr(var1,g_locale));


}else{
var version=projectNum+%2_DVS%2+eVersion;
va_setValue('dvs_version',version);
}
}


function downloadFormwork(){

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}


window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}
</script>



",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);

 if(!g_recordid ){
 var change_control_code =va_getLinkValue('plan_revision','change_control_code','obj.studyid='+va_getValue('studyid'),'obj.change_control_code desc');
 va_setValue('change_control_code',change_control_code?change_control_code:'NA')
 }

",
        "formvaljs": "",
        "id": "study_dvs",
        "lastmodifytime": "1503986834500",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓侀弽鍛婄叀鐠囧瓨妲�(DVS)",
        "nameen": "Data Validation Specification 閿涘湒VS閿涳拷",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "49DDA0D79B484817ACDD8465DE0F5B40"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
if(eVersion == '' || eVersion == null ){
var note=%2鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!%2
var noteen=%2Please select the current version!%2
var var1= {en:noteen,zh:note};
alert(getLocStr(var1,g_locale));
}else{
var version=projectNum+%2_EDMP%2+eVersion;
va_setValue('edmp_ver',version);
}
}

function downloadFormwork(){

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}


window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}


</script>




	",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);


if(!g_recordid){
var study_staus= va_getLinkValue('xsht','$zt','obj.id ='+va_getValue('studyid'),'obj.createtime desc');

var protocolVer= va_getLinkValue('xshtbbxx','xsht_version','obj.studyid='+va_getValue('studyid'),'obj.createtime desc');

va_setValue('protocol_ver',protocolVer);

if(study_staus==15){
va_setValue('version_zt','1')
}else if(study_staus==20){
va_setValue('version_zt','2')
}else if(study_staus==30){
va_setValue('version_zt','3')
}}

",
        "formvaljs": "",
        "id": "study_edm_plan",
        "lastmodifytime": "1503999556422",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "婢舵牠鍎撮弫鐗堝祦缁狅紕鎮婄拋鈥冲灊",
        "nameen": "Data Management Plan",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "D080103CFFA84645BFC4F51DA9694B66"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);

if(va_getValue('zq')=='5'){

$(%2#showdiv_reconciliation_check%2).css(%2display%2, %2none%2);
$(%2#showdiv_outboard_data_doc%2).css(%2display%2, %2none%2);
$(%2#showdiv_is_submission%2).css(%2display%2, %2none%2);


}

if(va_getLinkCount('ryjbzl',%2obj.loginid = '%2+g_loginid+%2' and obj.ryjs like '%1EDM%1'%2)==0){

var linkA = $('#showdiv_outboard_data_doc').find('a');
for(var i = 0; i < linkA.length; i++){
   // linkA.eq(i).attr('href', 'javascript:void(0)');
    //linkA.eq(i).attr('target', '');
}
//linkA.css(%2color%2, %2#303133%2)

}




//鐞涖劌宕熼崚婵嗩潗閸栨牞鍓奸張顒婄窗
$(document).ready(function(){ // document.ready閺勵垱瀵歞om閸旂姾娴囩�瑰本鍨氱亸鍗炲讲娴犮儻绱濇稉宥夋付鐟曚礁娴橀悧鍥ф嫲閸忔湹绮挧鍕爱閸旂姾娴囩�瑰苯姘ㄩ崣顖欎簰閹笛嗩攽
  $('.content').click(
   function(event){
     event.stopPropagation();
      $('.tip').fadeIn('slow'); // 濞ｂ�冲弳
   },function(){
	  $('.tip').hide();
	}
  );

  $(document).click(function(){
  $('.tip').fadeOut('slow'); 
  });

  $(%2.tip%2).click(function(event){
  event.stopPropagation();
  });

 $('.content').click(function(e){ // e娑撳搫缍嬮崜宥夌炊閺嶅洣缍旈悽鈺爋m閸忓啰绀岀�电钖�
   var top = e.pageY-100;
   var left = e.pageX;
   $('.tip').css({
    'top' : top+'px',
    'left': left+'px',
    'z-index': '99999'
   });
  });

  // $('.content').mouseout(function(){
  //    $('.tip').hide();
  // });
 });",
        "formvaljs": "",
        "id": "study_ext_test_data",
        "lastmodifytime": "1503813986140",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "婢舵牠鍎村ù瀣槸閺佺増宓侀懛顏勫З閸栨牜鏁撻幋锟�",
        "nameen": "External Data Management",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "59D8D4DDF388474291D0DF4C5AF940C6"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}
window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}


</script>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "",
        "forminitjs": "if(!va_getValue('dev_url')){
$.pageHideLine('7-11');

}


$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);",
        "formvaljs": "",
        "id": "sqcshj",
        "lastmodifytime": "1531985980035",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "閻㈠疇顕悽闈涚摍閸栨牜閮寸紒锟�",
        "nameen": "Request EDC Instances",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "600px",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "16A4995052E14DB288B77E171A6F3B27"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "dta_bind_method",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "false",
        "name": "閺佺増宓佺拋鍓ф锤鐠佹崘顓告稉搴☆吀閺嶏拷",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "EA99E057F3E3419B8FCB9B4FE9FD03C4"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "prot_workflow",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺傝顢嶅ù浣衡柤閸ワ拷",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "60%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "79745AEB2A004FBFB9944DFD185294A6"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "edcblind",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "EDC鐎涙顔岀拋鍓ф锤濞村鐦稉搴☆吀閺嶏拷",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "60%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "D39112FA5D304E2ABD93C4339F483B34"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>


",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "if(!g_recordid ){
 var change_control_code =va_getLinkValue('plan_revision','change_control_code','obj.studyid='+va_getValue('studyid'),'obj.change_control_code desc');
 va_setValue('change_control_code',change_control_code?change_control_code:'NA')
 }

 ",
        "formvaljs": "",
        "id": "prod_report",
        "lastmodifytime": "1503270300453",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "EDC閻㈢喍楠囬悳顖氼暔娴ｈ法鏁�",
        "nameen": "EDC Production Use",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "407AFF05580B4F44B6F8B44477649476"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "fsfv",
        "lastmodifytime": "1503226576265",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "FSFV",
        "nameen": "FSFV",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "E0A45773267A4A0096BCC7E751FDEE8B"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>

",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "project_report",
        "lastmodifytime": "1503227662328",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "瀹告彃鐣幋鎰畱閺佺増宓佺粻锛勬倞瀹搞儰缍�",
        "nameen": "Completed data cleanings",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "3963923E9BEF90C0A87B24611632A2B1"
    },
    {
        "appendhtml": "<script type=%2text/javascript%2 src=%2/public/js/jquery-3.2.1.min.js%2></script>
         <script type=%2text/javascript%2>
             var jQuery2 = $.noConflict(true);

         </script>

<script language=%2javascript%2>


function downloadFormwork(){


var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}


</script>



<style type=%2text/css%2>
  /* 閹绘劗銇氶崘鍛啇閺嶅嘲绱� */
  .tip{ 

    position:absolute;
    padding: 6px 5px; 
    background-color:#dddddd; 
    border-radius: 4px;
      font-size: 12px;
      position: absolute;
      top: -10%1;
margin-right:80px;
      left: 0%1;
      border: 2px solid #ffffff;
      border-radius: 8px;
      box-shadow: 2px 4px 5px #eeeaea;


  }
  .content{
    cursor:pointer;
    }
  </style>







<style>
    /* inputSelect */
		div.inputSelectDiv {
		    width: 220px;
			position: relative;
		}
		div.inputSelectDiv .inputSelect {
			width: 300px;
			height: 22px;
			line-height: 18px;
			margin: 0;
			border: 1px solid #DBDFE6;
		    cursor: pointer;
		}
		div.inputSelectDiv ul{
z-index: 9999;

			display: none;
			width: 218px;
			max-height: 200px;
			padding: 0;
			margin: 0;
			position: absolute;
			top: 24px;
			left: 0;
			border: 1px solid #DBDFE6;
			overflow: auto;
		    background-color: #fff;
		    border-top: 0;
		    scrollbar-arrow-color: #a2a2a2;
		    scrollbar-face-color: #c1c1c1;
		    scrollbar-shadow-color: #f1f1f1;
		    scrollbar-track-color: #f1f1f1;
		}
		div.inputSelectDiv ul li {
			list-style: none;
			line-height: 30px;
			padding: 0 10px;
		}
		div.inputSelectDiv ul li.selectLi {
		    cursor: pointer;
		}
		div.inputSelectDiv ul li.active {
			background-color: #5FB878;
			color: #fff;
		}
		div.inputSelectDiv ul li.selectLi:hover {
			background-color: #bbb;
		}
		div.inputSelectDiv.hover ul {
			display: block;
		}
  </style>





<script>


   // 閺傚洦婀板鍡樻暜閹镐線鈧瀚�
		/* 鏉烆剙瀵茬�涙鍚�閸婄唱r
		  @param data
		  @param inputId 娓氭盯鈧瀚ㄩ惃鍕瀮閺堫剚顢婭D 
		  @param dataSource 闁瀚ㄩ惃鍕瀮閺堫剚顢嬮崐鍏兼殶閹癸拷,娑撶儤鏆熺紒鍒搑
		  娓氬绱癧
		          {
		              title: %2%2閿涳拷//閸欘垯璐熺粚鐚寸礉濮ｅ繋閲滈柅澶嬪閸ф娈憈itle
		              children: [ 
		                {
		                    title: %2%2, //閸欘垯璐熺粚鐚寸礉娑撹櫣鈹栭柅澶嬪濡楀棗鐫嶇粈鍝勨偓闂磋礋value閿涘奔绗夋稉铏光敄闁瀚ㄥ鍡楃潔缁�鍝勨偓闂磋礋 title閿涙alue
		                    value: %2%2  //鐎圭偤妾棁鈧挧瀣偓鑲╃舶input濡楀棛娈戦崐绯猺
		                },
		              ]
		          },
		      ]
		*/	
		(function ($) {
		    $.inputSelect = function (options) {
		        var defaults = {
		            inputId: '',
		            dataSource: []
		        };
		        var options = $.extend(defaults, options);
 
		        if (!options.inputId || options.dataSource.length == 0) {
		            return false;
		        }
 
		        var inputDom = $(%2#%2 + options.inputId);
 
		        var inputValue = $(%2#%2 + options.inputId).val(); //闁鑵戦惃鍓塧l
		        console.log(inputValue)
		        var divOuterWidth = parseInt(inputDom.outerWidth()) + 2; //div閻ㄥ嫬顔旀惔顩俽
 
		        inputDom.wrap('<div id=%2' + options.inputId + 'SelectSearchDiv%2 class=%2' + options.inputId + 'SelectSearchDiv inputSelectDiv%2 style=%2width:' + divOuterWidth + 'px%2></div>');
		        inputDom.addClass(%2inputSelect%2);
		        var ulOuterWidth = parseInt(inputDom.outerWidth()) - 2;
		        var ulHtml = '<ul class=%2' + options.inputId + 'SearchUl selectSearchUl%2 style=%2width:' + ulOuterWidth + 'px%2></ul>';
		        inputDom.after(ulHtml);
 
		        $(%2#%2 + options.inputId).focus(function () {
		            defaultSelect();
		        });
 
		        $(%2#%2 + options.inputId).keyup(function () {
		            inputValue = $(this).val();
		        });
 
		        $(document).click(function (event) {
		            var event = event || window.event;
		            var target = event.target || event.srcElement;
		            var _con = $(%2#%2 + options.inputId);   // 鐠佸墽鐤嗛惄顔界垼閸栧搫鐓�
		            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
		                $(%2div.%2 + options.inputId + %2SelectSearchDiv%2).removeClass(%2hover%2);
		            }
		        });
 
		        $(document).on(%2click%2, %2#%2 + options.inputId + %2SelectSearchDiv ul li.selectLi%2, function () {
		            inputValue = $(this).data(%2value%2);
		            $(this).addClass(%2active%2);
		            $(%2#%2 + options.inputId).val(inputValue);
					
				var content = options.dataSource[inputValue].split(',;,');
				
				va_setValue('outboard_data_department',content[0]);
				va_setValue('receive_date',content[1]);
				va_setValue('edm_id',content[2]);

					
		            $(%2div.%2 + options.inputId + %2SelectSearchDiv%2).removeClass(%2hover%2);
		        });
 
		        function defaultSelect() {



		            $(%2div.%2 + options.inputId + %2SelectSearchDiv%2).addClass(%2hover%2);
 
		            var li = '';

$.each(options.dataSource, function(value, content) {  

	if (inputValue == value) {
		                            li += '<li class=%2selectLi active%2 data-value = ' + value + '>' + value + '</li>';
		                        } else {
		                            li += '<li class=%2selectLi%2 data-value = ' + value + '>' + value + '</li>';
		                        }

  //  console.log(index, value); // 鏉堟挸鍤ぐ鎾冲闁秴宸婚崚鎵畱閸忓啰绀岀槐銏犵穿閸滃苯鈧拷  
    // 閸︺劏绻栭柌灞藉讲娴犮儲澧界悰灞筋嚠瑜版挸澧犻柆宥呭坊閸掓壆娈戦崗鍐閻ㄥ嫪鎹㈡担鏇熸惙娴ｏ拷  
})





		         //   for (var i = 0; i < options.dataSource.length; i++) {
			//					if (inputValue == options.dataSource[i].value) {
		          //                 li += '<li class=%2selectLi active%2 data-value = ' + options.dataSource[i].value + '>' + options.dataSource[i].value + '</li>';
		           //             } else {
		         //                   li += '<li class=%2selectLi%2 data-value = ' + options.dataSource[i].value + '>' + options.dataSource[i].value + '</li>';
		           //             }
		          //  }
		            $(%2ul.%2 + options.inputId + %2SearchUl%2).html(li);
		        }
		    }
		})(jQuery2);
		
		
		
 
	
  </script>
",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "if((!va_getValue('edc_dataset') && g_recordid) || !g_recordid) $('#tr_id1434449946440_668_8').hide();

$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);

if(va_getValue('zq')=='5'){

$(%2#showdiv_reconciliation_check%2).css(%2display%2, %2none%2);
$(%2#showdiv_outboard_data_doc%2).css(%2display%2, %2none%2);
$(%2#showdiv_is_submission%2).css(%2display%2, %2none%2);
$(%2#fhead_chk_finding%2).text(%2DTA閺嶇厧绱″Λ鈧弻銉х波閺嬫粣绱�%2);




}

if(va_getLinkCount('ryjbzl',%2obj.loginid = '%2+g_loginid+%2' and obj.ryjs like '%1EDM%1'%2)==0){

var linkA = $('#showdiv_outboard_data_doc').find('a');
for(var i = 0; i < linkA.length; i++){
   // linkA.eq(i).attr('href', 'javascript:void(0)');
    //linkA.eq(i).attr('target', '');
}
//linkA.css(%2color%2, %2#303133%2)

}




//鐞涖劌宕熼崚婵嗩潗閸栨牞鍓奸張顒婄窗
$(document).ready(function(){ // document.ready閺勵垱瀵歞om閸旂姾娴囩�瑰本鍨氱亸鍗炲讲娴犮儻绱濇稉宥夋付鐟曚礁娴橀悧鍥ф嫲閸忔湹绮挧鍕爱閸旂姾娴囩�瑰苯姘ㄩ崣顖欎簰閹笛嗩攽
  $('.content').click(
   function(event){
     event.stopPropagation();
      $('.tip').fadeIn('slow'); // 濞ｂ�冲弳
   },function(){
	  $('.tip').hide();
	}
  );

  $(document).click(function(){
  $('.tip').fadeOut('slow'); 
  });

  $(%2.tip%2).click(function(event){
  event.stopPropagation();
  });

 $('.content').click(function(e){ // e娑撳搫缍嬮崜宥夌炊閺嶅洣缍旈悽鈺爋m閸忓啰绀岀�电钖�
   var top = e.pageY-100;
   var left = e.pageX;
   $('.tip').css({
    'top' : top+'px',
    'left': left+'px',
    'z-index': '99999'
   });
  });

  // $('.content').mouseout(function(){
  //    $('.tip').hide();
  // });
 });

jQuery2(function($){
  $.ajax({
                url: %2/extdatabind.ajaxgetfilename.do%2,
                type: %2POST%2,
                async: false,
                data: {studycode:va_getLinkValue('xsht','studyid','obj.id='+va_getValue('studyid'),''),studyid:va_getValue('studyid')},
                success: function (msg) {

                    if(msg){
					
					               $.inputSelect({
		    inputId: %2field_filename%2,
		    dataSource: JSON.parse(msg)
			
			
		});  
					
					}
                }
            });      

});
      ",
        "formvaljs": "",
        "id": "outboard_data_manager",
        "lastmodifytime": "1503813986140",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "閺佺増宓佸Λ鈧弻锟�",
        "nameen": "External Data Checking",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "240C46F8EC66FA1CD6907D594BA46134"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "dm_report",
        "lastmodifytime": "1502258908673",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓佺粻锛勬倞閹躲儱鎲�",
        "nameen": "Data Management Report",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "3ECF71AE86A6F0463A780F78B9F5D479"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>



	


<style type=%2text/css%2>
  /* 閹绘劗銇氶崘鍛啇閺嶅嘲绱� */
  .tip{ 

    position:absolute;
    padding: 6px 5px; 
    background-color:#dddddd; 
    border-radius: 4px;
      font-size: 12px;
      position: absolute;
      top: -10%1;
margin-right:80px;
      left: 0%1;
      border: 2px solid #ffffff;
      border-radius: 8px;
      box-shadow: 2px 4px 5px #eeeaea;


  }
  .content{
    cursor:pointer;
    }
  </style>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "
//鐞涖劌宕熼崚婵嗩潗閸栨牞鍓奸張顒婄窗
$(document).ready(function(){ // document.ready閺勵垱瀵歞om閸旂姾娴囩�瑰本鍨氱亸鍗炲讲娴犮儻绱濇稉宥夋付鐟曚礁娴橀悧鍥ф嫲閸忔湹绮挧鍕爱閸旂姾娴囩�瑰苯姘ㄩ崣顖欎簰閹笛嗩攽
  $('.content').click(
   function(event){
     event.stopPropagation();
      $('.tip').fadeIn('slow'); // 濞ｂ�冲弳
   },function(){
	  $('.tip').hide();
	}
  );

  $(document).click(function(){
  $('.tip').fadeOut('slow'); 
  });

  $(%2.tip%2).click(function(event){
  event.stopPropagation();
  });

 $('.content').click(function(e){ // e娑撳搫缍嬮崜宥夌炊閺嶅洣缍旈悽鈺爋m閸忓啰绀岀�电钖�
   var top = e.pageY-100;
   var left = e.pageX;
   $('.tip').css({
    'top' : top+'px',
    'left': left+'px',
    'z-index': '99999'
   });
  });

  // $('.content').mouseout(function(){
  //    $('.tip').hide();
  // });
 });",
        "formvaljs": "",
        "id": "coding",
        "lastmodifytime": "1504248685000",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閸栬顒熺紓鏍垳",
        "nameen": "Medical Coding",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "14C1258745DAB7858FE555EA2A87DEB5"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>


<style type=%2text/css%2>
  /* 閹绘劗銇氶崘鍛啇閺嶅嘲绱� */
  .tip{ 

    position:absolute;
    padding: 6px 5px; 
    background-color:#dddddd; 
    border-radius: 4px;
      font-size: 12px;
      position: absolute;
      top: -10%1;
margin-right:80px;
      left: 0%1;
      border: 2px solid #ffffff;
      border-radius: 8px;
      box-shadow: 2px 4px 5px #eeeaea;


  }
  .content{
    cursor:pointer;
    }
  </style>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": " 
 //鐞涖劌宕熼崚婵嗩潗閸栨牞鍓奸張顒婄窗
$(document).ready(function(){ // document.ready閺勵垱瀵歞om閸旂姾娴囩�瑰本鍨氱亸鍗炲讲娴犮儻绱濇稉宥夋付鐟曚礁娴橀悧鍥ф嫲閸忔湹绮挧鍕爱閸旂姾娴囩�瑰苯姘ㄩ崣顖欎簰閹笛嗩攽
  $('.content').click(
   function(event){
     event.stopPropagation();
      $('.tip').fadeIn('slow'); // 濞ｂ�冲弳
   },function(){
	  $('.tip').hide();
	}
  );

  $(document).click(function(){
  $('.tip').fadeOut('slow'); 
  });

  $(%2.tip%2).click(function(event){
  event.stopPropagation();
  });

 $('.content').click(function(e){ // e娑撳搫缍嬮崜宥夌炊閺嶅洣缍旈悽鈺爋m閸忓啰绀岀�电钖�
   var top = e.pageY-100;
   var left = e.pageX;
   $('.tip').css({
    'top' : top+'px',
    'left': left+'px',
    'z-index': '99999'
   });
  });

  // $('.content').mouseout(function(){
  //    $('.tip').hide();
  // });
 });",
        "formvaljs": "",
        "id": "q_management",
        "lastmodifytime": "1503240139765",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓佺粻锛勬倞闂傤噣顣芥稉搴☆槱閻烇拷",
        "nameen": "DM Issue and Resolution",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "90962A5686B2C1E025D45A5EA3233DA3"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "lslv",
        "lastmodifytime": "1503226404109",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "LSLV",
        "nameen": "LSLV",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "3F51F0E1245E432C3FA284DB3C9F5B44"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}



function asstAfterSelect(fid){

var va_idStr=va_getValue('$studyid');

var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');

if(fid=='change_control_code'){
var version=%2CC-%2+projectNum+%2-%2+va_getValue('change_control_code');
va_setValue('change_control_code',version);

}



}

</script>





",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);


",
        "formvaljs": "var studyid=va_getValue('studyid');
var tdm=va_getLinkValue('roles','$member',%2obj.limitnum='TDM' and obj.studyid='%2+studyid+%2' and obj.active='1'%2,'obj.createtime desc');
var loginid=va_getLinkValue('ryjbzl','loginid',%2obj.id='%2+tdm+%2' and obj.zt='1'%2,'obj.createtime desc');


var managername=va_getLinkValue('ryjl','managername',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
var work_group=va_getLinkValue('ryjl','work_group',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
if(managername=='妫版粌纾搾鍖紆0027){
var manager=va_getLinkValue('ryjl','ry',%2obj.work_group='%2+work_group+%2' and obj.email!='%2+loginid+%2' and obj.ylz='2' and managername='妫版粌纾搾鍖紆0027%2,'obj.createtime desc');

va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+manager+%2'%2,'obj.createtime desc'));

}else{
 if(managername.indexOf(%2,%2) < 0){
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+managername+%2'%2,'obj.createtime desc'));
 }else{
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+work_group.split(',').pop()+%2'%2,'obj.createtime desc'));
 }

}",
        "id": "plan_revision",
        "lastmodifytime": "1503239651937",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "EDC娣囶喛顓圭拋鈥冲灊",
        "nameen": "eCRF Amendment or EDC Upgrade",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "136F6C325A091F92086392F44D1D9CF7"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "fahsjkxd",
        "lastmodifytime": "1502174921920",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺傝顢嶉幋鏍ㄦ殶閹诡喖绨辨穱顔款吂1",
        "nameen": "Protocol or Database Amendment",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "B47A38DA6DA19D1618E3B9149EFC1449"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>

<style type=%2text/css%2>
  /* 閹绘劗銇氶崘鍛啇閺嶅嘲绱� */
  .tip{ 

    position:absolute;
    padding: 6px 5px; 
    background-color:#dddddd; 
    border-radius: 4px;
      font-size: 12px;
      position: absolute;
      top: -10%1;
margin-right:80px;
      left: 0%1;
      border: 2px solid #ffffff;
      border-radius: 8px;
      box-shadow: 2px 4px 5px #eeeaea;


  }
  .content{
    cursor:pointer;
    }
  </style>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "//鐞涖劌宕熼崚婵嗩潗閸栨牞鍓奸張顒婄窗
$(document).ready(function(){ // document.ready閺勵垱瀵歞om閸旂姾娴囩�瑰本鍨氱亸鍗炲讲娴犮儻绱濇稉宥夋付鐟曚礁娴橀悧鍥ф嫲閸忔湹绮挧鍕爱閸旂姾娴囩�瑰苯姘ㄩ崣顖欎簰閹笛嗩攽
  $('.content').click(
   function(event){
     event.stopPropagation();
      $('.tip').fadeIn('slow'); // 濞ｂ�冲弳
   },function(){
	  $('.tip').hide();
	}
  );

  $(document).click(function(){
  $('.tip').fadeOut('slow'); 
  });

  $(%2.tip%2).click(function(event){
  event.stopPropagation();
  });

 $('.content').click(function(e){ // e娑撳搫缍嬮崜宥夌炊閺嶅洣缍旈悽鈺爋m閸忓啰绀岀�电钖�
   var top = e.pageY-100;
   var left = e.pageX;
   $('.tip').css({
    'top' : top+'px',
    'left': left+'px',
    'z-index': '99999'
   });
  });

  // $('.content').mouseout(function(){
  //    $('.tip').hide();
  // });
 });",
        "formvaljs": "",
        "id": "dm_disc_log",
        "lastmodifytime": "1503814022187",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓佺粻锛勬倞瀹割喖绱撶拋鏉跨秿",
        "nameen": "Data Management Discrepancy",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "56E4C2DD329940A8FEEA8ADA158563A5"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "xtsj",
        "lastmodifytime": "1502154949150",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "EDC缁崵绮洪崡鍥╅獓",
        "nameen": "EDC Version Upgrade",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "26E14BC0F8F3C06E40FE1F68751CA991"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){


var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}
window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>





<style type=%2text/css%2>
  /* 閹绘劗銇氶崘鍛啇閺嶅嘲绱� */
  .tip{ 

    position:absolute;
    padding: 6px 5px; 
    background-color:#dddddd; 
    border-radius: 4px;
      font-size: 12px;
      position: absolute;
      top: -10%1;
margin-right:80px;
      left: 0%1;
      border: 2px solid #ffffff;
      border-radius: 8px;
      box-shadow: 2px 4px 5px #eeeaea;


  }
  .content{
    cursor:pointer;
    }
  </style>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);




//鐞涖劌宕熼崚婵嗩潗閸栨牞鍓奸張顒婄窗
$(document).ready(function(){ // document.ready閺勵垱瀵歞om閸旂姾娴囩�瑰本鍨氱亸鍗炲讲娴犮儻绱濇稉宥夋付鐟曚礁娴橀悧鍥ф嫲閸忔湹绮挧鍕爱閸旂姾娴囩�瑰苯姘ㄩ崣顖欎簰閹笛嗩攽
  $('.content').click(
   function(event){
     event.stopPropagation();
      $('.tip').fadeIn('slow'); // 濞ｂ�冲弳
   },function(){
	  $('.tip').hide();
	}
  );

  $(document).click(function(){
  $('.tip').fadeOut('slow'); 
  });

  $(%2.tip%2).click(function(event){
  event.stopPropagation();
  });

 $('.content').click(function(e){ // e娑撳搫缍嬮崜宥夌炊閺嶅洣缍旈悽鈺爋m閸忓啰绀岀�电钖�
   var top = e.pageY-100;
   var left = e.pageX;
   $('.tip').css({
    'top' : top+'px',
    'left': left+'px',
    'z-index': '99999'
   });
  });

  // $('.content').mouseout(function(){
  //    $('.tip').hide();
  // });
 });",
        "formvaljs": "",
        "id": "yzxhc",
        "lastmodifytime": "1503237362593",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閼筋垳澧跨�瑰鍙忛弫鐗堝祦娑撯偓閼峰瓨鈧勵梾閺岋拷",
        "nameen": "SAE Recociliation",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "068EED36207569BCDAAAC9B41FC2FBDB"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "interim_analyplan",
        "lastmodifytime": "1503236985515",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺堢喍鑵戦崚鍡樼��",
        "nameen": "Interim Analysis",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "E877B084AB0B9193EB5E1632EB4AE9A1"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "if(va_getLinkCount('ryjbzl',%2obj.loginid = '%2+g_loginid+%2' and obj.ryjs like '%1EDM%1'%2)==0){

var linkA = $('#showdiv_outboard_data_doc').find('a');
for(var i = 0; i < linkA.length; i++){
    linkA.eq(i).attr('href', 'javascript:void(0)');
    linkA.eq(i).attr('target', '');
linkA.eq(i).css(%2color%2, %2#303133%2);



}

linkA.css(%2color%2, %2#303133%2)

}",
        "formvaljs": "",
        "id": "non_outboard_data_manager",
        "lastmodifytime": "1503813986140",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "婢舵牠鍎撮弫鐗堝祦缁狅紕鎮婃径鏍劥閺佺増宓�",
        "nameen": "NON External Data Management",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "89D63168046D449C8B73AF8B8DBF7A91"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "if(va_getLinkCount('ryjbzl',%2obj.loginid = '%2+g_loginid+%2' and obj.ryjs like '%1EDM%1'%2)==0){

var linkA = $('#showdiv_outboard_data_doc').find('a');
for(var i = 0; i < linkA.length; i++){
    linkA.eq(i).attr('href', 'javascript:void(0)');
    linkA.eq(i).attr('target', '');
linkA.eq(i).css(%2color%2, %2#303133%2);



}

linkA.css(%2color%2, %2#303133%2)

}",
        "formvaljs": "",
        "id": "external_data_dta_check",
        "lastmodifytime": "1503813986140",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閹恒儲鏁规径鏍劥閺佺増宓佺粻锛勬倞",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "E8BF766A91D24F82858FCABB8B44EE43"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "study_rand_consistency",
        "lastmodifytime": "1503237362593",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "闂呭繑婧�閸栨牔绔撮懛瀛樷偓褎顥呴弻锟�",
        "nameen": "闂呭繑婧�閸栵拷 Recociliation",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "60%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "CE2901ADB84742BEA12FAF012549EF77"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}



function asstAfterSelect(fid){

var va_idStr=va_getValue('$studyid');

var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');

if(fid=='change_control_code'){
var version=%2CC-%2+projectNum+%2-%2+va_getValue('change_control_code');
va_setValue('change_control_code',version);

}



}

</script>





<style type=%2text/css%2>
  /* 閹绘劗銇氶崘鍛啇閺嶅嘲绱� */
  .tip{ 

    position:absolute;
    padding: 6px 5px; 
    background-color:#dddddd; 
    border-radius: 4px;
      font-size: 12px;
      position: absolute;
      top: -10%1;
margin-right:80px;
      left: 0%1;
      border: 2px solid #ffffff;
      border-radius: 8px;
      box-shadow: 2px 4px 5px #eeeaea;


  }
  .content{
    cursor:pointer;
    }
  </style>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "if(!g_recordid ){
 var change_control_code =va_getLinkValue('plan_revision','change_control_code','obj.studyid='+va_getValue('studyid'),'obj.change_control_code desc');
 va_setValue('change_control_code',change_control_code?change_control_code:'NA')
 }

$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);


//鐞涖劌宕熼崚婵嗩潗閸栨牞鍓奸張顒婄窗
$(document).ready(function(){ // document.ready閺勵垱瀵歞om閸旂姾娴囩�瑰本鍨氱亸鍗炲讲娴犮儻绱濇稉宥夋付鐟曚礁娴橀悧鍥ф嫲閸忔湹绮挧鍕爱閸旂姾娴囩�瑰苯姘ㄩ崣顖欎簰閹笛嗩攽
  $('.content').click(
   function(event){
     event.stopPropagation();
      $('.tip').fadeIn('slow'); // 濞ｂ�冲弳
   },function(){
	  $('.tip').hide();
	}
  );

  $(document).click(function(){
  $('.tip').fadeOut('slow'); 
  });

  $(%2.tip%2).click(function(event){
  event.stopPropagation();
  });

 $('.content').click(function(e){ // e娑撳搫缍嬮崜宥夌炊閺嶅洣缍旈悽鈺爋m閸忓啰绀岀�电钖�
   var top = e.pageY-100;
   var left = e.pageX;
   $('.tip').css({
    'top' : top+'px',
    'left': left+'px',
    'z-index': '99999'
   });
  });

  // $('.content').mouseout(function(){
  //    $('.tip').hide();
  // });
 });",
        "formvaljs": "var studyid=va_getValue('studyid');
var tdm=va_getLinkValue('roles','$member',%2obj.limitnum='TDM' and obj.studyid='%2+studyid+%2' and obj.active='1'%2,'obj.createtime desc');
var loginid=va_getLinkValue('ryjbzl','loginid',%2obj.id='%2+tdm+%2' and obj.zt='1'%2,'obj.createtime desc');


var managername=va_getLinkValue('ryjl','managername',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
var work_group=va_getLinkValue('ryjl','work_group',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
if(managername=='妫版粌纾搾鍖紆0027){
var manager=va_getLinkValue('ryjl','ry',%2obj.work_group='%2+work_group+%2' and obj.email!='%2+loginid+%2' and obj.ylz='2' and managername='妫版粌纾搾鍖紆0027%2,'obj.createtime desc');

va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+manager+%2'%2,'obj.createtime desc'));

}else{
 if(managername.indexOf(%2,%2) < 0){
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+managername+%2'%2,'obj.createtime desc'));
 }else{
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+work_group.split(',').pop()+%2'%2,'obj.createtime desc'));
 }

}",
        "id": "study_db_revise_assess",
        "lastmodifytime": "1503239651937",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "EDC娣囶喛顓圭涵顔款吇",
        "nameen": "Database amendment",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "632E1FA41DAB440C915DBAE183F204B4"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}



function asstAfterSelect(fid){

var va_idStr=va_getValue('$studyid');

var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');

if(fid=='change_control_code'){
var version=%2CC-%2+projectNum+%2-%2+va_getValue('change_control_code');
va_setValue('change_control_code',version);

}



}

</script>





<style type=%2text/css%2>
  /* 閹绘劗銇氶崘鍛啇閺嶅嘲绱� */
  .tip{ 

    position:absolute;
    padding: 6px 5px; 
    background-color:#dddddd; 
    border-radius: 4px;
      font-size: 12px;
      position: absolute;
      top: -10%1;
margin-right:80px;
      left: 0%1;
      border: 2px solid #ffffff;
      border-radius: 8px;
      box-shadow: 2px 4px 5px #eeeaea;


  }
  .content{
    cursor:pointer;
    }
  </style>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "if(!g_recordid ){
 var change_control_code =va_getLinkValue('plan_revision','change_control_code','obj.studyid='+va_getValue('studyid'),'obj.change_control_code desc');
 va_setValue('change_control_code',change_control_code?change_control_code:'NA')
 }

$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);


//鐞涖劌宕熼崚婵嗩潗閸栨牞鍓奸張顒婄窗
$(document).ready(function(){ // document.ready閺勵垱瀵歞om閸旂姾娴囩�瑰本鍨氱亸鍗炲讲娴犮儻绱濇稉宥夋付鐟曚礁娴橀悧鍥ф嫲閸忔湹绮挧鍕爱閸旂姾娴囩�瑰苯姘ㄩ崣顖欎簰閹笛嗩攽
  $('.content').click(
   function(event){
     event.stopPropagation();
      $('.tip').fadeIn('slow'); // 濞ｂ�冲弳
   },function(){
	  $('.tip').hide();
	}
  );

  $(document).click(function(){
  $('.tip').fadeOut('slow'); 
  });

  $(%2.tip%2).click(function(event){
  event.stopPropagation();
  });

 $('.content').click(function(e){ // e娑撳搫缍嬮崜宥夌炊閺嶅洣缍旈悽鈺爋m閸忓啰绀岀�电钖�
   var top = e.pageY-100;
   var left = e.pageX;
   $('.tip').css({
    'top' : top+'px',
    'left': left+'px',
    'z-index': '99999'
   });
  });

  // $('.content').mouseout(function(){
  //    $('.tip').hide();
  // });
 });",
        "formvaljs": "var studyid=va_getValue('studyid');
var tdm=va_getLinkValue('roles','$member',%2obj.limitnum='TDM' and obj.studyid='%2+studyid+%2' and obj.active='1'%2,'obj.createtime desc');
var loginid=va_getLinkValue('ryjbzl','loginid',%2obj.id='%2+tdm+%2' and obj.zt='1'%2,'obj.createtime desc');


var managername=va_getLinkValue('ryjl','managername',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
var work_group=va_getLinkValue('ryjl','work_group',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
if(managername=='妫版粌纾搾鍖紆0027){
var manager=va_getLinkValue('ryjl','ry',%2obj.work_group='%2+work_group+%2' and obj.email!='%2+loginid+%2' and obj.ylz='2' and managername='妫版粌纾搾鍖紆0027%2,'obj.createtime desc');

va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+manager+%2'%2,'obj.createtime desc'));

}else{
 if(managername.indexOf(%2,%2) < 0){
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+managername+%2'%2,'obj.createtime desc'));
 }else{
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+work_group.split(',').pop()+%2'%2,'obj.createtime desc'));
 }

}",
        "id": "study_db_revise_report",
        "lastmodifytime": "1503239651937",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "EDC娣囶喛顓归幎銉ユ啞",
        "nameen": "eCRF Amendment or EDC Upgrade",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "60%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "2B7FB78D51874C7BB1338557F559CE74"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "",
        "forminitjs": "var ext_data_id=va_getValue('ext_data_id');


var zq=va_getLinkValue('outboard_data_manager','zq','obj.id='+ext_data_id,'');

if(zq==6){


$(%2#tr_id1702359761013_761_13%2).hide();
$(%2#tr_id1702359761013_761_14%2).hide();

$(%2#tr_id1702359761013_761_15%2).hide();

}",
        "formvaljs": "",
        "id": "ext_data_bind",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐠佸墽娲哥拋鏉跨秿",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "85E0053878C543FE837E7319364F968A"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "helphtml": "",
        "id": "sas_ver_pro",
        "lastmodifytime": "1726022170384",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "false",
        "name": "IRC閺佺増宓佹导鐘虹翻",
        "nameen": "IRC Data transfer",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "60%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "A506176BE34147CBB3483A925BC0029E"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "sascheck",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "SAS閺嶅憡鐓￠崘鍛啇",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "C75BE828E9DE4F6089A00643969DC4EB"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "blindedtransfer",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "false",
        "name": "閺佺増宓佺拋鍓ф锤娑撳簼绱舵潏锟�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "45%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "E3DBCDA1E342402EB19A2DAC365CEF56"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "edcblindtransfer",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "EDC鐎涙顔岀拋鍓ф锤娑撳簼绱舵潏锟�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "60%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "874892EAB2FB4BE0813E91F79EB4C110"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
if(eVersion == '' || eVersion == null ){
var note=%2鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!%2
var noteen=%2Please select the current version!%2
var var1= {en:noteen,zh:note};
alert(getLocStr(var1,g_locale));

}else{
var version=projectNum+%2_MR%2+eVersion;
va_setValue('manual_rev_prog_version',version);
}
}

function asstAfterSelect(fid){

var va_idStr=va_getValue('$studyid');

var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');

if(fid=='bbh'){

var version=projectNum+%2_MR%2+va_getValue('bbh');
va_setValue('manual_rev_prog_version',version);

}


}






function downloadFormwork(){

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}
window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);

if(!g_recordid ){
 var change_control_code =va_getLinkValue('plan_revision','change_control_code','obj.studyid='+va_getValue('studyid'),'obj.change_control_code desc');
 va_setValue('change_control_code',change_control_code?change_control_code:'NA')
 }",
        "formvaljs": "",
        "id": "sas_check_run",
        "lastmodifytime": "1503987082859",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "SAS閺嶅憡鐓�",
        "nameen": "SAS Review",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "155A75E4BBA34E77A11ED389CFD1C1DC"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){


var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}


window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}


</script>




<style type=%2text/css%2>
  /* 閹绘劗銇氶崘鍛啇閺嶅嘲绱� */
  .tip{ 

    position:absolute;
    padding: 6px 5px; 
    background-color:#dddddd; 
    border-radius: 4px;
      font-size: 12px;
      position: absolute;
      top: -10%1;
margin-right:80px;
      left: 0%1;
      border: 2px solid #ffffff;
      border-radius: 8px;
      box-shadow: 2px 4px 5px #eeeaea;


  }
  .content{
    cursor:pointer;
    }
  </style>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);



//鐞涖劌宕熼崚婵嗩潗閸栨牞鍓奸張顒婄窗
$(document).ready(function(){ // document.ready閺勵垱瀵歞om閸旂姾娴囩�瑰本鍨氱亸鍗炲讲娴犮儻绱濇稉宥夋付鐟曚礁娴橀悧鍥ф嫲閸忔湹绮挧鍕爱閸旂姾娴囩�瑰苯姘ㄩ崣顖欎簰閹笛嗩攽
  $('.content').click(
   function(event){
     event.stopPropagation();
      $('.tip').fadeIn('slow'); // 濞ｂ�冲弳
   },function(){
	  $('.tip').hide();
	}
  );

  $(document).click(function(){
  $('.tip').fadeOut('slow'); 
  });

  $(%2.tip%2).click(function(event){
  event.stopPropagation();
  });

 $('.content').click(function(e){ // e娑撳搫缍嬮崜宥夌炊閺嶅洣缍旈悽鈺爋m閸忓啰绀岀�电钖�
   var top = e.pageY-100;
   var left = e.pageX;
   $('.tip').css({
    'top' : top+'px',
    'left': left+'px',
    'z-index': '99999'
   });
  });

  // $('.content').mouseout(function(){
  //    $('.tip').hide();
  // });
 });",
        "formvaljs": "",
        "id": "db_lock_proc",
        "lastmodifytime": "1503272644234",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "eCRF闁夸礁鐣惧〒鍛礋",
        "nameen": "eCRF Lock CheckList",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "0572237DBCDFEBAF6CBF32D2F7BC2A1B"
    },
    {
        "appendhtml": "<script language=%2javascript%2>


function downloadFormwork(){

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}


</script>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);


var contents_check=va_getValue('contents_check');

var valuesToRemove = [%205%2,%206%2,%207%2];


if (!(valuesToRemove.includes(contents_check))) {


// 閼惧嘲褰� select 閸忓啰绀�
var selectElement = document.getElementById(%2field_contents_check%2);


// 闁秴宸荤憰浣盒╅梽銈囨畱閸婂吋鏆熺紒鍒搑
valuesToRemove.forEach(function(value) {
  // 閺屻儲澹橀崗閿嬫箒閻楃懓鐣鹃崐鑲╂畱闁銆�
  var optionToRemove = selectElement.querySelector('option[value=%2' + value + '%2]');
  
  // 婵″倹鐏夐幍鎯у煂娴滃棜顕氶柅澶愩�嶉敍灞藉灟娴狅拷 select 娑擃厾些闂勵槀r
  if (optionToRemove) {
    selectElement.removeChild(optionToRemove);
  } else {
    console.log(%2閺堫亝澹橀崚鎷岊洣缁夊娅庨惃鍕偓澶愩�嶉敍灞解偓闂磋礋: %2 + value);
  }
});

}",
        "formvaljs": "",
        "id": "data_eval",
        "lastmodifytime": "1503371928453",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓佹惔鎾虫彥閻擄拷/闁夸礁鐣剧拹銊﹀付",
        "nameen": "DB Lock Data QC ",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "ADB2A4A16DA6D5E87511333C5F51C7E9"
    },
    {
        "appendhtml": "<script language=%2javascript%2>


function downloadFormwork(){

var studyPhase=va_getValue('zq');




var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}

if(studyPhase=='6'){
href+='%120and%120obj.id=1446969345'
}else{
href+='%120and%120obj.id!=1446969345'
}


window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}


</script>





",
        "delinnerdata": "true",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);


	if(g_recordid){
if(va_getValue('shzt')>='05')$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:mailSendHmtl();return false;' title='閸欐垿鈧線鍋栨禒绂眜0027>閸欐垿鈧線鍋栨禒绂眜003c/button>%2);
}



",
        "formvaljs": "var studyid=va_getValue('studyid');
var tdm=va_getLinkValue('roles','$member',%2obj.limitnum='TDM' and obj.studyid='%2+studyid+%2' and obj.active='1'%2,'obj.createtime desc');
var loginid=va_getLinkValue('ryjbzl','loginid',%2obj.id='%2+tdm+%2' and obj.zt='1'%2,'obj.createtime desc');


var managername=va_getLinkValue('ryjl','managername',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
var work_group=va_getLinkValue('ryjl','work_group',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
if(managername=='妫版粌纾搾鍖紆0027){
var manager=va_getLinkValue('ryjl','ry',%2obj.work_group='%2+work_group+%2' and obj.email!='%2+loginid+%2' and obj.ylz='2' and managername='妫版粌纾搾鍖紆0027%2,'obj.createtime desc');

va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+manager+%2'%2,'obj.createtime desc'));

}else{
 if(managername.indexOf(%2,%2) < 0){
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+managername+%2'%2,'obj.createtime desc'));
 }else{
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+work_group.split(',').pop()+%2'%2,'obj.createtime desc'));
 }

}",
        "id": "db_lock",
        "lastmodifytime": "1************",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "閺佺増宓佹惔鎾虫彥閻擄拷/闁夸礁鐣剧�光剝澹�",
        "nameen": "eCRF Snapshot",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "3CA8BAB97B690AF2812741386B41ADD8"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "project_oreport",
        "lastmodifytime": "1503297361125",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓佺粻锛勬倞濮瑰洦鈧拷",
        "nameen": "Data Management Summary",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "B3BBD8531AFCE366E57933BE54DBD8A4"
    },
    {
        "appendhtml": "<script language=%2javascript%2>


function downloadFormwork(){

var studyPhase=va_getValue('zq');

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}


if(studyPhase=='6'){
href+='%120and%120obj.id=1446969347'
}else{
href+='%120and%120obj.id!=1446969347'
}



window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>



",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "db_unlock",
        "lastmodifytime": "1503380284062",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "eCRF鐟欙綁鏀�",
        "nameen": "eCRF Unlock",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "4B5C1D5BED7BD98DC6772140985A37EF"
    },
    {
        "appendhtml": "<script language=%2javascript%2>


function downloadFormwork(){

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}


window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}


</script>",
        "delinnerdata": "false",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);",
        "formvaljs": "",
        "id": "blinding_export",
        "lastmodifytime": "1503323300469",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓佺�电厧鍤�",
        "nameen": "Data Extract",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "03AD48CD1F5FD3EBFC81A8342081B099"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>

",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "            if (va_getValue('cssjlx')!=4) {

  var optionToHide = document.querySelector(%2#field_cssjlx option[value='4']%2);
                if (optionToHide) {
                    optionToHide.style.display = 'none';
                }


            }







$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);


",
        "formvaljs": "",
        "id": "data_submission",
        "lastmodifytime": "1503814079359",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "閺佺増宓佺�电厧鍤稉搴濈炊鏉堬拷",
        "nameen": "Data Transfer",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "3F0C756EAA7EA1AC98C254A026D057E6"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "blinding_meeting",
        "lastmodifytime": "1505300411149",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "(閻╁弶鈧拷)閺佺増宓佺�光剝鐗抽幎銉ユ啞",
        "nameen": "(Blinded) Data Review Meeting",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "1FC5487C1972CBBA0D4EBA4FB9C29439"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function asstAfterSelect(fid){

var va_idStr=va_getValue('$studyid');

var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');

if(fid=='bbh'){

var version=projectNum+%2_DMP%2+va_getValue('bbh');
va_setValue('dmp_vers',version);

}


}


function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
if(eVersion == '' || eVersion == null ){
var note=%2鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!%2
var noteen=%2Please select the current version!%2
var var1= {en:noteen,zh:note};
alert(getLocStr(var1,g_locale));
}else{
var version=projectNum+%2_DMP%2+eVersion;
va_setValue('dmp_vers',version);
}
}

function downloadFormwork(){

var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}

",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);



if(!va_getValue('file_sum')){
$.pageHideLine('12-12');

}


var study_dmp_update_info= va_getLinkValue('study_dmp_update','wait_update_ver,wait_update_notes','obj.study_id='+va_getValue('studyid'),);


if(study_dmp_update_info){

if(!va_getValue('bbh')){

va_setValue('bbh',study_dmp_update_info[0]);
getV();


}


if(!va_getValue('zyxgnryyy')){
va_setValue('zyxgnryyy',study_dmp_update_info[1]);

}

}


",
        "formvaljs": "var studyid=va_getValue('studyid');
var tdm=va_getLinkValue('roles','$member',%2obj.limitnum='TDM' and obj.studyid='%2+studyid+%2' and obj.active='1'%2,'obj.createtime desc');
var loginid=va_getLinkValue('ryjbzl','loginid',%2obj.id='%2+tdm+%2' and obj.zt='1'%2,'obj.createtime desc');


var managername=va_getLinkValue('ryjl','managername',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
var work_group=va_getLinkValue('ryjl','work_group',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
if(managername=='妫版粌纾搾鍖紆0027){
var manager=va_getLinkValue('ryjl','ry',%2obj.work_group='%2+work_group+%2' and obj.email!='%2+loginid+%2' and obj.ylz='2' and managername='妫版粌纾搾鍖紆0027%2,'obj.createtime desc');

va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+manager+%2'%2,'obj.createtime desc'));

}else{
 if(managername.indexOf(%2,%2) < 0){
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+managername+%2'%2,'obj.createtime desc'));
 }else{
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+work_group.split(',').pop()+%2'%2,'obj.createtime desc'));
 }

}
window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>
",
        "id": "dmp_gen",
        "lastmodifytime": "1503999556422",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓佺粻锛勬倞鐠佲�冲灊",
        "nameen": "Data Management Plan",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "0F9A473B990DBA7593624498F4F953DD"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
if(eVersion == '' || eVersion == null ){
var note=%2鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!%2
var noteen=%2Please select the current version!%2
var var1= {en:noteen,zh:note};
alert(getLocStr(var1,g_locale));
}else{
var version=projectNum+%2_DMR%2+eVersion;
va_setValue('dmr_version',version);
}
}
</script>",
        "delinnerdata": "false",
        "forminitjs": "var date = new Date();
var mydate=date.getFullYear()+%2-%2+(date.getMonth()+1)+%2-%2+date.getDate();
va_setValue('reported_date',mydate);",
        "formvaljs": "",
        "id": "final_manage_report",
        "lastmodifytime": "1503999580468",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓佺粻锛勬倞閹躲儱鎲�",
        "nameen": "Data Management Report",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "E273E2D4D5C394DF47F7BFD8BF267204"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function setFileName(){

var study_id=va_getValue('studyid');
var study_code= va_getLinkValue('xsht','studyid','obj.id='+va_getValue('studyid'),'');

var med_archiv_type= va_getLinkValue('codelist','code_cdescription',%2obj.active='1' and obj.codelist_name='ARCHIVE_DM_TYPE' and obj.code_value='%2+va_getValue('med_archiv_type')+%2'%2,'');

let date= new Date();

let curDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();

var max_num=va_getLinkValue('dm_doc_archive','med_archiv_name',%2obj.med_archiv_name like '%2+study_code+'_'+med_archiv_type+'_'+curDate+%2%1'%2,'obj.med_archiv_name desc');
max_num=max_num?parseInt(max_num.substring(max_num.length-2))+1:1;
max_num=(Array(2).join(0) + max_num).slice(-2);
va_setValue('med_archiv_name',study_code+'_'+med_archiv_type+'_'+curDate+'_'+max_num);




}

function change() {

var med_archiv_type=va_getValue('med_archiv_type');

   
	var getCurStudyRole=%2obj.studyid=%2+va_getValue('studyid')+%2 and obj.member=%2+va_getLinkValue('ryjbzl','id',%2obj.loginid='%2+g_loginid+%2'%2,'');

	var CurStudyRole=va_getLinkValue('roles','limitnum',getCurStudyRole,'');
        var getFlListWhere=%2obj.active='1' and obj.codelist_name='ARCHIVE_DM_TYPE' and obj.code_value in ('2','5')%2;
if(CurStudyRole=='RandSpecialist') getFlListWhere=%2obj.active='1' and obj.codelist_name='ARCHIVE_DM_TYPE' and obj.code_value in ('7','9')%2;
	var flList=va_getLinkValueStr('codelist','code_value,code_cdescription',getFlListWhere,'obj.code_value asc','all');







	var flMap = flList.split(',|,');
	var str = '<option value=%2%2></option>';
	var fl1Value = '';

	for (let i=0; i < flMap.length; i++) {
		
		
		var fl = flMap[i].split(',;,');
		if(i==0){
		fl1Value=fl[0];
		}
		str = str + '<option value=%2'+fl[0]+'%2>' + fl[1] + '</option>';

	
	}

	$(%2#field_med_archiv_type%2).html(str);
	va_setValue('med_archiv_type',med_archiv_type);
	
}


</script>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "if(g_loginid!='supersa' && g_loginid!='sa'){change();}",
        "formvaljs": "if(!g_recordid){setFileName();}",
        "id": "dm_doc_archive",
        "lastmodifytime": "1503998396828",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓佺粻锛勬倞閺傚洣娆㈣ぐ鎺撱��",
        "nameen": "DM Document Archives",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "32CA21B1F546EC7C6AAE66B2888A9258"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "db_archive",
        "lastmodifytime": "1502268048900",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺佺増宓佹惔鎾崇秺濡楋拷",
        "nameen": "Database Unlock",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "A48D3FE4C3AADF249D9721612EFF0F96"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "db_unlock_application",
        "lastmodifytime": "1503300172093",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閻㈠疇顕弫鐗堝祦鎼存捁袙闁匡拷",
        "nameen": "DB Unlock Request",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "F2E284E6A8A014D3DDEED50EE35DA4B9"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "verification_pr_data",
        "lastmodifytime": "1503300266047",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓侀弽鍛婄叀鐠佲�冲灊",
        "nameen": "Data Validation Plan",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "A0E7CFCF69E4EE53F769D26930434EB5"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "verification_report_data",
        "lastmodifytime": "1503300327812",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓侀弽鍛婄叀閹躲儱鎲�",
        "nameen": "Data Validation Report",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "728353A06697BD3A3A7B0161FD0B4504"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "sjzlkz",
        "lastmodifytime": "1503300450015",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓佺拹銊╁櫤閹貉冨煑娑撳氦宸濋柌蹇庣箽鐠囷拷",
        "nameen": "QC & QA",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "C85126CD38AD03F184B8F69CA96DCCB1"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "sjzlbz",
        "lastmodifytime": "1505300005281",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓佺拹銊╁櫤娣囨繆鐦�",
        "nameen": "Data QA",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "01272A4F76F9D9F20689B524C6692980"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "va_setValue('sjgly',g_curruser);",
        "formvaljs": "",
        "id": "zxjsqrh",
        "lastmodifytime": "1503386164172",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "娑擃厼绺緀CRF閹恒儲鏁圭涵顔款吇閸戯拷",
        "nameen": "Site Confirmation eCRF Receiving",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "BE24261D309294F4E54FF97B64EB9CA6"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "approval_unblinding",
        "lastmodifytime": "1503300390234",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閹活厾娲搁悽瀹狀嚞娑撳骸顓搁幍锟�",
        "nameen": "Unblinding Application & Approval",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "5FB8EDE2B9B0429381DF365AD27A42BA"
    },
    {
        "appendhtml": "<script language=%2javascript%2>

function downloadFormwork(){

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}

</script>



<script language=%2javascript%2>


	function mailSendHmtl(){

var href=%2actionsendmail.getemail.do?tableid=crf_handover&recordid=%2+g_recordid+%2&studyid=%2+va_getValue('studyid')%2;


window.top.webmask_show_modalbyurl(%2闁喕娆�%2,encodeURI(href),1000,600);

}
	</script>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "if(g_recordid){
$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:mailSendHmtl();return false;' title='閸欐垿鈧線鍋栨禒绂眜0027>閸欐垿鈧線鍋栨禒绂眜003c/button>%2);
}",
        "formvaljs": "",
        "id": "crf_handover",
        "lastmodifytime": "1503386164172",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閸欐鐦懓鍗怌RF娴溿倖甯�",
        "nameen": "Site Confirmation eCRF Receiving",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "5B355B5191D7433FADEC2CF7ADF2BB2D"
    },
    {
        "appendhtml": "<script language=%2javascript%2>


function downloadFormwork(){

var studyPhase=va_getValue('zq');




var start_date= va_getLinkValue('xsht','start_date','obj.id='+va_getValue('studyid'),);

if(start_date>='2019-11-15'){
var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127%120and%120NVL(obj.overdate%12C%120sysdate)%13E%13Dto_date(%127'+start_date+'%127%12C%127yyyy-mm-dd%127)';
}else{

var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

}

if(studyPhase=='6'){
href+='%120and%120obj.id=1446969345'
}else{
href+='%120and%120obj.id!=1446969345'
}

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);


}


</script>",
        "delinnerdata": "true",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);

	if(g_recordid){
if(va_getValue('shzt')>='05')$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:mailSendHmtl();return false;' title='閸欐垿鈧線鍋栨禒绂眜0027>閸欐垿鈧線鍋栨禒绂眜003c/button>%2);
}
",
        "formvaljs": "var studyid=va_getValue('studyid');
var tdm=va_getLinkValue('roles','$member',%2obj.limitnum='TDM' and obj.studyid='%2+studyid+%2' and obj.active='1'%2,'obj.createtime desc');
var loginid=va_getLinkValue('ryjbzl','loginid',%2obj.id='%2+tdm+%2' and obj.zt='1'%2,'obj.createtime desc');


var managername=va_getLinkValue('ryjl','managername',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
var work_group=va_getLinkValue('ryjl','work_group',%2obj.email='%2+loginid+%2'%2,'obj.createtime desc');
if(managername=='妫版粌纾搾鍖紆0027){
var manager=va_getLinkValue('ryjl','ry',%2obj.work_group='%2+work_group+%2' and obj.email!='%2+loginid+%2' and obj.ylz='2' and managername='妫版粌纾搾鍖紆0027%2,'obj.createtime desc');

va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+manager+%2'%2,'obj.createtime desc'));

}else{
 if(managername.indexOf(%2,%2) < 0){
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+managername+%2'%2,'obj.createtime desc'));
 }else{
 va_setValue('approver',va_getLinkValue('ryjbzl','id',%2obj.xm='%2+work_group.split(',').pop()+%2'%2,'obj.createtime desc'));
 }

}",
        "id": "db_lock_plan",
        "lastmodifytime": "1************",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓佹惔鎾虫彥閻擄拷/闁夸礁鐣剧拋鈥冲灊",
        "nameen": "eCRF Snapshot",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "524B8FFE342544E197F8FB61431BDFC9"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "",
        "forminitjs": "",
        "formvaljs": "",
        "id": "db_lock_plan_item",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺佺増宓佹惔鎾虫彥閻擄拷/闁夸礁鐣惧〒鍛礋鐠佲�冲灊",
        "nameen": "Snapshot/DB lock checklist",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "992ECFFB9CC748FE94A10318C54BE1AC"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "",
        "forminitjs": "",
        "formvaljs": "",
        "id": "db_lock_item",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺佺増宓佹惔鎾虫彥閻擄拷/闁夸礁鐣惧〒鍛礋鐠佹澘缍�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "2042EA427F9E46EC882E3A52173E94B5"
    },
    {
        "appendhtml": "",
        "delinnerdata": "true",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "db_lock_data_qc",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺佺増宓佹惔鎾虫彥閻擄拷/闁夸礁鐣剧拹銊﹀付",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "9502D1EADB2F4990B8F975AB127C6E05"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "ext_data_final",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "false",
        "name": "缂佸牏澧楅弫鐗堝祦濡偓閺屻儰绗屾稉鈧懛瀛樷偓褎鐦�碉拷",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "60%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "50CFFBA7B6DA4954B0D60347F17E2AFA"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "zzjg",
        "lastmodifytime": "1503827326265",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "缂佸嫮绮愰弸鑸电��",
        "nameen": "Organization Chart",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "9CB4E34931596BBD749CA0E22E2B6B7C"
    },
    {
        "appendhtml": "<script src=%2http://libs.baidu.com/jquery/2.0.0/jquery.min.js%2></script>
<script type=%2text/javascript%2>
    var jQuery_1_10_2 = $.noConflict(true);
</script>






<script language=%2javascript%2>


function downloadFormwork(){


var href='/tableapp.list.do?tableid=dm_template_file&where=obj.table_id%13D%127'+g_tableid+'%127';

window.top.webmask_show_modalbyurl(%2瀹搞儰缍斿Ο鈩冩緲%2,href,1000,400);

}
</script>


<style>









.tab-buttons {
	width: 100%1;
	height: 40px;
	background-color: #fafafa
}
.tab-buttons .tab-button {
	width: 200px;
	float: left;
	height: 40px;
	text-align: center;
	font-size: 14px;
	color: #999;
	line-height: 40px;
	position: relative;
	cursor: pointer;
	background-color: #f2f2f3;
}
.tab-buttons .tab-button.cur {
	background-color: #fff;
	color: #333
}
.tab-buttons .tab-button.cur:after {
	content: '';
	position: absolute;
	top: -1px;
	left: 0;
	width: 100%1;
	height: 2px;
	background-color: #1183ff
}

</style>




",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "if(va_getValue('startdt').substring(0,4)<2021){
$(%2#div_13_1%2).hide();

}



$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:downloadFormwork();return false;' title='瀹搞儰缍斿Ο鈩冩緲'>瀹搞儰缍斿Ο鈩冩緲</button>%2);
$('.tab-button').click(function() {
		var tab = $(this).data('tab');
		$(this).addClass('cur').siblings('.tab-button').removeClass('cur');
		$('#' + tab + '').show().siblings('.tab-item').hide();

});


",
        "formvaljs": "",
        "id": "ryjl",
        "lastmodifytime": "1513219178347",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "娴滃搫鎲虫穱鈩冧紖",
        "nameen": "Employee",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "368E02066E5C83E6B2D45432060F37CD"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "employe_probation_report",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "鐠囨洜鏁ら張鐔哥湽閹讹拷",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "05001C4D783D478F92D7CE04AB818C7A"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "",
        "forminitjs": "",
        "formvaljs": "",
        "id": "resume",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缁犫偓閸樺棔绗孞D绾喛顓�",
        "nameen": "CVs and JDs",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "DD0FA55E9C234A12951EB4690AE80008"
    },
    {
        "id": "employe_work_experience",
        "name": "瀹搞儰缍旂紒蹇撳坊",
        "note": "",
        "uuid": "A01D47879FD34D48B8D78E5AD6AA9E0B"
    },
    {
        "id": "employe_growth",
        "name": "娑擃亙姹夐幋鎰版毐",
        "note": "",
        "uuid": "2031BE29FA674493B61BBF7690229BBB"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rewards",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "闁椒缍樻稉鈧張闈涚毈缁俱垼濮�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "false",
        "showsubtable": "true",
        "uuid": "A21EA0ED550D421F8C07929AE2E72660"
    },
    {
        "appendhtml": "",
        "delinnerdata": "true",
        "forminitjs": "",
        "formvaljs": "",
        "id": "pxjl",
        "lastmodifytime": "1503831471265",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "eLearning鐠佹澘缍�",
        "nameen": "eLearning Records",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "false",
        "uuid": "D041C9CD259286F877D4894C7258E4F1"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "pxjlmx",
        "lastmodifytime": "1503832373109",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸╃顔勭拋鏉跨秿閺勫海绮�",
        "nameen": "Training Record Details",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "31FFA637E566C030E2286BFB29859D28"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rwzz",
        "lastmodifytime": "1503301061187",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "娴犺濮熼懕宀冪煑",
        "nameen": "Roles & Responsibilities",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "A5D880637EF5ECBC3F24EBB3E834610A"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "jsxx",
        "lastmodifytime": "1503301104484",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐟欐帟澹婃穱鈩冧紖",
        "nameen": "Roles",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "2ABE7BFD7FED3BC56054EF1F986F630E"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "av_GenerateFirstPY('xmpy','xm');",
        "id": "ryjbzl",
        "lastmodifytime": "*************",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐠愶附鍩涙穱鈩冧紖",
        "nameen": "User Account",
        "newtoedit": "false",
        "note": "",
        "recordpwr": "false",
        "redirecturl": "",
        "showaddbtn": "true",
        "showreturnbtn": "false",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "false",
        "uuid": "917BFF3F144789439C605557F2DB482B"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "unit_info",
        "lastmodifytime": "*************",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸楁洑缍呮穱鈩冧紖",
        "nameen": "Orgnization",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "14BE0CE1113A679F0647251E25718217"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "dw",
        "lastmodifytime": "1503469070719",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "闁劑妫穱鈩冧紖",
        "nameen": "Department",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "DD3FF4CB2B5AAE56F085C3B3B64635BF"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "systemcode",
        "lastmodifytime": "1503402384562",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缁崵绮虹紓鏍垳鐎涙鍚�",
        "nameen": "System Codelists",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "935D1A2D898ABF0097150D1EED1D17DE"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "codelist",
        "lastmodifytime": "1505205762547",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缂傛牜鐖滈崐鐓庣摟閸忥拷",
        "nameen": "Codelists",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "C20E9209C094A9073C309773C25D8644"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "xmsjjhzd",
        "lastmodifytime": "1503559762875",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鏉╂稑瀹崇粻锛勬倞濡剝婢�",
        "nameen": "Task Management",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showreturnbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "119B946E1DEAD4D93FCBB683A4F3BA9C"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "jdhzlb",
        "lastmodifytime": "1502264987696",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鏉╂稑瀹冲Ч鍥ㄢ偓鑽よ閸掞拷",
        "nameen": "Progress Summary Category",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "2918ECA275060CCED79FF1D67C34231A"
    },
    {
        "id": "progress_template",
        "name": "鏉╂稑瀹崇粻锛勬倞濡紕澧楃悰锟�",
        "note": "",
        "uuid": "039F6A7B77FF407DB0424CE387DB315D"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "sjglzd",
        "lastmodifytime": "1503563624859",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺佺増宓佺粻锛勬倞SOP鐎涙鍚�",
        "nameen": "DM SOP Lists",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "A6D75F3C60523E45EB1C6E9B22462413"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "pxzd",
        "lastmodifytime": "1503563592218",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸╃顔勭�涙鍚�",
        "nameen": "Training Assignment",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "BDCBC21A479A1552DF8BE9A7F26B18A1"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "dm_template_file",
        "lastmodifytime": "1503627903109",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺佺増宓佺粻锛勬倞濡剝婢橀弬鍥︽",
        "nameen": "Working Templates",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "235120DED5A1C23C0FD4E2B878C0CD92"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "jlmb",
        "lastmodifytime": "1502264201191",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺傚洣娆㈠Ο鈩冩緲",
        "nameen": "Templates",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "CFD780E09E3F98A20A46B35458527AC1"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "wjlb",
        "lastmodifytime": "1502264006409",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "娑撴潙绨ラ弬鍥︽缁鍩嗙�涙鍚�",
        "nameen": "Trial File Types",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showreturnbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "8391098D1CE022A7AAC4C4F40EAFA268"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "yjzwjlbzd",
        "lastmodifytime": "1502263788072",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閻梻鈹掗懓鍛瀮娴犲墎琚崚顐㈢摟閸忥拷",
        "nameen": "Investigator Site File Category ",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showreturnbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "02FB571188E13287C57C9D5811523561"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "lcwjlbsjg",
        "lastmodifytime": "1502264090915",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "娑撴潙绨ラ弬鍥︽缁鍩嗛弽鎴犵波閺嬶拷",
        "nameen": "Trial File Tree Structures",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "6E34E5A436EB81C120E403362B86401F"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "mksjg",
        "lastmodifytime": "1502263880346",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐠у嫭绨崗鍙橀煩濡剝婢�",
        "nameen": "Public Templates",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "8DE586C34AAC20B7B53C594BE2C6C06A"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "help",
        "lastmodifytime": "1502172079167",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐢喖濮崘鍛啇",
        "nameen": "Help",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "36C61D2C3F458C3F752180351A0490BA"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "dta_ds",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "DTA濡偓濞村銆�/閸掑棙鐎介悧锟�",
        "nameen": "DTA Type",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "E4F0B7B6BA6F44C6A8C1FEF97201E63B"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "dta_ds_item_template",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "DTA鐎涙顔屾惔锟�",
        "nameen": "DTA Field Dictionary",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "785C1133882F4188B0305FA95043AEB2"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "dta_ds_item",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "DTA缁鐎峰Ο鈩冩緲閺佺増宓侀悙锟�",
        "nameen": "DTA Type Template",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "BD0A2F6029F54658B7988A02BA8720F9"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "dta_ds_template",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "DTA濡剝婢�",
        "nameen": "DTA Type",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "92538E91BEC84299AE598B87FD897C09"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "middle",
        "forminitjs": "",
        "formvaljs": "",
        "id": "email_template",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "false",
        "name": "鐞涖劌宕熼柇顔绘濡剝婢�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "D253F0AE8BCD4663AAE18A968A3B0FDB"
    },
    {
        "id": "db_lock_item_tmpl",
        "name": "闁夸礁绨辨い锟�",
        "note": "",
        "uuid": "96E5C4CA5DFF48F1BAEF536A2F21155D"
    },
    {
        "id": "study_user_resp_tmpl",
        "name": "study_user_resp_tmpl",
        "note": "",
        "uuid": "BB729E4D183842C197A5C9A95C3B5F59"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "middle",
        "forminitjs": "",
        "formvaljs": "",
        "helphtml": "",
        "id": "xxyjl1",
        "lastmodifytime": "1524303837890",
        "listeditforminitjs": "",
        "menuinfo": "",
        "modifyreason": "false",
        "name": "閻儴鐦戞惔锟�",
        "nameen": "Information Sharing",
        "newtoedit": "false",
        "note": "Knowledge Base",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "25C4E77ABC2A377461E519998130DA7C"
    },
    {
        "id": "new_post",
        "menuinfo": "",
        "modifyreason": "false",
        "name": "new_post",
        "note": "",
        "uuid": "62DBCDF5608C491AB8054FD86460672D"
    },
    {
        "id": "new_post1",
        "menuinfo": "",
        "modifyreason": "false",
        "name": "new_post1",
        "note": "",
        "uuid": "59AA5CF52206439282BA871E29A94209"
    },
    {
        "id": "study_dm_hour_sum",
        "menuinfo": "",
        "modifyreason": "false",
        "name": "妞ゅ湱娲癉M瀹搞儲妞傚Ч鍥ㄢ偓锟�",
        "note": "",
        "uuid": "1EF4A227C0904D878FDEDFC3D79D5F27"
    },
    {
        "id": "delete_study_listy",
        "menuinfo": "",
        "modifyreason": "false",
        "name": "閸掔娀娅庢い鍦窗濞撳懎宕�",
        "note": "",
        "uuid": "62D81315E3734EEB965CC48FBCCF6C2A"
    },
    {
        "id": "roster_lead",
        "menuinfo": "",
        "modifyreason": "false",
        "name": "缂佸嫮绮愰弸鑸电��",
        "note": "",
        "uuid": "51B3ADB7C70B4C17A3CA688BD47D5401"
    },
    {
        "id": "scp_update",
        "menuinfo": "",
        "modifyreason": "false",
        "name": "scp_update",
        "note": "",
        "uuid": "B1A73A7BE67542CB9DEE2A4EB478A6F6"
    },
    {
        "id": "employee_code",
        "menuinfo": "",
        "modifyreason": "false",
        "name": "闂嗗棗娲熷銉ュ娇",
        "note": "",
        "uuid": "61448BA401C24FE6A3BE4D32D55D7244"
    },
    {
        "id": "study_oa",
        "menuinfo": "",
        "modifyreason": "false",
        "name": "OA闂嗗棗娲熺化鑽ょ埠閺嶅洩鐦�",
        "note": "",
        "uuid": "BFECE8862D014CCF92A36753A1AF313D"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "holiday",
        "listeditforminitjs": "",
        "menuinfo": "",
        "modifyreason": "false",
        "name": "閼哄倸浜ｉ弮锟�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "99F4EC19BDC3488194225D64F8491D0A"
    },
    {
        "id": "special_working_day",
        "menuinfo": "",
        "modifyreason": "false",
        "name": "閻楄鐣╅惃鍕紣娴ｆ粍妫�",
        "note": "",
        "uuid": "F95AFBEF174947719369A787BD7C974E"
    },
    {
        "id": "temp1",
        "modifyreason": "false",
        "name": "eCRF鐎规矮绠�",
        "note": "",
        "uuid": "7602036022A74ECCA6ABA9C9E19D3DE9"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "middle",
        "forminitjs": "",
        "formvaljs": "",
        "id": "jyjl2",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "娑撴艾濮熸禍銈嗙ウ",
        "nameen": "Technical sharing",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "2DCDCEFEA3DC440881FB3492388FE8F8"
    },
    {
        "id": "study_dmp_update",
        "modifyreason": "false",
        "name": "study_dmp_update",
        "note": "",
        "uuid": "10A486F95B3241D2B14ACF4F1C65B408"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "knowledge_base_learner",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐎涳缚绡勭拋鏉跨秿",
        "nameen": "Training records",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "0F09D258A60B4F289C05C29D94C3C39F"
    },
    {
        "id": "study_for_diary",
        "modifyreason": "false",
        "name": "study_for_Diary",
        "note": "",
        "uuid": "647A47DD97894AF690818E569E0CB9D1"
    },
    {
        "id": "diary_task_template",
        "modifyreason": "false",
        "name": "閺冦儱绻旂�涙鍚�",
        "note": "",
        "uuid": "99173A28921E4F4EB9E0000933AFCFE0"
    },
    {
        "id": "employee_manager_map",
        "modifyreason": "false",
        "name": "閻€劍鍩涙稉鑽ゎ吀",
        "note": "",
        "uuid": "C32228846F4C4C428986AC7E49F2A37E"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "email_send_log",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "闁喕娆㈤崣鎴︹偓浣筋唶瑜帮拷",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "66981C69F2AE4365AE0406246394FB33"
    },
    {
        "id": "dm_guide",
        "modifyreason": "false",
        "name": "鐠囧瓨妲戞稊锟�",
        "note": "",
        "uuid": "5F1C87DBD6574A94B055B7414246C569"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "middle",
        "forminitjs": "",
        "formvaljs": "",
        "id": "standard_email",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸忔娊鏁柇顔绘",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "9747172E9E744954A35914128C6D7B99"
    },
    {
        "id": "employee_gifted_plan",
        "modifyreason": "false",
        "name": "閼昏鲸澧犵拋鈥冲灊",
        "note": "",
        "uuid": "603B0E33C5204CA9B508841E8D8F3362"
    },
    {
        "id": "study_work_subject",
        "modifyreason": "false",
        "name": "娴溿倖甯撮崘鍛啇",
        "note": "",
        "uuid": "B65FCB8E946F499587DEC8099111FABC"
    },
    {
        "id": "data_modify_track",
        "modifyreason": "false",
        "name": "data_modify_track",
        "note": "",
        "uuid": "D9011AD7B464465EA56AA7A3F813B681"
    },
    {
        "id": "innovation_team",
        "modifyreason": "false",
        "name": "閹垛偓閺堫垰鍨遍弬鏉跨毈缂侊拷",
        "note": "",
        "uuid": "5BE6F255FB58466C9614522EDAFBE74F"
    },
    {
        "id": "dvs_edm_plan",
        "modifyreason": "false",
        "name": "dvs_edm_plan",
        "note": "",
        "uuid": "0ABBD093757D4C30A60CEB4F7B54B2A5"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "wiki_auth",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "wiki_auth",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "947DD73A5F4940A5B6D66D82BD186D4F"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "",
        "forminitjs": "",
        "formvaljs": "",
        "id": "remotebuttontask",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鏉╂粎鈻兼禒璇插閹稿鎸�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "C0B0E3DE024E4322B2ABB86BDE9ED31A"
    },
    {
        "id": "ecrf",
        "name": "ecrf",
        "note": "",
        "uuid": "778B6B738C77491E8A2436CEA9E6BFD1"
    },
    {
        "id": "ec",
        "name": "ec",
        "note": "",
        "uuid": "51E56CC46E154F3A957121772C9AD278"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rtmstest",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "RTMS_TEST",
        "nameen": "rtms",
        "newtoedit": "true",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "A702695F3E5E46B5B92784DC2959B8BE"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "study_training",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "false",
        "name": "妞ゅ湱娲伴崺纭咁唲",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "71DB2F4963034CD8841B2D5B8E9FC05F"
    },
    {
        "id": "dmrp_map",
        "name": "DMRP濡剝婢�",
        "note": "",
        "uuid": "693FE84EAE1A486DA74D41A39F873D19"
    },
    {
        "id": "dmrp_map_test",
        "name": "dmrp_map_test",
        "note": "",
        "uuid": "BA02FC86E5A340C4B8967766E2F1564D"
    },
    {
        "id": "job_duty",
        "name": "瀹搞儰缍旈懕宀冪煑",
        "note": "",
        "uuid": "6FA52DE67CEC4AA48D876117951BA37E"
    },
    {
        "id": "content_text",
        "name": "閺呴缚鍏橀幓鎰仛鐢喖濮�",
        "note": "",
        "uuid": "4D8EAE0D588E4007BB8124018C7DCD57"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "gzzd",
        "lastmodifytime": "1503986142422",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐟欏嫮鐝烽崚璺哄",
        "nameen": "Regulations",
        "newtoedit": "false",
        "note": "",
        "recordpwr": "false",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "43F1C08304101C42A1DE6814109D2BF8"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "tzhgg",
        "lastmodifytime": "1503997551375",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸忣剙鎲＄粻锛勬倞",
        "nameen": "Announcements",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "5F518634E4A6810B6BC213E29847ED66"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "sop",
        "lastmodifytime": "1502162477991",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "SOP閺傚洣娆�",
        "nameen": "SOPs",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "A8BB2DB4A81BF4DAFA6CDDB1327C3338"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "mb",
        "lastmodifytime": "1502263347434",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "濡剝婢�",
        "nameen": "Templates",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "D91C1C478F100D04D5AC68B3778913F4"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "xxyjl",
        "lastmodifytime": "1502263158955",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閸忓彉闊╃挧鍕爱",
        "nameen": "Other",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "false",
        "uuid": "E84676A2380DA9830286D09E5DDAD94B"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "fgzn",
        "lastmodifytime": "1503985811984",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺�璺ㄧ摜濞夋洝顫�",
        "nameen": "Policies And Regulations",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showreturnbtn": "false",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "false",
        "uuid": "7140CF097EE33678DEC75C7F9715C57E"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "edc_validation_report",
        "lastmodifytime": "1502155649309",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缁崵绮烘宀冪槈閹躲儱鎲�",
        "nameen": "System Validation Report",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "E9A10B1F5849A71736A2E4572FA9AB11"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "edc_v_name",
        "lastmodifytime": "1502096169286",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缁崵绮洪悧鍫熸拱鐎涙鍚�",
        "nameen": "System Versions",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "F984E7EB1C151FC00D7F36BF866482A8"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "edc_user_manual",
        "lastmodifytime": "1502156555076",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缁崵绮洪悽銊﹀煕閹靛鍞�",
        "nameen": "System User Manual",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "E773571146F7A24C15D393E1FE2BE736"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "edc_support_agreement",
        "lastmodifytime": "1502096514592",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閹垛偓閺堫垱鏁幐浣规箛閸斺�冲礂鐠侊拷",
        "nameen": "SLAs",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "4CF733999DF44C3118D8505F0FEFC9DE"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "sjgl",
        "lastmodifytime": "1502261891876",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "CDSC SOPs",
        "nameen": "CDSC SOPs",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "true",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "27831B18EEED70F86D5A2238C02C4931"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "dbworkfile",
        "lastmodifytime": "1503494650703",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺佺増宓佺粻锛勬倞瀹搞儰缍旈弬鍥︽",
        "nameen": "DM Working Documents",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "964D006A2992CAA05CA83C24694BD70F"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "dbworkfile_his",
        "lastmodifytime": "1503494690750",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺佺増宓佺粻锛勬倞瀹搞儰缍旈弬鍥︽閸樺棗褰�",
        "nameen": "Data Management Flie History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "65C51997F59C248116C533F57E7E41CE"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "dbjsfile",
        "lastmodifytime": "1503494747359",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "CDSC閹靛鍞介弬鍥︽",
        "nameen": "DM Technical Documents",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "64F2C962B139AAEBB493922C0D06CA09"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "dbjsfile_his",
        "lastmodifytime": "1503494770922",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺佺増宓佺粻锛勬倞閹垛偓閺堫垱鏋冩禒璺哄坊閸欙拷",
        "nameen": "DM Technical Document History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "D195065EE72EBABD2E001D0408748861"
    },
    {
        "id": "regularattachment",
        "name": "閸ュ搫鐣鹃梽鍕",
        "note": "",
        "uuid": "08F7E2BFE8F544D09CF448354F85650F"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "db_backup",
        "lastmodifytime": "1451464121799",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺冦儱鐖堕弫鐗堝祦鎼存挸顦禒锟�",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "E051BE999789769A2C928151906F0173"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "qa_management",
        "lastmodifytime": "1451464420426",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缁嬭姤鐓＄粻锛勬倞",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "F91F6964CCD05F50FEAB3FB60C767FDA"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "db_qc",
        "lastmodifytime": "1434710358988",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺佺増宓佹惔鎾瑰窛闁插繑甯堕崚锟�",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "35A92DE0741876633016F73EE5D2337D"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "ext_data_load",
        "lastmodifytime": "1451464471913",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "婢舵牠鍎撮弫鐗堝祦鐎电厧鍙�",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "DC90D29CE215CA2BD13091349CC63EB1"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "final_report",
        "lastmodifytime": "1430301139790",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺堚偓缂佸牊鏆熼幑顔藉Г閸涳拷",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "DC93F861D6C6F42E47EB66A70241A997"
    },
    {
        "id": "db_lock_appr",
        "lastmodifytime": "1430377235911",
        "name": "閹电懓鍣弫鐗堝祦鎼存捇鏀ｇ�癸拷",
        "note": "",
        "uuid": "FD76AB9783B4CC93608C5A41BBF963EB"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "bb",
        "lastmodifytime": "1503301229984",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "EDC閻楀牊婀�",
        "nameen": "EDC Version",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "1C5D60F1BAAF673FDD35CDC6CCEDBF6E"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "yhxq",
        "lastmodifytime": "1503301362812",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閻€劍鍩涢棁鈧Ч锟�",
        "nameen": "User Requirement",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "40768CE5A569D0586413783D0B341F3E"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "gngg",
        "lastmodifytime": "1503301399593",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸旂喕鍏樼憴鍕壐",
        "nameen": "Functional Specification",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "6EBCC258E17C7B67A9992A13F881795C"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "yzbg",
        "lastmodifytime": "1503301433797",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "妤犲矁鐦夐幎銉ユ啞",
        "nameen": "Validation Report",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "347BC53EF7ED2C1A86697CC73C2A5A2F"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "sc",
        "lastmodifytime": "1503301468750",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "EDC閹靛鍞�",
        "nameen": "EDC Manual",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "F705220757A7C317E38A5E3AF9875F4A"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "sxtz",
        "lastmodifytime": "1503301577594",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "EDC娑撳﹦鍤庨柅姘辩叀",
        "nameen": "EDC Go-Live Notification",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "2123DDFFF3E3FC3420B355C7653DF8E7"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "fwq",
        "lastmodifytime": "1503301610718",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺堝秴濮熼崳锟�",
        "nameen": "Server",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "81F005629FF8C8724AAFFA0E3564D1DA"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "xthf",
        "lastmodifytime": "1503301645906",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "EDC缁崵绮洪幁銏狀槻",
        "nameen": "EDC System Recovery",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "A4AD1E40CB7912DD4D09F9EFE055544A"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "znhfgc",
        "lastmodifytime": "1503301680843",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閻忛箖姣﹂幁銏狀槻鏉╁洨鈻�",
        "nameen": "Disaster Recovery Process",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "C892BE3F48E1DAB0E8F3C3068F9B173D"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "study_edc_system_trace",
        "lastmodifytime": "1511331053749",
        "listeditforminitjs": "",
        "menuinfo": "",
        "modifyreason": "true",
        "name": "EDC妞ゅ湱娲伴崢鍡楀蕉",
        "nameen": "EDC",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "4B26CC4EA0614E70AE86A06BB2A65F4D"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "xtwt",
        "lastmodifytime": "1503301716547",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缁崵绮洪梻顕�顣�",
        "nameen": "System Problem",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "41CB60B17142C8A6EB7F69D0D4D9EA15"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "system_verification",
        "lastmodifytime": "1503841743312",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缁崵绮�",
        "nameen": "Systems",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "AEB4E64AAD4FE6C4E4C5860DC0932412"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "var date = new Date();
var mydate=date.getFullYear()+%2-%2+(date.getMonth()+1)+%2-%2+date.getDate();
va_setValue('upload_date',mydate);",
        "formvaljs": "",
        "id": "system_verification_list",
        "lastmodifytime": "1503841046765",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缁崵绮烘宀冪槈閺傚洦銆�",
        "nameen": "System Validation Deliverables",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "437C57225C72E39A635341B677C948C5"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "verification_list_dict",
        "lastmodifytime": "1503839311656",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缁崵绮烘宀冪槈鐎涙鍚�",
        "nameen": "System Validation Deliverables",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "11345378258DFAFF6C2B3C1DA243377C"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "verification_his",
        "lastmodifytime": "1503839479890",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閻楀牊婀伴崢鍡楀蕉",
        "nameen": "Version History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "false",
        "uuid": "2B802F84986FED9361B4B704D6634854"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "tool_verification",
        "lastmodifytime": "1503841743312",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "瀹搞儱鍙�",
        "nameen": "Systems",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "7EFCEB716FD546CC9A4572D8BDB0CADD"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "var date = new Date();
var mydate=date.getFullYear()+%2-%2+(date.getMonth()+1)+%2-%2+date.getDate();
va_setValue('upload_date',mydate);",
        "formvaljs": "",
        "id": "tool_verification_list",
        "lastmodifytime": "1503841046765",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "瀹搞儱鍙挎宀冪槈閺傚洦銆�",
        "nameen": "tool Validation Deliverables",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "5CDEF39CA3AA45578B4216CFA1A76258"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "manual_rev_prog_his",
        "lastmodifytime": "1503557355140",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閹靛浼愮�光剝鐓＄粙瀣碍閻楀牊婀伴崢鍡楀蕉",
        "nameen": "Manual Review History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "false",
        "uuid": "6FA38CCA67C80A9EC2A30C222DCB13FB"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "xm_view_his",
        "lastmodifytime": "1503557511328",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "妞ゅ湱娲扮憴鍡楁禈鐎规矮绠熼崢鍡楀蕉",
        "nameen": "View Definition History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "C0343E4314D17F5EC4EBB9F66B546B59"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
var version=projectNum+%2_CRF%2+eVersion;
va_setValue('crf_version',version);
}
</script>",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "crf_design_history",
        "lastmodifytime": "1503557699562",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "CRF鐠佹崘顓搁悧鍫熸拱閸樺棗褰�",
        "nameen": "CRF Design Hisitory",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "42591F27803372C3765EAD8B07F25CF5"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
var version=projectNum+%2_eCRF%2+eVersion;
va_setValue('ecrf_version',version);
}
</script>",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "ecrf_build_his",
        "lastmodifytime": "1503557789531",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "eCRF閻楀牊婀伴崢鍡楀蕉",
        "nameen": "eCRF History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "71FDCA747429628DECBA2C4A0E7F0A5E"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
var version=projectNum+%2_DVP%2+eVersion;
va_setValue('dvp_version',version);
}
</script>",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "edit_check_plan_his",
        "lastmodifytime": "1503557910859",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺佺増宓侀弽鍛婄叀鐠佲�冲灊閻楀牊婀伴崢鍡楀蕉",
        "nameen": "DVP History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "9D5C17D5E736E250759C15AD4E817C21"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "edit_check_prog_his",
        "lastmodifytime": "1503558970968",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "闁槒绶弽鍛婄叀鐠佹儳鐣鹃悧鍫熸拱閸樺棗褰�",
        "nameen": "EC Hisitory",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "82FF4A4F118A6A5324B2B161328B8A11"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('dqbb');
if(eVersion == '' || eVersion == null ){
alert('鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!');
}else{
var version=projectNum+%2_UAT%2+eVersion;
va_setValue('uat_version',version);
}
}
</script>",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "uta_his",
        "lastmodifytime": "1503559093281",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "UAT閻楀牊婀伴崢鍡楀蕉",
        "nameen": "UAT Version History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "false",
        "uuid": "2DF5DF8CDBD1F6390F223EE424B29B31"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "pv_his",
        "lastmodifytime": "1503559142515",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺傝顢嶆潻婵婂剹鐎规矮绠熼悧鍫熸拱閸樺棗褰�",
        "nameen": "Protocol Deviation Version History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "C3861BA38664B8A15D2197AE64A47EA9"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "edc_accountd_his",
        "lastmodifytime": "*************",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缁狅紕鎮婇崢鍡楀蕉",
        "nameen": "EDC Account Management History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "B7819B9F4B0A4457B97EF9BB926C8BBD"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "prod_report_dt",
        "lastmodifytime": "*************",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閻㈢喍楠囬悳顖氼暔閹躲儱鎲￠崢鍡楀蕉",
        "nameen": "Production Report History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "62CF23D51BD014D073CD093A450C7754"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "dmp_gen_his",
        "lastmodifytime": "1505297365005",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "DMP閻楀牊婀伴崢鍡楀蕉",
        "nameen": "DMP History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "E329993CD4E8BC880C5E829E07A50CF2"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
var version=projectNum+%2_eCRF%2+eVersion;
va_setValue('ecrf_version',version);
}
</script>",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "ecrf_build1_his",
        "lastmodifytime": "1503563087547",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "eCRF婵夘偄鍟撻幐鍥у础閻楀牊婀伴崢鍡楀蕉",
        "nameen": "CRF Completion Guideline",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "C0F37DF0C51A57EE7C4188999A8642F5"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "sjk_his",
        "lastmodifytime": "1503563169125",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺佺増宓佹惔鎻汣閻楀牊婀伴崢鍡楀蕉",
        "nameen": "Database QC History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "4CE6FDD73A160B6DFA55D170FA94BC88"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
var version=projectNum+%2%2+eVersion;
va_setValue('ecrf_version',version);
}
</script>",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "ecrf_build2_his",
        "lastmodifytime": "1503563912500",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺佺増宓佹惔鎾茬瑐缁惧灝顓搁幍鍦閺堫剙宸婚崣锟�",
        "nameen": "Database Go-Live Approval History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "45F5FDD6FE0F8DFAC1E92A8B970A9E8D"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rand_his",
        "lastmodifytime": "1503563962718",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "RTSM闂呭繑婧�閸栨牞顔曠純顔煎坊閸欙拷",
        "nameen": "Rand Setting History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "false",
        "uuid": "F4B19ECFA89029D7BA3DC8D487D6A964"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "coding_his",
        "lastmodifytime": "1503564007109",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸栬顒熺紓鏍垳缁狅紕鎮婇崢鍡楀蕉",
        "nameen": "Medical Coding Management  History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "ADAD83811C611B0CB270CF113AB25716"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "xmzqjm_his",
        "lastmodifytime": "1503564054062",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "娑擃厽婀￠幓顓犳锤閸樺棗褰�",
        "nameen": "Interim Unblinding  History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "F3900B60AE1193068914EA041E22614E"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
if(eVersion == '' || eVersion == null ){
alert('鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!');
}else{
var version=projectNum+%2_闂呭繑婧�閸栨牗鏌熷锟�%2+eVersion;
va_setValue('rand_version',version);
}
}
</script>",
        "delinnerdata": "false",
        "forminitjs": "va_setValue('rand_manager',g_curruser);
var date = new Date();
var mydate=date.getFullYear()+%2-%2+(date.getMonth()+1)+%2-%2+date.getDate();

va_setValue('rq',mydate);

",
        "formvaljs": "",
        "id": "randomized_schemehis",
        "lastmodifytime": "1503564130922",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "闂呭繑婧�閸栨牗鏌熷鍫㈡晸閹存劗澧楅張顒�宸婚崣锟�",
        "nameen": "Randomization Scheme Version History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "false",
        "uuid": "EB06A8CAEFA15BEBDF07AFED325F74CC"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('dqbb');
if(eVersion == '' || eVersion == null ){
alert('鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!');
}else{
var version=projectNum+%2_UAT%2+eVersion;
va_setValue('uat_version',version);
}
}
</script>",
        "delinnerdata": "false",
        "forminitjs": "va_setValue('name',g_curruser);
var date = new Date();
var mydate=date.getFullYear()+%2-%2+(date.getMonth()+1)+%2-%2+date.getDate();
va_setValue('rq',mydate);",
        "formvaljs": "",
        "id": "rutahis",
        "lastmodifytime": "1503564216281",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "UAT閻楀牊婀伴崢鍡楀蕉1",
        "nameen": "UAT Version History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "false",
        "uuid": "C312A77C006C7B6044D4B4033B9FCC3A"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "var date = new Date();
var mydate=date.getFullYear()+%2-%2+(date.getMonth()+1)+%2-%2+date.getDate();
va_setValue('date',mydate);",
        "formvaljs": "",
        "id": "rstm_accounthis",
        "lastmodifytime": "*************",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "RSTM鐠愶附鍩涚粻锛勬倞閻楀牊婀伴崢鍡楀蕉",
        "nameen": "RTSM Account Management History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "D300375694686626F142189C6C92B443"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rand_prohis",
        "lastmodifytime": "*************",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "闂呭繑婧�閸栨牗鏌熷鍫㈡暤鐠囬澧楅張顒�宸婚崣锟�",
        "nameen": "Randomization Application History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "B103E5B7815158B353F907B97B79D3A5"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "final_manage_report_his",
        "lastmodifytime": "1503566106937",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺佺増宓佺粻锛勬倞閹躲儱鎲￠悧鍫熸拱閸樺棗褰�",
        "nameen": "DM Report History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "BF7C3BAE9275D4CEA5480A27711CDD11"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "va_setValue('uploader',g_curruser);",
        "formvaljs": "",
        "id": "esignature_statement_his",
        "lastmodifytime": "1503566149812",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閻梻鈹掗懓鍛暩鐎涙劗顒烽崥宥囨畱婢圭増妲�",
        "nameen": "Investigator e-Signature Statement",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "20941A6AF3379A1F7A13B01FC15C33BF"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "lnr_his",
        "lastmodifytime": "1503924225531",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐎圭偤鐛欑�广倕寮懓鍐ㄢ偓鑲╁閺堫剙宸婚崣锟�",
        "nameen": "Lab Reference Range History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "ABCD1FECA3F7164D92F8123EA861D544"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "va_setValue('uploader',g_curruser);",
        "formvaljs": "",
        "id": "edc_permission_his_his",
        "lastmodifytime": "1503925201750",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "EDC閻€劍鍩涢弶鍐閸樺棗褰�",
        "nameen": "EDC User History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "13A1FB742FB111C2C229E4105D6DFC18"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "va_setValue('reported_by',g_curruser);",
        "formvaljs": "",
        "id": "q_management_his",
        "lastmodifytime": "1503925731562",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺佺増宓佺粻锛勬倞闂傤噣顣芥稉搴☆槱閻烇拷",
        "nameen": "DM Issue and Resolution",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "CAAA7B44207DC7320D6F0D59A3E981E9"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
if(eVersion == '' || eVersion == null ){
alert('鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!');
}else{
var version=projectNum+%2_闂呭繑婧�閸栨牗鏌熷锟�%2+eVersion;
va_setValue('rand_version',version);
}
}
</script>",
        "delinnerdata": "false",
        "forminitjs": "va_setValue('rand_manager',g_curruser);
var date = new Date();
var mydate=date.getFullYear()+%2-%2+(date.getMonth()+1)+%2-%2+date.getDate();

va_setValue('rq',mydate);

",
        "formvaljs": "",
        "id": "drugnum_create_his",
        "lastmodifytime": "1510646407693",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閼筋垳澧跨紓鏍у娇閸掓銆冮悽鐔稿灇閻楀牊婀伴崢鍡楀蕉",
        "nameen": "Randomization Scheme Version History",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "false",
        "uuid": "72CC1565973549F199B5490B852058E5"
    },
    {
        "appendhtml": "    <script>

        var studyId = '';
        try {
            parent.addwidth();
        } catch (error) {

            parent.clickHideLeft();



            }



    </script>",
        "delinnerdata": "true",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "middle",
        "forminitjs": "",
        "formvaljs": "var n=va_getValue('gzsjxs');
if(n<6){
//alert('瀹搞儰缍旈弮鍫曟毐娑撳秴顧�!');
}else{
//alert('瀹搞儰缍旈弮鍫曟毐瀹告彃顧�!');
}",
        "id": "xmrz",
        "lastmodifytime": "1512374503468",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "妞ゅ湱娲伴弮銉ョ箶",
        "nameen": "Study Diary",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "E283D9E5B8DF95B22EF6EB180CF71341"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "xmrznr",
        "lastmodifytime": "1512374523538",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺冦儱绻旈崘鍛啇",
        "nameen": "Study Diary Details",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "80DAF844BBB9EFED6B11548D541066E2"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "xjjl",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐠囧嘲浜ｇ拋鏉跨秿",
        "nameen": "Leaves",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "EB346F32C4AA40119CCD8FBB718BB0D1"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "var beginArr = av_private_getDate(va_getValue('kssj'));
var endArr =av_private_getDate(va_getValue('jssj'));
var hours = ((endArr.getTime() - beginArr.getTime())/ 1000 / 3600).toFixed(1);
va_setValue('qjsc',hours);",
        "id": "jbsq",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸旂姷褰悽瀹狀嚞",
        "nameen": "Overtime",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "562B200F697D4A3896A4C323EB273DAB"
    },
    {
        "appendhtml": "",
        "delinnerdata": "true",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "middle",
        "forminitjs": "",
        "formvaljs": "",
        "id": "yxbzzj",
        "lastmodifytime": "1510661701869",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸涖劍鈧崵绮�",
        "nameen": "Weekly Summary",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "95F509F46FC340F1D5E5393D6ED57444"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "weekly_summary_list",
        "lastmodifytime": "1512374523538",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閹崵绮ㄩ崚妤勩��",
        "nameen": "Study Diary Details",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "5BC9CE2DB1484670ABD85765F43A4492"
    },
    {
        "appendhtml": "",
        "delinnerdata": "true",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "middle",
        "forminitjs": "var n=va_getValue('month');
if(n<n+1)
{
//alert('n+1')
}",
        "formvaljs": "",
        "id": "yzj",
        "lastmodifytime": "1510661708859",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閺堝牊鈧崵绮�",
        "nameen": "Monthly Summary",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "9CD2096720E6CB49F39DF7275E6C1453"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "monthly_summary_list",
        "lastmodifytime": "1512374523538",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閹崵绮ㄩ崚妤勩��",
        "nameen": "Study Diary Details",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "971BB846F20844AF9438232210D4A938"
    },
    {
        "id": "study_daily_monthly_sum",
        "name": "妞ゅ湱娲伴弮銉ョ箶閹躲儱鎲�",
        "note": "",
        "uuid": "6FB2C1FC2C2E4066A6024125A6FFE3A5"
    },
    {
        "id": "monthly_sort_dict",
        "name": "閺堝牆瀹抽幒鎺戠碍",
        "note": "",
        "uuid": "ADF526A4D67849DCA9FD631E5CF49089"
    },
    {
        "id": "studyid_oa_map",
        "name": "studyid_oa_map",
        "note": "",
        "uuid": "3CCBC696335340D59A2FAC15C46ACCA4"
    },
    {
        "id": "study_phase_daily_sum",
        "name": "妞ゅ湱娲伴梼鑸殿唽閺冦儱绻旈幎銉ユ啞",
        "note": "",
        "uuid": "4310CC27BFB3400D8B219C193A713915"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('sjhfabbh');
if(eVersion == '' || eVersion == null ){
alert('鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!');
}else{
var version=projectNum+%2_闂呭繑婧�閸栨牔绗岄懡顖滃⒖閸掑棝鍘ら悽瀹狀嚞%2+eVersion;
va_setValue('version_name',version);
}
}
</script>






<style type=%2text/css%2>
  /* 閹绘劗銇氶崘鍛啇閺嶅嘲绱� */
  .tip{ 

    position:absolute;
    padding: 6px 5px; 
    background-color:#dddddd; 
    border-radius: 4px;
    font-size: 12px;
    position: absolute;
    top: -10%1;
    margin-right:80px;
    left: 0%1;
    border: 2px solid #ffffff;
    border-radius: 8px;
    box-shadow: 2px 4px 5px #eeeaea;


  }
  .content{
    cursor:pointer;
  </style>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "var date = new Date();
var mydate=date.getFullYear()+%2-%2+(date.getMonth()+1)+%2-%2+date.getDate();

va_setValue('rq',mydate);





//鐞涖劌宕熼崚婵嗩潗閸栨牞鍓奸張顒婄窗
$(document).ready(function(){ // document.ready閺勵垱瀵歞om閸旂姾娴囩�瑰本鍨氱亸鍗炲讲娴犮儻绱濇稉宥夋付鐟曚礁娴橀悧鍥ф嫲閸忔湹绮挧鍕爱閸旂姾娴囩�瑰苯姘ㄩ崣顖欎簰閹笛嗩攽
  $('.content').click(
   function(event){
     event.stopPropagation();
      $('.tip').fadeIn('slow'); // 濞ｂ�冲弳
   },function(){
	  $('.tip').hide();
	}
  );

  $(document).click(function(){
  $('.tip').fadeOut('slow'); 
  });

  $(%2.tip%2).click(function(event){
  event.stopPropagation();
  });

 $('.content').click(function(e){ // e娑撳搫缍嬮崜宥夌炊閺嶅洣缍旈悽鈺爋m閸忓啰绀岀�电钖�
   var top = e.pageY-100;
   var left = e.pageX;
   $('.tip').css({
    'top' : top+'px',
    'left': left+'px',
    'z-index': '99999'
   });
  });

  // $('.content').mouseout(function(){
  //    $('.tip').hide();
  // });
 });",
        "formvaljs": "",
        "id": "rand_pro",
        "lastmodifytime": "1510664812384",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "闂呭繑婧�閸栨牔绗岄懡顖滃⒖閸掑棝鍘ら悽瀹狀嚞",
        "nameen": "Randomization Application",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "FC77E4C4B0D846B98050E4B717438570"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
if(eVersion == '' || eVersion == null ){
alert('鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!');
}else{
var version=projectNum+%2_闂呭繑婧�鐞涳拷%2+eVersion;
va_setValue('rand_version',version);
}
}
</script>

<script language=%2javascript%2>


function mailSendHmtl(){

var href=%2actionsendmail.getemail.do?tableid=%2+g_tableid+%2&recordid=%2+g_recordid+%2&studyid=%2+va_getValue('studyid')+%2&where=1=1%2;

window.top.webmask_show_modalbyurl(%2闁喕娆�%2,encodeURI(href),1000,600);

}
</script>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "va_setValue('rand_manager',g_curruser);
var date = new Date();
var mydate=date.getFullYear()+%2-%2+(date.getMonth()+1)+%2-%2+date.getDate();

va_setValue('rq',mydate);



if(g_recordid){
$(%2#tbl_buttons%2).append(%2<button class='btn btn-sm btn-outline-secondary btn-radius' onclick='javascript:mailSendHmtl();return false;' title='閸欐垿鈧線鍋栨禒绂眜0027>閸欐垿鈧線鍋栨禒绂眜003c/button>%2);
}",
        "formvaljs": "",
        "id": "randomized_scheme",
        "lastmodifytime": "1512114553370",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "闂呭繑婧�閸掑棝鍘ょ悰锟�(濞村鐦悧鍫礆",
        "nameen": "Randomization Scheme",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "06400D6C9E374AB3AFCED51001C3E697"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rtsmsqcshj",
        "lastmodifytime": "1503234542578",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閻㈠疇顕ù瀣槸閻滎垰顣�",
        "nameen": "Test Environment Application",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "1DF790E33A00439D978F76CD7FF77F23"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('dqbb');
if(eVersion == '' || eVersion == null ){
alert('鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!');
}else{
var version=projectNum+%2_UAT%2+eVersion;
va_setValue('uat_version',version);
}
}
</script>



<style type=%2text/css%2>
  /* 閹绘劗銇氶崘鍛啇閺嶅嘲绱� */
  .tip{ 

    position:absolute;
    padding: 6px 5px; 
    background-color:#dddddd; 
    border-radius: 4px;
      font-size: 12px;
      position: absolute;
      top: -10%1;
margin-right:80px;
      left: 0%1;
      border: 2px solid #ffffff;
      border-radius: 8px;
      box-shadow: 2px 4px 5px #eeeaea;


  }
  .content{
    cursor:pointer;
    }
  </style>
",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "va_setValue('name',g_curruser);
var date = new Date();
var mydate=date.getFullYear()+%2-%2+(date.getMonth()+1)+%2-%2+date.getDate();
va_setValue('rq',mydate);



//鐞涖劌宕熼崚婵嗩潗閸栨牞鍓奸張顒婄窗
$(document).ready(function(){ // document.ready閺勵垱瀵歞om閸旂姾娴囩�瑰本鍨氱亸鍗炲讲娴犮儻绱濇稉宥夋付鐟曚礁娴橀悧鍥ф嫲閸忔湹绮挧鍕爱閸旂姾娴囩�瑰苯姘ㄩ崣顖欎簰閹笛嗩攽
  $('.content').click(
   function(event){
     event.stopPropagation();
      $('.tip').fadeIn('slow'); // 濞ｂ�冲弳
   },function(){
	  $('.tip').hide();
	}
  );

  $(document).click(function(){
  $('.tip').fadeOut('slow'); 
  });

  $(%2.tip%2).click(function(event){
  event.stopPropagation();
  });

 $('.content').click(function(e){ // e娑撳搫缍嬮崜宥夌炊閺嶅洣缍旈悽鈺爋m閸忓啰绀岀�电钖�
   var top = e.pageY-100;
   var left = e.pageX;
   $('.tip').css({
    'top' : top+'px',
    'left': left+'px',
    'z-index': '99999'
   });
  });

  // $('.content').mouseout(function(){
  //    $('.tip').hide();
  // });
 });",
        "formvaljs": "",
        "id": "ruta",
        "lastmodifytime": "1510883150798",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "RTSM Team UAT",
        "nameen": "RTSM Team UAT",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "346BC3E1775D47ED817F5A9CB2542598"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('version_number');
if(eVersion == '' || eVersion == null ){
alert('鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!');
}else{
var version=projectNum+%2_缁崵绮烘稉濠勫殠閻㈠疇顕�%2+eVersion;
va_setValue('version_name',version);
}
}
</script>
",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rtschj",
        "lastmodifytime": "1510388185539",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "false",
        "name": "RTSM娑撳﹦鍤庨悽瀹狀嚞",
        "nameen": "RTSM Production Environment",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "5E4473AB873744EE9E2953C503BD1533"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "drug_blind",
        "lastmodifytime": "1510646619085",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閼筋垳澧跨紓鏍锤",
        "nameen": "Drug Blinding",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "2A698178C448498E89A32A461CEC3EB5"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "drug_blindtrain",
        "lastmodifytime": "1503234852750",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "true",
        "name": "閸╃顔�",
        "nameen": "RTSM User Training",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "60%",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "02A323715A3641F2B9DE8ED0273C341E"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "var date = new Date();
var mydate=date.getFullYear()+%2-%2+(date.getMonth()+1)+%2-%2+date.getDate();
va_setValue('date',mydate);",
        "formvaljs": "",
        "id": "rstm_account",
        "lastmodifytime": "*************",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐠愶附鍩涚粻锛勬倞",
        "nameen": "RTSM Account Management",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "FBEC94A360364DBAB1E97DA612DEB8EA"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "jjjm",
        "lastmodifytime": "*************",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "娑擃亙绶ラ幓顓犳锤",
        "nameen": "Emergency Unblinding",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "830445762A58407FA2871FB361B39E8A"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('version_number');
if(eVersion == '' || eVersion == null ){
alert('鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!');
}else{
var version=projectNum+%2_缁崵绮洪柊宥囩枂閹躲儱鎲�%2+eVersion;
va_setValue('version_name',version);
}
}
</script>


<script language=%2javascript%2>


function mailSendHmtl(){

var href=%2actionsendmail.getemail.do?tableid=%2+g_tableid+%2&recordid=%2+g_recordid+%2&studyid=%2+va_getValue('studyid')+%2&where=1=1%2;

window.top.webmask_show_modalbyurl(%2闁喕娆�%2,encodeURI(href),1000,600);

}
</script>
	",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rand",
        "lastmodifytime": "1503235835922",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "RTSM闁板秶鐤嗛幎銉ユ啞",
        "nameen": "Randomization Reports",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "66B1F3C7884A410EB698C359E069C9CF"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "",
        "formvaljs": "var sjbm=va_getValue('sjbm');
var sjbjsrmd5=va_getValue('sjbjsrmd5');
if(sjbm==sjbjsrmd5){

va_setValue('msfxt','1');


}else{
va_setValue('msfxt','2');
}",
        "id": "xmzqjm",
        "lastmodifytime": "1503236032937",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "妞ゅ湱娲伴幓顓犳锤",
        "nameen": "Trial Unblinding",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "6E925DFA2DA6436E89EA630AE49C7802"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('sjhfabbh');
if(eVersion == '' || eVersion == null ){
alert('鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!');
}else{
var version=projectNum+%2_閼筋垳澧跨紓鏍у娇閸掓銆冮悽瀹狀嚞%2+eVersion;
va_setValue('version_name',version);
}
}
</script>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "drugnum_apply",
        "lastmodifytime": "1510664882634",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閼筋垳澧跨紓鏍у娇閸掓銆冮悽瀹狀嚞 ",
        "nameen": "Randomization Application",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "1A4540E1766848A4BCA855B3E8F5B1A0"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('bbh');
if(eVersion == '' || eVersion == null ){
alert('鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!');
}else{
var version=projectNum+%2_閼筋垳澧跨紓鏍у娇閸掓銆�%2+eVersion;
va_setValue('rand_version',version);
}
}
</script>
",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "drugnum_create",
        "lastmodifytime": "1510664601290",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閼筋垳澧跨紓鏍у娇鐞涱煉绱欏ù瀣槸閻楀牞绱�",
        "nameen": "Randomization Scheme",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "B51FBE8B3DC24F129AFC3A556AEA2DAB"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "mblind_record",
        "lastmodifytime": "1510798544268",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閼筋垳澧跨紓鏍锤",
        "nameen": "Randomization Application",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "F39D02CD369A4822BD73B2B37CE86573"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "tconfigure_reoort",
        "lastmodifytime": "1510641451906",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "濞村鐦悳顖氼暔闁板秶鐤嗗Λ鈧ù瀣Г閸涳拷",
        "nameen": "RTSM User Training",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "A0334A679FC74E5CA2C9A92B8D334B0C"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rconfigure_reoort",
        "lastmodifytime": "1510642259105",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閻㈢喍楠囬悳顖氼暔闁板秶鐤嗗Λ鈧ù瀣Г閸涳拷",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "E2D5691143A94288ACF5EFE0E24D4ECE"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "var sjbm=va_getValue('sjbm');
var sjbjsrmd5=va_getValue('sjbjsrmd5');
if(sjbm==sjbjsrmd5){

va_setValue('msfxt','1');


}else{
va_setValue('msfxt','2');
}",
        "id": "unblinding",
        "lastmodifytime": "1510901285406",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "妞ゅ湱娲伴幓顓犳锤",
        "nameen": "Trial Unblinding",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "B91C7C04D34047059682912B00E66346"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('version_number');
if(eVersion == '' || eVersion == null ){
alert('鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!');
}else{
var version=projectNum+%2_SAS缁嬪绨�%2+eVersion;
va_setValue('version_name',version);
}
}
</script>",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rand_sas_program",
        "lastmodifytime": "1510664812384",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閻╂彃绨�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "C5608560E7284D8AB462D29B019D5367"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rand_other",
        "lastmodifytime": "1510664812384",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸忔湹绮�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "67BD9E90417A4C66BC82E3077E5562A9"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('version_number');
if(eVersion == '' || eVersion == null ){
alert('鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!');
}else{
var version=projectNum+%2_闂呭繑婧�閸栨牞顓搁崚锟�%2+eVersion;
va_setValue('version_name',version);
}
}
</script>

",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rand_protocol_design",
        "lastmodifytime": "1510664812384",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "闂呭繑婧�閸栨牞顓搁崚锟�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "7FD0AD1199354B8CB144C4257B3BD1DD"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('version_number');
if(eVersion == '' || eVersion == null ){
alert('鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!');
}else{
var version=projectNum+%2_缁崵绮洪柊宥囩枂鐠囧瓨妲�%2+eVersion;
va_setValue('version_name',version);
}
}
</script>





<style type=%2text/css%2>
  /* 閹绘劗銇氶崘鍛啇閺嶅嘲绱� */
  .tip{ 

    position:absolute;
    padding: 6px 5px; 
    background-color:#dddddd; 
    border-radius: 4px;
      font-size: 12px;
      position: absolute;
      top: -10%1;
margin-right:80px;
      left: 0%1;
      border: 2px solid #ffffff;
      border-radius: 8px;
      box-shadow: 2px 4px 5px #eeeaea;


  }
  .content{
    cursor:pointer;
    }
  </style>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "
//鐞涖劌宕熼崚婵嗩潗閸栨牞鍓奸張顒婄窗
$(document).ready(function(){ // document.ready閺勵垱瀵歞om閸旂姾娴囩�瑰本鍨氱亸鍗炲讲娴犮儻绱濇稉宥夋付鐟曚礁娴橀悧鍥ф嫲閸忔湹绮挧鍕爱閸旂姾娴囩�瑰苯姘ㄩ崣顖欎簰閹笛嗩攽
  $('.content').click(
   function(event){
     event.stopPropagation();
      $('.tip').fadeIn('slow'); // 濞ｂ�冲弳
   },function(){
	  $('.tip').hide();
	}
  );

  $(document).click(function(){
  $('.tip').fadeOut('slow'); 
  });

  $(%2.tip%2).click(function(event){
  event.stopPropagation();
  });

 $('.content').click(function(e){ // e娑撳搫缍嬮崜宥夌炊閺嶅洣缍旈悽鈺爋m閸忓啰绀岀�电钖�
   var top = e.pageY-100;
   var left = e.pageX;
   $('.tip').css({
    'top' : top+'px',
    'left': left+'px',
    'z-index': '99999'
   });
  });

  // $('.content').mouseout(function(){
  //    $('.tip').hide();
  // });
 });",
        "formvaljs": "",
        "id": "rtsm_setting_explain",
        "lastmodifytime": "1510664812384",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "RTSM闁板秶鐤嗛棁鈧Ч锟�",
        "nameen": "Randomization Application",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "6290FCB9EC17418E98A5DD49B4367C8D"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "comparison_report",
        "lastmodifytime": "1510644593831",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閻滎垰顣ㄩ柊宥囩枂濮ｆ柨顕幎銉ユ啞",
        "nameen": "RTSM User Training",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "2211DEF0B30C4783B9FD4A8DB49E632C"
    },
    {
        "appendhtml": "<script language=%2javascript%2>
function getV(){
var va_id=va_getValue('studyid');
var where = %2obj.id = %2+ va_id;
var projectNum= va_getLinkValue('xsht','studyid',where,'obj.createtime desc');
var eVersion=va_getValue('version_number');
if(eVersion == '' || eVersion == null ){
alert('鐠囩兘鈧瀚ㄨぐ鎾冲閻楀牊婀�!');
}else{
var version=projectNum+%2_閹活厾娲哥拋鈥冲灊%2+eVersion;
va_setValue('version_name',version);
}
}
</script>





<style type=%2text/css%2>
  /* 閹绘劗銇氶崘鍛啇閺嶅嘲绱� */
  .tip{ 

    position:absolute;
    padding: 6px 5px; 
    background-color:#dddddd; 
    border-radius: 4px;
      font-size: 12px;
      position: absolute;
      top: -10%1;
margin-right:80px;
      left: 0%1;
      border: 2px solid #ffffff;
      border-radius: 8px;
      box-shadow: 2px 4px 5px #eeeaea;


  }
  .content{
    cursor:pointer;
    }
  </style>",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "


//鐞涖劌宕熼崚婵嗩潗閸栨牞鍓奸張顒婄窗
$(document).ready(function(){ // document.ready閺勵垱瀵歞om閸旂姾娴囩�瑰本鍨氱亸鍗炲讲娴犮儻绱濇稉宥夋付鐟曚礁娴橀悧鍥ф嫲閸忔湹绮挧鍕爱閸旂姾娴囩�瑰苯姘ㄩ崣顖欎簰閹笛嗩攽
  $('.content').click(
   function(event){
     event.stopPropagation();
      $('.tip').fadeIn('slow'); // 濞ｂ�冲弳
   },function(){
	  $('.tip').hide();
	}
  );

  $(document).click(function(){
  $('.tip').fadeOut('slow'); 
  });

  $(%2.tip%2).click(function(event){
  event.stopPropagation();
  });

 $('.content').click(function(e){ // e娑撳搫缍嬮崜宥夌炊閺嶅洣缍旈悽鈺爋m閸忓啰绀岀�电钖�
   var top = e.pageY-100;
   var left = e.pageX;
   $('.tip').css({
    'top' : top+'px',
    'left': left+'px',
    'z-index': '99999'
   });
  });

  // $('.content').mouseout(function(){
  //    $('.tip').hide();
  // });
 });",
        "formvaljs": "",
        "id": "unbeautiful_plan",
        "lastmodifytime": "1510664812384",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閹活厾娲哥拋鈥冲灊",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "8407C3D44C3B48E18B99AE62381CAF76"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rtsm_plan_version",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "false",
        "name": "RTSM缁崵绮烘穱顔款吂鐠佲�冲灊",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "630C7071B98C4989AB5B19409BD79ABF"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rtsm_pg_version",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "false",
        "name": "RTSM缁崵绮烘穱顔款吂鐠愩劍甯�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "9EC221B9AEB4487D8137618316C8C56B"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rtsm_report_version",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "false",
        "name": "RTSM缁崵绮烘穱顔款吂閹躲儱鎲�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "1C4156FFC14945D4A06C8243680AA841"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rtsm_sjk",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "false",
        "name": "RTSM闁板秶鐤嗙拹銊﹀付",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "A4A0117CD2324CE6A0A3BE46B4820423"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "",
        "formvaljs": "",
        "id": "rtsm_jc",
        "listeditforminitjs": "",
        "modifylock": "false",
        "modifyreason": "false",
        "name": "闂呭繑婧�閸掑棝鍘ら惄鎴炵ゴ",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "saveas_copyattach": "false",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "9085164A6DA3405F9411BFD3EBD8A0DA"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "site_trainee",
        "lastmodifytime": "1500372370948",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閹存垹娈戞径鏍劥閸╃顔�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "1820202D86EE4D03AF0DCA90535444A0"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "site_mmcai",
        "lastmodifytime": "1500514081718",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐠囧彞娆�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsubtable": "false",
        "uuid": "9F8675129F5740A2979AE37EB9A3F930"
    },
    {
        "appendhtml": "<script>
function change() {


$(%2#course_name_selbutton%2).hide();
var kczt=va_getValue('kczt');
var kcbh=va_getValue('kcbh');

    var kcfl = $(%2#field_kcfl%2).val();
	
	if(!kcfl){
	$(%2#field_kczt%2).html('<option selected value></option>');
		
		return false;
	
	}
	
	
	var getFlListWhere=%2obj.active='1' and obj.codelist_name='TRAIN' and obj.code_value not in ('00','01','02','03','04','05') and SUBSTRING(obj.code_value,1,2)='%2+kcfl+%2'%2;

	
	var flList=va_getLinkValueStr('codelist','code_value,code_cdescription',getFlListWhere,'obj.code_value asc','all');
	


	var flMap = flList.split(',|,');
	var str = '<option value=%2%2></option>';
	var fl1Value = '';

	for (let i=0; i < flMap.length; i++) {
		
		
		var fl = flMap[i].split(',;,');
		if(i==0){
		fl1Value=fl[0];
		}
		str = str + '<option value=%2'+fl[0]+'%2>' + fl[1] + '</option>';

		
	}

	$(%2#field_kczt%2).html(str);
        

va_setValue('kczt',kczt);
va_setValue('kcbh',kcbh);
if(kcbh=='0430'||kcbh=='0410'){
$(%2#course_name_selbutton%2).show();


}
}



function kcztchange() {

var kcbh=va_getValue('kcbh');
if(kcbh=='0430'||kcbh=='0410'){
$(%2#course_name_selbutton%2).show();


}else{

$(%2#course_name_selbutton%2).hide();


}
	
	if(!kcbh){
		
		return false;
	
	}
	
	
	var getFlListWhere=%2obj.active='1' and obj.codelist_name='TRAIN' and obj.code_value='%2+kcbh+%2'%2;

	var notes=va_getLinkValue('codelist','notes1',getFlListWhere,'obj.code_value asc');


	va_setValue('course_name',notes);
	
}



</script>
",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "right",
        "forminitjs": "change();

 var cNodes = document.getElementsByName(%2frm_innershow0%2);
  for(var i=0;i<cNodes.length;i++){
   cNodes[i].style.display = %2none%2;
  }",
        "formvaljs": "",
        "id": "site_train",
        "lastmodifytime": "1499997169651",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸╃顔勭拋鏉跨秿",
        "nameen": "My Trainings",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "50%",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "6808E592D0D5487095F81D34B4BDFC8A"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "item_control",
        "lastmodifytime": "1524454241852",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "妫版ê绨辩粻锛勬倞",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "1101D8526F3149F39436136884DE1BE7"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "tpaper_control",
        "lastmodifytime": "1524454291346",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐠囨洖宓庣粻锛勬倞",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "false",
        "uuid": "C0DAA140F83F46FCB57D8EE4716FA662"
    },
    {
        "id": "ctgzb",
        "lastmodifytime": "1485065494152",
        "name": "閹朵粙顣界憴鍕灟鐞涳拷",
        "note": "",
        "uuid": "E65235DE860049598E1FFB30C7224134"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "detailed_item",
        "lastmodifytime": "1486450645080",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐠囨洟顣�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "66FC757167684947B977994036A2C125"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "exam_plan",
        "lastmodifytime": "1499997262097",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閼板啳鐦�瑰甯�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "false",
        "uuid": "75183B168C754A169A56F72695F84774"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "exam_user",
        "lastmodifytime": "1486624126968",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閼板啳鐦禍鍝勬喅",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "ACB24976179C44AB8D053F7AD6143BDC"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "scores_info",
        "lastmodifytime": "1490164271049",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閹存劗鍝楃拋鏉跨秿",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "prjexam.showreadonly.do?ridgrade={id}",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "A01DC8222A2742D98891FE98B4446F75"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "detailed_item1",
        "lastmodifytime": "1496312601420",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "缁涙棃顣界拋鏉跨秿",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "42547437DD6045CB88BFBAC7F159A3ED"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "mmcai_control",
        "lastmodifytime": "1500357317062",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐠囧彞娆㈢粻锛勬倞",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsubtable": "false",
        "uuid": "B810099C825E43119E06348589A1830A"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "course_control",
        "lastmodifytime": "1499997169651",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸╃顔勭拠鍓р柤",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "false",
        "uuid": "A99483E216024185B5869D68CE3E87FB"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "trainee",
        "lastmodifytime": "1500372370948",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸╃顔勬禍鍝勬喅",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "F121B1174B94428DBCD05E140F9590C1"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "detailed_mmcai",
        "lastmodifytime": "1500514081718",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐠囧墽鈻肩拠鍙ユ",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "false",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsubtable": "false",
        "uuid": "8AC14EBACDEB49949BC84D58C43AA8C8"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "pxjl1",
        "lastmodifytime": "1500514108247",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸╃顔勯弰搴ｇ矎",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "8944F9A5920E4033B0524D50FB048085"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "llscb",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸╃顔勭拋鏉跨秿",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "E0634EFEDB004C54959006A54179B2BD"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "pxsm",
        "lastmodifytime": "1467872571183",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸╃顔勯悽铏",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "29E46E643D5346FCADDFB7CB0963AB6D"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "zzxx",
        "lastmodifytime": "1475034278230",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閼奉亙瀵岀�涳缚绡�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "6501FDCA9839483495063B6FC14E4444"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "train_credential",
        "lastmodifytime": "1500372370948",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸╃顔勭拠浣峰姛",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "false",
        "showsubtable": "true",
        "uuid": "E87F506006874067ADD628BE0198579F"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "var tablevalue='<div fieldid=%2exam_plan%2></div><div fieldid=%2pass_score%2></div><div fieldid=%2credential_name%2 fieldvalue=%21111%2></div><div fieldid=%2credential_model%2></div>'

            $.ajax({
                url: %2/tableapp.save.do%2,
                type: %2POST%2,
                async: false,
                data: {tableid: 'credential', tablevalue: tablevalue,prjid:'cdtmsen_val'},
                success: function (msg) {
                    if (msg == %2OK%2) {
                        alert(%2娣囨繂鐡ㄩ幋鎰閿涳拷%2);
                        window.location.reload();
                    }
                }
            });",
        "id": "credential",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鐠囦椒鍔熺粻锛勬倞",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "4472306A70244A02BEFD4A38726A675B"
    },
    {
        "id": "dm_quantity",
        "name": "闁插繐瀵�",
        "note": "",
        "uuid": "E4A18F9C51B84CD99678CCDA8F7538D5"
    },
    {
        "id": "dm_dur",
        "name": "閺冨爼妫�",
        "note": "",
        "uuid": "BC638EB579394EB7BE5B239250F307C6"
    },
    {
        "id": "dm_efficiency",
        "name": "閺佸牏宸�",
        "note": "",
        "uuid": "69279709D332418BB91B8FC8022A4DDD"
    },
    {
        "id": "dm_cost",
        "name": "鐠愬湱鏁�",
        "note": "",
        "uuid": "BEB6A0D55D184173868197EE584A0410"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "conpub",
        "lastmodifytime": "1518427755878",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "鏉╂粎鈻奸崣鎴濈閸欏倹鏆�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "55A7BAFF62FC4DE5A31785B9531D8AAB"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "pubhis",
        "lastmodifytime": "1520591523512",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "妞ゅ湱娲扮�规矮绠熼幒銊┾偓锟�",
        "nameen": "Pushing Study Definition ",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "206FD074285F4A5AB4DC1721FCD7EFFD"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "contable",
        "lastmodifytime": "1520583493116",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸欐垵绔�",
        "nameen": "Publishing",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsubtable": "true",
        "uuid": "8DCD2AC84A4140509616398984215CA5"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "forminitjs": "",
        "formvaljs": "",
        "id": "study_file",
        "lastmodifytime": "1503998396828",
        "listeditforminitjs": "",
        "modifyreason": "true",
        "name": "閺佺増宓佺粻锛勬倞閺傚洣娆㈣ぐ鎺撱��",
        "nameen": "DM Document Archives",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "showaddbtn": "true",
        "showsaveandnew": "false",
        "showsaveas": "false",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "E215838B93CF474BBF829E524638C18D"
    },
    {
        "id": "langmanage",
        "name": "婢舵俺顕㈢懛鈧粻锛勬倞",
        "note": "",
        "uuid": "63CCD66D839C45A2AA6BCCF2DB68B5F3"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "",
        "forminitjs": "",
        "formvaljs": "",
        "id": "online_template",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "闁喕娆㈤柊宥囩枂濡剝婢�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "3717840516E144948758F49594A77832"
    },
    {
        "id": "emaildistribute",
        "name": "闁喕娆㈤崚鍡楀絺",
        "note": "",
        "uuid": "D6EF87B926CA401D95EA6AFD5ACC2450"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "",
        "forminitjs": "",
        "formvaljs": "",
        "id": "online_review",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "閸︺劎鍤庣�光剝鐗�",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "false",
        "uuid": "5D240165CDF6431F9B66779730031507"
    },
    {
        "id": "review_detail",
        "name": "鐎光剝鐗抽弰搴ｇ矎",
        "note": "",
        "uuid": "80B0D7783CED4414A58FBF38FB093DDE"
    },
    {
        "appendhtml": "",
        "delinnerdata": "false",
        "delrefdata": "false",
        "delreflink": "false",
        "editpopuptype": "",
        "forminitjs": "",
        "formvaljs": "",
        "id": "tableid",
        "listeditforminitjs": "",
        "modifyreason": "false",
        "name": "tableid_ch_en",
        "nameen": "",
        "newtoedit": "false",
        "note": "",
        "redirecturl": "",
        "rightPopWidth": "",
        "saveCloseWindow": "",
        "showaddbtn": "true",
        "showsaveandnew": "true",
        "showsaveas": "true",
        "showsearchbtn": "true",
        "showsubtable": "true",
        "uuid": "56C387D4E7B04DF6A8E7FC517264D21E"
    },
    {
        "id": "datamerge",
        "name": "閺佺増宓侀崥鍫濊嫙",
        "note": "",
        "uuid": "EB59F7AC7BED4DE78FF6054720FA1628"
    },
    {
        "id": "datamerge1",
        "name": "閺佺増宓侀崥鍫濊嫙",
        "note": "",
        "uuid": "E4E5C157A9604572B3BD7AE7E8DB1E0E"
    },
    {
        "id": "datamergelog",
        "name": "閺佺増宓侀崥鍫濊嫙log",
        "note": "",
        "uuid": "D52C92BD0B514FB9B4F4978DE3B62CE5"
    }
]
