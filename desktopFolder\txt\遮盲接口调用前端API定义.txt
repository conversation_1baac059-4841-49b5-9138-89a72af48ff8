export function submitJsonParams(data, blindDemand, blindMember, fileName, projectName, userName, role, originalFileId, exDataId, token, dataType, blindOperateName,blindMemberWithMail) {
  return request({
    url: 'external_data/submitJsonParams?fileName=' + fileName + '&blindDemand=' + blindDemand + '&blindMember=' + blindMember + '&projectName=' + projectName + '&userName=' + userName + '&role=' + role + '&originalFileId=' + originalFileId + '&exDataId=' + exDataId + '&token=' + encodeURI(token) + '&dataType=' + dataType + '&blindOperateName=' + blindOperateName+ '&blindMemberWithMail=' + blindMemberWithMail,
    headers: { 'content-type': 'application/json;charset=utf-8' },
    method: 'post',
    data
  })
}