﻿<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=utf-8" %>
<%@ page language="java" pageEncoding="utf-8" %>

<html>
<head>
    <meta charset="utf-8">
    <title>签署信息</title>
    <script src="/prjplug/esign/js/jquery-3.4.1.min.js"></script>
    <link rel="stylesheet" href="/prjplug/esign/js/esign.css">


</head>


<body>


<svg style="display:none">


    <symbol id="icon-pdf" viewBox="0 0 20 20">
        <path d="M9.863 5.503l.01-.032-.113-.058-.087-.038-.138.018-.055.013a.296.296 0 00-.143.087c-.044.056-.084.176-.115.375a3 3 0 00-.016.79c.032.287.11.623.23.997.174-.597.296-1.174.355-1.649l.059-.432.013-.07zm-.96 3.628c.055-.126.108-.255.16-.385l-.094-.2a6.294 6.294 0 01-.238-.631 6.1 6.1 0 01-.27-1.174 3.746 3.746 0 01.02-.987c.048-.314.125-.544.263-.721a1.01 1.01 0 01.501-.34c.096-.03.156-.041.257-.051l.126-.015a.68.68 0 01.24.023c.***************.302.13l.002-.005c.*************.416.868l-.053.455c-.09.736-.32 1.681-.65 2.595l.08.136c.274.45.68.98 1.164 1.516.239.262.506.536.78.802a9.508 9.508 0 012.55.178c.419.087.742.193 1.028.349.338.184.61.432.76.72.164.323.172.696.016.985-.143.268-.402.437-.73.526-.303.086-.615.09-.952.002-.32-.083-.684-.255-1.092-.506a9.477 9.477 0 01-1.277-.955 19.23 19.23 0 01-.58-.525l-.194.02-.427.055-.498.074c-.676.106-1.216.212-1.871.38-.348.089-.7.188-1.039.293l-.35.447c-.59.728-1.143 1.279-1.625 1.636-.439.325-.807.48-1.158.533-.33.05-.574-.016-.735-.265-.119-.193-.14-.452-.071-.717.077-.297.26-.569.6-.858.39-.333.973-.679 1.685-.98a14.92 14.92 0 011.198-.438c.703-.953 1.33-1.992 1.756-2.97zM5.18 14.224c.312-.231.666-.563 1.05-.99l-.234.103c-.532.244-.96.508-1.245.752-.23.195-.326.338-.362.476l-.01.05c.222-.038.47-.146.801-.391zm7.976-2.325a8.448 8.448 0 00-.455-.02l.218.185c.352.292.655.51.961.698.35.216.651.358.89.42.206.054.383.051.561 0 .15-.04.238-.097.27-.157.035-.066.033-.18-.022-.287-.072-.139-.235-.287-.451-.405-.213-.116-.47-.2-.826-.274a8.461 8.461 0 00-1.146-.16zm-2.583-1.05a12.29 12.29 0 01-1.036-1.3l-.209.441a16.508 16.508 0 01-1.031 1.774l.554-.138c.515-.121.987-.21 1.545-.297l.552-.082c-.13-.134-.256-.268-.375-.398zM2.75 1h14.5c.966 0 1.75.784 1.75 1.75v14.5A1.75 1.75 0 0117.25 19H2.75A1.75 1.75 0 011 17.25V2.75C1 1.784 1.784 1 2.75 1zm0 1a.75.75 0 00-.75.75v14.5c0 .414.336.75.75.75h14.5a.75.75 0 00.75-.75V2.75a.75.75 0 00-.75-.75H2.75z"></path>
    </symbol>

    <symbol id="svg-link-solid-icon" viewBox="0 0 20 20">
        <path d="M2.96396108,2.96555934 C4.91658254,1.01293788 8.08061259,1.01114304 10.0316725,2.96220298 L17.1094531,10.0399835 C19.0602209,11.9907513 19.054745,15.1590467 17.1060967,17.107695 C15.1534752,19.0603164 11.9894452,19.0621113 10.0383853,17.1110513 L2.96060471,10.0332708 C1.00983692,8.082503 1.01531281,4.91420761 2.96396108,2.96555934 Z M4.37817464,4.3797729 C3.20865692,5.54929063 3.20701579,7.45125474 4.37481828,8.61905723 L6.49949498,10.7437339 L5.08528142,12.1579475 L7.91370855,14.9863746 L9.32792211,13.5721611 L11.4525988,15.6968378 C12.6221903,16.8664293 14.5198907,16.8654739 15.6918831,15.6934814 C16.8614009,14.5239637 16.863042,12.6219996 15.6952395,11.4541971 L13.5705628,9.32952037 L14.9847764,7.91530681 L12.1563492,5.08687969 L10.7421357,6.50109325 L8.61745896,4.37641654 C7.44786746,3.20682503 5.55016711,3.20778044 4.37817464,4.3797729 Z M7.68691062,6.62786993 L13.3437456,12.2847242 L12.2830891,13.3453844 L6.62625405,7.6885301 L7.68691062,6.62786993 Z"
              transform="translate(10.035029, 10.036627) scale(-1, 1) translate(-10.035029, -10.036627) "></path>
    </symbol>
</svg>


<div id="mainExtContainer" class="zps-detail-container" current-view="yes" detail-module="task">
    <div>
        <div class="detail_rhs widget-custom-scroll" data-id="detail-rhs" id="detail-rhs">
            <table cellspacing="0" cellpadding="0" border="0" class="detail-main-container">
                <tbody class="detail-main-header">
                <tr>
                    <td>
                        <div class="detail-header-sticky">
                            <div class="detail-title-action">
                                <div class="detail-title-content">
                                    <div class="detail-title-holder">
                                        <div class="detail-title-container" allow-edit="true" data-id="titleParent"
                                             line-count="1" data-connecteddom="true">
                                            <%--                                                <div class="detail-title-plain"> ${eSignDataMap.studyid} | ${eSignDataMap.ecrf_version} </div>--%>
                                            <div class="detail-title-plain"> ${esignInstanceMap.edition_name} </div>
                                        </div>
                                    </div>
                                </div>


                                <div class="detail-action-section">
                                    <c:if test="${esignInstanceMap.status=='签署中'}">

                                     <span class="top-btn"    word-wrap="true">
        <button class="zps-primary-button" zps-loader="" type="button" onclick="loadSingerControl()" name="" id=""
                value="签字人管理">签字人管理</button>

		<span id="newfollowcnt" class="top-btn-count"></span>
		<div class="zps-pop-container hide" oscclass="hide" id="followerPop" pop-arrow="TR">
			<div id="showfollower" class="follower-pop">
				<div id="viewmore" data-leftstyle="false" style="">
					<div class="zps-form-field full-field">  <label class="mandatory-field">废除原因</label>

                        <textarea id="revokeReson" name="revokeReson"></textarea>  </div>
					<div class="flex mT15">
						<button class="zps-primary-button" zps-loader="" type="button"
                                name="revokeButton" id="revokeButton" value="确认">确认</button>
						<button type="button" value="取消" class="zps-secondary-button"
                                onclick="jQuery('#followerPop').addClass('hide');">取消</button>
					</div>
				</div>
			</div>
			<input type="hidden" id="infoluser" value="">
		</div>
	</span>




                                        <span class="top-btn" data-zpqa="addfollower" data-id="addfollower"
                                          word-wrap="true">
        <button class="zps-primary-button" zps-loader="" type="button" onclick="popBox()" name="" id=""
                value="作废">废除</button>

		<span id="newfollowcnt" class="top-btn-count"></span>
		<div class="zps-pop-container hide" oscclass="hide" id="followerPop" pop-arrow="TR">
			<div id="showfollower" class="follower-pop">
				<div id="viewmore" data-leftstyle="false" style="">
					<div class="zps-form-field full-field">  <label class="mandatory-field">废除原因</label>

                        <textarea id="revokeReson" name="revokeReson"></textarea>  </div>
					<div class="flex mT15">
						<button class="zps-primary-button" zps-loader="" type="button"
                                name="revokeButton" id="revokeButton" value="确认">确认</button>
						<button type="button" value="取消" class="zps-secondary-button"
                                onclick="jQuery('#followerPop').addClass('hide');">取消</button>
					</div>
				</div>
			</div>
			<input type="hidden" id="infoluser" value="">
		</div>
	</span>

                                    </c:if>


                                    <c:if test="${esignInstanceMap.status=='已废除'||esignInstanceMap.status=='已完成'||esignInstanceMap.status=='已过期'}">

                                    <span class="top-btn" data-zpqa="addfollower" data-id="addfollower"
                                          word-wrap="true">
        <button class="zps-primary-button" zps-loader="" type="button" id="anewSign">

                                                <c:if test="${esignInstanceMap.status=='已废除'}">

                                                    重新发起

                                                </c:if>


                                                <c:if test="${esignInstanceMap.status=='已完成'|| esignInstanceMap.status=='已过期'}">

                                                    补签

                                                </c:if>
        </button>


	</span>

                                    </c:if>


                                </div>


                            </div>
                            <div class="detail-updates">
                                <div id="headerIconSection" class="detail-quick-link">
                    <span class="entity-section entity-creator">
                      <span class="text-ellipsis"
                            ellipsis="true">来自 ${esignInstanceMap.initiator}  于  ${esignInstanceMap.esign_begin_date}   截止于 ${esignInstanceMap.sign_expire_data}</span>

                    </span>


                                    <span class="entity-section entity-project">${esignInstanceMap.status}</span>
                                    <%--                                    <c:if test="${not empty esignInstanceMap.Signer.esign_url && !(esignInstanceMap.Signer.esign_url eq null) && (esignInstanceMap.Signer.status==0 || esignInstanceMap.Signer.status==1) && esignInstanceMap.status=='签署中'}">--%>

                                    <%--                                    <span class="entity-section" data-id="topiconclick" data-zpqa="topiconclick" id="comment_icon">--%>

                                    <%--                                    <a target="_blank" href="${esignInstanceMap.Signer.esign_url}">去签署</a>--%>


                                    <%--                                    </span>--%>
                                    <%--                                    </c:if>--%>

                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                </tbody>
                <tbody class="detail-main-content">
                <tr>
                    <td>
                        <div class="detail-block-area module-content-area">

                            <div id="descriptionContainer" class="module-content-data">
                                <div style="font-family: Lato, LatoRegular, sans-serif, sans-serif;font-size: 15px;">
                                    <div>${esignInstanceMap.subject}
                                        <br><br>
                                        ${esignInstanceMap.remark}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <c:forEach items="${esignFileList}" var="eSignFileList" varStatus="eSignFileListStatus">


                            <div class="detail-block-area module-content-area">


                                <div id="ember142" class="ember-view" tabindex="1">
                                    <div class="zwd-atom-filepreview zwd-ui zwd-fullscreen zwd-secondary zwd-container zwd-filepreview zwd-fixed-position zwd-width100 zwd-height100vh zwd-t-0 zwd-l-0 zwd-daylightbg    "
                                         id="zd-file-preview">
                                        <div class="zwd-content ">
                                            <div id="ember143" class="ember-view">
                                                <div class="zwd-preview-header">
                                                    <div class="zwd-preview-text-header">
                                                        <h5 class="zwd-ui zwd-header">
                                                            <div class="zwd-ui zwd-mini zwd-image zwd-fl-left zwd-flex zwd-justify-center zwd-icon-36 zwd-widthauto">
                                                                <svg class="pdf zwd-inline-block zwd-icon-32">
                                                                    <use xlink:href="#icon-pdf">
                                                                    </use>
                                                                </svg>
                                                            </div>
                                                            <div class="zwd-content zwd-min-width0px">
                                                                <div class="zwd-flex zwd-alignitem-end">
								<span class="zwd-preview-file-name zwd-inline-block zwd-overflow-ellipsis "
                                      zd-tooltip="true" data-ember-action="" data-ember-action-144="144"
                                      zd-title="${eSignFileList.filename}">
                                        ${eSignFileList.filename}
                                </span>
                                                                    <!---->
                                                                    <!---->
                                                                    <!---->
                                                                </div>


                                                                    <%--                                                            <div class="zwd-sub zwd-header zwd-lineheight20 zwd-rsp-button">--%>
                                                                    <%--                                                                <div id="ember158" class="zwd-ui zwd-breadcrumb ember-view">--%>
                                                                    <%--                                                                    <div class="zwd-hide-display">--%>
                                                                    <%--                                                                        true--%>
                                                                    <%--                                                                    </div>--%>
                                                                    <%--                                                                    <div class="zwd-fl-left zwd-breadcrumb-left zwd-flex">--%>

                                                                    <%--                                                                        <svg datasvgname="wd_zdworkspace" viewBox="0 0 20 20" id="ember159" class="zwd-white zwd-icon-14 ember-view">--%>

                                                                    <%--                                                                            <path d="M6.617 4.998L6.627 3H2v14h16V4.998H6.617zm11.383-1a1 1 0 011 1V17a1 1 0 01-1 1H2a1 1 0 01-1-1V3a1 1 0 011-1h4.627a1 1 0 011 .998l-.005 1 1 .005L18 3.998z"--%>
                                                                    <%--                                                                                  fill="" opacity="" stroke="" transform="" fill-rule="">--%>
                                                                    <%--                                                                            </path>--%>
                                                                    <%--                                                                            <path d="M10 11a2 2 0 110-4 2 2 0 010 4zm-3 4a2 2 0 110-4 2 2 0 010 4zm6 0a2 2 0 110-4 2 2 0 010 4z"--%>
                                                                    <%--                                                                                  fill="" opacity="" stroke="" transform="" fill-rule="">--%>
                                                                    <%--                                                                            </path>--%>

                                                                    <%--                                                                        </svg>--%>
                                                                    <%--                                                                        <a class="zwd-breadcrumb-section" zd-tooltip="true" --%>
                                                                    <%--                                                                           zd-tooltip-position="bottom-center">--%>
                                                                    <%--                                                                            SHR0302--%>
                                                                    <%--                                                                        </a>--%>

                                                                    <%--                                                                        <svg datasvgname="wd_zdrhtarrow" viewBox="0 0 20 20" id="ember160" class="zwd-medium-grey zwd-icon-12 ember-view">--%>

                                                                    <%--                                                                            <path d="M14.5303301,2.53033009 C14.8232233,2.23743687 14.8232233,1.76256313 14.5303301,1.46966991 C14.2374369,1.1767767 13.7625631,1.1767767 13.4696699,1.46966991 L5.46966991,9.46966991 C5.1767767,9.76256313 5.1767767,10.2374369 5.46966991,10.5303301 L13.4696699,18.5303301 C13.7625631,18.8232233 14.2374369,18.8232233 14.5303301,18.5303301 C14.8232233,18.2374369 14.8232233,17.7625631 14.5303301,17.4696699 L7.06066017,10 L14.5303301,2.53033009 Z"--%>
                                                                    <%--                                                                                  fill="" opacity="" stroke="none" transform="translate(10.000000, 10.000000) scale(-1, 1) translate(-10.000000, -10.000000)"--%>
                                                                    <%--                                                                                  fill-rule="nonzero">--%>
                                                                    <%--                                                                            </path>--%>

                                                                    <%--                                                                        </svg>--%>
                                                                    <%--                                                                        <a class="zwd-breadcrumb-section" zd-tooltip="true" zd-title="Discussion"--%>
                                                                    <%--                                                                           zd-tooltip-position="bottom-center">--%>
                                                                    <%--                                                                            Discussion--%>
                                                                    <%--                                                                        </a>--%>
                                                                    <%--                                                                    </div>--%>
                                                                    <%--                                                                </div>--%>
                                                                    <%--                                                            </div>--%>


                                                            </div>
                                                        </h5>
                                                    </div>
                                                    <div class="zwd-preview-button-header">
                                                        <!---->
                                                        <a class="zwd-ui zwd-primary zwd-small zwd-buttons zwd-split-buttons">
                                                        </a>
                                                        <!---->
                                                        <!---->
                                                        <!---->
                                                        <button class="zwd-ui zwd-inverted zwd-basic zwd-icon zwd-button zwd-icon-button zwd-relative-position zwd-js-preview-permalink zwd-rsp-button "

                                                                onclick="window.open('ff_showpdf2swf.show.do?tableid=esign_file&fieldid=file&filename=${eSignFileList.file_uuid}&ap=true')"


                                                                id="zd-attach" zd-tooltip="true" zd-title="查看"
                                                                zd-tooltip-position="bottom-center"
                                                                type="button" data-ember-action=""
                                                                data-ember-action-161="161">
                                                            <i class="zwd-icon zwd-icon-16 zwd-no-pointer-event">
                                                                <svg class="zwd-icon-16 zwd-white">
                                                                    <use xlink:href="#svg-link-solid-icon">
                                                                    </use>
                                                                </svg>
                                                            </i>
                                                            <!---->
                                                        </button>

                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </c:forEach>


                        <div class="detail-block-area module-content-area">

                            <div id="projectcontent" class="zps-main-content" zps-content-scroll=""><!---->
                                <div id="chatWrapper" class="chat-wrapper wrapper-project">
                                    <div id="chat-data-container" class="cliq-chat-list-view">

                                        <div id="chat-list-view" class="chat-list-holder">
                                            <table class="chat-list-container zps-GLT w100per" cellpadding="0"
                                                   cellspacing="0" sticky-header="yes">
                                                <thead>
                                                <tr class="chat-item">
                                                    <th class="chat-project-header">姓名</th>
                                                    <th class="chat-project-header">状态</th>
                                                    <th class="chat-project-header">时间</th>
                                                    <th class="chat-project-header">签署原因</th>
                                                </tr>
                                                </thead>
                                                <tbody id="chat-item-container" class="chat-item-container"><!---->
                                                <!----><!----> <!---->
                                                <c:forEach items="${fileSignerList}" var="fileSignerList"
                                                           varStatus="fileSignerListStatus">

                                                    <tr class="chat-item">


                                                        <td>
                                                            <div class="flex aic chat-list-item"
                                                                 data-zpqa="associated_module">
                                                       <span class="minw0 text-ellipsis" data-zpqa="associated_entity"
                                                             ellipsis="true"
                                                       >

                                                            <div>${fileSignerList.name}

                                                           <c:if test="${fileSignerList.status == '等待签署'}">
                                                              &nbsp;&nbsp; <button  zps-loader="" type="button" onclick="Urging(${fileSignerList.id})" name="" id=""   value="提醒">提醒</button>
                                                           </c:if>
</div>  <c:if test="${fileSignerList.status == '等待签署'|| fileSignerList.status == '等待设置签字位置'}">
                                                           &nbsp;&nbsp; <button  zps-loader="" type="button" onclick="Remove(${fileSignerList.id})" name="" id=""   value="移除">移除</button>
                                                       </c:if>
                                                        </span>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="flex aic chat-list-item">
                                                        <span class="minw0 text-ellipsis" data-zpqa="associated_entity"
                                                              ellipsis="true"
                                                        >
                                                                ${fileSignerList.status}

                                                        </span>
                                                            </div>
                                                        </td>
                                                        <td class="chat-item-right">
                                                            <div class="flex aic chat-list-item">
                                                         <span class="minw0 text-ellipsis" data-zpqa="associated_entity"
                                                               ellipsis="true"
                                                         >

                                                                 ${fileSignerList.execute_date}

                                                         </span>

                                                            </div>
                                                        </td>

                                                        <td class="chat-item-right">
                                                            <div class="flex aic chat-list-item">
                                                         <span class="minw0 text-ellipsis" data-zpqa="associated_entity"
                                                               ellipsis="true"
                                                         >

                                                                 ${fileSignerList.sign_reason}

                                                         </span>

                                                            </div>
                                                        </td>

                                                        <!---->
                                                    </tr>
                                                    <!----><!----><!----><!---->
                                                </c:forEach>

                                                </tbody>

                                            </table>
                                        </div><!---->
                                    </div>
                                </div>
                                <div class="hideimp" id="chat-cliq-container"></div>
                                <div class="hide" id="chat-form-container"></div>
                            </div>
                            <!----></div>

                        <%--                        </c:forEach>--%>


                        <div class="detail-block-area module-content-area" id="tabs_content">
                            <div data-id="tabcontent" data-zpqa="tabcontent" id="tabContainer"
                                 class="detail-tabinner-content">
                                <div class="tasklistTitle">
                                    <div class="taskdetsection">
                                        <div class="activity-container activity-print" numbering="yes">


                                            <div class="activity-data">


                                                <c:forEach items="${esignLogGroupDayMap}" var="esignLogMap"
                                                           varStatus="esignLogStatus">

                                                    <activity-date>
                                                        <span>${esignLogMap.key}</span>
                                                    </activity-date>

                                                    <c:forEach items="${esignLogMap.value}" var="esignLogInfoList"
                                                               varStatus="esignLogInfoStatus">
                                                        <div class="activity-threads">
                                                            <activity-thread>
                              <span>
                                <span class="cmnt-id">${esignLogInfoList.sn}</span>
                              </span>
                                                            </activity-thread>
                                                            <activity-content>
                                                                <div class="activity-row-data">
                                                                    <div class="activity-particular">${esignLogInfoList.name}
                                                                        <span class="primary-color">${esignLogInfoList.desc}</span>
                                                                    </div>
                                                                    <time-stamp>
                                                                        - ${esignLogInfoList.timeStamp}</time-stamp>
                                                                </div>
                                                            </activity-content>
                                                        </div>
                                                    </c:forEach>

                                                </c:forEach>


                                            </div>


                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>


<script type="text/javascript">
    /*点击弹出按钮*/
    function popBox() {
        jQuery('#followerPop').removeClass('hide');
    }

</script>


<script type="text/javascript">


    //发送表单ajax请求



    function Urging(id){
        $.ajax({
            url: "LightpdfSignIntergrate.Urging.do",
            type: "POST",
            data: {
                id: id
            },
            async: false,
            success: function (data) {

                if (data.status == 200) {
                    alert("发送成功");

                } else {
                    alert(data);
                }

            }
        });
    }

    function Remove(id){
        $.ajax({
            url: "LightpdfSignIntergrate.RemoveSigner.do",
            type: "POST",
            data: {
                id: id
            },
            async: false,
            success: function (data) {

                if (data.status == 200) {
                    alert("移除成功");
                    window.location.reload();
                } else {

                    alert(data);
                }

            }
        });
    }



    function loadSingerControl() {

        var url = '/LightpdfSignIntergrate.ajaxsetSigner2.do?id=${esignInstanceMap.id}';

        window.top.webmask_show_modalbyurl('添加签字人',url,'800px','400px','');
    }


        $('#revokeButton').on('click', function () {
        const revokeResonInput = document.getElementById("revokeReson");
        const revokeResonValue = revokeResonInput.value.trim();
        if (revokeResonValue === "") {
            revokeResonInput.style.border = "2px solid red";
            alert("废除原因不能为空！");
        } else {

            revokeResonInput.style.border = "";


            $.ajax({
                url: "LightpdfSignIntergrate.Revoke.do",
                type: "POST",
                data: {
                    signFlowId: '${esignInstanceMap.sign_flow_id}',
                    revokeReson: $('#revokeReson').val(),
                    esignInstanceId: '${esignInstanceMap.id}'

                },
                async: false,
                success: function (data) {

                    if (data == 200) {
                        window.location.reload();

                        <%--window.location.href="/eSignIntergrate.eSign.do?recordid=${param.recordid}&tableid=${param.tableid}";--%>

                    } else {

                        alert(data);
                    }

                }
            });
        }
    });


    $('#anewSign').on('click', function () {

        window.location.href = "/LightpdfSignIntergrate.eSign.do?recordid=${param.recordid}&tableid=${param.tableid}&anewSign=true&anewSign=true&currid=${esignInstanceMap.id}";

    });

</script>

</body>


</html>
