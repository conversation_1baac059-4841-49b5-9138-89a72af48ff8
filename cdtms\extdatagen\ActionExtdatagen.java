package net.bioknow.cdtms.extdatagen;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.itextpdf.text.pdf.PdfReader;
import net.bioknow.cdtms.formMail.DAOTransemail;
import net.bioknow.cdtms.lightpdfSign.*;
import net.bioknow.dbplug.wordreport.DAOWordreport;
import net.bioknow.dbplug.wordreport.UtilAsposeword;
import net.bioknow.mvc.RootAction;
import net.bioknow.mvc.tools.WebPath;
import net.bioknow.passport.datamng.DAOPPData;
import net.bioknow.passport.datamng.PassportCacheUtil;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.schemaplug.attach.AttachDAO;
import net.bioknow.uap.schemaplug.dtref.DtrefDAO;
import net.bioknow.uapplug.usersyn.CNT_Usersyn;
import net.bioknow.uapplug.usersyn.DAOUsersyn;
import net.bioknow.webutil.fileup.DAOFileup;
import net.bioknow.webutil.session.SessInfo;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import net.bioknow.webutil.tools.UUIDUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static net.bioknow.cdtms.lightpdfSign.PdfHelper.getKeyWords;
import static net.bioknow.cdtms.lightpdfSign.ResponseUtils.response;
import static net.bioknow.cdtms.lightpdfSign.WaterMarkUtils.PDFAddWatermark;


public class ActionExtdatagen extends RootAction {


    public void ajaxmenu(HttpServletRequest request, HttpServletResponse response){
        this.forward(request,response,"ajaxmenu");
    }


    public void selDTA(HttpServletRequest request, HttpServletResponse response){


        try {

            String studyId = request.getParameter("id");
            String projectid = SessUtil.getSessInfo().getProjectid();

            DAODataMng daoDataMng = new DAODataMng(projectid);
            AttachDAO attachDAO = new AttachDAO(projectid);

            List<Map> DTAList = daoDataMng.listRecord("ext_data", "obj.studyid=" + studyId, null, 1000);

            if (CollectionUtils.isEmpty(DTAList)) {
                response.getOutputStream().write("DTA Not Found".getBytes());
                return;
            }
            for (Map DTAMap : DTAList) {
                String DTAFileArrStr = (String) DTAMap.get("dta_doc");
                if (StringUtils.isEmpty(DTAFileArrStr)) {

                    continue;
                }



            }




        } catch (Exception e) {
            throw new RuntimeException(e);
        }


    }

}
