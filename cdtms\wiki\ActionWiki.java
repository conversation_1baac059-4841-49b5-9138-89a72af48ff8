//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package net.bioknow.cdtms.wiki;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mchange.v1.db.sql.ConnectionUtils;
import net.bioknow.mvc.RootAction;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Type;
import java.util.*;

import static org.apache.commons.lang3.StringUtils.contains;

public class ActionWiki extends RootAction {


    public void rating(HttpServletRequest request, HttpServletResponse response) {
        this.forward(request, response, "rating");
    }

    public static void main(String[] args) {
        String json = "[{\"label\":\"数据管理\",\"electiveValue\":\"1\",\"authValue\":\"00\"},{\"label\":\"英才计划\",\"electiveValue\":\"1\",\"authValue\":\"00\"},{\"label\":\"随机化\",\"electiveValue\":\"1\",\"authValue\":\"00\"},{\"label\":\"Techops_SAS编程\",\"electiveValue\":\"1\",\"authValue\":\"00\"},{\"label\":\"Techops_技术支持\",\"electiveValue\":\"1\",\"authValue\":\"00\"},{\"label\":\"Techops_实验室数据\",\"electiveValue\":\"1\",\"authValue\":\"00\"},{\"label\":\"Techops_外部数据\",\"electiveValue\":\"1\",\"authValue\":\"00\"},{\"label\":\"Techops_医学编码\",\"electiveValue\":\"1\",\"authValue\":\"00\"},{\"label\":\"Publication\",\"electiveValue\":\"1\",\"authValue\":\"00\"},{\"label\":\"技术创新部\",\"electiveValue\":\"1\",\"authValue\":\"00\"},{\"label\":\"经理级\",\"electiveValue\":\"1\",\"authValue\":\"00\"}]";

        Gson gson = new Gson();
        Type type = new TypeToken<List<Map<String, String>>>(){}.getType();
        List<Map<String, String>> list = gson.fromJson(json, type);

        // 输出转换后的数据以验证结果
        for (Map<String, String> map : list) {
            for (Map.Entry<String, String> entry : map.entrySet()) {
                System.out.println(entry.getKey() + ": " + entry.getValue());
            }
            System.out.println("---------------------");
        }
    }

    public void autoAuth(HttpServletRequest request, HttpServletResponse response) {
        try {

            String projectid = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectid);

            List<Map> wikiList = daoDataMng.listRecord("xxyjl1", "1=1", null, 1000);

            ArrayList<Object> authListToSave = new ArrayList<>();
            for (Map wikiMap : wikiList) {

                Long id = (Long) wikiMap.get("id");
                String appliesPerson = (String) wikiMap.get("applies_person");
                if (StringUtils.isEmpty(appliesPerson)) {
                    continue;
                }

                String[] appliesArr = appliesPerson.split(",");
                String deptStr="数据管理\t英才计划\t随机化\tTechOps_SAS编程\tTechOps_技术支持\tTechOps_实验室数据\tTechOps_外部数据\tTechOps_医学编码\tPublication\t技术创新部\t";
                String[] deptStrArr = deptStr.split("\t");

                for (String applie : appliesArr) {
//                    ArrayUtils.contains(deptStrArr,applie)


                    Map<Object, Object> authMapToSave = new HashMap<>();
                    authMapToSave.put("wiki_id",id);

                    if (StringUtils.contains(deptStr,applie)) {
                        authMapToSave.put("dept_name",applie);
                        authMapToSave.put("auth_code","00");
                    }else if(StringUtils.contains(applie,"数据管理员")){
                        authMapToSave.put("dept_name","数据管理");
                        authMapToSave.put("auth_code","00");


                    }else if(StringUtils.contains(applie,"高级数据管理员")){
                        authMapToSave.put("dept_name","数据管理");
                        authMapToSave.put("auth_code","00");


                    }else if(StringUtils.contains(applie,"数据管理高级主管")){
                        authMapToSave.put("dept_name","数据管理");
                        authMapToSave.put("auth_code","50");


                    }else if(StringUtils.contains(applie,"数据管理经理")){
                        authMapToSave.put("dept_name","数据管理");
                        authMapToSave.put("auth_code","60");
                    }

                    authListToSave.add(authMapToSave);


                }



            }
            daoDataMng.saveBatch("wiki_auth",authListToSave,2l,null);


        } catch (Exception e) {
            Log.error("", e);
        }



    }


    public void toSetAuth(HttpServletRequest request, HttpServletResponse response) {
        try {

            String recordid = request.getParameter("recordid");
            request.setAttribute("id", recordid);
            String projectid = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectid);

            List<Map> wikiAuthList = daoDataMng.listRecord("wiki_auth", "obj.wiki_id=" + recordid, null, 100);
            String wikiAuthListStrToPage = null;
            String wikiElectiveListStrToPage = null;
            if (CollectionUtils.isNotEmpty(wikiAuthList)) {
                Map wikiAuthMapToPage = new HashMap<>();
                Map wikiElectiveMapToPage = new HashMap<>();
                for (Map wikiAuthMap : wikiAuthList) {
                    wikiAuthMapToPage.put(wikiAuthMap.get("dept_name"), wikiAuthMap.get("auth_code"));
                    wikiElectiveMapToPage.put(wikiAuthMap.get("dept_name"), wikiAuthMap.get("is_elective"));
                }
                Gson gson = new Gson();
                wikiAuthListStrToPage = gson.toJson(wikiAuthMapToPage);
                wikiElectiveListStrToPage = gson.toJson(wikiElectiveMapToPage);

            }else {
                wikiAuthListStrToPage = "[]";
                wikiElectiveListStrToPage = "[]";
            }



            request.setAttribute("wikiAuthArr", wikiAuthListStrToPage);
            request.setAttribute("wikiElectiveArr", wikiElectiveListStrToPage);


            this.forward(request, response, "toSetAuth");

        } catch (Exception e) {
            Log.error("", e);
        }

    }


    public void setAuth(HttpServletRequest request, HttpServletResponse response) {
        try {

        Long recordId = Long.valueOf(request.getParameter("recordid"));

        String authDataStr = request.getParameter("authData");
        Gson gson = new Gson();


            List<Map<String, String>> authDataList = gson.fromJson(authDataStr, new TypeToken<List<Map<String, String>>>(){}.getType());


        String projectid = SessUtil.getSessInfo().getProjectid();
            Long currUserid = Long.valueOf(SessUtil.getSessInfo().getUserid());
            DAODataMng daoDataMng = new DAODataMng(projectid);

            daoDataMng.delRecord("wiki_auth","obj.wiki_id="+recordId);

            ArrayList<Object> authListToSave = new ArrayList<>();

            for (Map<String, String> authDataMap : authDataList) {
                if (!StringUtils.isNotEmpty(authDataMap.get("authValue"))) {
                    continue;
                }
                    Map<String, Object> authMapToSave = new HashMap<>();
                    authMapToSave.put("dept_name", authDataMap.get("label"));
                    authMapToSave.put("auth_code", authDataMap.get("authValue"));
                    authMapToSave.put("is_elective", StringUtils.isNotEmpty(authDataMap.get("electiveValue"))?"1":"0");
                    authMapToSave.put("wiki_id", recordId);
                authListToSave.add(authMapToSave);

            }


        daoDataMng.saveBatch("wiki_auth",authListToSave,currUserid,null);


        } catch (Exception e) {
            Log.error("", e);
        }

//        authDataMap.forEach();
//        authDataList

    }
    public void setAuthButton(HttpServletRequest request, HttpServletResponse response) {
        this.forward(request, response, "setAuthButton");
    }

    public void ratingButton(HttpServletRequest request, HttpServletResponse response) {
        this.forward(request, response, "ratingButton");
    }

    public void ratingSave(HttpServletRequest request, HttpServletResponse response) {


        try {
            String star = request.getParameter("star");
            String comment = request.getParameter("comment");
            String recordid = request.getParameter("recordid");
            Map userMap = SessUtil.getSessInfo().getUser();
            String projectid = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectid);
            List learnerList = daoDataMng.listRecord("knowledge_base_learner", "obj.account_id=" + userMap.get("id") + " and obj.knowledge_base_id=" + recordid, null, 1);

            Map learnerMapToSave = (Map) learnerList.get(0);
            learnerMapToSave.put("star", star);
            learnerMapToSave.put("comment", comment);
            learnerMapToSave.put("finish_date", new Date());
            learnerMapToSave.put("status", "10");
            daoDataMng.saveRecord("knowledge_base_learner",learnerMapToSave);

        } catch (Exception e) {
            Log.error("", e);
        }
    }

}
