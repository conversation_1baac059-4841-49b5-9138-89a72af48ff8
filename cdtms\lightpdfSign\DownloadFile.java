package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.webutil.tools.Log;

import java.io.*;
import java.net.*;
import java.nio.channels.Channels;
import java.nio.channels.FileChannel;
import java.nio.channels.ReadableByteChannel;

public class DownloadFile{
    public static void main(String[] args) {
        String fileUrl = "http://tarly.aoscdn.com/resource/output/20230418/35/SOP-CDSC-001F2eCRF审批表V2.0.pdf";

        String saveDir = "D:/bioknowshell64/apache-tomcat-8.5.5/webapps/ROOT/webutil/fileup/temp/eSign";
        String fileName = "SOP-CDSC-001F2eCRF审批表V2.0.pdf";
        downloadByNIO(fileUrl,saveDir,fileName);
//        try {
//            URL url = new URL(fileUrl);
//            HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
//            int responseCode = httpConn.getResponseCode();
//            if (responseCode == HttpURLConnection.HTTP_OK) {
//                InputStream inputStream = httpConn.getInputStream();
//                FileOutputStream outputStream = new FileOutputStream(saveDir + fileName);
//                int bytesRead = -1;
//                byte[] buffer = new byte[4096];
//                while ((bytesRead = inputStream.read(buffer)) != -1) {
//                    outputStream.write(buffer, 0, bytesRead);
//                }
//                outputStream.close();
//                inputStream.close();
//                System.out.println("文件下载成功！");
//            } else {
//                System.out.println("无法下载文件，服务器返回代码： " + responseCode);
//            }
//            httpConn.disconnect();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
    }

    public static void downloadByNIO(String url, String saveDir, String fileName) {



        ReadableByteChannel rbc = null;
        FileOutputStream fos = null;
        FileChannel foutc = null;
        try {
            rbc = Channels.newChannel(new URL(url).openStream());
            File file = new File(saveDir, fileName);
            file.getParentFile().mkdirs();
            fos = new FileOutputStream(file);
            foutc = fos.getChannel();
            foutc.transferFrom(rbc, 0, Long.MAX_VALUE);

        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (rbc != null) {
                try {
                    rbc.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (foutc != null) {
                try {
                    foutc.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }
}
