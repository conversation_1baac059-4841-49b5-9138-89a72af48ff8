package net.bioknow.cdtms.wrokFileTmpl;

import net.bioknow.cdtms.lightpdfSign.*;
import net.bioknow.mvc.ioc.AppStartFace;
import net.bioknow.passport.uiframe.FuncUtil;
import net.bioknow.passport.validate.PageIgnore;
import net.bioknow.services.uap.dbdatamng.function.FuncFactoryNew;
import net.bioknow.uap.dbdatamng.function.FuncFactory;

import javax.servlet.ServletConfig;

public class EHAppStart implements AppStartFace{

    public void onStart(ServletConfig arg) throws Exception {

        FuncFactoryNew.addRecordFunc(new DTRFwrokFileTmplVue());
    }

}
