import os
from rwslib import RWSConnection
from rwslib.rws_requests import StudyVersionsRequest, StudyVersionRequest

# =============================================================================
# 创建连接 
# =============================================================================
def testConn(username, password):
    conn = RWSConnection('hengruimedicine-ravex', username, password)
    try:
        # 发送一个简单请求测试连接
        studies = conn.send_request(StudyVersionsRequest('SHR-1701-001AUS')) 
        print('Connection succeeded!')
        print(studies)
    except Exception as e:
        print('Connection failed!')
        print(e)

# =============================================================================
# 主函数 
# =============================================================================
def main():
    username = input('Username: ')
    password = input('Password: ')
    testConn(username, password)

if __name__ == '__main__':    
    main()