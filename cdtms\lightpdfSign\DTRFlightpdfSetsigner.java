package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTRecordFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


public class DTRFlightpdfSetsigner extends DTRecordFuncAction {


	public boolean canUse(int auth, String tableid, Long recordid) {

		try {

			if (!StringUtils.equals(tableid,"esign_instance")) {
				return false;
			}
			String projectId = SessUtil.getSessInfo().getProjectid();

			DAODataMng daoDataMng=new DAODataMng(projectId);
			Map esignInstanceMap = daoDataMng.getRecord(tableid, recordid);
			if (!StringUtils.equals(SessUtil.getSessInfo().getUserid(),String.valueOf(esignInstanceMap.get("userid")))) {

				return false;
			}
			String esignInstanceStatus = (String) esignInstanceMap.get("status");
			if (StringUtils.equals(esignInstanceStatus,"1")) {
				return true;
			}

		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}


	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBean fpb) {
		try {





//			this.forwardByUri(request,response,redirectUrl);
//			this.forwardByUri(request,response,"setSigner");
			this.forwardByUri(request,response,"/LightpdfSignIntergrate.ajaxsetSigner.do?id="+fpb.getRecordid());

			//			this.forward();
		} catch (Exception e) {
			Log.error("",e);
		}
	}

	public FuncInfoBean getFIB(String tableid) {
		FuncInfoBean fib = new FuncInfoBean();


		fib.setName("添加签字人");
//		fib.setType(FuncInfoBean.FUNCTYPE_INNERWINDOW);
		fib.setType(FuncInfoBean.FUNCTYPE_AJAXMENU);
		fib.setWinHeight(500);
		fib.setWinWidth(500);
//		fib.setSimpleViewShow(true);




		return fib;
	}

}
