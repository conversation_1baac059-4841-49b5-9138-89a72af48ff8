from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 创建连接引擎
engine = create_engine('mysql+pymysql://root:Hr_mysql1024@localhost:3306/airflow?charset=utf8')

# 创建Session类
Session = sessionmaker(bind=engine)

# 创建session实例 
session = Session()

try:
    # 使用session执行SQL查询
    result = session.execute("SELECT 1")
    
    # 打印查询结果 
    print(result.fetchone())
    
    print("成功连接到MySQL数据库!")
except Exception as e:   
    print("连接 MySQL 失败!")
    print(e) 
finally:    
    session.close()

# 关闭连接引擎
engine.dispose()  