#register.title = 设置签字密码
LoginSign=登录签字
register.title = 重设签字密码
register.titleFirst = 设定签字密码
register.titleFirst2 =非邮件中验证码
register.Informed = 声明：使用本电子签名即表示签字用户同意本电子签名在法律上等同于本人的手写签名
Email=邮箱
register.Checkcode=验证码
register.changeErrorInfo=密码不一致
Password=签字密码
FileName=文件
Name=姓名
confirmPassword=确认密码
register.notice = 首次使用UAP电子签名模块需设置个人识别密码，该密码仅在该模块中生效，后续签名验证需要填写该密码， 请妥善保管，勿传递给他人。
submit = 确认
cancel=不同意
sign.submit = 登录
#register.sendcheckcode = 接收密码设定验证码
register.sendcheckcode = 重置密码
#checkcodemail.title=签字密码设定验证
#checkcodemail.content1=您的邮件验证码为：
#checkcodemail.content2=，请10分钟内及时输入！
checkcodemail.title=签字密码设定验证
checkcodemail.content1=您的签字密码验证码为：
checkcodemail.content2=，请您在10分钟内将6位数验证码填写在重设签字密码页面。
sign.title=签字验证
sign.forgot_password=忘记密码
sign.guide=签署操作指南
initiator=创建人
InitiatDate=创建日期
Signature=发送签字
Signatures=签字情况
abolished=废除
