#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Filter PERFECT_CHINESE_DATASET.json using condition.txt exclusion list
and export to CSV format (which can be opened in Excel)
Using only built-in Python libraries
"""

import json
import codecs
import csv

def load_exclusion_list(condition_file):
    """
    Load the list of IDs to exclude from condition.txt
    """
    try:
        with codecs.open(condition_file, 'r', encoding='utf-8') as f:
            exclusion_ids = [line.strip() for line in f if line.strip()]
        
        # Remove duplicates while preserving order
        unique_exclusion_ids = []
        seen = set()
        for id_val in exclusion_ids:
            if id_val not in seen:
                unique_exclusion_ids.append(id_val)
                seen.add(id_val)
        
        print("📋 Loaded exclusion list from " + condition_file)
        print("   - Total exclusion entries: " + str(len(exclusion_ids)))
        print("   - Unique exclusion IDs: " + str(len(unique_exclusion_ids)))
        
        return unique_exclusion_ids
        
    except Exception as e:
        print("❌ Error loading exclusion list: " + str(e))
        return []

def load_json_data(json_file):
    """
    Load the JSON dataset
    """
    try:
        with codecs.open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("📊 Loaded JSON data from " + json_file)
        print("   - Total records: " + str(len(data)))
        
        return data
        
    except Exception as e:
        print("❌ Error loading JSON data: " + str(e))
        return []

def filter_excluded_data(data, exclusion_ids):
    """
    Filter data to get records whose IDs are NOT in the exclusion list
    """
    excluded_data = []
    included_data = []
    
    exclusion_set = set(exclusion_ids)
    
    for record in data:
        record_id = record.get('id', '')
        if record_id in exclusion_set:
            included_data.append(record)  # These are in the exclusion list (excluded from output)
        else:
            excluded_data.append(record)  # These are NOT in exclusion list (included in output)
    
    print("🔍 Filtering results:")
    print("   - Records in exclusion list (excluded from output): " + str(len(included_data)))
    print("   - Records NOT in exclusion list (included in output): " + str(len(excluded_data)))
    
    return excluded_data

def get_all_attributes(data):
    """
    Get all unique attributes from the dataset
    """
    all_attributes = set()
    for record in data:
        all_attributes.update(record.keys())
    
    # Sort attributes with important ones first
    important_attrs = ['id', 'name', 'nameen', 'note', 'uuid']
    other_attrs = sorted([attr for attr in all_attributes if attr not in important_attrs])
    
    ordered_attributes = []
    for attr in important_attrs:
        if attr in all_attributes:
            ordered_attributes.append(attr)
    ordered_attributes.extend(other_attrs)
    
    print("📋 Found " + str(len(all_attributes)) + " unique attributes")
    
    return ordered_attributes

def create_csv_file(data, output_file):
    """
    Create CSV file with filtered data (can be opened in Excel)
    """
    try:
        if not data:
            print("⚠️  No data to export")
            return False

        # Get all attributes
        all_attributes = get_all_attributes(data)

        # Create CSV file (Python 2.7 compatible)
        with codecs.open(output_file, 'w', encoding='utf-8-sig') as csvfile:
            # Write header
            header_line = ','.join(['"{}"'.format(attr.replace('"', '""')) for attr in all_attributes])
            csvfile.write(header_line + '\n')

            # Write data rows
            for record in data:
                row_values = []
                for attr in all_attributes:
                    # Use the value if it exists, otherwise use "NA"
                    value = record.get(attr, "NA")
                    # Convert to string and handle None values
                    if value is None:
                        value = "NA"
                    # Escape quotes and wrap in quotes
                    escaped_value = str(value).replace('"', '""')
                    row_values.append('"{}"'.format(escaped_value))

                row_line = ','.join(row_values)
                csvfile.write(row_line + '\n')

        print("✅ CSV file created successfully!")
        print("   - Output file: " + output_file)
        print("   - Records exported: " + str(len(data)))
        print("   - Columns: " + str(len(all_attributes)))

        return True

    except Exception as e:
        print("❌ Error creating CSV file: " + str(e))
        return False

def create_excel_compatible_file(data, output_file):
    """
    Create an Excel-compatible file using xlwt (if available) or fall back to CSV
    """
    try:
        import xlwt
        
        if not data:
            print("⚠️  No data to export")
            return False
        
        # Get all attributes
        all_attributes = get_all_attributes(data)
        
        # Create workbook
        workbook = xlwt.Workbook(encoding='utf-8')
        worksheet = workbook.add_sheet('Filtered Data')
        
        # Create styles
        header_style = xlwt.XFStyle()
        header_font = xlwt.Font()
        header_font.bold = True
        header_style.font = header_font
        
        # Write headers
        for col_num, attr in enumerate(all_attributes):
            worksheet.write(0, col_num, attr, header_style)
        
        # Write data
        for row_num, record in enumerate(data, 1):
            for col_num, attr in enumerate(all_attributes):
                value = record.get(attr, "NA")
                if value is None:
                    value = "NA"
                worksheet.write(row_num, col_num, str(value))
        
        # Save workbook
        workbook.save(output_file)
        
        print("✅ Excel file created successfully!")
        print("   - Output file: " + output_file)
        print("   - Records exported: " + str(len(data)))
        print("   - Columns: " + str(len(all_attributes)))
        
        return True
        
    except ImportError:
        print("📝 xlwt not available, falling back to CSV format...")
        csv_file = output_file.replace('.xls', '.csv')
        return create_csv_file(data, csv_file)
    except Exception as e:
        print("❌ Error creating Excel file: " + str(e))
        csv_file = output_file.replace('.xls', '.csv')
        return create_csv_file(data, csv_file)

def create_summary_report(original_count, filtered_count, exclusion_count, output_file):
    """
    Create a summary report of the filtering process
    """
    report_file = output_file.replace('.csv', '_summary_report.txt').replace('.xls', '_summary_report.txt')
    
    try:
        with codecs.open(report_file, 'w', encoding='utf-8') as f:
            f.write("📊 DATA FILTERING SUMMARY REPORT\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("🎯 FILTERING RESULTS:\n")
            f.write("-" * 30 + "\n")
            f.write("Original dataset records: " + str(original_count) + "\n")
            f.write("Exclusion list entries: " + str(exclusion_count) + "\n")
            f.write("Records exported: " + str(filtered_count) + "\n")
            f.write("Records excluded: " + str(original_count - filtered_count) + "\n\n")
            
            f.write("📁 OUTPUT FILES:\n")
            f.write("-" * 30 + "\n")
            f.write("Data file: " + output_file + "\n")
            f.write("Summary report: " + report_file + "\n\n")
            
            f.write("📋 PROCESS:\n")
            f.write("-" * 30 + "\n")
            f.write("1. Loaded exclusion list from condition.txt\n")
            f.write("2. Loaded JSON dataset from PERFECT_CHINESE_DATASET.json\n")
            f.write("3. Filtered out records whose IDs are in exclusion list\n")
            f.write("4. Exported remaining records with all attributes\n")
            f.write("5. Used 'NA' for missing attribute values\n\n")
            
            success_rate = (filtered_count / original_count * 100) if original_count > 0 else 0
            f.write("✅ Export success rate: " + "{:.1f}".format(success_rate) + "%\n")
        
        print("📋 Summary report saved as: " + report_file)
        return True
        
    except Exception as e:
        print("❌ Error creating summary report: " + str(e))
        return False

def main():
    """
    Main function to execute the filtering and export process
    """
    print("🚀 FILTERING AND EXPORT PROCESS")
    print("=" * 60)
    
    # File paths
    condition_file = "condition.txt"
    json_file = "PERFECT_CHINESE_DATASET.json"
    
    # Try Excel format first, fall back to CSV
    output_file = "filtered_excluded_data.xls"
    
    # Step 1: Load exclusion list
    exclusion_ids = load_exclusion_list(condition_file)
    if not exclusion_ids:
        print("❌ Failed to load exclusion list")
        return
    
    # Step 2: Load JSON data
    data = load_json_data(json_file)
    if not data:
        print("❌ Failed to load JSON data")
        return
    
    # Step 3: Filter data (get records NOT in exclusion list)
    filtered_data = filter_excluded_data(data, exclusion_ids)
    
    # Step 4: Create output file
    success = create_excel_compatible_file(filtered_data, output_file)
    
    # Step 5: Create summary report
    if success:
        create_summary_report(len(data), len(filtered_data), len(exclusion_ids), output_file)
        
        print("\n🎊 PROCESS COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("📊 Results:")
        print("   - Original records: " + str(len(data)))
        print("   - Exclusion list entries: " + str(len(exclusion_ids)))
        print("   - Records exported: " + str(len(filtered_data)))
        print("   - Output file: " + output_file)
        
        if len(filtered_data) > 0:
            print("\n✨ Your file is ready with " + str(len(filtered_data)) + " records!")
            print("Each record has all possible attributes as columns.")
            print("Missing values are filled with 'NA'.")
            print("📝 The file can be opened directly in Excel.")
        else:
            print("\n⚠️  No records to export (all records were in exclusion list)")
    else:
        print("\n❌ Process failed!")

if __name__ == "__main__":
    main()
