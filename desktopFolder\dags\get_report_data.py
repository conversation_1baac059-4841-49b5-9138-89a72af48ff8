import airflow
from airflow.models import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.bash import <PERSON>sh<PERSON>per<PERSON>  
from airflow.utils.dates import days_ago
import pymysql
import json
import subprocess
from airflow.utils.log.logging_mixin import LoggingMixin

args = {
    'owner': 'zhou<PERSON>',
    'start_date': days_ago(1)
}

dag = DAG(dag_id='mysql_to_scp', default_args=args, schedule_interval=None)
json_data = []
# 任务1:使用PyMySQL查询dag_id和fileloc,并转换为JSON
def mysql_query():
	host = '***********'
	port = 3306
	user = 'weix5'
	password = 'weix@edc'
	database = 'report_server'
	conn = pymysql.connect(host=host,port=port,user=user,passwd=password,db=database)
	# 获取游标
	cursor = conn.cursor()
	# 执行SQL查询并获取结果
	sql = "SELECT REPORTFILENAME, REPORTFILEPATH FROM tbl_report_pro WHERE REPORTFILENAME like '%SAS%' or REPORTFILENAME like '%CRF%' "
	cursor.execute(sql)
	results = cursor.fetchall()
	data = []
	for row in results:
		data.append({
			"dag_id": row[0],
			"fileloc": row[1]
		})
#	data = json.dumps(data)
	global json_data
	json_data = data  # 将数据存储在全局变量中
	cursor.close() 
	conn.close()

task1 = PythonOperator(
    task_id='mysql_query',  
    python_callable=mysql_query,
    dag=dag,  
)

def download_files(ti):
	json_data
	with open('/home/<USER>', 'w') as f:
		file = None
		for file in json_data:
			self.log.info(file)
		bash_command = f'sshpass -p Hr@airflow0509 scp -P 22  root@***********:group1/M00/ /home/<USER>/M00/' 
		output = subprocess.check_output(bash_command,shell=True)

task2 = PythonOperator(
    task_id='download_files', 
    python_callable=download_files,
    dag=dag,
) 

task1>>task2