-- =====================================================
-- SPECIFIC TABLES COMPARISON - BASED ON 比对范围.txt
-- Source Schema: CDTMS_PILOT
-- Target Schema: CDTMS_TEMP
-- Optimized for Same Database Instance
-- =====================================================

-- Set session parameters
SET PAGESIZE 1000
SET LINESIZE 200
SET TRIMSPOOL ON
SET FEEDBACK ON

-- Define schemas
DEFINE SOURCE_SCHEMA = 'CDTMS_PILOT'
DEFINE TARGET_SCHEMA = 'CDTMS_TEMP'

PROMPT =====================================================
PROMPT SPECIFIC TABLES COMPARISON REPORT
PROMPT Source Schema: &SOURCE_SCHEMA
PROMPT Target Schema: &TARGET_SCHEMA
PROMPT Based on: 比对范围.txt table list
PROMPT =====================================================

-- =====================================================
-- STEP 1: TABLE EXISTENCE CHECK FOR SPECIFIC TABLES
-- =====================================================

PROMPT 
PROMPT =====================================================
PROMPT CHECKING EXISTENCE OF SPECIFIC TABLES
PROMPT =====================================================

WITH specific_tables AS (
    -- First 20 tables from 比对范围.txt for quick test
    SELECT 'tbl_attachment' as table_name FROM dual UNION ALL
    SELECT 'tbl_mdchapter_menuid' FROM dual UNION ALL
    SELECT 'tbl_mdchapter' FROM dual UNION ALL
    SELECT 'tbl_eclinichistory' FROM dual UNION ALL
    SELECT 'tbl_esign_account' FROM dual UNION ALL
    SELECT 'tbl_study_visit_set' FROM dual UNION ALL
    SELECT 'tbl_study_visit_table' FROM dual UNION ALL
    SELECT 'tbl_study_crf_visit' FROM dual UNION ALL
    SELECT 'tbl_study_crf_visit_table' FROM dual UNION ALL
    SELECT 'tbl_study_crf_table_pctpt' FROM dual UNION ALL
    SELECT 'tbl_study_coding_ver_update1' FROM dual UNION ALL
    SELECT 'tbl_xmgt' FROM dual UNION ALL
    SELECT 'tbl_study_partner' FROM dual UNION ALL
    SELECT 'tbl_proj_plan' FROM dual UNION ALL
    SELECT 'tbl_yqsq' FROM dual UNION ALL
    SELECT 'tbl_risk_management' FROM dual UNION ALL
    SELECT 'tbl_study_qc' FROM dual UNION ALL
    SELECT 'tbl_schedule' FROM dual UNION ALL
    SELECT 'tbl_crf_design' FROM dual UNION ALL
    SELECT 'tbl_manual_rev_plan' FROM dual
),
table_existence AS (
    SELECT 
        st.table_name,
        (SELECT COUNT(*) FROM all_tables WHERE owner = '&SOURCE_SCHEMA' AND table_name = st.table_name) as source_exists,
        (SELECT COUNT(*) FROM all_tables WHERE owner = '&TARGET_SCHEMA' AND table_name = st.table_name) as target_exists
    FROM specific_tables st
)
SELECT 
    table_name as "Table Name",
    CASE WHEN source_exists = 1 THEN 'EXISTS' ELSE 'MISSING' END as "Source (&SOURCE_SCHEMA)",
    CASE WHEN target_exists = 1 THEN 'EXISTS' ELSE 'MISSING' END as "Target (&TARGET_SCHEMA)",
    CASE 
        WHEN source_exists = 1 AND target_exists = 1 THEN '✓ BOTH_EXIST'
        WHEN source_exists = 1 AND target_exists = 0 THEN '✗ SOURCE_ONLY'
        WHEN source_exists = 0 AND target_exists = 1 THEN '✗ TARGET_ONLY'
        ELSE '✗ BOTH_MISSING'
    END as "Status"
FROM table_existence
ORDER BY table_name;

-- =====================================================
-- STEP 2: ROW COUNT COMPARISON FOR EXISTING TABLES
-- =====================================================

PROMPT 
PROMPT =====================================================
PROMPT ROW COUNT COMPARISON FOR EXISTING TABLES
PROMPT =====================================================

-- Dynamic row count comparison for tables that exist in both schemas
DECLARE
    v_source_count NUMBER;
    v_target_count NUMBER;
    v_sql VARCHAR2(1000);
    v_table_found BOOLEAN;
    
    TYPE table_array IS TABLE OF VARCHAR2(128);
    v_tables table_array := table_array(
        'tbl_attachment', 'tbl_mdchapter_menuid', 'tbl_mdchapter', 'tbl_eclinichistory',
        'tbl_esign_account', 'tbl_study_visit_set', 'tbl_study_visit_table', 'tbl_study_crf_visit',
        'tbl_study_crf_visit_table', 'tbl_study_crf_table_pctpt', 'tbl_study_coding_ver_update1',
        'tbl_xmgt', 'tbl_study_partner', 'tbl_proj_plan', 'tbl_yqsq', 'tbl_risk_management',
        'tbl_study_qc', 'tbl_schedule', 'tbl_crf_design', 'tbl_manual_rev_plan'
    );
BEGIN
    DBMS_OUTPUT.PUT_LINE('Table Name' || CHR(9) || CHR(9) || CHR(9) || 'Source Count' || CHR(9) || 'Target Count' || CHR(9) || 'Difference' || CHR(9) || 'Status');
    DBMS_OUTPUT.PUT_LINE('--------------------------------------------------------------------------------------------------------');
    
    FOR i IN 1..v_tables.COUNT LOOP
        BEGIN
            -- Check if table exists in both schemas
            SELECT COUNT(*) INTO v_source_count 
            FROM all_tables 
            WHERE owner = '&SOURCE_SCHEMA' AND table_name = v_tables(i);
            
            SELECT COUNT(*) INTO v_target_count 
            FROM all_tables 
            WHERE owner = '&TARGET_SCHEMA' AND table_name = v_tables(i);
            
            IF v_source_count > 0 AND v_target_count > 0 THEN
                -- Get actual row counts
                v_sql := 'SELECT COUNT(*) FROM &SOURCE_SCHEMA.' || v_tables(i);
                EXECUTE IMMEDIATE v_sql INTO v_source_count;
                
                v_sql := 'SELECT COUNT(*) FROM &TARGET_SCHEMA.' || v_tables(i);
                EXECUTE IMMEDIATE v_sql INTO v_target_count;
                
                DBMS_OUTPUT.PUT_LINE(
                    RPAD(v_tables(i), 30) || CHR(9) ||
                    LPAD(v_source_count, 10) || CHR(9) ||
                    LPAD(v_target_count, 10) || CHR(9) ||
                    LPAD(ABS(v_source_count - v_target_count), 10) || CHR(9) ||
                    CASE WHEN v_source_count = v_target_count THEN '✓ MATCH' ELSE '✗ DIFF' END
                );
            ELSE
                DBMS_OUTPUT.PUT_LINE(RPAD(v_tables(i), 30) || CHR(9) || 'TABLE NOT FOUND IN ONE OR BOTH SCHEMAS');
            END IF;
            
        EXCEPTION
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE(RPAD(v_tables(i), 30) || CHR(9) || 'ERROR: ' || SQLERRM);
        END;
    END LOOP;
END;
/

-- =====================================================
-- STEP 3: QUICK SAMPLE DATA COMPARISON
-- =====================================================

PROMPT 
PROMPT =====================================================
PROMPT SAMPLE DATA COMPARISON (First 5 records)
PROMPT =====================================================

-- Example for tbl_attachment (modify table name as needed)
PROMPT Comparing sample data from tbl_attachment:

-- Check if table exists first
DECLARE
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count 
    FROM all_tables 
    WHERE owner = '&SOURCE_SCHEMA' AND table_name = 'TBL_ATTACHMENT';
    
    IF v_count > 0 THEN
        DBMS_OUTPUT.PUT_LINE('Source schema sample (first 5 records):');
    ELSE
        DBMS_OUTPUT.PUT_LINE('Table TBL_ATTACHMENT not found in source schema');
    END IF;
END;
/

-- Show sample from source (uncomment and modify if table exists)
/*
SELECT * FROM (
    SELECT * FROM &SOURCE_SCHEMA..tbl_attachment 
    ORDER BY ROWNUM
) WHERE ROWNUM <= 5;
*/

-- =====================================================
-- STEP 4: COLUMN COUNT COMPARISON
-- =====================================================

PROMPT 
PROMPT =====================================================
PROMPT COLUMN COUNT COMPARISON
PROMPT =====================================================

WITH specific_tables AS (
    SELECT 'tbl_attachment' as table_name FROM dual UNION ALL
    SELECT 'tbl_mdchapter_menuid' FROM dual UNION ALL
    SELECT 'tbl_mdchapter' FROM dual UNION ALL
    SELECT 'tbl_eclinichistory' FROM dual UNION ALL
    SELECT 'tbl_esign_account' FROM dual UNION ALL
    SELECT 'tbl_study_visit_set' FROM dual UNION ALL
    SELECT 'tbl_log_event' FROM dual UNION ALL
    SELECT 'tbl_systemcode' FROM dual
),
column_counts AS (
    SELECT 
        st.table_name,
        (SELECT COUNT(*) FROM all_tab_columns WHERE owner = '&SOURCE_SCHEMA' AND table_name = st.table_name) as source_columns,
        (SELECT COUNT(*) FROM all_tab_columns WHERE owner = '&TARGET_SCHEMA' AND table_name = st.table_name) as target_columns
    FROM specific_tables st
)
SELECT 
    table_name as "Table Name",
    source_columns as "Source Columns",
    target_columns as "Target Columns",
    ABS(source_columns - target_columns) as "Difference",
    CASE 
        WHEN source_columns = target_columns THEN '✓ MATCH'
        WHEN source_columns = 0 OR target_columns = 0 THEN '✗ MISSING'
        ELSE '✗ DIFFERENT'
    END as "Status"
FROM column_counts
WHERE source_columns > 0 OR target_columns > 0
ORDER BY table_name;

-- =====================================================
-- STEP 5: SUMMARY STATISTICS
-- =====================================================

PROMPT 
PROMPT =====================================================
PROMPT SUMMARY STATISTICS
PROMPT =====================================================

-- Overall schema comparison summary
SELECT 
    'Schema Comparison Summary' as "Report Section",
    '&SOURCE_SCHEMA vs &TARGET_SCHEMA' as "Schemas Compared",
    TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') as "Comparison Time"
FROM dual;

-- Table existence summary
WITH table_check AS (
    SELECT 
        (SELECT COUNT(*) FROM all_tables WHERE owner = '&SOURCE_SCHEMA') as source_total_tables,
        (SELECT COUNT(*) FROM all_tables WHERE owner = '&TARGET_SCHEMA') as target_total_tables,
        (SELECT COUNT(*) FROM all_tables s WHERE owner = '&SOURCE_SCHEMA' 
         AND EXISTS (SELECT 1 FROM all_tables t WHERE owner = '&TARGET_SCHEMA' AND t.table_name = s.table_name)) as common_tables
    FROM dual
)
SELECT 
    'Total tables in ' || '&SOURCE_SCHEMA' as "Metric",
    TO_CHAR(source_total_tables) as "Count"
FROM table_check
UNION ALL
SELECT 
    'Total tables in ' || '&TARGET_SCHEMA' as "Metric",
    TO_CHAR(target_total_tables) as "Count"
FROM table_check
UNION ALL
SELECT 
    'Common tables' as "Metric",
    TO_CHAR(common_tables) as "Count"
FROM table_check;

PROMPT 
PROMPT =====================================================
PROMPT QUICK MANUAL VERIFICATION COMMANDS
PROMPT =====================================================

PROMPT To manually verify specific tables, run these commands:
PROMPT 
PROMPT 1. Check table existence:
PROMPT    SELECT table_name FROM all_tables WHERE owner='&SOURCE_SCHEMA' AND table_name='TBL_YOUR_TABLE';
PROMPT    SELECT table_name FROM all_tables WHERE owner='&TARGET_SCHEMA' AND table_name='TBL_YOUR_TABLE';
PROMPT 
PROMPT 2. Compare row counts:
PROMPT    SELECT COUNT(*) FROM &SOURCE_SCHEMA..tbl_your_table;
PROMPT    SELECT COUNT(*) FROM &TARGET_SCHEMA..tbl_your_table;
PROMPT 
PROMPT 3. Compare column structures:
PROMPT    SELECT column_name, data_type, data_length FROM all_tab_columns 
PROMPT    WHERE owner='&SOURCE_SCHEMA' AND table_name='TBL_YOUR_TABLE' ORDER BY column_id;
PROMPT 
PROMPT =====================================================
PROMPT COMPARISON COMPLETED!
PROMPT =====================================================
