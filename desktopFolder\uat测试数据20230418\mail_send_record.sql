/*
 Navicat MySQL Data Transfer

 Source Server         : 测试环境
 Source Server Type    : MySQL
 Source Server Version : 80028 (8.0.28)
 Source Host           : ***********:3306
 Source Schema         : susar_auto_mail

 Target Server Type    : MySQL
 Target Server Version : 80028 (8.0.28)
 File Encoding         : 65001

 Date: 18/04/2023 23:01:50
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for mail_send_record
-- ----------------------------
DROP TABLE IF EXISTS `mail_send_record`;
CREATE TABLE `mail_send_record`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `mail_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `sender` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `receiver` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `operate_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'system' COMMENT '发送人',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N',
  `compound_folder` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '化合物文件夹名称',
  `send_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_deleted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mail_send_record
-- ----------------------------
INSERT INTO `mail_send_record` VALUES (1, '', '20230418 H2023001201347 SHR-A1811-I-102_SAE46_FU2_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR-A1811', '2023-04-18 11:45:00', 'N');
INSERT INTO `mail_send_record` VALUES (2, '', '20230417 H2023001201449 SHR-1316-III-302_SAE263_FU2_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR-1316', '2023-04-18 11:45:01', 'N');
INSERT INTO `mail_send_record` VALUES (3, '', '20230413 H2022001203970 SHR6390-III-302_SAE65_FU6_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-04-18 11:45:01', 'N');
INSERT INTO `mail_send_record` VALUES (4, '', 'H2023001201117 SHR-1316-III-302_SAE246_FU2_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR-1316', '2023-04-18 17:45:00', 'N');

SET FOREIGN_KEY_CHECKS = 1;
