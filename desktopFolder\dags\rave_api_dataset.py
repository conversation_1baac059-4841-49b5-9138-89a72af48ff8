import os
from datetime import datetime,timedelta 
from airflow import DAG
from airflow.operators.bash_operator import Bash<PERSON>perator
from airflow.operators.python_operator import PythonOperator
DAG_DEFAULT_ARGS = {
    'owner': '<PERSON><PERSON><PERSON>',
    'depends_on_past': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=1)
}
# 本地文件目录
LOCAL_DIR = '/tmp/hsperfdata_root/api_download_data/'

dag = DAG('sync_rave_api_data',         
       schedule_interval='30 23 * * *',
       start_date= datetime(2023, 6, 1),
       default_args=DAG_DEFAULT_ARGS
       )

def upload_to_minio(): 
    os.system(f'mc cp -r {LOCAL_DIR}rave_dataset minios3/raw --tags "key1=RAVE&key2=rave_api"')


sync_rave_data = PythonOperator(task_id='sync_rave_data',  
                       python_callable=upload_to_minio,  
                       dag=dag)

run_script = BashOperator(task_id='run_script',   
                      bash_command=f'python3 {LOCAL_DIR}SyncRaveData.py',
                      dag=dag) 

run_script >> sync_rave_data