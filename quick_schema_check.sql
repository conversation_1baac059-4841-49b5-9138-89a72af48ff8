-- =====================================================
-- QUICK SCHEMA COMPARISON - ONE-LINER CHECKS
-- Source: CDTMS_PILOT, Target: CDTMS_TEMP
-- =====================================================

-- Quick table count comparison
SELECT 
    'CDTMS_PILOT' as schema_name, 
    COUNT(*) as table_count 
FROM all_tables 
WHERE owner = 'CDTMS_PILOT'
UNION ALL
SELECT 
    'CDTMS_TEMP' as schema_name, 
    COUNT(*) as table_count 
FROM all_tables 
WHERE owner = 'CDTMS_TEMP';

-- Quick common tables check
SELECT 
    'Common tables between schemas: ' || COUNT(*) as summary
FROM all_tables s
WHERE owner = 'CDTMS_PILOT'
AND EXISTS (SELECT 1 FROM all_tables t WHERE owner = 'CDTMS_TEMP' AND t.table_name = s.table_name);

-- Quick row count comparison for specific table (example: tbl_attachment)
-- Uncomment and modify table name as needed:
/*
SELECT 
    'tbl_attachment' as table_name,
    (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_attachment) as source_rows,
    (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_attachment) as target_rows,
    (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_attachment) - (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_attachment) as difference
FROM dual;
*/

-- Check if specific tables from 比对范围.txt exist
SELECT 
    table_name,
    CASE WHEN source_exists = 1 THEN 'YES' ELSE 'NO' END as in_source,
    CASE WHEN target_exists = 1 THEN 'YES' ELSE 'NO' END as in_target
FROM (
    SELECT 
        'tbl_attachment' as table_name,
        (SELECT COUNT(*) FROM all_tables WHERE owner = 'CDTMS_PILOT' AND table_name = 'TBL_ATTACHMENT') as source_exists,
        (SELECT COUNT(*) FROM all_tables WHERE owner = 'CDTMS_TEMP' AND table_name = 'TBL_ATTACHMENT') as target_exists
    FROM dual
    UNION ALL
    SELECT 
        'tbl_log_event' as table_name,
        (SELECT COUNT(*) FROM all_tables WHERE owner = 'CDTMS_PILOT' AND table_name = 'TBL_LOG_EVENT') as source_exists,
        (SELECT COUNT(*) FROM all_tables WHERE owner = 'CDTMS_TEMP' AND table_name = 'TBL_LOG_EVENT') as target_exists
    FROM dual
    UNION ALL
    SELECT 
        'tbl_systemcode' as table_name,
        (SELECT COUNT(*) FROM all_tables WHERE owner = 'CDTMS_PILOT' AND table_name = 'TBL_SYSTEMCODE') as source_exists,
        (SELECT COUNT(*) FROM all_tables WHERE owner = 'CDTMS_TEMP' AND table_name = 'TBL_SYSTEMCODE') as target_exists
    FROM dual
);

-- Show current user and accessible schemas
SELECT USER as current_user FROM dual;
SELECT DISTINCT owner FROM all_tables WHERE owner IN ('CDTMS_PILOT', 'CDTMS_TEMP') ORDER BY owner;
