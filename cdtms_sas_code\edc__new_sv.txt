{"code": 200, "success": true, "msg": "successfully", "data": {"tableList": [{"tid": "SUBJECT", "tname": "受试者信息", "fieldList": [{"innerId": "studyid", "fieldName": "研究代码", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "STUDYID"}, {"innerId": "sitename", "fieldName": "中心名称", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "SITENAME"}, {"innerId": "sitecode", "fieldName": "中心编号", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "SITECODE"}, {"innerId": "invname", "fieldName": "研究者", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "INVNAME"}, {"innerId": "rficdat", "fieldName": "知情同意签署日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "RFICDAT"}, {"innerId": "subjid", "fieldName": "受试者代码", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "SUBJID"}, {"innerId": "status", "fieldName": "受试者状态", "codeName": "", "format": "", "fieldType": "string", "defValue": "10", "fieldId": "STATUS"}, {"innerId": "protover", "fieldName": "方案版本", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "PROTOVER"}, {"innerId": "crfver", "fieldName": "eCRF版本号", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "CRFVER"}, {"innerId": "crfsts", "fieldName": "eCRF状态", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "CRFSTS"}, {"innerId": "invid", "fieldName": "研究者代码", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "INVID"}, {"innerId": "country", "fieldName": "国家", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "COUNTRY"}, {"innerId": "usub<PERSON>", "fieldName": "唯一受试者代码", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "USUBJID"}, {"innerId": "v001", "fieldName": "子方案", "codeName": "SUBPR", "format": "", "fieldType": "select", "defValue": "01", "fieldId": "SUBPR"}]}, {"tid": "SV", "tname": "访视日期", "fieldList": [{"innerId": "v003", "fieldName": "访视日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "VISDAT"}]}, {"tid": "DM", "tname": "人口学资料", "fieldList": [{"innerId": "v001", "fieldName": "知情同意签署日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "RFICDAT"}, {"innerId": "v002", "fieldName": "出生日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "BRTHDAT"}, {"innerId": "v003", "fieldName": "性别", "codeName": "SEX", "format": "", "fieldType": "select", "defValue": "", "fieldId": "SEX"}, {"innerId": "v004", "fieldName": "是否有生育能力", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PREGYN"}, {"innerId": "v006", "fieldName": "民族", "codeName": "ETHNICC", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CETHNIC"}, {"innerId": "t001", "fieldName": "其他民族", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CETHNICO"}, {"innerId": "v008", "fieldName": "身高", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "HEIGHT"}, {"innerId": "v009", "fieldName": "身高单位", "codeName": "HEIGHTU", "format": "", "fieldType": "select", "defValue": "CM", "fieldId": "HEIGHTU"}]}, {"tid": "MH", "tname": "既往病史", "fieldList": [{"innerId": "t001", "fieldName": "疾病名称/症状", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "MHTERM"}, {"innerId": "v001", "fieldName": "确诊/开始日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "MHSTDAT"}, {"innerId": "v002", "fieldName": "是否持续", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "MHONGO"}, {"innerId": "v003", "fieldName": "结束日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "MHENDAT"}]}, {"tid": "PE", "tname": "体格检查", "fieldList": [{"innerId": "v001", "fieldName": "检查日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "PEDAT"}]}, {"tid": "PE_SUB", "tname": "体格检查明细", "fieldList": [{"innerId": "v001", "fieldName": "检查项", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "PETEST"}, {"innerId": "v002", "fieldName": "结果", "codeName": "PERES", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PERES"}, {"innerId": "t001", "fieldName": "异常请描述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "PEDESC"}]}, {"tid": "LB", "tname": "实验室检查(有临床意义)", "fieldList": [{"innerId": "v001", "fieldName": "模块名称", "codeName": "LBCAT", "format": "", "fieldType": "select", "defValue": "", "fieldId": "LBCAT"}, {"innerId": "v002", "fieldName": "采样日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "LBDAT"}, {"innerId": "v004", "fieldName": "LAB名称", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "LBNAM"}]}, {"tid": "LB_SUB", "tname": "实验室检查明细", "fieldList": [{"innerId": "v001", "fieldName": "检查项", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "LBTEST"}, {"innerId": "v002", "fieldName": "结果", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "LBORRES"}, {"innerId": "v003", "fieldName": "单位", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "LBORRESU"}, {"innerId": "v004", "fieldName": "临床意义", "codeName": "LBCLSIG", "format": "", "fieldType": "select", "defValue": "", "fieldId": "LBCLSIG"}, {"innerId": "v005", "fieldName": "正常值范围-下限", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "LBORNRLO"}, {"innerId": "v006", "fieldName": "正常值范围-上限", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "LBORNRHI"}]}, {"tid": "VSWT", "tname": "体重", "fieldList": [{"innerId": "v001", "fieldName": "检查日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "VSDAT"}, {"innerId": "v002", "fieldName": "体重", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "WEIGHT"}, {"innerId": "v003", "fieldName": "体重单位", "codeName": "WEIGHTU", "format": "", "fieldType": "select", "defValue": "KG", "fieldId": "WEIGHTU"}]}, {"tid": "LBHCG", "tname": "妊娠试验", "fieldList": [{"innerId": "v001", "fieldName": "采样日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "LBDAT"}, {"innerId": "v002", "fieldName": "妊娠试验样本", "codeName": "LBSPEC", "format": "", "fieldType": "select", "defValue": "BLOOD", "fieldId": "LBSPEC"}, {"innerId": "v003", "fieldName": "妊娠试验结果", "codeName": "NP", "format": "", "fieldType": "select", "defValue": "", "fieldId": "LBORRES"}]}, {"tid": "EG", "tname": "12导联心电图", "fieldList": [{"innerId": "v001", "fieldName": "检查日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "EGDAT"}, {"innerId": "v003", "fieldName": "临床意义", "codeName": "CLSIG", "format": "", "fieldType": "select", "defValue": "", "fieldId": "EGCLSIG"}, {"innerId": "t001", "fieldName": "异常描述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "EGDESC"}]}, {"tid": "EG_SUB", "tname": "12导联心电图明细", "fieldList": [{"innerId": "v001", "fieldName": "检查项", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "EGTEST"}, {"innerId": "v002", "fieldName": "结果", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "EGORRES"}, {"innerId": "v003", "fieldName": "单位", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "EGORRESU"}]}, {"tid": "MOULS", "tname": "腹部B超", "fieldList": [{"innerId": "v001", "fieldName": "检查日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "MODAT"}, {"innerId": "v002", "fieldName": "临床意义", "codeName": "CLSIG", "format": "", "fieldType": "select", "defValue": "", "fieldId": "MOCLSIG"}, {"innerId": "t001", "fieldName": "异常描述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "MODESC"}]}, {"tid": "MOXC", "tname": "X-胸片正位", "fieldList": [{"innerId": "v001", "fieldName": "检查日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "MODAT"}, {"innerId": "v002", "fieldName": "临床意义", "codeName": "CLSIG", "format": "", "fieldType": "select", "defValue": "", "fieldId": "MOCLSIG"}, {"innerId": "t001", "fieldName": "异常描述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "MODESC"}]}, {"tid": "DSENROLL", "tname": "入组信息", "fieldList": [{"innerId": "v001", "fieldName": "筛选时使用的方案版本号", "codeName": "TIVERS", "format": "", "fieldType": "select", "defValue": "", "fieldId": "TIVERS"}, {"innerId": "v002", "fieldName": "筛选结果", "codeName": "DSCAT_SCREEN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "DSCAT"}, {"innerId": "v005", "fieldName": "筛选失败主要原因", "codeName": "DSDECOD_RAND", "format": "1", "fieldType": "select", "defValue": "", "fieldId": "DSDECOD"}, {"innerId": "t001", "fieldName": "其他原因", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "DSTERM"}]}, {"tid": "DSENROLL_SUB", "tname": "入组信息明细", "fieldList": [{"innerId": "v001", "fieldName": "入排标准类型", "codeName": "IECAT", "format": "", "fieldType": "select", "defValue": "", "fieldId": "IECAT"}, {"innerId": "v002", "fieldName": "不符合的入选标准/符合的排除标准序号", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "IETESTCD"}]}, {"tid": "EXO", "tname": "对乙酰氨基酚给药记录", "fieldList": [{"innerId": "v001", "fieldName": "药物名称", "codeName": "EXTRT_A", "format": "", "fieldType": "select", "defValue": "ACETAMINOPHEN", "fieldId": "EXTRT"}, {"innerId": "v002", "fieldName": "药物编号", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "EXREFID"}, {"innerId": "v004", "fieldName": "计划给药剂量", "codeName": "EXPDOST_A", "format": "", "fieldType": "select", "defValue": "", "fieldId": "EXPDOST"}, {"innerId": "v005", "fieldName": "计划给药剂量单位", "codeName": "EXPDOSTU_A", "format": "", "fieldType": "select", "defValue": "G", "fieldId": "EXPDOSTU"}, {"innerId": "v006", "fieldName": "实际给药剂量", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "EXDSTXT"}, {"innerId": "v007", "fieldName": "实际给药剂量单位", "codeName": "EXDOSU_A", "format": "", "fieldType": "select", "defValue": "G", "fieldId": "EXDOSU"}, {"innerId": "v008", "fieldName": "开始日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "EXSTDAT"}, {"innerId": "v018", "fieldName": "给药时间", "codeName": "", "format": "hh:mm", "fieldType": "time", "defValue": "", "fieldId": "EXSTTIM"}, {"innerId": "v010", "fieldName": "计划剂量是否暂停或调整", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "EXDOSADJ"}, {"innerId": "v011", "fieldName": "计划剂量暂停/调整原因", "codeName": "EXADJ", "format": "", "fieldType": "select", "defValue": "", "fieldId": "EXADJ"}, {"innerId": "t001", "fieldName": "相关AE序号", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "EXADAENO"}, {"innerId": "t004", "fieldName": "其他原因", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "EXADJSP"}]}, {"tid": "EXI", "tname": "诺利糖肽或安慰剂给药记录", "fieldList": [{"innerId": "v001", "fieldName": "药物名称", "codeName": "EXTRT", "format": "", "fieldType": "select", "defValue": "SHR20004", "fieldId": "EXTRT"}, {"innerId": "v004", "fieldName": "计划剂量是否调整", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "EXDOSADJ"}, {"innerId": "v005", "fieldName": "计划剂量调整原因", "codeName": "EXADJ", "format": "", "fieldType": "select", "defValue": "", "fieldId": "EXADJ"}, {"innerId": "t004", "fieldName": "导致剂量调整的AE序号", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "EXADAENO"}, {"innerId": "t005", "fieldName": "其他计划剂量调整原因", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "EXADJSP"}, {"innerId": "v007", "fieldName": "计划给药剂量", "codeName": "EXPDOST", "format": "", "fieldType": "select", "defValue": "", "fieldId": "EXPDOST"}, {"innerId": "v008", "fieldName": "计划给药剂量单位", "codeName": "EXPDOSTU", "format": "", "fieldType": "select", "defValue": "MG/D", "fieldId": "EXPDOSTU"}, {"innerId": "v009", "fieldName": "实际给药剂量", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "EXDSTXT"}, {"innerId": "v010", "fieldName": "实际给药剂量单位", "codeName": "EXDOSU", "format": "", "fieldType": "select", "defValue": "MG/D", "fieldId": "EXDOSU"}, {"innerId": "v011", "fieldName": "给药开始日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "EXSTDAT"}, {"innerId": "v012", "fieldName": "开始时间", "codeName": "", "format": "hh:mm", "fieldType": "time", "defValue": "", "fieldId": "EXSTTIM"}]}, {"tid": "ML", "tname": "进餐记录", "fieldList": [{"innerId": "v001", "fieldName": "进餐类型", "codeName": "MLCAT", "format": "", "fieldType": "select", "defValue": "", "fieldId": "MLCAT"}, {"innerId": "v005", "fieldName": "对乙酰氨基酚是否给药前及给药后禁水1h/给药后禁食5h", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "MLNOT"}, {"innerId": "v002", "fieldName": "进餐日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "MLDAT"}, {"innerId": "v003", "fieldName": "进餐开始时间", "codeName": "", "format": "hh:mm", "fieldType": "partialtime", "defValue": "", "fieldId": "MLSTTIM"}, {"innerId": "v004", "fieldName": "进餐结束时间", "codeName": "", "format": "hh:mm", "fieldType": "partialtime", "defValue": "", "fieldId": "MLENTIM"}]}, {"tid": "LBQUAL", "tname": "实验室定性检查(定性&无临床意义)", "fieldList": [{"innerId": "v002", "fieldName": "模块名称", "codeName": "LBCAT_QUAL", "format": "", "fieldType": "select", "defValue": "", "fieldId": "LBCAT"}, {"innerId": "v001", "fieldName": "采样日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "LBDAT"}]}, {"tid": "LBQUAL_SUB", "tname": "实验室定性检查明细", "fieldList": [{"innerId": "v001", "fieldName": "检查项", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "LBTEST"}, {"innerId": "v002", "fieldName": "结果", "codeName": "NPN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "LBORRES"}]}, {"tid": "VS", "tname": "生命体征", "fieldList": [{"innerId": "v003", "fieldName": "计划检查时间点", "codeName": "VSTPT", "format": "", "fieldType": "select", "defValue": "", "fieldId": "VSTPT"}, {"innerId": "v002", "fieldName": "检查日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "VSDAT"}, {"innerId": "v001", "fieldName": "检查时间", "codeName": "", "format": "hh:mm", "fieldType": "time", "defValue": "", "fieldId": "VSTIM"}]}, {"tid": "VS_SUB", "tname": "生命体征明细", "fieldList": [{"innerId": "v001", "fieldName": "检查项", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "VSTEST"}, {"innerId": "v002", "fieldName": "结果", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "VSORRES"}, {"innerId": "v003", "fieldName": "单位", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "VSORRESU"}, {"innerId": "v004", "fieldName": "临床意义", "codeName": "CLSIG", "format": "", "fieldType": "select", "defValue": "", "fieldId": "VSCLSIG"}]}, {"tid": "EGM", "tname": "<PERSON><PERSON> 心电图", "fieldList": [{"innerId": "v008", "fieldName": "是否检查", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "EGPERF"}, {"innerId": "v001", "fieldName": "开始日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "EGSTDAT"}, {"innerId": "v002", "fieldName": "开始时间", "codeName": "", "format": "hh:mm", "fieldType": "time", "defValue": "", "fieldId": "EGSTTIM"}, {"innerId": "v003", "fieldName": "结束日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "EGENDAT"}, {"innerId": "v004", "fieldName": "结束时间", "codeName": "", "format": "hh:mm", "fieldType": "time", "defValue": "", "fieldId": "EGENTIM"}]}, {"tid": "DSRAND", "tname": "随机", "fieldList": [{"innerId": "v001", "fieldName": "是否随机", "codeName": "RANDTRIG", "format": "", "fieldType": "select", "defValue": "", "fieldId": "RANDTRIG"}, {"innerId": "v002", "fieldName": "随机号", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "RAND_ID"}, {"innerId": "v004", "fieldName": "随机日期", "codeName": "", "format": "mm", "fieldType": "datetime", "defValue": "", "fieldId": "RANDDTC"}]}, {"tid": "PC", "tname": "免疫原性采血", "fieldList": [{"innerId": "v005", "fieldName": "计划采样时间点", "codeName": "PCTPT", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PCTPT"}, {"innerId": "v001", "fieldName": "是否进行样本采集?", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PCPERF"}, {"innerId": "v004", "fieldName": "未采集原因", "codeName": "NDREAS", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PCREASND"}, {"innerId": "v002", "fieldName": "采样日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "PCDAT"}, {"innerId": "v003", "fieldName": "采样时间", "codeName": "", "format": "hh:mm", "fieldType": "time", "defValue": "", "fieldId": "PCTIM"}]}, {"tid": "PCF", "tname": "对乙酰氨基酚PK采血", "fieldList": [{"innerId": "v003", "fieldName": "模块名称", "codeName": "PCCAT", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PCCAT"}, {"innerId": "v001", "fieldName": "是否采样", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PCPERF"}, {"innerId": "v002", "fieldName": "未采集原因", "codeName": "NDREAS", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PCREAND"}]}, {"tid": "PCF_SUB", "tname": "对乙酰氨基酚PK采血明细", "fieldList": [{"innerId": "v001", "fieldName": "计划采样时间点", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "PCTPT"}, {"innerId": "v002", "fieldName": "异常采集情况", "codeName": "PCPERF", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PCSTAT"}, {"innerId": "v005", "fieldName": "未采集/超窗原因", "codeName": "NDREAS", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PCREASND"}, {"innerId": "v003", "fieldName": "实际采样日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "PCDAT"}, {"innerId": "v004", "fieldName": "实际采样时间", "codeName": "", "format": "hh:mm", "fieldType": "time", "defValue": "", "fieldId": "PCTIM"}]}, {"tid": "PCF1", "tname": "诺利糖肽/安慰剂PK采血", "fieldList": [{"innerId": "v004", "fieldName": "模块名称", "codeName": "PCCAT_F", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PCCAT"}, {"innerId": "v001", "fieldName": "是否采样", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PCPERF"}, {"innerId": "v002", "fieldName": "未采集原因", "codeName": "NDREAS", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PCREAND"}]}, {"tid": "PCF1_SUB", "tname": "诺利糖肽/安慰剂PK采血明细", "fieldList": [{"innerId": "v001", "fieldName": "计划采样时间点", "codeName": "", "format": "", "fieldType": "string", "defValue": "", "fieldId": "PCTPT"}, {"innerId": "v002", "fieldName": "异常采集情况", "codeName": "PCPERF", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PCSTAT"}, {"innerId": "v005", "fieldName": "未采集/超窗原因", "codeName": "NDREAS", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PCREASND"}, {"innerId": "v003", "fieldName": "实际采样日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "PCDAT"}, {"innerId": "v004", "fieldName": "实际采样时间", "codeName": "", "format": "hh:mm", "fieldType": "time", "defValue": "", "fieldId": "PCTIM"}]}, {"tid": "FAISR", "tname": "注射部位反应", "fieldList": [{"innerId": "t001", "fieldName": "不良事件序号", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "FAAENO"}, {"innerId": "v001", "fieldName": "症状体征", "codeName": "FAOBJ", "format": "3", "fieldType": "select", "defValue": "", "fieldId": "FAOBJ"}, {"innerId": "t002", "fieldName": "其他症状描述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "FAOBJO"}, {"innerId": "v002", "fieldName": "严重程度", "codeName": "SEV", "format": "", "fieldType": "select", "defValue": "", "fieldId": "FASEV"}, {"innerId": "v003", "fieldName": "开始日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "FASTDAT"}, {"innerId": "v004", "fieldName": "开始时间", "codeName": "", "format": "hh:mm", "fieldType": "partialtime", "defValue": "", "fieldId": "FASTTIM"}, {"innerId": "v005", "fieldName": "是否持续", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "FAONGO"}, {"innerId": "v006", "fieldName": "结束日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "FAENDAT"}, {"innerId": "v007", "fieldName": "结束时间", "codeName": "", "format": "hh:mm", "fieldType": "partialtime", "defValue": "", "fieldId": "FAENTIM"}]}, {"tid": "AE", "tname": "不良事件", "fieldList": [{"innerId": "t001", "fieldName": "不良事件名称", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "AETERM"}, {"innerId": "v001", "fieldName": "开始日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "AESTDAT"}, {"innerId": "v003", "fieldName": "AE转归", "codeName": "AEOUT", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AEOUT"}, {"innerId": "v004", "fieldName": "转归日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "AEENDAT"}, {"innerId": "v002", "fieldName": "对对乙酰氨基酚采取措施", "codeName": "AEACN_A", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AEACN1"}, {"innerId": "v006", "fieldName": "对诺利糖肽/安慰剂采取措施", "codeName": "AEACN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AEACN"}, {"innerId": "v008", "fieldName": "严重程度", "codeName": "SEV", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESEV"}, {"innerId": "v009", "fieldName": "是否有纠正治疗", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AECONTRT"}, {"innerId": "v005", "fieldName": "与对乙酰氨基酚的关系", "codeName": "REL", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AEREL1"}, {"innerId": "v010", "fieldName": "与诺利糖肽/安慰剂的关系", "codeName": "REL", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AEREL"}, {"innerId": "v014", "fieldName": "是否是特别关注的不良事件", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESI"}, {"innerId": "v015", "fieldName": "是否是严重不良事件", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESER"}, {"innerId": "v016", "fieldName": "导致死亡", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESDTH"}, {"innerId": "v017", "fieldName": "危及生命", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESLIFE"}, {"innerId": "v018", "fieldName": "需要住院治疗或延长住院时间", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESHOSP"}, {"innerId": "v019", "fieldName": "导致永久或严重的残疾/功能丧失", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESDISAB"}, {"innerId": "v020", "fieldName": "先天异常/出生缺陷", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESCONG"}, {"innerId": "v021", "fieldName": "其他重要医学事件", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESMIE"}, {"innerId": "t002", "fieldName": "其他重要医学事件详述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "AESOSP"}, {"innerId": "v022", "fieldName": "该不良事件是否导致退出研究", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AEDIS"}]}, {"tid": "CEHYPO", "tname": "低血糖事件", "fieldList": [{"innerId": "v001", "fieldName": "开始日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "CESTDAT"}, {"innerId": "v002", "fieldName": "开始时间", "codeName": "", "format": "hh:mm", "fieldType": "partialtime", "defValue": "", "fieldId": "CESTTIM"}, {"innerId": "v018", "fieldName": "结束日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "CEENDAT"}, {"innerId": "v019", "fieldName": "结束时间", "codeName": "", "format": "hh:mm", "fieldType": "partialtime", "defValue": "", "fieldId": "CEENTIM"}, {"innerId": "v017", "fieldName": "使用最后一剂诺利糖肽/安慰剂的日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "EXSTDAT"}, {"innerId": "v021", "fieldName": "使用最后一剂诺利糖肽/安慰剂的时间", "codeName": "", "format": "hh:mm", "fieldType": "partialtime", "defValue": "", "fieldId": "EXSTTIM"}, {"innerId": "v022", "fieldName": "发生低血糖前最后一餐日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "MLSTDAT"}, {"innerId": "v023", "fieldName": "发生低血糖前最后一餐时间", "codeName": "", "format": "hh:mm", "fieldType": "partialtime", "defValue": "", "fieldId": "MLSTTIM"}, {"innerId": "v024", "fieldName": "是否有低血糖症状", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CEYN"}, {"innerId": "v025", "fieldName": "低血糖症状", "codeName": "CETERM", "format": "5", "fieldType": "mselect", "defValue": "", "fieldId": "CETERM"}, {"innerId": "t005", "fieldName": "其他低血糖症状", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CETERMO"}, {"innerId": "v026", "fieldName": "低血糖诱因", "codeName": "FAORRES", "format": "", "fieldType": "select", "defValue": "", "fieldId": "FAORRES"}, {"innerId": "t006", "fieldName": "其他低血糖诱因", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "FAORRESO"}, {"innerId": "v027", "fieldName": "低血糖发生时是否测量血糖", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "LBPERF"}, {"innerId": "v028", "fieldName": "血糖值", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "LBORRES"}, {"innerId": "v029", "fieldName": "血糖值单位", "codeName": "LBORRESU_CE", "format": "", "fieldType": "select", "defValue": "MMOLL", "fieldId": "LBORRESU"}, {"innerId": "v013", "fieldName": "低血糖分类", "codeName": "CECAT", "format": "1", "fieldType": "select", "defValue": "", "fieldId": "CECAT"}, {"innerId": "v015", "fieldName": "是否采取纠正治疗或措施", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CECONTRT"}, {"innerId": "v030", "fieldName": "采取的纠正治疗或措施", "codeName": "CMTRT", "format": "1", "fieldType": "select", "defValue": "", "fieldId": "CMTRT"}, {"innerId": "t007", "fieldName": "其他纠正治疗或措施", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CMTRTO"}, {"innerId": "v020", "fieldName": "采取的纠正治疗措施是否需要帮助", "codeName": "CMSTAT", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CESTAT"}, {"innerId": "v003", "fieldName": "是否是不良事件", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CEAEYN"}, {"innerId": "t004", "fieldName": "相关AE序号", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CEAENO"}]}, {"tid": "CM", "tname": "既往及合并用药", "fieldList": [{"innerId": "t001", "fieldName": "药物名称", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CMTRT"}, {"innerId": "v001", "fieldName": "开始日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "CMSTDAT"}, {"innerId": "v002", "fieldName": "是否持续", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CMONGO"}, {"innerId": "v003", "fieldName": "结束日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "CMENDAT"}, {"innerId": "v004", "fieldName": "用药原因", "codeName": "CMREAS", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CMREAS"}, {"innerId": "t002", "fieldName": "用药原因详述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CMINDC"}, {"innerId": "t003", "fieldName": "相关既往病史序号", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CMMHNO"}, {"innerId": "t004", "fieldName": "相关AE序号", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CMAENO"}, {"innerId": "v005", "fieldName": "每次剂量", "codeName": "", "format": "6.3", "fieldType": "stringnum", "defValue": "", "fieldId": "CMDSTXT"}, {"innerId": "v006", "fieldName": "剂量单位", "codeName": "CMDOSU", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CMDOSU"}, {"innerId": "t005", "fieldName": "其他单位", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CMDOSUO"}, {"innerId": "v007", "fieldName": "剂型", "codeName": "CMDOSFRM", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CMDOSFRM"}, {"innerId": "t006", "fieldName": "其他剂型", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CMDSFMO"}, {"innerId": "v008", "fieldName": "给药途径", "codeName": "CMROUTE", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CMROUTE"}, {"innerId": "t007", "fieldName": "其他途径", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CMROUTEO"}, {"innerId": "v009", "fieldName": "给药频率", "codeName": "CMDOSFRQ", "format": "", "fieldType": "select", "defValue": "", "fieldId": "CMDOSFRQ"}, {"innerId": "t008", "fieldName": "其他频率", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "CMDSFQO"}]}, {"tid": "PRCND", "tname": "既往及合并非药物治疗/操作", "fieldList": [{"innerId": "t001", "fieldName": "治疗名称", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "PRTRT"}, {"innerId": "v001", "fieldName": "开始日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "PRSTDAT"}, {"innerId": "v002", "fieldName": "是否持续", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PRONGO"}, {"innerId": "v003", "fieldName": "结束日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "PRENDAT"}, {"innerId": "v004", "fieldName": "治疗原因", "codeName": "PRREAS_CND", "format": "", "fieldType": "select", "defValue": "", "fieldId": "PRREAS"}, {"innerId": "t002", "fieldName": "治疗原因详述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "PRINDC"}, {"innerId": "t003", "fieldName": "相关既往病史序号", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "PRMHNO"}, {"innerId": "t004", "fieldName": "相关AE序号", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "PRAENO"}]}, {"tid": "DSEOT1", "tname": "诺利糖肽或安慰剂治疗结束页", "fieldList": [{"innerId": "v001", "fieldName": "药物名称", "codeName": "DSSCAT", "format": "", "fieldType": "select", "defValue": "SHR20004", "fieldId": "DSSCAT"}, {"innerId": "v002", "fieldName": "治疗结束日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "DSSTDAT"}, {"innerId": "v003", "fieldName": "治疗结束主要原因", "codeName": "DSDECOD_EOT", "format": "1", "fieldType": "select", "defValue": "", "fieldId": "DSDECOD"}, {"innerId": "t001", "fieldName": "治疗结束主要原因详述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "DSTERM"}]}, {"tid": "DSEOT", "tname": "对乙酰氨基酚治疗结束页", "fieldList": [{"innerId": "v001", "fieldName": "药物名称", "codeName": "DSSCAT_A", "format": "", "fieldType": "select", "defValue": "ACETAMINOPHEN", "fieldId": "DSSCAT"}, {"innerId": "v002", "fieldName": "治疗结束日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "DSSTDAT"}, {"innerId": "v003", "fieldName": "治疗结束主要原因", "codeName": "DSDECOD_EOT", "format": "1", "fieldType": "select", "defValue": "", "fieldId": "DSDECOD"}, {"innerId": "t001", "fieldName": "治疗结束主要原因详述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "DSTERM"}]}, {"tid": "RPPFU", "tname": "妊娠报告/随访", "fieldList": [{"innerId": "v001", "fieldName": "孕/产妇是否为男性受试者的女性伴侣", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "RPCWHO"}, {"innerId": "v002", "fieldName": "末次月经日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "RPSTDAT"}, {"innerId": "v003", "fieldName": "妊娠结果", "codeName": "RPTERM", "format": "1", "fieldType": "select", "defValue": "", "fieldId": "RPTERM"}, {"innerId": "v004", "fieldName": "分娩日期/妊娠终止日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "RPENDAT"}, {"innerId": "t001", "fieldName": "胎儿异常详述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "RPDESC"}, {"innerId": "v005", "fieldName": "是否是严重不良事件", "codeName": "YN", "format": "", "fieldType": "select", "defValue": "", "fieldId": "AESER"}]}, {"tid": "DSEOS", "tname": "随访结束页", "fieldList": [{"innerId": "v001", "fieldName": "随访结束日期", "codeName": "", "format": "0", "fieldType": "date", "defValue": "", "fieldId": "DSSTDAT"}, {"innerId": "v002", "fieldName": "随访结束原因", "codeName": "DSDECOD_EOS", "format": "1", "fieldType": "select", "defValue": "", "fieldId": "DSDECOD"}, {"innerId": "t001", "fieldName": "随访结束原因详述", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "DSTERM"}, {"innerId": "v003", "fieldName": "死亡原因", "codeName": "DTHREAS", "format": "", "fieldType": "select", "defValue": "", "fieldId": "DTHREAS"}, {"innerId": "t002", "fieldName": "其他死亡原因", "codeName": "", "format": "", "fieldType": "clob", "defValue": "", "fieldId": "DTHREASO"}, {"innerId": "v004", "fieldName": "死亡日期", "codeName": "", "format": "d", "fieldType": "partialdate", "defValue": "", "fieldId": "DTHDAT"}]}]}}