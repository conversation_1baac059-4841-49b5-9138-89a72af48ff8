location /susarAuto/{
        add_header X-Content-Type-Options nosniff;
        add_header Content-Security-Policy upgrade-insecure-requests;
        proxy_pass  http://localhost:8085;
}


        location ~ /susarAuto/  {
                  #host修改为真实的域名和端口
                  proxy_set_header   Host             $host;
                   #客户真实ip
                   proxy_set_header   X-Real-IP        $remote_addr;
                   proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
                   #客户端真实协议
                   # proxy_set_header   X-Forwarded-Proto  $scheme;
                    proxy_pass  http://localhost:8085;   #设置代理服务器的协义和地址
                    client_max_body_size 50m;
                   # proxy_ssl_verify off;
                   # proxy_ssl_server_name on;
        }
        
        
        
        
        localStorage.setItem('role',role)
        localStorage.role=role
        
        
        
        CTAProject
        
        localStorage.ctaPro!=='undefined'?localStorage.ctaPro.toString():''
        
        

 SHR2554 排除文件 20240318 H2024001201140 SHR2554-I-101_INI_INV.pdf
   
   
   
   
   INSERT INTO mail_send_record (file_name,compound_folder,sender,receiver, status, send_time,mail_content,operate_user) VALUES ( ?, ?, ?, ?, ?, NOW(), "", ? )
==> Parameters: 20240318 H2023001205015 SHR-A1811-I-103_FU3_INV.pdf/20240315 H2024001200718 SHR-A1811-209_FU2_INV.pdf/(String), SHR-A1811(String), <EMAIL>(String), <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>(String), N(String), system(String)
   
   
   
   
   :filters="columnFilterItems"
   :filter-method="filterHandler"
   :render-header="labelHead"
   :fixed="[0, 1, 2, tableColumns.length - 2].includes(index)"
   
   
   el-table :data="reportData" size="mini" :highlight-current-row="true" @selection-change="handleSelection"
      style="width: 98%; margin: auto; margin-top: 10px; margin-left: 2%; " :border=true @sort-change="sortChange"
      :header-cell-class-name="handleHeadAddClass" :row-class-name="tableRowClassName" @filter-change="filterChange" fit
   
   :key=Math.random()
   
   
   v-show="tableShow"
        ref="multipleTable"
        :height="tableHight"
        :row-class-name="tableRowClassName"
        :data="tableData"
        :border="true"
        style="width: 95vw;text-align: center;
        margin-left: 1vw;"
        table-layout="auto"
        @header-click="headerClick"
        @filter-change="filterChange"
        @selection-change="handleSelectionChange"
        @sort-change="changeTableSort"
   
   
   
   
   judgeRole.indexOf('CTA') === -1 || judgeRole.indexOf('Admin') === -1
   
   
   

   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   