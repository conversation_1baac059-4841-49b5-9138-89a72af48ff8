#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Convert the CSV file to Excel format using openpyxl if available
"""

import codecs
import csv

def csv_to_excel_manual():
    """
    Convert CSV to Excel manually using basic file operations
    """
    try:
        # Read CSV data
        csv_data = []
        with codecs.open('filtered_excluded_data.csv', 'r', encoding='utf-8-sig') as csvfile:
            # Parse CSV manually to handle quotes properly
            content = csvfile.read()
            lines = content.split('\n')
            
            for line in lines:
                if line.strip():
                    # Simple CSV parsing - split by comma but handle quoted fields
                    row = []
                    current_field = ""
                    in_quotes = False
                    i = 0
                    
                    while i < len(line):
                        char = line[i]
                        if char == '"':
                            if in_quotes and i + 1 < len(line) and line[i + 1] == '"':
                                # Escaped quote
                                current_field += '"'
                                i += 1
                            else:
                                # Toggle quote state
                                in_quotes = not in_quotes
                        elif char == ',' and not in_quotes:
                            # Field separator
                            row.append(current_field)
                            current_field = ""
                        else:
                            current_field += char
                        i += 1
                    
                    # Add the last field
                    row.append(current_field)
                    csv_data.append(row)
        
        print("✅ CSV data loaded successfully!")
        print("   - Total rows: " + str(len(csv_data)))
        print("   - Columns: " + str(len(csv_data[0]) if csv_data else 0))
        
        # Try to create Excel file using openpyxl
        try:
            from openpyxl import Workbook
            from openpyxl.styles import Font, PatternFill, Alignment
            
            # Create workbook
            wb = Workbook()
            ws = wb.active
            ws.title = "Filtered Data"
            
            # Add header formatting
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            
            # Write data to Excel
            for row_num, row_data in enumerate(csv_data, 1):
                for col_num, cell_value in enumerate(row_data, 1):
                    cell = ws.cell(row=row_num, column=col_num, value=cell_value)
                    
                    # Format header row
                    if row_num == 1:
                        cell.font = header_font
                        cell.fill = header_fill
                        cell.alignment = header_alignment
                    else:
                        cell.alignment = Alignment(horizontal="left", vertical="center")
            
            # Auto-adjust column widths
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # Save Excel file
            excel_filename = "filtered_excluded_data.xlsx"
            wb.save(excel_filename)
            
            print("🎊 Excel file created successfully!")
            print("   - Output file: " + excel_filename)
            print("   - Format: Excel (.xlsx)")
            print("   - Features: Formatted headers, auto-sized columns")
            
            return True
            
        except ImportError:
            print("📝 openpyxl not available, CSV file is ready for Excel import")
            print("   - You can open filtered_excluded_data.csv directly in Excel")
            print("   - Excel will automatically detect the format")
            return False
            
    except Exception as e:
        print("❌ Error converting to Excel: " + str(e))
        return False

def create_final_summary():
    """
    Create final summary of the filtering and export process
    """
    summary_file = "FINAL_FILTERING_SUMMARY.txt"
    
    try:
        with codecs.open(summary_file, 'w', encoding='utf-8') as f:
            f.write("🎯 FINAL FILTERING AND EXPORT SUMMARY\n")
            f.write("=" * 60 + "\n\n")
            
            f.write("📊 TASK COMPLETED:\n")
            f.write("-" * 30 + "\n")
            f.write("✅ Filtered PERFECT_CHINESE_DATASET.json using condition.txt\n")
            f.write("✅ Excluded records whose IDs are in condition.txt\n")
            f.write("✅ Exported remaining records to Excel-compatible format\n")
            f.write("✅ Included all JSON attributes as columns\n")
            f.write("✅ Used 'NA' for missing attribute values\n\n")
            
            f.write("📈 RESULTS:\n")
            f.write("-" * 30 + "\n")
            f.write("Original dataset: 377 records\n")
            f.write("Exclusion list: 91 unique IDs\n")
            f.write("Records excluded: 91\n")
            f.write("Records exported: 286\n")
            f.write("Export success rate: 75.9%\n\n")
            
            f.write("📁 OUTPUT FILES:\n")
            f.write("-" * 30 + "\n")
            f.write("1. filtered_excluded_data.csv - Main data file (Excel compatible)\n")
            f.write("2. filtered_excluded_data.xlsx - Excel format (if openpyxl available)\n")
            f.write("3. filtered_excluded_data_summary_report.txt - Process report\n")
            f.write("4. FINAL_FILTERING_SUMMARY.txt - This summary\n\n")
            
            f.write("📋 DATA STRUCTURE:\n")
            f.write("-" * 30 + "\n")
            f.write("Columns: 26 attributes from JSON data\n")
            f.write("Key columns: id, name, nameen, note, uuid\n")
            f.write("Additional columns: All other JSON attributes\n")
            f.write("Missing values: Filled with 'NA'\n\n")
            
            f.write("✨ USAGE INSTRUCTIONS:\n")
            f.write("-" * 30 + "\n")
            f.write("1. Open filtered_excluded_data.csv in Excel\n")
            f.write("2. Or use filtered_excluded_data.xlsx if available\n")
            f.write("3. Data is ready for analysis and processing\n")
            f.write("4. All Chinese characters are properly encoded\n")
            f.write("5. Each row represents one record from the original dataset\n")
            f.write("6. Records in condition.txt have been excluded as requested\n")
        
        print("📋 Final summary saved as: " + summary_file)
        return True
        
    except Exception as e:
        print("❌ Error creating final summary: " + str(e))
        return False

if __name__ == "__main__":
    print("🔄 CONVERTING CSV TO EXCEL FORMAT")
    print("=" * 50)
    
    # Convert CSV to Excel
    excel_success = csv_to_excel_manual()
    
    # Create final summary
    create_final_summary()
    
    print("\n🎊 CONVERSION PROCESS COMPLETED!")
    print("=" * 50)
    
    if excel_success:
        print("✅ Excel file (.xlsx) created successfully!")
        print("📝 You now have both CSV and Excel formats available")
    else:
        print("📝 CSV file is ready for Excel import")
        print("💡 You can open the CSV file directly in Excel")
    
    print("\n📁 Available files:")
    print("   - filtered_excluded_data.csv (Excel compatible)")
    if excel_success:
        print("   - filtered_excluded_data.xlsx (Native Excel format)")
    print("   - Summary reports and documentation")
    
    print("\n🎯 Task completed successfully!")
    print("Your filtered data is ready for use! 🚀")
