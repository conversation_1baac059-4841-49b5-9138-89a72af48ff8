package net.bioknow.cdtms.formMail;

import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTRecordFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class DTRFSendMail extends DTRecordFuncAction {

    private static final ThreadLocal name = new ThreadLocal();
    private static final ThreadLocal currEngineListFunc = new ThreadLocal();

    public boolean canUse(int auth, String tableid, Long recordid) {
        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            DAODbApi apidao = new DAODbApi(projectId);
            Map mapT = apidao.getMapTable("email_template");
            if (mapT == null) return false;
            List<Map> engineList = daoDataMng.listRecord("email_template", "obj.tableid='" + tableid + "'", null, 100);
            if (CollectionUtils.isEmpty(engineList)) {
                return false;
            }
            ArrayList<Map> currEngineList = new ArrayList<>();
            for (Map engineMap : engineList) {
                String showWhere = (String) engineMap.get("where");
                if (StringUtils.isEmpty(showWhere)) {
                    showWhere = "1=1";
                }
                int ActiveEngineCount = daoDataMng.count(tableid, "obj.id=" + recordid + " and (" + showWhere + ")");

                if (ActiveEngineCount == 1) {
                    currEngineList.add(engineMap);
                }
            }
            if (currEngineList.size() > 0) {
                currEngineListFunc.set(currEngineList);
                if (currEngineList.size() == 1) {
                    String funcName = (String) currEngineList.get(0).get("name");
                    name.set(funcName);
                }
                return true;
            }

        } catch (Exception e) {
            Log.error("", e);
        }
        return false;
    }

    public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBean fpb) {
        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            List<Map> engineList = daoDataMng.listRecord("email_template", "obj.tableid='" + fpb.getTableid() + "'", null, 100);
            ArrayList<Map> currEngineList = new ArrayList<>();
            for (Map engineMap : engineList) {
                String showWhere = (String) engineMap.get("where");
                if (StringUtils.isEmpty(showWhere)) {
                    showWhere = "1=1";
                }
                int ActiveEngineCount = daoDataMng.count(fpb.getTableid(), "obj.id=" + fpb.getRecordid() + " and (" + showWhere + ")");
                if (ActiveEngineCount == 1) {
                    currEngineList.add(engineMap);
                }
            }
//			if(currEngineList.size()==1){
//				this.redirectByUri(request, response,"actionsendmail.getemail.do?id="+currEngineList.get(0).get("id")+"&recordid="+fpb.getRecordid());
//			}else {
            request.setAttribute("listr", currEngineList);
            this.forwardByUri(request, response, "/formMail.ajaxmenu.do");
//			}
//
        } catch (Exception e) {
            Log.error("", e);
        }
    }

    public FuncInfoBean getFIB(String tableid) {
        FuncInfoBean fib = new FuncInfoBean();
        List<Map> currEngineList = (List<Map>) currEngineListFunc.get();

        fib.setWinHeight(800);
        fib.setWinWidth(1000);

        if (currEngineList.size() == 1) {
            String funcName = (String) name.get();
            fib.setName(!StringUtils.isEmpty(funcName) ? funcName : this.getLanguage().get("sendMail"));
        } else {
            fib.setName(this.getLanguage().get("sendMail"));
        }
        fib.setType(FuncInfoBean.FUNCTYPE_AJAXMENU);
        return fib;
    }
}
