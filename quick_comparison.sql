-- =====================================================
-- QUICK DATABASE COMPARISON - IMMEDIATE EXECUTION
-- Copy and paste this into SQL*Plus or SQL Developer
-- =====================================================

-- =====================================================
-- STEP 1: SETUP (MODIFY THESE VALUES)
-- =====================================================

-- TODO: Replace these with your actual database connection details
/*
CREATE DATABASE LINK source_db_link
CONNECT TO your_source_username IDENTIFIED BY your_source_password
USING 'your_source_connection_string';
*/

-- Test connection (uncomment after creating database link)
-- SELECT 'Database link working' as status FROM dual@source_db_link;

-- =====================================================
-- STEP 2: QUICK TABLE COUNT COMPARISON
-- =====================================================

PROMPT =====================================================
PROMPT TABLE COUNT COMPARISON
PROMPT =====================================================

SELECT 
    'SOURCE DATABASE' as database_type,
    COUNT(*) as table_count
FROM user_tables@source_db_link
WHERE table_name NOT LIKE 'BIN$%'
UNION ALL
SELECT 
    'TARGET DATABASE' as database_type,
    COUNT(*) as table_count
FROM user_tables
WHERE table_name NOT LIKE 'BIN$%';

-- =====================================================
-- STEP 3: MISSING TABLES CHECK
-- =====================================================

PROMPT 
PROMPT =====================================================
PROMPT MISSING TABLES CHECK
PROMPT =====================================================

-- Tables in source but not in target
SELECT 
    table_name,
    'EXISTS IN SOURCE ONLY' as status
FROM user_tables@source_db_link
WHERE table_name NOT IN (SELECT table_name FROM user_tables)
AND table_name NOT LIKE 'BIN$%'
UNION ALL
-- Tables in target but not in source
SELECT 
    table_name,
    'EXISTS IN TARGET ONLY' as status
FROM user_tables
WHERE table_name NOT IN (SELECT table_name FROM user_tables@source_db_link)
AND table_name NOT LIKE 'BIN$%'
ORDER BY table_name;

-- =====================================================
-- STEP 4: ROW COUNT COMPARISON FOR COMMON TABLES
-- =====================================================

PROMPT 
PROMPT =====================================================
PROMPT ROW COUNT COMPARISON (TOP 20 TABLES)
PROMPT =====================================================

-- Compare row counts for common tables
WITH common_tables AS (
    SELECT table_name FROM user_tables
    WHERE table_name IN (SELECT table_name FROM user_tables@source_db_link)
    AND table_name NOT LIKE 'BIN$%'
    AND ROWNUM <= 20  -- Limit to first 20 tables for quick check
),
source_counts AS (
    SELECT table_name, num_rows as row_count
    FROM user_tables@source_db_link
    WHERE table_name IN (SELECT table_name FROM common_tables)
),
target_counts AS (
    SELECT table_name, num_rows as row_count
    FROM user_tables
    WHERE table_name IN (SELECT table_name FROM common_tables)
)
SELECT 
    s.table_name,
    NVL(s.row_count, 0) as source_rows,
    NVL(t.row_count, 0) as target_rows,
    NVL(s.row_count, 0) - NVL(t.row_count, 0) as difference,
    CASE 
        WHEN NVL(s.row_count, 0) = NVL(t.row_count, 0) THEN 'MATCH'
        ELSE 'MISMATCH'
    END as status
FROM source_counts s
FULL OUTER JOIN target_counts t ON s.table_name = t.table_name
ORDER BY ABS(NVL(s.row_count, 0) - NVL(t.row_count, 0)) DESC;

-- =====================================================
-- STEP 5: ACTUAL ROW COUNT FOR CRITICAL TABLES
-- =====================================================

PROMPT 
PROMPT =====================================================
PROMPT ACTUAL ROW COUNT FOR CRITICAL TABLES
PROMPT (Modify table names below for your critical tables)
PROMPT =====================================================

-- TODO: Replace these table names with your critical tables
-- Example for common table names - modify as needed

-- Check if EMPLOYEES table exists and compare
SELECT 
    'EMPLOYEES' as table_name,
    (SELECT COUNT(*) FROM user_tables@source_db_link WHERE table_name = 'EMPLOYEES') as source_exists,
    (SELECT COUNT(*) FROM user_tables WHERE table_name = 'EMPLOYEES') as target_exists
FROM dual;

-- If EMPLOYEES exists, compare row counts (uncomment if table exists)
/*
SELECT 
    'EMPLOYEES' as table_name,
    (SELECT COUNT(*) FROM EMPLOYEES@source_db_link) as source_count,
    (SELECT COUNT(*) FROM EMPLOYEES) as target_count,
    (SELECT COUNT(*) FROM EMPLOYEES@source_db_link) - (SELECT COUNT(*) FROM EMPLOYEES) as difference
FROM dual;
*/

-- Add more tables as needed:
/*
SELECT 
    'CUSTOMERS' as table_name,
    (SELECT COUNT(*) FROM CUSTOMERS@source_db_link) as source_count,
    (SELECT COUNT(*) FROM CUSTOMERS) as target_count,
    (SELECT COUNT(*) FROM CUSTOMERS@source_db_link) - (SELECT COUNT(*) FROM CUSTOMERS) as difference
FROM dual;

SELECT 
    'ORDERS' as table_name,
    (SELECT COUNT(*) FROM ORDERS@source_db_link) as source_count,
    (SELECT COUNT(*) FROM ORDERS) as target_count,
    (SELECT COUNT(*) FROM ORDERS@source_db_link) - (SELECT COUNT(*) FROM ORDERS) as difference
FROM dual;
*/

-- =====================================================
-- STEP 6: COLUMN COUNT COMPARISON
-- =====================================================

PROMPT 
PROMPT =====================================================
PROMPT COLUMN COUNT COMPARISON
PROMPT =====================================================

WITH source_col_counts AS (
    SELECT table_name, COUNT(*) as column_count
    FROM user_tab_columns@source_db_link
    WHERE table_name NOT LIKE 'BIN$%'
    GROUP BY table_name
),
target_col_counts AS (
    SELECT table_name, COUNT(*) as column_count
    FROM user_tab_columns
    WHERE table_name NOT LIKE 'BIN$%'
    GROUP BY table_name
)
SELECT 
    COALESCE(s.table_name, t.table_name) as table_name,
    NVL(s.column_count, 0) as source_columns,
    NVL(t.column_count, 0) as target_columns,
    NVL(s.column_count, 0) - NVL(t.column_count, 0) as column_difference,
    CASE 
        WHEN NVL(s.column_count, 0) = NVL(t.column_count, 0) THEN 'MATCH'
        ELSE 'MISMATCH'
    END as status
FROM source_col_counts s
FULL OUTER JOIN target_col_counts t ON s.table_name = t.table_name
WHERE NVL(s.column_count, 0) != NVL(t.column_count, 0)  -- Show only mismatches
ORDER BY ABS(NVL(s.column_count, 0) - NVL(t.column_count, 0)) DESC;

-- =====================================================
-- STEP 7: QUICK SUMMARY
-- =====================================================

PROMPT 
PROMPT =====================================================
PROMPT QUICK COMPARISON SUMMARY
PROMPT =====================================================

SELECT 'Comparison completed at: ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') as summary FROM dual;

-- Count of common tables
SELECT 
    'Common tables: ' || COUNT(*) as summary
FROM user_tables
WHERE table_name IN (SELECT table_name FROM user_tables@source_db_link)
AND table_name NOT LIKE 'BIN$%';

-- Count of source-only tables
SELECT 
    'Source-only tables: ' || COUNT(*) as summary
FROM user_tables@source_db_link
WHERE table_name NOT IN (SELECT table_name FROM user_tables)
AND table_name NOT LIKE 'BIN$%';

-- Count of target-only tables
SELECT 
    'Target-only tables: ' || COUNT(*) as summary
FROM user_tables
WHERE table_name NOT IN (SELECT table_name FROM user_tables@source_db_link)
AND table_name NOT LIKE 'BIN$%';

PROMPT 
PROMPT =====================================================
PROMPT COMPARISON COMPLETED!
PROMPT =====================================================
PROMPT Review the results above for any mismatches.
PROMPT For detailed comparison, run the full script:
PROMPT @pure_sql_database_comparison.sql
PROMPT =====================================================

-- =====================================================
-- OPTIONAL: SAVE RESULTS TO TABLE
-- =====================================================

-- Uncomment below to save results to a table for later analysis
/*
CREATE TABLE quick_comparison_results AS
WITH comparison_summary AS (
    SELECT 
        SYSDATE as comparison_date,
        'TABLE_COUNT' as comparison_type,
        (SELECT COUNT(*) FROM user_tables@source_db_link WHERE table_name NOT LIKE 'BIN$%') as source_value,
        (SELECT COUNT(*) FROM user_tables WHERE table_name NOT LIKE 'BIN$%') as target_value
    FROM dual
    UNION ALL
    SELECT 
        SYSDATE as comparison_date,
        'COMMON_TABLES' as comparison_type,
        (SELECT COUNT(*) FROM user_tables WHERE table_name IN (SELECT table_name FROM user_tables@source_db_link) AND table_name NOT LIKE 'BIN$%') as source_value,
        (SELECT COUNT(*) FROM user_tables WHERE table_name IN (SELECT table_name FROM user_tables@source_db_link) AND table_name NOT LIKE 'BIN$%') as target_value
    FROM dual
)
SELECT 
    comparison_date,
    comparison_type,
    source_value,
    target_value,
    CASE WHEN source_value = target_value THEN 'MATCH' ELSE 'MISMATCH' END as status
FROM comparison_summary;

-- View saved results
SELECT * FROM quick_comparison_results;
*/
