package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.services.core.ApiResult;
import net.bioknow.services.uap.dbdatamng.function.DTRecordFuncActionNew;
import net.bioknow.services.uap.dbdatamng.function.FuncInfoBeanNew;
import net.bioknow.services.uap.dbdatamng.function.FuncParamBeanNew;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTRecordFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class DTRFlightpdfReplenishVue extends DTRecordFuncActionNew {

	@Override
	public boolean canUse(int auth, String tableid, Long recordid) {

		try {

			if (!StringUtils.equals(tableid,"esign_instance")) {
				return false;
			}
			String projectId = SessUtil.getSessInfo().getProjectid();

			DAODataMng daoDataMng=new DAODataMng(projectId);
			Map esignInstanceMap = daoDataMng.getRecord(tableid, recordid);
			if (!StringUtils.equals(SessUtil.getSessInfo().getUserid(),String.valueOf(esignInstanceMap.get("userid")))) {

				return false;
			}
			String esignInstanceStatus = (String) esignInstanceMap.get("status");
			if (StringUtils.equals(esignInstanceStatus,"3")) {
				if (daoDataMng.count("esign_signer","obj.esign_instance_id="+recordid+" and obj.status=2")>0) {
					return true;
				}
			}

		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}

	@Override
	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBeanNew fpb) {
		try {

			String projectid = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng = new DAODataMng(projectid);

			List esignFileList = daoDataMng.listRecord("esign_file", "obj.esign_instance_id=" + fpb.getRecordid(), null, 1);

			Map esignFileMap = (Map) esignFileList.get(0);
			String signedFile = (String) esignFileMap.get("signed_file");
			String signedFileName = signedFile.split("\\|")[0].split("\\*")[0];


			Map esignInstanceMap = daoDataMng.getRecord("esign_instance", Long.valueOf(fpb.getRecordid()));
			List<Map> esignUserList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id=" + fpb.getRecordid() +" and obj.status='0'", null, 1);

			ArrayList<String> options = new ArrayList<String>();

			if (!CollectionUtils.isEmpty(esignUserList)) {
				for (Map signerMap : esignUserList) {
					options.add(signerMap.get("name") + "<" + signerMap.get("user_code") + ">");
				}


				request.setAttribute("ReSignerOptions",StringUtils.join(options,"|"));

			}

			if (!ObjectUtils.isEmpty(esignInstanceMap.get("study_id"))) {
				Map StudyMap = daoDataMng.getRecord("xsht", (Long) esignInstanceMap.get("study_id"));
				request.setAttribute("ReStudyId",esignInstanceMap.get("study_id"));
				request.setAttribute("ReStudyName",StudyMap.get("studyid"));

			}
			request.setAttribute("ReLanguage",esignInstanceMap.get("language"));
			request.setAttribute("ReType",esignInstanceMap.get("type"));
			request.setAttribute("signedFile",signedFile);
			request.setAttribute("signedFileName",signedFileName);

			HashMap map = new HashMap();
			map.put("url", "/LightpdfSignIntergrate.signCreate.do");
			response.getOutputStream().write(ApiResult.ok("ok", map).getBytes(StandardCharsets.UTF_8));
			response.getOutputStream().close();
		} catch (Exception e) {
			Log.error("",e);
		}
	}

	@Override
	public FuncInfoBeanNew getFIB(String tableid) {
		FuncInfoBeanNew fib = new FuncInfoBeanNew("cdtms_DTRFlightpdfReplenishVue");
		try {
			fib.setName("补签");
			fib.setType(FuncInfoBeanNew.FUNCTYPE_INNERWINDOW);
			fib.setWinHeight(800);
			fib.setWinWidth(1000);
			fib.setAppendParams(false);

			return fib;
		} catch (Exception e) {
			Log.error("", e);
		}
		return fib;
	}
}
