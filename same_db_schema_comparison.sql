-- =====================================================
-- OPTIMIZED SCHEMA COMPARISON - SAME DATABASE INSTANCE
-- Source Schema: CDTMS_PILOT
-- Target Schema: CDTMS_TEMP
-- No Database Links Required
-- =====================================================

-- =====================================================
-- STEP 1: SETUP AND CONFIGURATION
-- =====================================================

-- Set session parameters for better output
SET PAGESIZE 1000
SET LINESIZE 200
SET TRIMSPOOL ON
SET FEEDBACK ON
SET TIMING ON

-- Define schemas (modify if needed)
DEFINE SOURCE_SCHEMA = 'CDTMS_PILOT'
DEFINE TARGET_SCHEMA = 'CDTMS_TEMP'

PROMPT =====================================================
PROMPT SCHEMA COMPARISON REPORT
PROMPT Source Schema: &SOURCE_SCHEMA
PROMPT Target Schema: &TARGET_SCHEMA
PROMPT Comparison Date: 
SELECT TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') as comparison_time FROM dual;
PROMPT =====================================================

-- =====================================================
-- STEP 2: CREATE RESULT TABLES (Run once)
-- =====================================================

-- Drop tables if they exist (optional)
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE schema_comparison_summary';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE schema_comparison_details';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

-- Create summary table
CREATE TABLE schema_comparison_summary (
    comparison_date DATE DEFAULT SYSDATE,
    comparison_type VARCHAR2(50),
    total_items NUMBER,
    matches NUMBER,
    mismatches NUMBER,
    match_percentage NUMBER(5,2),
    status VARCHAR2(20)
);

-- Create details table
CREATE TABLE schema_comparison_details (
    detail_id NUMBER GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    comparison_date DATE DEFAULT SYSDATE,
    table_name VARCHAR2(128),
    comparison_type VARCHAR2(50),
    source_value VARCHAR2(4000),
    target_value VARCHAR2(4000),
    match_status VARCHAR2(10),
    difference_info CLOB
);

-- =====================================================
-- STEP 3: TABLE EXISTENCE COMPARISON
-- =====================================================

PROMPT 
PROMPT =====================================================
PROMPT CHECKING TABLE EXISTENCE
PROMPT =====================================================

-- Clear previous results
DELETE FROM schema_comparison_details WHERE comparison_type = 'TABLE_EXISTENCE';

-- Insert table existence comparison
INSERT INTO schema_comparison_details (table_name, comparison_type, source_value, target_value, match_status, difference_info)
WITH source_tables AS (
    SELECT table_name FROM all_tables
    WHERE owner = '&SOURCE_SCHEMA'
    AND table_name NOT LIKE 'BIN$%'
),
target_tables AS (
    SELECT table_name FROM all_tables
    WHERE owner = '&TARGET_SCHEMA'
    AND table_name NOT LIKE 'BIN$%'
),
table_comparison AS (
    SELECT 
        COALESCE(s.table_name, t.table_name) as table_name,
        CASE WHEN s.table_name IS NOT NULL THEN 'EXISTS' ELSE 'MISSING' END as source_status,
        CASE WHEN t.table_name IS NOT NULL THEN 'EXISTS' ELSE 'MISSING' END as target_status
    FROM source_tables s
    FULL OUTER JOIN target_tables t ON s.table_name = t.table_name
)
SELECT 
    table_name,
    'TABLE_EXISTENCE',
    source_status,
    target_status,
    CASE WHEN source_status = target_status THEN 'MATCH' ELSE 'MISMATCH' END,
    CASE 
        WHEN source_status = 'MISSING' THEN 'Table exists only in ' || '&TARGET_SCHEMA'
        WHEN target_status = 'MISSING' THEN 'Table exists only in ' || '&SOURCE_SCHEMA'
        ELSE 'Table exists in both schemas'
    END
FROM table_comparison;

COMMIT;

-- Show table existence summary
SELECT 
    'Table Existence Check' as check_type,
    COUNT(*) as total_tables,
    SUM(CASE WHEN match_status = 'MATCH' THEN 1 ELSE 0 END) as matching_tables,
    SUM(CASE WHEN match_status = 'MISMATCH' THEN 1 ELSE 0 END) as different_tables
FROM schema_comparison_details 
WHERE comparison_type = 'TABLE_EXISTENCE';

-- =====================================================
-- STEP 4: ROW COUNT COMPARISON
-- =====================================================

PROMPT 
PROMPT =====================================================
PROMPT COMPARING ROW COUNTS
PROMPT =====================================================

-- Clear previous results
DELETE FROM schema_comparison_details WHERE comparison_type = 'ROW_COUNT';

-- Get list of common tables and compare row counts
DECLARE
    v_source_count NUMBER;
    v_target_count NUMBER;
    v_sql VARCHAR2(1000);
    v_table_count NUMBER := 0;
    
    CURSOR common_tables IS
        SELECT table_name FROM all_tables
        WHERE owner = '&TARGET_SCHEMA'
        AND table_name IN (SELECT table_name FROM all_tables WHERE owner = '&SOURCE_SCHEMA')
        AND table_name NOT LIKE 'BIN$%'
        ORDER BY table_name;
BEGIN
    FOR rec IN common_tables LOOP
        BEGIN
            v_table_count := v_table_count + 1;
            
            -- Get source count
            v_sql := 'SELECT COUNT(*) FROM ' || '&SOURCE_SCHEMA' || '.' || rec.table_name;
            EXECUTE IMMEDIATE v_sql INTO v_source_count;
            
            -- Get target count
            v_sql := 'SELECT COUNT(*) FROM ' || '&TARGET_SCHEMA' || '.' || rec.table_name;
            EXECUTE IMMEDIATE v_sql INTO v_target_count;
            
            -- Insert comparison result
            INSERT INTO schema_comparison_details (
                table_name, comparison_type, source_value, target_value, match_status, difference_info
            ) VALUES (
                rec.table_name,
                'ROW_COUNT',
                TO_CHAR(v_source_count),
                TO_CHAR(v_target_count),
                CASE WHEN v_source_count = v_target_count THEN 'MATCH' ELSE 'MISMATCH' END,
                '&SOURCE_SCHEMA: ' || v_source_count || ' rows, &TARGET_SCHEMA: ' || v_target_count || ' rows, Difference: ' || ABS(v_source_count - v_target_count)
            );
            
            -- Progress indicator
            IF MOD(v_table_count, 10) = 0 THEN
                DBMS_OUTPUT.PUT_LINE('Processed ' || v_table_count || ' tables...');
            END IF;
            
        EXCEPTION
            WHEN OTHERS THEN
                INSERT INTO schema_comparison_details (
                    table_name, comparison_type, source_value, target_value, match_status, difference_info
                ) VALUES (
                    rec.table_name,
                    'ROW_COUNT',
                    'ERROR',
                    'ERROR',
                    'ERROR',
                    'Error: ' || SQLERRM
                );
        END;
    END LOOP;
    
    DBMS_OUTPUT.PUT_LINE('Row count comparison completed for ' || v_table_count || ' tables.');
    COMMIT;
END;
/

-- Show row count summary
SELECT 
    'Row Count Check' as check_type,
    COUNT(*) as total_tables,
    SUM(CASE WHEN match_status = 'MATCH' THEN 1 ELSE 0 END) as matching_counts,
    SUM(CASE WHEN match_status = 'MISMATCH' THEN 1 ELSE 0 END) as different_counts,
    SUM(CASE WHEN match_status = 'ERROR' THEN 1 ELSE 0 END) as error_count
FROM schema_comparison_details 
WHERE comparison_type = 'ROW_COUNT';

-- =====================================================
-- STEP 5: COLUMN STRUCTURE COMPARISON
-- =====================================================

PROMPT 
PROMPT =====================================================
PROMPT COMPARING COLUMN STRUCTURES
PROMPT =====================================================

-- Clear previous results
DELETE FROM schema_comparison_details WHERE comparison_type = 'COLUMN_STRUCTURE';

-- Compare column structures
INSERT INTO schema_comparison_details (table_name, comparison_type, source_value, target_value, match_status, difference_info)
WITH source_columns AS (
    SELECT 
        table_name,
        column_name,
        data_type || '(' || NVL(TO_CHAR(data_length), 'NULL') || ')' || 
        CASE WHEN nullable = 'Y' THEN ' NULL' ELSE ' NOT NULL' END as column_def
    FROM all_tab_columns
    WHERE owner = '&SOURCE_SCHEMA'
    AND table_name NOT LIKE 'BIN$%'
),
target_columns AS (
    SELECT 
        table_name,
        column_name,
        data_type || '(' || NVL(TO_CHAR(data_length), 'NULL') || ')' || 
        CASE WHEN nullable = 'Y' THEN ' NULL' ELSE ' NOT NULL' END as column_def
    FROM all_tab_columns
    WHERE owner = '&TARGET_SCHEMA'
    AND table_name NOT LIKE 'BIN$%'
),
column_comparison AS (
    SELECT 
        COALESCE(s.table_name, t.table_name) as table_name,
        COALESCE(s.column_name, t.column_name) as column_name,
        NVL(s.column_def, 'MISSING') as source_def,
        NVL(t.column_def, 'MISSING') as target_def,
        CASE 
            WHEN s.column_def = t.column_def THEN 'MATCH'
            WHEN s.column_def IS NULL THEN 'TARGET_ONLY'
            WHEN t.column_def IS NULL THEN 'SOURCE_ONLY'
            ELSE 'DIFFERENT'
        END as comparison_result
    FROM source_columns s
    FULL OUTER JOIN target_columns t 
        ON s.table_name = t.table_name AND s.column_name = t.column_name
)
SELECT 
    table_name,
    'COLUMN_STRUCTURE',
    source_def,
    target_def,
    CASE WHEN comparison_result = 'MATCH' THEN 'MATCH' ELSE 'MISMATCH' END,
    'Column: ' || column_name || ', Status: ' || comparison_result
FROM column_comparison
WHERE comparison_result != 'MATCH';

COMMIT;

-- Show column structure summary
SELECT 
    'Column Structure Check' as check_type,
    COUNT(*) as total_differences,
    SUM(CASE WHEN difference_info LIKE '%SOURCE_ONLY%' THEN 1 ELSE 0 END) as source_only_columns,
    SUM(CASE WHEN difference_info LIKE '%TARGET_ONLY%' THEN 1 ELSE 0 END) as target_only_columns,
    SUM(CASE WHEN difference_info LIKE '%DIFFERENT%' THEN 1 ELSE 0 END) as different_columns
FROM schema_comparison_details 
WHERE comparison_type = 'COLUMN_STRUCTURE';

-- =====================================================
-- STEP 6: SPECIFIC TABLE LIST COMPARISON (from 比对范围.txt)
-- =====================================================

PROMPT 
PROMPT =====================================================
PROMPT COMPARING SPECIFIC TABLES FROM COMPARISON SCOPE
PROMPT =====================================================

-- Compare specific tables from your list (modify as needed)
WITH specific_tables AS (
    SELECT 'tbl_attachment' as table_name FROM dual UNION ALL
    SELECT 'tbl_mdchapter_menuid' FROM dual UNION ALL
    SELECT 'tbl_mdchapter' FROM dual UNION ALL
    SELECT 'tbl_eclinichistory' FROM dual UNION ALL
    SELECT 'tbl_esign_account' FROM dual UNION ALL
    SELECT 'tbl_study_visit_set' FROM dual UNION ALL
    SELECT 'tbl_log_event' FROM dual UNION ALL
    SELECT 'tbl_systemcode' FROM dual
    -- Add more tables from your 比对范围.txt as needed
),
specific_comparison AS (
    SELECT 
        st.table_name,
        (SELECT COUNT(*) FROM all_tables WHERE owner = '&SOURCE_SCHEMA' AND table_name = st.table_name) as source_exists,
        (SELECT COUNT(*) FROM all_tables WHERE owner = '&TARGET_SCHEMA' AND table_name = st.table_name) as target_exists
    FROM specific_tables st
)
SELECT 
    table_name,
    CASE WHEN source_exists = 1 THEN 'EXISTS' ELSE 'MISSING' END as source_status,
    CASE WHEN target_exists = 1 THEN 'EXISTS' ELSE 'MISSING' END as target_status,
    CASE 
        WHEN source_exists = 1 AND target_exists = 1 THEN 'BOTH_EXIST'
        WHEN source_exists = 1 AND target_exists = 0 THEN 'SOURCE_ONLY'
        WHEN source_exists = 0 AND target_exists = 1 THEN 'TARGET_ONLY'
        ELSE 'BOTH_MISSING'
    END as comparison_result
FROM specific_comparison
ORDER BY table_name;

-- =====================================================
-- STEP 7: GENERATE SUMMARY STATISTICS
-- =====================================================

PROMPT 
PROMPT =====================================================
PROMPT GENERATING SUMMARY STATISTICS
PROMPT =====================================================

-- Clear previous summary
DELETE FROM schema_comparison_summary;

-- Generate summary for each comparison type
INSERT INTO schema_comparison_summary (comparison_type, total_items, matches, mismatches, match_percentage, status)
SELECT 
    comparison_type,
    COUNT(*) as total_items,
    SUM(CASE WHEN match_status = 'MATCH' THEN 1 ELSE 0 END) as matches,
    SUM(CASE WHEN match_status != 'MATCH' THEN 1 ELSE 0 END) as mismatches,
    ROUND(SUM(CASE WHEN match_status = 'MATCH' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as match_percentage,
    CASE 
        WHEN SUM(CASE WHEN match_status != 'MATCH' THEN 1 ELSE 0 END) = 0 THEN 'PERFECT'
        WHEN SUM(CASE WHEN match_status = 'MATCH' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) >= 95 THEN 'GOOD'
        WHEN SUM(CASE WHEN match_status = 'MATCH' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) >= 80 THEN 'FAIR'
        ELSE 'POOR'
    END as status
FROM schema_comparison_details
WHERE match_status != 'ERROR'
GROUP BY comparison_type;

COMMIT;

-- =====================================================
-- STEP 8: DISPLAY FINAL RESULTS
-- =====================================================

PROMPT 
PROMPT =====================================================
PROMPT FINAL COMPARISON SUMMARY
PROMPT =====================================================

SELECT 
    comparison_type as "Comparison Type",
    total_items as "Total Items",
    matches as "Matches",
    mismatches as "Mismatches", 
    match_percentage || '%' as "Match %",
    status as "Status"
FROM schema_comparison_summary
ORDER BY comparison_type;

PROMPT 
PROMPT =====================================================
PROMPT DETAILED MISMATCHES
PROMPT =====================================================

SELECT 
    table_name as "Table Name",
    comparison_type as "Type",
    source_value as "Source (&SOURCE_SCHEMA)",
    target_value as "Target (&TARGET_SCHEMA)",
    difference_info as "Details"
FROM schema_comparison_details
WHERE match_status = 'MISMATCH'
ORDER BY table_name, comparison_type;

PROMPT 
PROMPT =====================================================
PROMPT TABLES WITH MOST ISSUES
PROMPT =====================================================

SELECT 
    table_name as "Table Name",
    COUNT(*) as "Issue Count",
    LISTAGG(comparison_type, ', ') WITHIN GROUP (ORDER BY comparison_type) as "Issue Types"
FROM schema_comparison_details
WHERE match_status = 'MISMATCH'
GROUP BY table_name
HAVING COUNT(*) > 0
ORDER BY COUNT(*) DESC;

PROMPT 
PROMPT =====================================================
PROMPT ROW COUNT DIFFERENCES (TOP 10)
PROMPT =====================================================

SELECT 
    table_name as "Table Name",
    source_value as "Source Rows",
    target_value as "Target Rows",
    ABS(TO_NUMBER(source_value) - TO_NUMBER(target_value)) as "Difference"
FROM schema_comparison_details
WHERE comparison_type = 'ROW_COUNT'
AND match_status = 'MISMATCH'
AND source_value != 'ERROR'
AND target_value != 'ERROR'
ORDER BY ABS(TO_NUMBER(source_value) - TO_NUMBER(target_value)) DESC
FETCH FIRST 10 ROWS ONLY;

PROMPT 
PROMPT =====================================================
PROMPT COMPARISON COMPLETED SUCCESSFULLY!
PROMPT =====================================================
PROMPT Results stored in:
PROMPT - schema_comparison_summary (summary statistics)
PROMPT - schema_comparison_details (detailed results)
PROMPT =====================================================
