package org.susarauto.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.susarauto.utils.constant.UtilConstant;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
@Configuration
@Slf4j
@EnableAutoConfiguration
@PropertySource("classpath:config.properties")
@ConfigurationProperties(prefix = "redis")
public class RedisUtil {
    private static JedisPool jedisPool;
    @Value("${redis.pool.max-active}")
    private static int maxActive;

    @Value("${redis.maxTotal}")
    private static int maxTotal;

    @Value("${redis.pool.max-wait}")
    private static int maxWait;

    @Value("${redis.pool.max-idle}")
    private static int maxIdle;

    @Value("${redis.pool.min-idle}")
    private static int minIdle;
    private static String password;
    @Value("${redis.host}")
    private  static String redisHost;
    @Value("${redis.port}")
    private  static int redisPort;
    private static final String TOKEN_PREFIX = UtilConstant.LOGIN_PREFIX;
    private static final int TOKEN_EXPIRE_TIME = 900000; // token过期时间，单位秒

    static {
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(100);
        config.setMaxIdle(maxIdle);
        config.setMaxWaitMillis(maxWait);
        config.setMaxTotal(maxActive);
        config.setMinIdle(minIdle);
        config.setTestOnBorrow(false);
        config.setTestOnReturn(false);
        config.setTestOnCreate(true);
        config.setBlockWhenExhausted(true);
        // 密码为空设置为null
        if (StringUtils.isEmpty(password)) {
            password = null;
        }
        config.setTestOnBorrow(true);
        jedisPool = new JedisPool(config, redisHost, redisPort);
    }
    public static Jedis getJedis() {
        return jedisPool.getResource();
    }

    public static void returnResource(Jedis jedis) {
        if (jedis != null) {
            jedis.close();
        }
    }


    public  boolean checkTokenExpiration(String token) {
        Long ttl = getJedis().ttl(token);
        return ttl != null && ttl > 0;
    }

    /**
     * 校验token是否有效
     *
     * @param token
     * @return
     */
    public static boolean checkToken(String token) {
        try (Jedis jedis = jedisPool.getResource()) {
            String userId = jedis.get(TOKEN_PREFIX + token);
            if (userId != null) {
                jedis.expire(TOKEN_PREFIX + token, TOKEN_EXPIRE_TIME);
                return true;
            }
        }
        return false;
    }

    /**
     * 根据用户id获取token
     *
     * @param userId
     * @return
     */
    public static String getToken(String userId) {
        try (Jedis jedis = jedisPool.getResource()) {
            String token = jedis.get(TOKEN_PREFIX + userId);
            if (token != null) {
                jedis.expire(TOKEN_PREFIX + token, TOKEN_EXPIRE_TIME);
                return token;
            }
        }
        return null;
    }

    public static void set(String prefix,String token){
        try( Jedis jedis = jedisPool.getResource()){
            jedis.setex(prefix,TOKEN_EXPIRE_TIME,token);
        }catch (RuntimeException e){
            RedisUtil.log.error(e.getMessage());
        }

    }
    /**
     * 刷新token的过期时间
     * @param userInfo 用户信息
     * @param TokenExpireTime
     */
    public static void refreshToken(String userInfo,int TokenExpireTime) {
        try (Jedis jedis = jedisPool.getResource()) {
            String token = jedis.get(userInfo);
            if (token != null) {
                jedis.expire(userInfo, TokenExpireTime);
            }
        }
    }
}


downloadPreviewFile

.catch(e => {
          loading.close()
          this.$message.error('导出查询结果失败!')
        })













eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJwYXNzd29yZCI6IiQyYSQxMCRMTnFXR3IxYWY2NElXVGpFaUF3WGx1UmkxNDVuMFEvbk9GM2M0VEo2TGhOSnVtcTAyQk9zLiIsImV4cCI6MTY4MDY5ODM5MCwidXNlcm5hbWUiOiJhZG1pbiJ9.q8XPR_4-ubM9f9a6Cw63XNp1esJljXCfJUdgwRnCmo0


$2a$10$LNqWGr1af64IWTjEiAwXluRi145n0Q/nOF3c4TJ6LhNJumq02BOs.




















      <el-table
        v-show="operateTableShow"
        id="out-table1"
        ref="operateTable"
        :data="operatetableData"
        style="width: 90%;"
        :default-sort="{prop: 'date', order: 'descending'}"
        @sort-change="sortChange"
      >
        <el-table-column
          label="操作人"
          prop="userName"
          class-name="user_name"
          sortable="custom"
          header-align="left"
          :formatter="formatter"
        />
        <el-table-column
          prop="email"
          sortable="custom"
          class-name="email"
          header-align="center"
          label="操作人邮箱"
        />
        <el-table-column
          prop="compound"
          label="化合物文件夹"
        />
        <el-table-column
          prop="operateType"
          label="操作类型"
          sortable="custom"
          class-name="operate_type"
        >
          <template slot-scope="scope">
            <el-tag v-if="scope.row.operateType === '上传'" type="success" effect="plain">上传</el-tag>
            <el-tag v-if="scope.row.operateType === '删除'" type="danger" effect="plain">删除</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="objectType"
          label="文件类型"
          sortable="custom"
          class-name="object_type"
        >
          <template slot-scope="scope">
            <el-tag v-if="scope.row.objectType === '文件'" type="success" effect="plain">文件</el-tag>
            <el-tag v-if="scope.row.objectType === '文件夹'" type="danger" effect="plain">文件夹</el-tag>
          </template>
        </el-table-column>
       <el-table-column
          prop="objectName"
          label="文件名称"
        />
        <el-table-column
          prop="operateTime"
          label="操作日期"
          sortable="custom"
          class-name="operate_time"
        />
      </el-table>





















