# DAG参数设置
dag_id = 'ftp_file_download'
schedule_interval = '@once' 

# 任务函数
def download_files(**kwargs):
    # 获取连接参数
    host = kwargs['host']
    user = kwargs['user']
    passwd = kwargs['passwd']
    remote_dir = kwargs['remote_dir']
    local_dir = kwargs['local_dir']
    
    # 连接ftpes服务器
    ftp_cmd = f'lftp {host} -u {user},{passwd}'
    os.system(ftp_cmd)
    
    # 设置本地目录和远程目录
    ftp_cmd = f'lcd {local_dir}' 
    os.system(ftp_cmd)
    ftp_cmd = f'cd {remote_dir}'
    os.system(ftp_cmd)
    
    # 下载匹配文件 
    ftp_cmd = f'mget -c -O *SHR*' 
    os.system(ftp_cmd)  
    
    # 退出ftp
    ftp_cmd = 'exit'
    os.system(ftp_cmd)

# 定义DAG  
dag = DAG(dag_id, schedule_interval=schedule_interval)

# 任务1:下载raw数据
task1 = PythonOperator(
    task_id ='download_raw', 
    python_callable = download_files,
    op_kwargs={
        'host': 'pengrui.hrglobe.cn',
        'user': 'PengRui-2021',
        'passwd': '***',
        'remote_dir': '/data/raw',
        'local_dir': '/data/ftp/raw'
    },
    dag=dag
) 