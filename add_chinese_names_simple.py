#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple script to add TABLE_NAME_CH column to Sheet2 in Excel file
Uses openpyxl to avoid pandas dependency
"""

try:
    from openpyxl import load_workbook
except ImportError:
    print("❌ openpyxl not available. Trying alternative approach...")
    import sys
    sys.exit(1)

import os

def add_chinese_names_to_sheet2(excel_file_path):
    """
    Add TABLE_NAME_CH column to Sheet2 with Chinese names from Sheet1
    """
    try:
        print("📖 Loading Excel file...")
        workbook = load_workbook(excel_file_path)
        
        # Get worksheets
        sheet1 = workbook['Sheet1']
        sheet2 = workbook['Sheet2']
        
        print("✅ Successfully loaded both sheets")
        
        # Read Sheet1 data to create mapping
        print("🔍 Analyzing Sheet1 structure...")
        
        # Get headers from Sheet1
        sheet1_headers = []
        for cell in sheet1[1]:
            if cell.value:
                sheet1_headers.append(cell.value)
        
        print("Sheet1 headers: {}".format(sheet1_headers))
        
        # Find table name and Chinese name columns in Sheet1
        table_name_col_idx = None
        chinese_name_col_idx = None
        
        for idx, header in enumerate(sheet1_headers):
            header_str = str(header).lower()
            if 'table' in header_str or 'name' in header_str:
                # Check some sample values to determine if it's table names
                sample_values = []
                for row in range(2, min(6, sheet1.max_row + 1)):
                    cell_value = sheet1.cell(row=row, column=idx+1).value
                    if cell_value:
                        sample_values.append(str(cell_value))
                
                print("Column '{}' sample values: {}".format(header, sample_values[:3]))
                
                # Check if this looks like table names
                if any('tbl_' in val.lower() for val in sample_values):
                    table_name_col_idx = idx
                    print("✅ Found table name column: {} (index {})".format(header, idx))
                
                # Check if this looks like Chinese names
                elif any(any('\u4e00' <= char <= '\u9fff' for char in val) for val in sample_values):
                    chinese_name_col_idx = idx
                    print("✅ Found Chinese name column: {} (index {})".format(header, idx))
        
        if table_name_col_idx is None:
            print("❌ Could not find table name column in Sheet1")
            return False
            
        if chinese_name_col_idx is None:
            print("❌ Could not find Chinese name column in Sheet1")
            return False
        
        # Create mapping dictionary from Sheet1
        print("📝 Creating mapping dictionary...")
        mapping_dict = {}
        
        for row in range(2, sheet1.max_row + 1):
            table_name = sheet1.cell(row=row, column=table_name_col_idx+1).value
            chinese_name = sheet1.cell(row=row, column=chinese_name_col_idx+1).value
            
            if table_name and chinese_name:
                mapping_dict[str(table_name).strip()] = str(chinese_name).strip()
        
        print("✅ Created mapping for {} tables".format(len(mapping_dict)))
        
        # Show sample mappings
        print("📋 Sample mappings:")
        for i, (k, v) in enumerate(list(mapping_dict.items())[:5]):
            print("  {} -> {}".format(k, v))
        
        # Analyze Sheet2 structure
        print("\n🔍 Analyzing Sheet2 structure...")
        
        # Get headers from Sheet2
        sheet2_headers = []
        for cell in sheet2[1]:
            if cell.value:
                sheet2_headers.append(cell.value)
        
        print("Sheet2 headers: {}".format(sheet2_headers))
        
        # Find table name column in Sheet2
        table_name_col_idx_sheet2 = None
        for idx, header in enumerate(sheet2_headers):
            header_str = str(header).lower()
            if 'table' in header_str and 'name' in header_str:
                table_name_col_idx_sheet2 = idx
                print("✅ Found table name column in Sheet2: {} (index {})".format(header, idx))
                break
        
        if table_name_col_idx_sheet2 is None:
            print("❌ Could not find table name column in Sheet2")
            return False
        
        # Add TABLE_NAME_CH header to Sheet2
        new_col_idx = len(sheet2_headers) + 1
        sheet2.cell(row=1, column=new_col_idx, value='TABLE_NAME_CH')
        print("✅ Added TABLE_NAME_CH header to column {}".format(new_col_idx))
        
        # Fill in Chinese names
        print("📝 Filling in Chinese names...")
        matches = 0
        total = 0
        
        for row in range(2, sheet2.max_row + 1):
            table_name = sheet2.cell(row=row, column=table_name_col_idx_sheet2+1).value
            total += 1
            
            if table_name:
                table_name_str = str(table_name).strip()
                chinese_name = mapping_dict.get(table_name_str, 'NA')
                if chinese_name != 'NA':
                    matches += 1
            else:
                chinese_name = 'NA'
            
            sheet2.cell(row=row, column=new_col_idx, value=chinese_name)
        
        print("✅ Matched {} out of {} tables ({:.1f}%)".format(matches, total, matches/total*100 if total > 0 else 0))
        
        # Save the updated file
        output_file = excel_file_path.replace('.xlsx', '_updated.xlsx')
        workbook.save(output_file)
        
        print("✅ Successfully added TABLE_NAME_CH column to Sheet2!")
        print("📁 Updated file saved as: {}".format(output_file))
        
        # Show sample results
        print("\n📋 Sample results:")
        for row in range(2, min(12, sheet2.max_row + 1)):
            table_name = sheet2.cell(row=row, column=table_name_col_idx_sheet2+1).value
            chinese_name = sheet2.cell(row=row, column=new_col_idx).value
            print("  {} -> {}".format(table_name, chinese_name))
        
        return True
        
    except Exception as e:
        print("❌ Error: {}".format(e))
        import traceback
        traceback.print_exc()
        return False

def main():
    excel_file = "meduap未同步表数据.xlsx"
    
    if not os.path.exists(excel_file):
        print("❌ File not found: {}".format(excel_file))
        return
    
    print("🔄 Processing file: {}".format(excel_file))
    success = add_chinese_names_to_sheet2(excel_file)
    
    if success:
        print("\n🎉 Process completed successfully!")
        print("📝 The TABLE_NAME_CH column has been added to Sheet2")
        print("💡 Chinese names are matched from Sheet1 based on TABLE_NAME")
    else:
        print("\n💥 Process failed!")

if __name__ == "__main__":
    main()
