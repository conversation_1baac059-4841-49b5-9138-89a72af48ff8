INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('UAP-20220302', 'UAP-20220302', 'UAP-20220302');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('UAP-20220302', 'E417', 'E4017R300集群全系统验证');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR7280', 'HR_A008', '多中心、随机、双盲、安慰剂平行对照评价SHR4640片联合非布司他片治疗经非布司他治疗未达标的原发性高尿酸血症受试者的降尿酸疗效与安全性研究');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('EDC-20220302', 'teststudy', 'test study');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR1020', 'HR_TEST_HOME1', 'EDC封面数据统计测试1');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR-7367', 'SHR-7367-I-101', 'SHR-7367注射液（FAP/CD40双抗）在晚期恶性肿瘤患者中的安全性、耐受性、药代动力学和疗效的I期临床研究');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR1020', 'Account test', '在线账号申请');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR-7367', 'SHR-7367-I-101', 'SHR-7367注射液（FAP/CD40双抗）在晚期恶性肿瘤患者中的安全性、耐受性、药代动力学和疗效的I期临床研究');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR1020', 'Account test', '在线账号申请');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR1020', 'RTSM_A001_WJ', 'RTSM_A001_WJ');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR17031', 'EDC-UAP TEST', 'EDC UAP测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR1020', 'SHR1020-I-201', 'SHR1020联合测试-CDTMS');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR1020', 'SHR1020-I-201', 'SHR1020联合测试-CDTMS');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR17031', 'EDC_001_CHN', 'EDC_001 整体流程测试(中文)');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR17031', 'EDC_002_CHN', 'EDC_002 整体流程测试(中文)');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR17031', 'EDC_004_EN', 'EDC_004 English Study');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR17031', 'EDC_005_EN', 'EDC_005 English Study');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR17031', 'RTSM_001_CHN', 'RTMS_001 整体流程测试(中文)');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR6390', 'SHR-6390-III-301', '用于SUSAR报告平台内部人员测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR6390', 'SHR-6390-III-301', '用于SUSAR报告平台内部人员测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR17031', 'RTSM_002_CHN', 'RTMS_002 整体流程测试(中文)');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR4640', 'SHR4640-301', '用于SUSAR报告平台内部人员测试01，多中心添加研究者');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR17031', 'RTSM_004_EN', 'RTSM_004 English Study');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR4640', 'SHR4640-301', '用于SUSAR报告平台内部人员测试01，多中心添加研究者');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR17031', 'RTSM_005_EN', 'RTSM_005 English Study');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR1020', 'UAP_TEST_001', 'UAP_TEST_001');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR6390', 'SHR-6390-209', 'SHR-6390-209');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR1020', 'UAP_TEST_001', 'UAP_TEST_001');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR1210', 'CDTMS_TEST_002', 'CDTMS_TEST_002 Study');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR19034', 'Test_Yan', 'test 123');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR19034', 'Test_Yan', 'test 123');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR070803', 'HR_A007_01', 'HR_A007_01阿美替尼联合SHR-1701等创新药物治疗EGFR突变的复发或晚期非小细胞肺癌开放、多中心的Ⅰb/Ⅱ期临床研究阿美替尼联合SHR-1701等创新药物治疗EGFR突变');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR8554', 'EDC_TEST_005', '多中心、开放、固定序列的SHR3680对地高辛（P-gp底物），瑞舒伐他汀钙（BCRP和/或OATP1B1/1B3底物）和盐酸二甲双胍（MATE1/2-K底物）在前列腺癌患者中的药代动力学影响研究');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR8554', 'RTSM_006_EN', 'RTSM_006_EN');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR8554', 'RTSM_007_EN', 'RTSM_007_EN');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR8554', 'RTSM_008_CHN', 'RTSM_008_CHN');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR8554', 'RTSM_009_CHN', 'RTSM_009_CHN');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR8554', 'RTSM_009_CHN', 'RTSM_009_CHN');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR-A1811、SHR-1316', 'SHR-A1811-II-202', '用于SUSAR报告平台内部人员测试02，联合用药');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR8554', 'RTSM_010_CHN', 'RTSM_010_CHN');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HRS-1358', 'HRS-1358-I-101', '用于SUSAR报告平台PV&CTA人员测试用001研究项目');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HRS9531', 'HRS9531-102', '用于SUSAR报告平台PV&CTA人员测试用002研究项目');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HRS9531', 'HRS9531-102', '用于SUSAR报告平台PV&CTA人员测试用002研究项目');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('INS068', 'INS068-301', '用于SUSAR报告平台PV&CTA人员测试用003研究项目');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR8554', 'RTSM_011_CHN', 'RTSM_011_CHN');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('INS068', 'INS068-301', '用于SUSAR报告平台PV&CTA人员测试用003研究项目');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR-1802', 'SHR-1802-II-201', '抗LAG-3抗体SHR-1802联合卡瑞利珠单抗及苹果酸法米替尼治疗晚期实体肿瘤的剂量探索、疗效拓展的Ⅰb/Ⅱ期研究');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR-A1811,SHR-1316', 'SHR-A1811-II-203', '用于SUSAR报告平台内部人员测试04，联合用药');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR8554', 'RTSM_011', 'RTSM_011');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR-1701,HRS-1358', 'SHR-1701-209', 'SUSAR平台测试（PV）');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR-1316', 'SHR-1316-322', 'SUSAR平台测试（PV）');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR8554', 'RTSM_011', 'RTSM_011');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR-1802', 'RTSM_012', 'RTSM_012');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('EDCTEST', 'HR18034-201', 'HR18034-201');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('EDCTEST', 'HR18034-201', 'HR18034-201');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('EDCTEST', 'Atest01', 'Atest01(对接EDC)');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('EDCTEST', 'Atest01', 'Atest01(对接EDC)');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('EDCTEST', 'Atest01', 'Atest01(对接EDC)');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR0410', 'RTSM_001', 'RTSM_001');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR20014,INS062', 'CTMS-eTMF', 'CTMS-eTMF测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR20014,INS062', 'CTMS-eTMF', 'CTMS-eTMF测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('EDCTEST', 'RTSM-TEST-0824', '1013测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR-1316(SHR-8068)', 'SHR-8068-2-201-HCC-1', '抗CTLA-4抗体SHR-8068联合阿得贝利单抗及贝伐珠单抗治疗晚期肝细胞癌的开放、多中心的b/期临床研究');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('1104', 'SHR-8068-Ⅱ-201-HCC-tst-1104', '(测试1111)抗CTLA-4抗体SHR-8068联合阿得贝利单抗及贝伐珠单抗治疗晚期肝细胞癌的开放、多中心的Ⅰb/Ⅱ期临床研究');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('1114', '1114Ⅱ', '1114Ⅱ');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('1209', '1209', '1209');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('1213', '1213', '1213');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('1104', 'INS068-302', 'INS068-302');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('1104', 'SHR-001-CMM', 'SHR-001-CMM（修改研究名称测试）');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A', 'SUSAR-301', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound  C', 'SUSAR-304', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR0302,SHR6390', 'SUSAR-305', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR0302,SHR6390', 'SUSAR-305', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR6390,APTN(YN968D1)', 'SHR6390-306', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('APTN(YN968D1),SHR0302', 'SUSAR-306', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('APTN(YN968D1),SHR0302', 'SUSAR-306', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR4640,SHR-1802', 'SUSAR-307', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HRS001,APTN(YN968D1)', 'SUSAR-308', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR18034', 'HRS8427-103', 'HRS8427-103');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR7777', 'HR7777-101', 'Test01 gzh');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR7777', 'HR7777-101', 'Test01 gzh');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR7777', 'HR7777-101_EN', 'Test01_EN_gzh');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR7777', 'HR7777-101_EN', 'Test01_EN_gzh');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR-7367-测试', 'SHR-7367-BY', 'SHR-7367-BY测试用');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR-7367-测试', 'EDC2323', '随机3.3.1新集群新项目下发测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR-7367-测试', 'EDC2323', '随机3.3.1新集群新项目下发测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('RTSM330TEST', 'EDC-001-UAP', 'UAP测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('RTSM330TEST', 'EDC-001-UAP', 'UAP测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR-7367-测试', 'EDC-003-UAP', 'EDC-003-UAP');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR-7367-测试', 'E421（1）', 'E421测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR7777', 'HR7777-102', '添加pv人员登录uap测试项目');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR7777', 'HR7777-102', '添加pv人员登录uap测试项目');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR7777', 'HR7777-102', '添加pv人员登录uap测试项目');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR-1707', 'SHR-1707-Dummy', 'Phase I study to evaluate the safety, tolerability, and pharmacokinetics of HRS-4642 in patients with advanced solid tumors harboring KRAS G12D mutation');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('20230713', '20230713', '20230713医学二部');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('20230713', '20230713', '20230713医学二部');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('20230717', '20230717', '20230717');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('20230717', '20230717', '20230717');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('20230719-1', '20230719-1', '20230719-1');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('20230720', '20230720', '镓[⁶⁸Ga]伊索曲肽用于生长抑素受体过表达的高分化胃肠胰神经内分泌瘤PET/CT诊断的前瞻性、单臂、盲态阅片、自身对照III期临床研究');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('20230720', '20230720', '镓[⁶⁸Ga]伊索曲肽用于生长抑素受体过表达的高分化胃肠胰神经内分泌瘤PET/CT诊断的前瞻性、单臂、盲态阅片、自身对照III期临床研究');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('TEST-1', 'SUSAR-TEST-101', 'SUSAR平台测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('TEST-1', 'SUSAR-TEST-101', 'SUSAR平台测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound A,Compound B', 'SUSAR-302', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('TEST-1', 'SUSAR-TEST-101', 'SUSAR平台测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('TEST-1', 'SUSAR-TEST-101', 'SUSAR平台测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('TEST-2', 'SUSAR-TEST-102', 'SUSAR平台测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('TEST-2', 'SUSAR-TEST-102', 'SUSAR平台测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('TEST-3', 'SUSAR-TEST-103', 'SUSAR平台测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('TEST-3', 'SUSAR-TEST-103', 'SUSAR平台测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('20230727', '20230727', '20230727');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('20230727', '20230727', '20230727');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('20230807', '20230807', '20230807');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('SHR-9839', 'SHR-9839-I-101', '注射用SHR-9839在晚期实体瘤患者中的安全性、耐受性、药代动力学及疗效的开放、多中心I期临床研究');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HRS001,APTN(YN968D1)', 'SUSAR-309', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HRS002', 'SUSAR-310', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HRS001,HRS002', 'SUSAR-311', 'SUSAR平台对接测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('EDCTEST', 'E4.1.9R3.3.6测试项目', 'E4.1.9R3.3.6测试项目');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('EDCTEST', 'E4.1.9R3.3.6测试项目', 'E4.1.9R3.3.6测试项目');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HRS001,HRS002', 'EDC-2023-01', 'EDC新版本测试创建的第一个新项目');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HRS001,HRS002', 'EDC-TEST-QLN-01', 'EDC-TEST-QLN-01');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HRS001,HRS002', 'EDC-TEST-DL-02', 'EDC-TEST-DL-02');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR7777', 'HR7777-105', '中心账号申请测试项目');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR7777', 'HR7777-105', '中心账号申请测试项目');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('HR7777', 'HR7777-I-104', '核查规则测试项目');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('Compound C,Compound A', 'SUSAR-303', 'SUSAR收件人数测试');
INSERT INTO `t_project_info`(`compound_no`, `study_no`, `study_name`) VALUES ('compoundTest,HRS002', 'localProTest', '本地项目测试');
