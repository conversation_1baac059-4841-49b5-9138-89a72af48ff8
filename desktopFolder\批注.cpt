<?xml version="1.0" encoding="UTF-8"?>
<WorkBook xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="ds1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[数据连接1]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT * FROM tb_clin_consis_01_5]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<ReportWebAttr>
<ServerPrinter/>
<WebWriteContent>
<Listener event="afterload">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[setTimeout(function() {
	document.getElementById('r-7-0').style.display = 'none';
	document.getElementById('r-6-0').style.display = 'none';
}, 50)]]></Content>
</JavaScript>
</Listener>
<Listener event="writesuccess">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[alert("填报成功！")
parent.FR.closeDialog(); //关闭对话框
window.parent.location.reload();//刷新父页面]]></Content>
</JavaScript>
</Listener>
<ToolBars>
<ToolBarManager>
<Location>
<Embed position="1"/>
</Location>
<ToolBar>
<Widget class="com.fr.report.web.button.write.Submit">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Report_Utils_Submit')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[submit]]></IconName>
<Verify failVerifySubmit="false" value="true"/>
<Sheet onlySubmitSelect="false"/>
</Widget>
<Widget class="com.fr.report.web.button.write.Verify">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Report_Verify_Data')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[verify]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.NewPrint">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Print')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[print]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.Export">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[PPT兼容模式]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[PPT]]></IconName>
<Buttons pdf="true" excelP="true" excelO="true" excelS="true" word="true" image="true" html="true"/>
</Widget>
<Widget class="com.fr.report.web.button.Email">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Report_Email')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[email]]></IconName>
<EmailButton customConsignee="true" consigneeByDepartment="false" consigneeByRole="false"/>
</Widget>
<Widget class="com.fr.report.web.button.write.AppendColumnRow">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Add_Record')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[appendrow]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.write.ShowCellValue">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue/>
<LabelAttr verticalcenter="true" textalign="0" autoline="false"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</Widget>
</ToolBar>
</ToolBarManager>
</ToolBars>
<EditRowColor setColor="false"/>
<WebWrite SheetPosition="3"/>
<RptLocation isShowAtLeft="true"/>
<UnloadCheck/>
<ShowWidgets/>
<OtherAttr autoStash="false"/>
</WebWriteContent>
</ReportWebAttr>
<ElementCaseMobileAttr>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
</ElementCaseMobileAttr>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet1">
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1440000,720000,723900,1152000,1152000,1152000,1152000,1152000,1152000,1152000,1152000,1152000,1152000,720000,723900,1440000,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[1440000,720000,3314700,2743200,2743200,1219200,2743200,2743200,720000,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="2">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="1" s="2">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="1" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="1" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="1" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="1" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="1" s="4">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="2" s="5">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" cs="2" s="6">
<O>
<![CDATA[添加批注]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="2" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="2" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="2" s="7">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="3" s="8">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="3" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="3" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="3" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="3" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="3" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="3" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" s="7">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="4" s="5">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="4" cs="2" s="9">
<O>
<![CDATA[请选择批注的级别]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="4" cs="3" s="0">
<PrivilegeControl/>
<Widget class="com.fr.form.ui.ComboBox">
<Listener event="afteredit" name="编辑后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="a"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=E5]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var a=this.getValue();
 if(FR.remoteEvaluate("find('中心层面','"+a+"')")>0)
{
	 
	document.getElementById('r-6-0').style.display = '';
	document.getElementById('r-7-0').style.display = 'none';
}
if(FR.remoteEvaluate("find('受试者层面','"+a+"')")>0)
{
	 
	document.getElementById('r-7-0').style.display = '';	
	document.getElementById('r-6-0').style.display = 'none';
}]]></Content>
</JavaScript>
</Listener>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<allowBlank>
<![CDATA[false]]></allowBlank>
<DirectEdit>
<![CDATA[false]]></DirectEdit>
<watermark>
<![CDATA[请选择批注的级别]]></watermark>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="中心层面" value="中心层面"/>
<Dict key="受试者层面" value="受试者层面"/>
</CustomDictAttr>
</Dictionary>
<widgetValue/>
</Widget>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="4" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="4" s="7">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="5" s="8">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="5" s="9">
<O>
<![CDATA[批注等级]]></O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="5" s="9">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(E5 = "中心层面",1,2)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="5" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="5" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="5" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="5" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="5" s="7">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="6" s="8">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="6" cs="2" s="9">
<O>
<![CDATA[请选择中心代码]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="6" cs="3" s="0">
<PrivilegeControl/>
<Widget class="com.fr.form.ui.ComboBox">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<DirectEdit>
<![CDATA[false]]></DirectEdit>
<watermark>
<![CDATA[请选择中心代码]]></watermark>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="01" value="01"/>
<Dict key="02" value="02"/>
</CustomDictAttr>
</Dictionary>
<widgetValue/>
</Widget>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="6" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="6" s="7">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="7" s="8">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="7" cs="2" s="9">
<O>
<![CDATA[请选择受试者代码]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="7" cs="3" s="0">
<PrivilegeControl/>
<Widget class="com.fr.form.ui.ComboBox">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<DirectEdit>
<![CDATA[false]]></DirectEdit>
<watermark>
<![CDATA[请选择其余代码]]></watermark>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="03" value="03"/>
<Dict key="04" value="04"/>
</CustomDictAttr>
</Dictionary>
<widgetValue/>
</Widget>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="7" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="7" s="7">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="8" s="5">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="8" s="9">
<O>
<![CDATA[请填写批注：]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="8" s="10">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="8" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="8" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="8" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="8" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="8" s="7">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="9" s="8">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="9" cs="6" rs="4" s="11">
<PrivilegeControl/>
<Widget class="com.fr.form.ui.TextArea">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<allowBlank>
<![CDATA[false]]></allowBlank>
<TextAttr allowBlank="false"/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue/>
<MobileScanCodeAttr scanCode="false" textInputMode="1" isSupportManual="true" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
</Widget>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="9" s="7">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="10" s="8">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="10" s="7">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="11" s="8">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="11" s="7">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="12" s="8">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="12" s="7">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="13" s="12">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="13" s="13">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="13" s="13">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="13" s="13">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="13" s="13">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="13" s="13">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="13" s="13">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="13" s="14">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="15" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SPLIT($a,",")]]></Attributes>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B16"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="16" s="0">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="id"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[UUID]]></CNAME>
<Compare op="0">
<ColumnRow column="1" row="15"/>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B17"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting/>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
<Header reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Header>
<Footer reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Footer>
</ReportAttrSet>
<ReportWriteAttr>
<SubmitVisitor class="com.fr.report.write.BuiltInSQLSubmiter">
<Name>
<![CDATA[内置SQL1]]></Name>
<Attributes dsName="swpublic(优先使用该库进行demo制作)"/>
<DMLConfig class="com.fr.write.config.IntelliDMLConfig">
<Table schema="" name="tb_comment"/>
<ColumnConfig name="id" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=RANDBETWEEN(1,100000)]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="level" isKey="false" skipUnmodified="false">
<ColumnRow column="3" row="5"/>
</ColumnConfig>
<ColumnConfig name="comments" isKey="false" skipUnmodified="false">
<ColumnRow column="2" row="9"/>
</ColumnConfig>
<ColumnConfig name="UUID" isKey="true" skipUnmodified="false">
<ColumnRow column="1" row="15"/>
</ColumnConfig>
<ColumnConfig name="levelvalue" isKey="false" skipUnmodified="false">
<ColumnRow column="4" row="4"/>
</ColumnConfig>
<ColumnConfig name="creater" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$fine_username]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="create_time" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NOW()]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="deleted" isKey="false" skipUnmodified="false">
<O>
<![CDATA[0]]></O>
</ColumnConfig>
<ColumnConfig name="pid" isKey="false" skipUnmodified="false">
<ColumnRow column="1" row="16"/>
</ColumnConfig>
<Condition class="com.fr.data.condition.ListCondition"/>
</DMLConfig>
</SubmitVisitor>
<TopVerifier class="com.fr.report.write.ValueVerifier">
<name>
<![CDATA[内置校验1]]></name>
<VerifyItem class="com.fr.data.VerifyItem">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=LEN(G7) + LEN(G8) > 0]]></Attributes>
</O>
<message>
<![CDATA[="未选择代码"]]></message>
</VerifyItem>
</TopVerifier>
<TopVerifier class="com.fr.report.write.ValueVerifier">
<name>
<![CDATA[内置校验2]]></name>
<VerifyItem class="com.fr.data.VerifyItem">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=len(b16)>0]]></Attributes>
</O>
<message>
<![CDATA[="未选择报告"]]></message>
</VerifyItem>
</TopVerifier>
</ReportWriteAttr>
<PrivilegeControl/>
</Report>
<ReportParameterAttr>
<Attributes showWindow="true" delayPlaying="true" windowPosition="1" align="0" useParamsTemplate="true" currentIndex="0"/>
<PWTitle>
<![CDATA[参数]]></PWTitle>
</ReportParameterAttr>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="2">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Top>
<Left style="2">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Left>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="2">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Top>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="2">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Top>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="2">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Top>
<Right style="2">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Right>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Left style="2">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Left>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Right style="2">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Right>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Left style="2">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Left>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="1" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Top>
<Bottom style="1">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Bottom>
<Left style="1">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Left>
<Right style="1">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Right>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Bottom style="2">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Bottom>
<Left style="2">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Left>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Bottom style="2">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Bottom>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Bottom style="2">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Bottom>
<Right style="2">
<color>
<FineColor color="-6119272" hor="3" ver="0"/>
</color>
</Right>
</Border>
</Style>
</StyleList>
<DesensitizationList/>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="1"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典浅灰" dark="false"/>
</TemplateThemeAttrMark>
<FileAttrErrorMarker-StrongestControlAttr class="com.fr.base.io.FileAttrErrorMarker" pluginID="com.fr.plugin.strongest.control" plugin-version="1.0.1" oriClass="com.fr.plugin.strongest.control.attr.StrongestControlMarkAttr">
<StrongestControlAttr widgetEnhance="true"/>
</FileAttrErrorMarker-StrongestControlAttr>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1695483660135"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="9731e14d-c822-4048-b9ca-0e0108608032"/>
</TemplateIdAttMark>
</WorkBook>
