#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Aggressive JSON fixer that handles control characters and Chinese encoding
"""

import json
import re
import codecs

def aggressive_clean_json(content):
    """
    Aggressively clean JSON content
    """
    # First, let's handle the multiline strings in JavaScript code
    # Find patterns like "forminitjs": "..." that span multiple lines
    
    def fix_multiline_string(match):
        field_name = match.group(1)
        string_content = match.group(2)
        
        # Escape newlines and other control characters
        string_content = string_content.replace('\n', '\\n')
        string_content = string_content.replace('\r', '\\r')
        string_content = string_content.replace('\t', '\\t')
        string_content = string_content.replace('"', '\\"')
        
        return f'"{field_name}": "{string_content}"'
    
    # Pattern to match multiline string fields
    pattern = r'"(forminitjs|formvaljs|appendhtml)"\s*:\s*"([^"]*(?:\\.[^"]*)*)"'
    
    # This is tricky with multiline strings, so let's try a different approach
    # Split by lines and process each line
    lines = content.split('\n')
    cleaned_lines = []
    in_multiline_string = False
    current_field = None
    string_buffer = []
    
    for line in lines:
        # Check if we're starting a multiline string field
        if not in_multiline_string:
            # Look for the start of problematic fields
            match = re.match(r'\s*"(forminitjs|formvaljs|appendhtml)"\s*:\s*"(.*)$', line)
            if match:
                field_name = match.group(1)
                string_start = match.group(2)
                
                # Check if the string ends on the same line
                if string_start.endswith('",') or string_start.endswith('"'):
                    # Single line string, just clean it
                    cleaned_string = string_start.replace('\t', '\\t').replace('\r', '\\r')
                    cleaned_lines.append(f'        "{field_name}": "{cleaned_string}')
                else:
                    # Start of multiline string
                    in_multiline_string = True
                    current_field = field_name
                    string_buffer = [string_start]
            else:
                cleaned_lines.append(line)
        else:
            # We're in a multiline string
            if line.strip().endswith('",') or line.strip().endswith('"'):
                # End of multiline string
                string_buffer.append(line.rstrip('",').rstrip('"'))
                
                # Join and clean the string
                full_string = '\\n'.join(string_buffer)
                full_string = full_string.replace('\t', '\\t').replace('\r', '\\r')
                
                # Add the cleaned string
                if line.strip().endswith('",'):
                    cleaned_lines.append(f'        "{current_field}": "{full_string}",')
                else:
                    cleaned_lines.append(f'        "{current_field}": "{full_string}"')
                
                # Reset state
                in_multiline_string = False
                current_field = None
                string_buffer = []
            else:
                # Continue collecting the string
                string_buffer.append(line)
    
    return '\n'.join(cleaned_lines)

def simple_fix_approach(input_file, output_file=None):
    """
    Simple approach: remove problematic fields and keep only basic ones
    """
    if output_file is None:
        output_file = input_file.replace('.txt', '_simple_fixed.json')
    
    try:
        with codecs.open(input_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Try to extract just the basic structure
        # Remove problematic fields that contain JavaScript
        content = re.sub(r'"forminitjs"\s*:\s*"[^"]*(?:\\.[^"]*)*",?\s*', '', content, flags=re.DOTALL)
        content = re.sub(r'"formvaljs"\s*:\s*"[^"]*(?:\\.[^"]*)*",?\s*', '', content, flags=re.DOTALL)
        content = re.sub(r'"appendhtml"\s*:\s*"[^"]*(?:\\.[^"]*)*",?\s*', '', content, flags=re.DOTALL)
        content = re.sub(r'"listeditforminitjs"\s*:\s*"[^"]*(?:\\.[^"]*)*",?\s*', '', content, flags=re.DOTALL)
        
        # Clean up any remaining control characters
        content = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', content)
        
        # Fix trailing commas before closing braces
        content = re.sub(r',(\s*[}\]])', r'\1', content)
        
        try:
            data = json.loads(content)
            
            with codecs.open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"Simple fix successful! File saved as: {output_file}")
            return True
            
        except json.JSONDecodeError as e:
            print(f"Simple fix failed: {e}")
            return False
            
    except Exception as e:
        print(f"Error in simple fix: {e}")
        return False

def manual_reconstruction(input_file, output_file=None):
    """
    Manually reconstruct the JSON by extracting key fields
    """
    if output_file is None:
        output_file = input_file.replace('.txt', '_reconstructed.json')
    
    try:
        with codecs.open(input_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Extract individual records using regex
        records = []
        
        # Find all record blocks
        record_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.findall(record_pattern, content, re.DOTALL)
        
        for match in matches:
            try:
                # Extract basic fields from each record
                record = {}
                
                # Extract id
                id_match = re.search(r'"id"\s*:\s*"([^"]*)"', match)
                if id_match:
                    record['id'] = id_match.group(1)
                
                # Extract name (Chinese)
                name_match = re.search(r'"name"\s*:\s*"([^"]*)"', match)
                if name_match:
                    record['name'] = name_match.group(1)
                
                # Extract nameen (English)
                nameen_match = re.search(r'"nameen"\s*:\s*"([^"]*)"', match)
                if nameen_match:
                    record['nameen'] = nameen_match.group(1)
                
                # Extract note
                note_match = re.search(r'"note"\s*:\s*"([^"]*)"', match)
                if note_match:
                    record['note'] = note_match.group(1)
                
                # Extract uuid
                uuid_match = re.search(r'"uuid"\s*:\s*"([^"]*)"', match)
                if uuid_match:
                    record['uuid'] = uuid_match.group(1)
                
                if record.get('id'):  # Only add if we have at least an id
                    records.append(record)
                    
            except Exception as e:
                print(f"Error processing record: {e}")
                continue
        
        if records:
            with codecs.open(output_file, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)
            
            print(f"Manual reconstruction successful! {len(records)} records saved as: {output_file}")
            return True
        else:
            print("No valid records found during manual reconstruction")
            return False
            
    except Exception as e:
        print(f"Error in manual reconstruction: {e}")
        return False

if __name__ == "__main__":
    input_file = "json.txt"
    
    print("Trying simple fix approach...")
    if not simple_fix_approach(input_file):
        print("Simple fix failed. Trying manual reconstruction...")
        manual_reconstruction(input_file)
