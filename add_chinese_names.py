#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON><PERSON> to add TABLE_NAME_CH column to Sheet2 in Excel file
Matches TABLE_NAME from Sheet2 with Sheet1 to get Chinese names
"""

import pandas as pd
import sys
import os

def add_chinese_names_to_sheet2(excel_file_path):
    """
    Add TABLE_NAME_CH column to Sheet2 with Chinese names from Sheet1
    
    Args:
        excel_file_path (str): Path to the Excel file
    """
    try:
        # Read both sheets
        print("Reading Excel file...")
        sheet1_df = pd.read_excel(excel_file_path, sheet_name='Sheet1')
        sheet2_df = pd.read_excel(excel_file_path, sheet_name='Sheet2')
        
        print("Sheet1 shape: {}".format(sheet1_df.shape))
        print("Sheet2 shape: {}".format(sheet2_df.shape))

        # Display column names to understand the structure
        print("\nSheet1 columns:")
        print(sheet1_df.columns.tolist())
        print("\nSheet2 columns:")
        print(sheet2_df.columns.tolist())

        # Display first few rows to understand the data structure
        print("\nSheet1 first 5 rows:")
        print(sheet1_df.head())
        print("\nSheet2 first 5 rows:")
        print(sheet2_df.head())
        
        # Find the column that contains table names in Sheet1
        # Look for columns that might contain table names
        table_name_col_sheet1 = None
        chinese_name_col_sheet1 = None
        
        for col in sheet1_df.columns:
            if 'table' in col.lower() or 'name' in col.lower():
                print("Potential table name column in Sheet1: {}".format(col))
                if sheet1_df[col].dtype == 'object':  # String column
                    sample_values = sheet1_df[col].dropna().head(3).tolist()
                    print("Sample values: {}".format(sample_values))

                    # Check if values look like table names
                    if any('tbl_' in str(val).lower() for val in sample_values):
                        table_name_col_sheet1 = col
                        print("Found table name column in Sheet1: {}".format(col))
        
        # Find Chinese name column in Sheet1
        for col in sheet1_df.columns:
            if '中文' in col or '名' in col or col != table_name_col_sheet1:
                if sheet1_df[col].dtype == 'object':  # String column
                    sample_values = sheet1_df[col].dropna().head(3).tolist()
                    # Check if values contain Chinese characters
                    if any(any('\u4e00' <= char <= '\u9fff' for char in str(val)) for val in sample_values):
                        chinese_name_col_sheet1 = col
                        print("Found Chinese name column in Sheet1: {}".format(col))
                        break

        # Find table name column in Sheet2
        table_name_col_sheet2 = None
        for col in sheet2_df.columns:
            if 'table' in col.lower() and 'name' in col.lower():
                table_name_col_sheet2 = col
                print("Found table name column in Sheet2: {}".format(col))
                break
        
        if not table_name_col_sheet1:
            print("Could not find table name column in Sheet1")
            return False
            
        if not chinese_name_col_sheet1:
            print("Could not find Chinese name column in Sheet1")
            return False
            
        if not table_name_col_sheet2:
            print("Could not find table name column in Sheet2")
            return False
        
        print("\nUsing columns:")
        print("Sheet1 - Table names: {}".format(table_name_col_sheet1))
        print("Sheet1 - Chinese names: {}".format(chinese_name_col_sheet1))
        print("Sheet2 - Table names: {}".format(table_name_col_sheet2))

        # Create a mapping dictionary from Sheet1
        print("\nCreating mapping dictionary...")
        mapping_dict = {}
        for idx, row in sheet1_df.iterrows():
            table_name = row[table_name_col_sheet1]
            chinese_name = row[chinese_name_col_sheet1]
            if pd.notna(table_name) and pd.notna(chinese_name):
                mapping_dict[str(table_name).strip()] = str(chinese_name).strip()

        print("Created mapping for {} tables".format(len(mapping_dict)))
        print("Sample mappings:")
        for i, (k, v) in enumerate(list(mapping_dict.items())[:5]):
            print("  {} -> {}".format(k, v))
        
        # Add TABLE_NAME_CH column to Sheet2
        print("\nAdding TABLE_NAME_CH column to Sheet2...")
        
        def get_chinese_name(table_name):
            if pd.isna(table_name):
                return 'NA'
            table_name_str = str(table_name).strip()
            return mapping_dict.get(table_name_str, 'NA')
        
        sheet2_df['TABLE_NAME_CH'] = sheet2_df[table_name_col_sheet2].apply(get_chinese_name)
        
        # Count matches
        matches = sum(1 for val in sheet2_df['TABLE_NAME_CH'] if val != 'NA')
        total = len(sheet2_df)
        print("Matched {} out of {} tables ({:.1f}%)".format(matches, total, matches/total*100))

        # Show some results
        print("\nSample results:")
        result_sample = sheet2_df[[table_name_col_sheet2, 'TABLE_NAME_CH']].head(10)
        print(result_sample)

        # Save the updated Excel file
        output_file = excel_file_path.replace('.xlsx', '_updated.xlsx')
        print("\nSaving updated file to: {}".format(output_file))

        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            sheet1_df.to_excel(writer, sheet_name='Sheet1', index=False)
            sheet2_df.to_excel(writer, sheet_name='Sheet2', index=False)

        print("✅ Successfully added TABLE_NAME_CH column to Sheet2!")
        print("📁 Updated file saved as: {}".format(output_file))

        return True

    except Exception as e:
        print("❌ Error: {}".format(e))
        import traceback
        traceback.print_exc()
        return False

def main():
    excel_file = "meduap未同步表数据.xlsx"
    
    if not os.path.exists(excel_file):
        print("❌ File not found: {}".format(excel_file))
        return

    print("🔄 Processing file: {}".format(excel_file))
    success = add_chinese_names_to_sheet2(excel_file)
    
    if success:
        print("\n🎉 Process completed successfully!")
    else:
        print("\n💥 Process failed!")

if __name__ == "__main__":
    main()
