﻿<%@ page contentType="text/html;charset=utf-8" %>
<%@ page language="java" pageEncoding="utf-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="/webplug/help/help-tlib.tld" prefix="h" %>
<%@ taglib uri="/webutil/tlib/bioknow-tlib.tld" prefix="bt" %>

<% response.setHeader("Pragma", "no-cache"); %>

<html>
<head>

    <title>${subject}</title>


    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-Control" content="no-cache">
    <script src="/public/lang/uaplang_zh.js?v=1655722155194" type="text/javascript">
    </script>
    <%--    <script src="/webutil/js/jquery/jquery.pack.js?v=1655722155194" type="text/javascript">--%>
    <script type="text/javascript" src="/public/js/jquery-3.2.1.min.js"></script>
    <script src="/webutil/js/cookie.js?v=1655722155194" type="text/javascript">
    </script>
    <script src="/webutil/js/locale.js?v=1655722155194" type="text/javascript">
    </script>
    <script type="text/javascript">var g_locale = getLocale("test");</script>
    <script src="/webutil/js/winopen.js?v=1655722155194" type="text/javascript">
    </script>
    <script src="/webutil/js/popupdiv.js?v=1655722155194" type="text/javascript">
    </script>
    <script src="/webplug/notice/selectuser.js?v=1655722155194" type="text/javascript">
    </script>
    <script type="text/javascript" src="/webplug/ckeditor/ckeditor.js?v=1655722155194">
    </script>
    <script type="text/javascript" src="/webplug/ckeditor/config.js?v=1655722155194">
    </script>
    <script type="text/javascript" src="/webplug/ckeditor/config.js?t=H8DA">
    </script>
    <link rel="stylesheet" type="text/css" href="/webplug/ckeditor/skins/moono-lisa/editor.css?t=H8DA">
    <script type="text/javascript" src="/webplug/ckeditor/lang/zh-cn.js?t=H8DA">
    </script>
    <script type="text/javascript" src="/webplug/ckeditor/styles.js?t=H8DA">
    </script>
    <!--核心样式-->
    <script src="/cdtms/formMail/js/xm-select.js"></script>
    <script src="/cdtms/formMail/js/flatpickr.js"></script>
    <link rel="stylesheet" href="/cdtms/formMail/js/flatpickr.min.css">


    <%--    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">--%>
    <%--    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>--%>


    <link rel="stylesheet" type="text/css"
          href="/webplug/ckeditor/plugins/scayt/skins/moono-lisa/scayt.css?v=1655722155194">
    <link rel="stylesheet" type="text/css" href="/webplug/ckeditor/plugins/scayt/dialogs/dialog.css?v=1655722155194">
    <link rel="stylesheet" type="text/css"
          href="/webplug/ckeditor/plugins/tableselection/styles/tableselection.css?v=1655722155194">
    <link rel="stylesheet" type="text/css"
          href="/webplug/ckeditor/plugins/wsc/skins/moono-lisa/wsc.css?v=1655722155194">
    <link rel="stylesheet" type="text/css" href="/public/bootstrap/css/bootstrap.min.css?v=1655722155194">
    <link rel="stylesheet" type="text/css" href="/public/css/public.css?v=1655722155194">
    <link rel="stylesheet" type="text/css" href="/css/notice.css?v=1655722155194">

    <style>
        #cke_1_contents {
            height: 350px !important;
        }

    </style>


</head>
<body style="width:100%;padding-left:50px;padding-right:50px;box-sizing:border-box;">


<form method="post" id="mailform"
      onsubmit="if($('#receiver').val()==''){alert('收件人不能为空！');return false;}if($('#subject').val()==''){alert('主题不能为空！')}">
    <table class="table table-xs" id="sendmail_table" style="width:100%;">
        <tbody>
        <tr>
            <td nowrap=""><bt:lang name="To"/>:</td>
            <td>
                <div id="receiver" class="xm-select-demo"></div>
            </td>
        </tr>
        <c:if test="${isSign != '1'}">

            <tr>
                <td nowrap=""><bt:lang name="Cc"/>：</td>
                <td>
                    <div id="cc" class="xm-select-demo"></div>
                </td>
            </tr>
        </c:if>
        <tr>
            <td nowrap=""><bt:lang name="Subject"/>：</td>
            <td>
                <input name="subject" id="subject" type="input" style="width:740px" value="${subject}">
            </td>
        </tr>
        <c:if test="${isSign == '1'}">

            <tr>
                <td nowrap=""><bt:lang name="Languages"/>：</td>
                <td>
                   <select name="signLanguage" id="signLanguage" style="width:740px">
                       <option value="cn" selected><bt:lang name="cn"/></option>
                        <option value="en"><bt:lang name="en"/></option>
                   </select>
                </td>
            </tr>
            <tr>
                <td nowrap=""><bt:lang name="Deadline"/>：</td>
                <td>
                    <input name="signFlowExpireTime" id="signFlowExpireTime" type="input" style="width:740px">
                </td>
            </tr>
            <tr>

                <td nowrap=""><bt:lang name="documentsToBeSigned"/>：</td>

                <td>


                    <c:forEach items="${eSignFileList}" var="eSignFileList" varStatus="eSignFileListStatus">
                        <div>
                            <input type="radio" name="signfile"
                                   value="${eSignFileList.filename}*${eSignFileList.file_uuid}"

                            <c:if test="${eSignFileListStatus.first}">
                                   checked
                            </c:if>
                            > <a target="_blank"
                                 href="ff_showpdf2swf.show.do?tableid=${tableid}&fieldid=file&filename=${eSignFileList.file_uuid}&ap=true">${eSignFileList.filename}</a>
                        </div>
                    </c:forEach>
                </td>
            </tr>


            <c:if test="${not empty signPageFileList}">
                <tr>
                    <td nowrap=""><bt:lang name="signaturePage"/>：</td>
                    <td>

                        <c:forEach items="${signPageFileList}" var="signPageFileList"
                                   varStatus="signPageFileListStatus">
                            <div>
                                <input type="radio" name="signpagefile"
                                       value="${signPageFileList.filename}*${signPageFileList.file_uuid}"

                                <c:if test="${signPageFileListStatus.first}">
                                       checked
                                </c:if>
                                > <a target="_blank"
                                     href="ff_showpdf2swf.show.do?tableid=${tableid}&fieldid=file&filename=${signPageFileList.file_uuid}&ap=true">${signPageFileList.filename}</a>
                            </div>
                        </c:forEach>


                            <%--                <a target="_blank" href="ff_showpdf2swf.show.do?tableid=${tableid}&fieldid=file&filename=${signPageFileMap.file_uuid}&ap=true">${signPageFileMap.filename}</a>--%>
                    </td>
                </tr>
                </div>
            </c:if>


        </c:if>
        <td>

            <tr>
            <tr>
                <td valign="top" nowrap="" colspan="2">
          <textarea cols="80" id="content" name="content" rows="10">
          </textarea>
                    <script type="text/javascript">//<![CDATA[
                    CKEDITOR.replace('content');
                    //]]>
                    </script>
                </td>
            </tr>
            <tr>
                <td nowrap=""><bt:lang name="Attachments"/>：</td>
                <td>
                    ${fileCheckbox}
                </td>

            </tr>
            <tr>
                <td colspan="2" align="center">

                    <input type="button" id="sendMailSubmit" class="btn btn-sm btn-radius btn-yellow" value="<bt:lang name="Send"/>">

                </td>
            </tr>
        </tbody>
    </table>
</form>

</body>


<c:if test="${isSign == '1'}">

    <script type="text/javascript">
        var threeDaysFromNow = new Date();
        threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
        var month = threeDaysFromNow.getMonth() + 1;
        var day = threeDaysFromNow.getDate();
        month = (month.toString().length == 1) ? ("0" + month) : month;
        day = (day.toString().length == 1) ? ("0" + day) : day;
        var defaultDate = threeDaysFromNow.getFullYear() + '-' + month + '-' + day + " 15:00";

        const signFlowExpireTime = flatpickr("#signFlowExpireTime", {
            enableTime: true,
            dateFormat: "Y-m-d H:i:S",
            minDate: "today",
            defaultHour: "15",
            defaultdefaultMinute: "00",
            locale: "zh",
            defaultDate: defaultDate
        });

    </script>
</c:if>


<script type="text/javascript">


    var content = `<%  out.print(request.getAttribute("content").toString().replaceAll("[\\t\\n\\r]", "<br>"));%>`;

    CKEDITOR.instances.content.setData(content);

    $("#sendMailSubmit").click(function () {

        debugger;
        var $vm = $(this);
        $(this).prop('disabled', true);

        var fileSelectedValues = [];

        $('input[type="checkbox"][name="files"]:checked').each(function () {
            fileSelectedValues.push($(this).val());
        });




        $.ajax({
            type: "POST",
            url: "/formMail.sendMail.do",
            traditional: true,
            data: {
                recordid: "${recordid}",
                isCompress: "${isCompress}",
                tableid: "${tableid}",
                studyid: "${studyid}",
                email_from: "${email_from}",
                <c:if test="${isSign != '1'}">
                cc: cc.getValue("valueStr"),
                </c:if>
                receiver: receiver.getValue("valueStr"),
                subject: $('#subject').val(),
                fileSelected: fileSelectedValues.join(','),
                content: CKEDITOR.instances.content.getData()
                <c:if test="${isSign == '1'}">
                ,
                                <c:if test="${not empty signPageFileMap}">
                                signPage:"${signPageFileMap.filename}*${signPageFileMap.file_uuid}",
                                </c:if>

                needSigin: 1,
                signfuncid:"${signfuncid}",
                signLanguage:$("#signLanguage").val(),
                isSign: "${isSign}",
                signersJson: JSON.stringify(receiver.getValue(), null, 2),
                eSigndata: JSON.stringify($('#mailform').serializeObject())
                </c:if>
                <c:if test="${isSign == '2'}">
                ,
                <%--                <c:if test="${not empty signPageFileMap}">--%>
                <%--                signPage:"${signPageFileMap.filename}*${signPageFileMap.file_uuid}",--%>
                <%--                </c:if>--%>

                isSign: "${isSign}",
                </c:if>


            },
            // 序列化表单值
            async: false,
            error: function (request) { //失败的话
                alert("Connection error");
                $vm.prop('disabled', false);

            },
            success: function (data) { //成功

                <c:if test="${isSign == '1'}">

                if (data.status == 200) {
                   window.open(data.data.setUrl);
                    window.location.href = "/LightpdfSignIntergrate.View.do?recordid=${param.recordid}&tableid=${param.tableid}";
                    return;
                }
                </c:if>

                $vm.prop('disabled', false);
                window.top.notifySuccess(data,false);
                window.top.webmask_close_modal();

            }
        });
    });
    /**
     * 自动将form表单封装成json对象formMail
     extdatabind
     */
    $.fn.serializeObject = function () {
        var o = {};
        var a = this.serializeArray();
        $.each(a, function () {
            if (o[this.name]) {
                if (!o[this.name].push) {
                    o[this.name] = [o[this.name]];
                }
                o[this.name].push(this.value || '');
            } else {
                o[this.name] = this.value || '';
            }
        });
        return o;
    };

</script>
<script>
    var receiver = xmSelect.render({
        el: '#receiver',
        language: 'zn',
        searchTips: '搜索',
        filterable: true,
        toolbar: {
            show: true,
        },
        data:
            [
                ${listRec}
            ]
    })
    var cc = xmSelect.render({
        el: '#cc',
        language: 'zn',
        searchTips: '搜索',
        filterable: true,
        toolbar: {
            show: true,
        },
        data:
            [
                ${listCc}
            ]
    })


</script>
</html>
