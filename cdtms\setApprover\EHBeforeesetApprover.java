package net.bioknow.cdtms.setApprover;

import net.bioknow.cdtms.extdatabind.Actionextdatabind;
import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.face.BeforeSaveFace;
import net.bioknow.uap.schemaplug.dtref.DtrefDAO;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EHBeforeesetApprover implements BeforeSaveFace {


	@Override
	public boolean isTableTrigEvent(String tableid, String projectId) {
		try {

			DAODbApi daoDbApi = new DAODbApi(projectId);



			List<Map<String, Object>> listFieldByTableId = daoDbApi.getListFieldByTableId(tableid);

			return listFieldByTableId.stream()
					.anyMatch(map -> StringUtils.equals("approver",(String)map.get("id")));



		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}

	@Override
	public String onAdd(String tableid, Map valueMap, String projectId) {
		try {

			String userloginid = SessUtil.getSessInfo().getUserloginid();
			Map userMap = SessUtil.getSessInfo().getUser();


			DtrefDAO dtrefDAO = new DtrefDAO(projectId);
			String RefField = dtrefDAO.getRefField("xsht", tableid);
			DAODataMng daoDataMng = new DAODataMng(projectId);

			Long studyid = null;

			if (StringUtils.isNotEmpty(RefField)) {

				studyid = (Long) valueMap.get(RefField);
			}
			if (ObjectUtils.isNotEmpty(studyid)) {


				List<Map> studyUersList = daoDataMng.listRecord("roles", "obj.limitnum='TDM' and obj.studyid='" + studyid + "' and obj.active='1'", null, 1);

				if (CollectionUtils.isNotEmpty(studyUersList)) {

					Long tdmUserId = (Long) studyUersList.get(0).get("member");
					String tdmloginid = (String) daoDataMng.getRecord("ryjbzl", tdmUserId).get("loginid");
					List<Map> tdmInfoList = daoDataMng.listRecord("ryjl", "obj.email='" + tdmloginid + "'", null, 1);

					if (CollectionUtils.isNotEmpty(tdmInfoList)) {
						String tdmManagername = (String) tdmInfoList.get(0).get("managername");
						List<Map> tdmManagerAccountList = daoDataMng.listRecord("ryjbzl", "obj.xm='" + tdmManagername + "'", null, 1);
						if (CollectionUtils.isNotEmpty(tdmManagerAccountList)) {

							valueMap.put("approver", tdmManagerAccountList.get(0).get("id"));
						}
					}

				}

			}else{

				List<Map> tdmInfoList = daoDataMng.listRecord("ryjl", "obj.email='" + userloginid + "'", null, 1);

				if (CollectionUtils.isNotEmpty(tdmInfoList)) {
					String tdmManagername = (String) tdmInfoList.get(0).get("managername");
					List<Map> tdmManagerAccountList = daoDataMng.listRecord("ryjbzl", "obj.xm='" + tdmManagername + "'", null, 1);
					if (CollectionUtils.isNotEmpty(tdmManagerAccountList)) {

						valueMap.put("approver",tdmManagerAccountList.get(0).get("id"));
					}
				}



			}








		} catch (Exception e) {
			Log.error("",e);
		}
		return null;
	}

	@Override
	public String onUpdate(String tableid, Map valueMap, Map valueMapOrg, String projectId) {
		try {
			Map user = SessUtil.getSessInfo().getUser();
			DtrefDAO dtrefDAO = new DtrefDAO(projectId);
			String RefField = dtrefDAO.getRefField("xsht", tableid);
			DAODataMng daoDataMng = new DAODataMng(projectId);


			if (StringUtils.isNotEmpty(RefField)) {

				Long studyid = (Long) valueMap.get(RefField);

				List<Map> studyUersList = daoDataMng.listRecord("roles", "','||obj.limitnum||',' like '%,TDM,%' and obj.studyid='" + studyid + "' and obj.active='1'", null, 1);

				if (CollectionUtils.isNotEmpty(studyUersList)) {

					Long tdmUserId = (Long) studyUersList.get(0).get("member");
					String tdmloginid = (String) daoDataMng.getRecord("ryjbzl", tdmUserId).get("loginid");
					List<Map> tdmInfoList = daoDataMng.listRecord("ryjl", "obj.email='" + tdmloginid + "'", null, 1);

					if (CollectionUtils.isNotEmpty(tdmInfoList)) {
						String tdmManagername = (String) tdmInfoList.get(0).get("managername");
						List<Map> tdmManagerAccountList = daoDataMng.listRecord("ryjl", "obj.xm='" + tdmManagername + "'", null, 1);
						if (CollectionUtils.isNotEmpty(tdmManagerAccountList)) {

							valueMap.put("approver",tdmManagerAccountList.get(0).get("id"));
						}
					}

				}

			}








		} catch (Exception e) {
			Log.error("",e);
		}
		return null;
	}

	
}
