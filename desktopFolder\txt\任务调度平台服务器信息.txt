全平台任务调度的服务器airflow和存储对象服务器信息如下：
开发：***********-22
测试：***********；***********
生产：**********-75
账号密码：root\Hr@airflow0509

mysql数据库：

root/Hr@mysql1024

Hr_mysql1024

smb://zhouh36:HR9cf3cbd8@**********/susar-pro/test/2023-05/



 mysql+pymysql
 
 pymysql
 
 --upgrade
 
 
 export AIRFLOW_HOME=~/airflow

 
 pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/  pymysql;
 
 
  pip3.8 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/  mysql-server ;
 
 sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'mysql1024@127.0.0.1' ([Errno -2] Name or service not known)")
 
 
 mysql://root:Hr_mysql1024@localhost:3306/airflow

 root/Hr@mysql1024
 
 pip3 uninstall PyYAML --yes  --force-reinstall
 
   nohup airflow scheduler>>$AIRFLOW_HOME/airflow-scheduler.log 2>&1 &
 sql
SELECT * FROM mysql.user WHERE User='root';

nohup airflow webserver -p 8090>>$AIRFLOW_HOME/airflow-webserver.log 2>&1 &
sql
GRANT SELECT ON *.* TO 'root'@'%';


bash 
pip3 uninstall PyYAML==3.12 --yes --force-reinstall



sql
SHOW GRANTS FOR 'user'@'%';

bash
yum install mysql-devel-8.0.28  # 指定与已安装版本相同的版本号



sql
UPDATE mysql.user SET Password=PASSWORD('Hrmysql1024') WHERE User='root' AND Host='localhost';
FLUSH PRIVILEGES; 


 update user set password=password(‘Hrmysql1024′) where user=’root’ and host=’localhost’; 

mysqladmin -uroot -pHr@mysql1024 password Hr_mysql1024 
flask
mysqladmin -uroot -pHr@mysql1024 password Hr_mysql1024 

mysql -uroot -pHr_mysql1024 < /usr/local/lib/python3.8/site-packages/airflow/migrations/env.py



sql

GRANT ALL PRIVILEGES ON airflow.* TO 'root'@'localhost' WITH GRANT OPTION;



apache-airflow  2.6.0



sql
UPDATE mysql.user SET Host='%' WHERE User='root' AND Host='localhost'; 
FLUSH PRIVILEGES;

python
engine = create_engine(SQL_ALCHEMY_CONN, connect_args=connect_args) 


python
engine = create_engine(SQL_ALCHEMY_CONN, dialect='mysql', connect_args=connect_args)


psycopg2




#安装mysql依赖
sudo yum -y install python-devel libevent-devel mysql-devel mysqlclient
pip install mysqlclient pymysql mysql
 
#修改mysql参数
vi /etc/my.cnf
explicit_defaults_for_timestamp=1
 
#创建个airflow的用户，airflow数据库
mysql -uroot -p
CREATE DATABASE IF NOT EXISTS airflow DEFAULT CHARACTER SET utf8 DEFAULT COLLATE utf8_general_ci;

grant all privileges on airflow.* to root@localhost identified by 'Hr_mysql1024';
grant all privileges on airflow.* to 'root'@'%' identified by 'Hr_mysql1024';
flush privileges;
select user,authentication_string,host from user;

airflow users create --username admin --firstname admin --lastname admin --role Admin --email <EMAIL>




sql
ALTER USER 'root'@'localhost' IDENTIFIED BY 'Hr_mysql1024';


GRANT ALL PRIVILEGES ON *.* TO 'root'@'%'WITH GRANT OPTION;


apache-airflow-providers-ftp (2.1.0)
apache-airflow-providers-http (2.1.0)
apache-airflow-providers-imap (2.2.1)
apache-airflow-providers-sqlite (2.1.1)

bash 
pip3 install setuptools_rust


pip install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/   apache-airflow;

sql_alchemy_conn =  mysql://root:Hr_mysql1024@localhost:3306/airflow?charset=utf8

bash
ls /usr/bin/python*


bash
ln -s /usr/bin/python3.6 /usr/bin/python

sql_alchemy_conn =  postgresql://postgres:postgres@127.0.0.1:5432/airflow


/usr/local/lib64/python3.6/site-packages/sqlalchemy/dialects/mysql/mysqldb.py

sql_alchemy_conn =  mysql+pymysql://root:Hr_mysql1024@localhost:3306/airflow?charset=utf8
mysql+pymysql
executor = LocalExecutor


sql
CREATE DATABASE airflow;


Rave数据下载方式
"C:\Program Files (x86) WinSCP WinScp.exe” /command "openftpes://pengrui.hrglobe.cn:<EMAIL>/"cdhengruimedicine-ravex.mdsol.com/sasondemand”"lcdZ: Projects\GRP CDSC PENGR\SHR-1210-III-315\data\raw”"get-neweronly *SHR 1219 III 315 ???? *" "exit"/log=Z:\Projects GRP CDSC PENGR SHR-1210-III-315 data raw log file %ymz
d%.txt



sas数据集下载方式
wget https://libvirt.org/sources/libvirt-4.7.0.tar.xz --no-check-certificate









