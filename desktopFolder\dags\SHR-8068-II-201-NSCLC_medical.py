import airflow
from airflow.models import DAG
from airflow.operators.python import Python<PERSON><PERSON><PERSON>
from airflow.operators.bash import <PERSON><PERSON><PERSON>per<PERSON>  
from airflow.utils.dates import days_ago
import pymysql
import json
import saspy
import subprocess
from airflow.utils.log.logging_mixin import LoggingMixin
import os
import re
from datetime import datetime,timedelta 

args = {
    'owner': 'zhouhui',
    'start_date': datetime(2023, 10, 16)
}

dag = DAG(dag_id='SHR-8068-II-201-NSCLC_medical', default_args=args, schedule_interval="0 17 * * *")
# 任务1:使用PyMySQL查询文件路径和文件名,并根据文件路径上传至minio,上传时以文件名命名
def mysql_query_upload():
	host = '***********'
	port = 3306
	user = 'weix5'
	password = 'weix@edc'
	database = 'edc_report5'
	conn = pymysql.connect(host=host,port=port,user=user,passwd=password,db=database)
	# 获取游标
	cursor = conn.cursor()
	# 执行SQL查询并获取结果
	sql = "SELECT REPORTFILENAME, REPORTFILEPATH, EXPORTTIME FROM tbl_report_pro WHERE (REPORTFILENAME like '%SAS%' )and REPORTFILENAME like '%SHR-8068-Ⅱ-201-NSCLC%' ORDER BY EXPORTTIME desc LIMIT 1"
	cursor.execute(sql)
	results = cursor.fetchall()
	data = []
	# 获取minios3/raw目录下的文件列表
	list_cmd = 'mc ls minios3/raw'
	list_file_name = os.popen(list_cmd).read()
	for row in results:
		export_time = row[2].strftime("%Y/%m/%d/%H/%M")
		project=row[0][:row[0].index('_')]
		full_path=project+'_sas.zip'
		# 检查是否有同名文件
		if row[0].replace("(", "[").replace(")", "]") in list_file_name:
			result = re.split('/|\\\\', row[1])
			path_segment1 = result[-3]
			path_segment2 = result[-2]
			path_segment3 = result[-1]
			local_md5_cmd = f'md5sum /home/<USER>/data/{path_segment1}/{path_segment2}/{path_segment3}'
			local_md5 = os.popen(local_md5_cmd).read().split(' ')[0]
			remote_md5_cmd = f'mc cat minios3/raw/{row[0].replace("(", "[").replace(")", "]")} | md5sum' 
			remote_md5 = os.popen(remote_md5_cmd).read().split(' ')[0]
			# 比较md5,如果不同则上传并覆盖
			if local_md5 != remote_md5:
				result = re.split('/|\\\\', row[1])
				path_segment1 = result[-3]
				path_segment2 = result[-2]
				path_segment3 = result[-1]	
				upload_cmd = f'mc cp /home/<USER>/data/{path_segment1}/{path_segment2}/{path_segment3} minios3/raw/{full_path} --tags "key1=HRTAU&key2={project}&key3={export_time}"' 
				os.system(upload_cmd)
		else:
			result = re.split('/|\\\\', row[1])
			path_segment1 = result[-3]
			path_segment2 = result[-2]
			path_segment3 = result[-1]	
			upload_cmd =f'mc cp /home/<USER>/data/{path_segment1}/{path_segment2}/{path_segment3} minios3/raw/{full_path} --tags "key1=HRTAU&key2={project}&key3={export_time}"' 
			os.system(upload_cmd)
	# 重命名文件并上传文件
		# upload_cmd = f'mc cp /home/<USER>/raw/{row[0].replace("(", "[").replace(")", "]")} --tags "key1=HRTAU&key2={row[0].replace("(", "[").replace(")", "]")}"' 
		# os.system(upload_cmd)    # 上传到Minio并添加标签
		data.append({
			    "dag_id": row[0],
			    "fileloc": row[1]
		})
	data = json.dumps(data)
	with open('/home/<USER>', 'w') as f:
		f.write(str(data))
	cursor.close() 
	conn.close()

task2 = PythonOperator(
    task_id='mysql_query_upload',  
    python_callable=mysql_query_upload,
    dag=dag,  
)

def download_and_execute_sas_script(**kwargs):
	filename = kwargs['filename']
	sas = saspy.SASsession()
	sas.submit('''%global system studyid root version dir lang;''')
	sas.submit('''proc datasets lib=work nolist kill; run;quit;%let dir=%sysfunc(getoption(work));x "cd &dir";x "mkdir ./pgm";x 'mc find minios3/pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';%include "&dir/pgm/*.sas";''')
	sas.submit('''%let studyid=SHR-8068-II-201-NSCLC;''')
	sas.submit('''%m_post2s3(studyid=&studyid.);''')
	sas.submit('''%m_gets3data(studyid=&studyid.,data=@);''')
	sas.submit('''proc sql;
    alter table raw.lb modify LBORRES char(200) format=$200. informat=$200.;
    alter table derived.lb modify LBORRES char(200) ;
    quit;''')
	sas.submit('''%let lang="EN";''')
	sas.submit('''option mprint symbolgen validvarname=v7;''')
	sas.submit('%m_medical_joinaecm;')
	sas.submit('%clin_consistency;')
	sas.submit('''%m_medical_aedrugeq;''')
	sas.submit('''%include "./pgm/m_out2s3.sas);''')
	sas.submit('''%m_out2s3(studyid=&studyid.);''')
	sas.submit('''%m_med_formfie;''')
	sas.submit('''%m_med_stadm;''')
	sas.submit('''%m_med_starand;''')
	sas.submit('''%m_med_stasub;''')
	sas.submit('''%m_med_staae;''')
	sas.submit('''%m_med_stalb;''')
	sas.submit('''%m_create_hrtaubuild_lab;''')
	sas.submit('''%m_derive_hrtaubuild(folder=csvdata_lab);''')
	sas.submit('''%m_create_hrtau_lab(is_als=N);''')
	sas.submit('''%M_std_dvs_labreview;''')
	sas.submit('''%m_out2s3T(studyid=&studyid.,suffix=labctc);''')
	sas.submit('''%m_mean_meanlb;''')
	sas.submit('''%m_out2s3T(studyid=&studyid.,suffix=lbvis);''')
	sas.submit('''%m_med_stavs;''')
	sas.submit('''%M_std_dvs_vsview;''')
	sas.submit('''%m_out2s3T(studyid=&studyid.,suffix=vsview);''')
	sas.submit('''%m_med_staeg;''')
	sas.submit('''%M_std_dvs_egview;''')
	sas.submit('''%m_out2s3T(studyid=&studyid.,suffix=egview);''')
	
	sas.submit('''%let studyid = SHR-8068-II-201-NSCLC;''')
	sas.submit('''%M_gen_pro_coding(formoid=ae,folder=,output=Y);''')
	sas.submit('''%readin_coding;''')
	sas.submit('''%m_med_dseos_pre_process(dseos=dseos,dsstdat =dsstdat);''')
	sas.submit('''%m_med_dsrand_pre_process(dsrand=dsrand,regime = regime);''')

	sas.submit('''%m_med_hrtau_dseot_pre_process(dseot=dseot, dsstdat = dsstdat,dsdecod = dsdecod,dsterm = dsterm);''')
	sas.submit('''%m_med_hrtau_ae_pre_process(aerel = aerel, ctcae = aetoxgr);''')
	sas.submit('''%m_med_hrtau_ex_pre_process(data = exinf,expdost=expdose,exdosadj=);''')
	sas.submit('''%ae_ex_process;''')
	sas.submit('''%label_ae_ex;''')
	sas.submit('''data ae_ex_med1; set out.ae_ex; run;''')
	sas.submit('''data ex_med1; set out.ex; run;''')

	sas.submit('''%m_med_hrtau_dseot_pre_process(dseot=dseot_a, dsstdat = dsstdat,dsdecod = dsdecod,dsterm = dsterm);''')
	sas.submit('''%m_med_hrtau_ae_pre_process(aerel = aerel_a, ctcae = aetoxgr);''')
	sas.submit('''%m_med_hrtau_ex_pre_process(data = exinf_a,expdost=expdose,exdosadj=);''')
	sas.submit('''%ae_ex_process;''')
	sas.submit('''%label_ae_ex;''')
	sas.submit('''data ae_ex_med2; set out.ae_ex; run;''')
	sas.submit('''data ex_med2; set out.ex; run;''')

	sas.submit('''%m_med_hrtau_dseot_pre_process(dseot=dseot_b, dsstdat = dsstdat,dsdecod = dsdecod,dsterm = dsterm);''')
	sas.submit('''%m_med_hrtau_ae_pre_process(aerel = aerel_b, ctcae = aetoxgr);''')
	sas.submit('''%m_med_hrtau_ex_pre_process(data = exinf_b,expdost=expdose,exdosadj=);''')
	sas.submit('''%ae_ex_process;''')
	sas.submit('''%label_ae_ex;''')
	sas.submit('''data ae_ex_med3; set out.ae_ex; run;''')
	sas.submit('''data ex_med3; set out.ex; run;''')

	sas.submit('''%m_med_hrtau_dseot_pre_process(dseot=dseot_c, dsstdat = dsstdat,dsdecod = dsdecod,dsterm = dsterm);''')
	sas.submit('''%m_med_hrtau_ae_pre_process(aerel = aerel_c, ctcae = aetoxgr);''')
	sas.submit('''%m_med_hrtau_ex_pre_process(data = exinf_c,expdost=expdose,exdosadj=exdosadj);''')
	sas.submit('''%ae_ex_process;''')
	sas.submit('''%label_ae_ex;''')
	sas.submit('''data ae_ex_med4; set out.ae_ex; run;''')
	sas.submit('''data ex_med4; set out.ex; run;''')

	sas.submit('''%m_med_hrtau_dseot_pre_process(dseot=dseot_d, dsstdat = dsstdat,dsdecod = dsdecod,dsterm = dsterm);''')
	sas.submit('''%m_med_hrtau_ae_pre_process(aerel = aerel_d, ctcae = aetoxgr);''')
	sas.submit('''%m_med_hrtau_ex_pre_process(data = exinf_d,expdost=expdose,exdosadj=exdosadj);''')
	sas.submit('''%ae_ex_process;''')
	sas.submit('''%label_ae_ex;''')
	sas.submit('''data ae_ex_med5; set out.ae_ex; run;''')
	sas.submit('''data ex_med5; set out.ex; run;''')

	sas.submit('''%m_med_hrtau_dseot_pre_process(dseot=dseot_e, dsstdat = dsstdat,dsdecod = dsdecod,dsterm = dsterm);''')
	sas.submit('''%m_med_hrtau_ae_pre_process(aerel = aerel_e, ctcae = aetoxgr);''')
	sas.submit('''%m_med_hrtau_ex_pre_process(data = exinf_e,expdost=expdose,exdosadj=exdosadj);''')
	sas.submit('''%ae_ex_process;''')
	sas.submit('''%label_ae_ex;''')
	sas.submit('''data ae_ex_med6; set out.ae_ex; run;''')
	sas.submit('''data ex_med6; set out.ex; run;''')

	sas.submit('''%m_med_hrtau_dseot_pre_process(dseot=dseot_f, dsstdat = dsstdat,dsdecod = dsdecod,dsterm = dsterm);''')
	sas.submit('''%m_med_hrtau_ae_pre_process(aerel = aerel_f, ctcae = aetoxgr);''')
	sas.submit('''%m_med_hrtau_ex_pre_process(data = exinf_f,expdost=expdose,exdosadj=exdosadj);''')
	sas.submit('''%ae_ex_process;''')
	sas.submit('''%label_ae_ex;''')
	sas.submit('''data ae_ex_med7; set out.ae_ex; run;''')
	sas.submit('''data ex_med7; set out.ex; run;''')

	sas.submit('''data out.ae_ex(label='AE_EX'); set ae_ex_med7 ae_ex_med6 ae_ex_med5 ae_ex_med4 ae_ex_med3 ae_ex_med2 ae_ex_med1; run;''')
	sas.submit('''data out.ex(label='EX'); set ex_med7 ex_med6 ex_med5 ex_med4 ex_med3 ex_med2 ex_med1; run;''')

	sas.submit('''%m_out2s3T(studyid=&studyid.,suffix=AEEX);''')
	sas.endsas()   
# 任务4:执行sas脚本 
task4 = PythonOperator(
	task_id ='download_and_execute_sas_script', 
	python_callable = download_and_execute_sas_script,
	op_kwargs={
			'filename': 'SHR-8068-II-201-NSCLC'
		},
		dag=dag
)

task2>>task4