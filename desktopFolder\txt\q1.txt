WITH minio_tbl AS ( SELECT *, toString ( sipHash64 ( * ) ) AS lab_data_id FROM s3 ( ?, 'minioadmin', 'minioadmin', 'CSVWithNames' ) ),
minio_data AS ( SELECT * FROM minio_tbl ),
result AS (
SELECT
	mysql_lab_review.*,
	minio_data.* 
FROM
	(
SELECT
	id,
	lab_data_id,
	file_name,
	operate_user,
	lab_comment,
	level_content,
	comment_level,
	multiIf (
	equals ( comment_level, 1 ),
	'中心层面',
	equals ( comment_level, 2 ),
	'受试者层面',
	equals ( comment_level, 3 ),
	'记录层面',
	'未知' 
	) AS commentLevel,
IF
	( equals ( comment_level, 1 ), level_content, '' ) AS centerContent,
IF
	( equals ( comment_level, 2 ), level_content, '' ) AS subjectContent,
	toString ( create_time, 'Asia/Shanghai' ) AS createTime 
FROM
	mysql ( '***********:3306', 'dm_platform', 'dm_lab_ae_comments', 'susaruser', 'Hr@db0316' ) 
WHERE
	equals ( file_name, ? ) 
	AND is_deleted = 'N' 
	) AS mysql_lab_review
	LEFT JOIN minio_data ON mysql_lab_review.lab_data_id = minio_data.lab_data_id 
	) SELECT
	* 
FROM
	result