C:\apache-tomcat-8.5.85\bin\catalina.bat run
[2025-02-19 06:06:21,201] Artifact Test:war exploded: Waiting for server connection to start artifact deployment...
Using CATALINA_BASE:   "C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2022.3\tomcat\7df39c4b-331b-4838-8251-63cd2ff2430f"
Using CATALINA_HOME:   "C:\apache-tomcat-8.5.85"
Using CATALINA_TMPDIR: "C:\MyFile\百奥知\源码\tomcat\apache-tomcat-8.5.5\temp"
Using JRE_HOME:        "C:\Program Files\Java\jdk1.8.0_202"
Using CLASSPATH:       "C:\apache-tomcat-8.5.85\bin\bootstrap.jar;C:\apache-tomcat-8.5.85\bin\tomcat-juli.jar"
Using CATALINA_OPTS:   ""
Connected to the target VM, address: '127.0.0.1:61477', transport: 'socket'
19-Feb-2025 18:06:22.247 警告 [main] org.apache.catalina.startup.ClassLoaderFactory.validateFile Problem with directory [C:\MyFile\°?°???\????\tomcat\apache-tomcat-8.5.5\lib], exists: [false], isDirectory: [false], canRead: [false]
19-Feb-2025 18:06:22.250 警告 [main] org.apache.catalina.startup.ClassLoaderFactory.validateFile Problem with directory [C:\MyFile\°?°???\????\tomcat\apache-tomcat-8.5.5\lib], exists: [false], isDirectory: [false], canRead: [false]
Connected to server
[2025-02-19 06:06:23,285] Artifact Test:war exploded: Artifact is being deployed, please wait...
19-Feb-2025 18:06:29.917 严重 [RMI TCP Connection(3)-127.0.0.1] org.apache.catalina.session.PersistentManagerBase.startInternal No Store configured, persistence disabled
InitCounter: 1
Web root path is: C:/MyFile/百奥知/源码/HRUAP/BioknowCdtms/web
-->sacn net.bioknow
  find package:163
Begin Load Package :
----------------------
net.bioknow.webio.dbmanage
net.bioknow.webutil.tlib
net.bioknow.webutil.session
net.bioknow.webutil.fileup
net.bioknow.webutil.syscfg
net.bioknow.webutil.remotecall
net.bioknow.webutil.timercache
net.bioknow.webutil.tools
net.bioknow.passport.datamng
net.bioknow.passport.projectmng
net.bioknow.passport.validate
net.bioknow.passport.register
net.bioknow.passport.uiframe
net.bioknow.passport.webvar
net.bioknow.passport.power
net.bioknow.passport.ldap
net.bioknow.passport.sysmaintenance
net.bioknow.webplug.taskmng
net.bioknow.webplug.actionlog
net.bioknow.webplug.backup
net.bioknow.webplug.cache
net.bioknow.webplug.transemail
net.bioknow.webplug.notice
net.bioknow.webplug.configdb
net.bioknow.webplug.help
net.bioknow.uap.dbmenu
net.bioknow.uap.dbmenuentry
net.bioknow.uap.dbcp
net.bioknow.uap.dbcore
net.bioknow.uap.dbdatamng
net.bioknow.uap.dbpubremote
net.bioknow.uap.schemaplug
net.bioknow.uap.sysinfo
net.bioknow.uap.webfolder
net.bioknow.uapplug.publicworkflow
net.bioknow.uapplug.reportrunner
net.bioknow.uapplug.statistic
net.bioknow.uapplug.pdfreport
net.bioknow.uapplug.usersyn
net.bioknow.uapplug.dbview
net.bioknow.uapplug.reportform
net.bioknow.uapplug.fieldinputrule
net.bioknow.dbplug.portal
net.bioknow.dbplug.portalmobile
net.bioknow.dbplug.portalwx
net.bioknow.dbplug.portal_jumei
net.bioknow.dbplug.portal_medaros
net.bioknow.dbplug.easydt
net.bioknow.dbplug.reportformattachout
net.bioknow.dbplug.reportformexcelin
net.bioknow.dbplug.notice
net.bioknow.dbplug.autovalue
net.bioknow.dbplug.autovalue2
net.bioknow.dbplug.batchadd
net.bioknow.dbplug.batchaddinterserver
net.bioknow.dbplug.batchappend
net.bioknow.dbplug.transcribe
net.bioknow.dbplug.classify
net.bioknow.dbplug.fastsearch
net.bioknow.dbplug.dataview3d
net.bioknow.dbplug.dataviewtree
net.bioknow.dbplug.emailsend
net.bioknow.dbplug.emailreceive
net.bioknow.dbplug.fulltext
net.bioknow.dbplug.input
net.bioknow.dbplug.input2
net.bioknow.dbplug.input3
net.bioknow.dbplug.output
net.bioknow.dbplug.outputclearsql
net.bioknow.dbplug.outputadv
net.bioknow.dbplug.outputhistory
net.bioknow.dbplug.joinsearch
net.bioknow.dbplug.userui
net.bioknow.dbplug.wordreport
net.bioknow.prjplug.getwordreport
net.bioknow.dbplug.excelreport
net.bioknow.dbplug.excelreport2
net.bioknow.dbplug.timeline
net.bioknow.dbplug.subscribe
net.bioknow.dbplug.lvrelation
net.bioknow.dbplug.linkreplace
net.bioknow.dbplug.statinex
net.bioknow.dbplug.statinex2
net.bioknow.dbplug.schedule
net.bioknow.dbplug.repeatinput
net.bioknow.prjplug.recordcompare
net.bioknow.prjplug.recordcompare2
net.bioknow.dbplug.pygenerator
net.bioknow.dbplug.batchreplace
net.bioknow.dbplug.ipauth
net.bioknow.dbplug.tabledown
net.bioknow.dbplug.inputtxt
net.bioknow.dbplug.inputjoin
net.bioknow.dbplug.advsummary
net.bioknow.dbplug.randomcheck
net.bioknow.dbplug.mutisaveas
net.bioknow.dbplug.changecreator
net.bioknow.dbplug.barcodeprinter
net.bioknow.dbplug.groupview
net.bioknow.dbplug.tempsave
net.bioknow.dbplug.joinunique
net.bioknow.dbplug.tablecount
net.bioknow.dbplug.animalfee
net.bioknow.dbplug.deluselessfile
net.bioknow.dbplug.calview
net.bioknow.dbplug.logsaver
net.bioknow.dbplug.jsontoimport
net.bioknow.dbplug.helpcenter
net.bioknow.prjplug.projectempower
net.bioknow.prjplug.fieldextract
net.bioknow.fieldformat.sequence
net.bioknow.fieldformat.linkview
net.bioknow.fieldformat.linkviewtoweb
net.bioknow.fieldformat.abvview
net.bioknow.fieldformat.showpdf2swf
net.bioknow.fieldformat.pdfannotation
net.bioknow.fieldformat.wfshowvideo
net.bioknow.fieldformat.showdoc
net.bioknow.fieldformat.scinotation
net.bioknow.appplug.homepage
net.bioknow.appplug.webfolderfunc
net.bioknow.appplug.systhreadmng
net.bioknow.appplug.remotebackup
net.bioknow.appplug.remotebackup_EDC4
net.bioknow.appplug.serverconfig
net.bioknow.appplug.dynmenu
net.bioknow.appplug.bioknowchart
net.bioknow.appplug.fileupdrag
net.bioknow.appplug.mq
net.bioknow.appplug.mq_remotecopy
net.bioknow.prjplug.bioknow_regdown
net.bioknow.prjplug.tablewatcher
net.bioknow.prjplug.dbsyn
net.bioknow.prjplug.dbsyn_attachment
net.bioknow.prjplug.dbsyn_linkupdate
net.bioknow.prjplug.dbsynbyid
net.bioknow.prjplug.dbmigration
net.bioknow.prjplug.dbbatchupdate
net.bioknow.prjplug.codegen
net.bioknow.prjplug.inbreeding
net.bioknow.prjplug.projectvote
net.bioknow.prjplug.attachpdf2swf
net.bioknow.prjplug.datapub
net.bioknow.prjplug.workplan
net.bioknow.prjplug.scoremodel
net.bioknow.prjplug.objectscore
net.bioknow.prjplug.scoretranslate
net.bioknow.prjplug.grouptotable
net.bioknow.prjplug.edccoding
net.bioknow.prjplug.edccoding_asstinput
net.bioknow.prjplug.uijs
net.bioknow.prjplug.webmonitor
net.bioknow.prjplug.dynparamcreator
net.bioknow.prjplug.dynparamlist
net.bioknow.prjplug.attendancestat
net.bioknow.prjplug.linktransfer
net.bioknow.prjplug.fileindex
net.bioknow.prjplug.savelistener
net.bioknow.prjplug.getage
net.bioknow.prjplug.drugrandomize
net.bioknow.prjplug.receivesample2
net.bioknow.prjplug.publicworkflowlinkfunc
net.bioknow.prjplug.hardwarelogin
net.bioknow.prjplug.appointment
net.bioknow.prjplug.printer
net.bioknow.prjplug.barcodein
net.bioknow.prjplug.listlink
net.bioknow.prjplug.jarrunner
net.bioknow.prjplug.precoding
net.bioknow.prjplug.getdataversion
net.bioknow.prjplug.webservice
net.bioknow.prjplug.mimageinterface
net.bioknow.hardware.gsmmodem
net.bioknow.hardware.eswitch
net.bioknow.hardware.eswitch_tcp
net.bioknow.hardware.door_hanwang
net.bioknow.hardware.camera
net.bioknow.hardware.barcodeadd
net.bioknow.hardware.barcode_bloodcollection
net.bioknow.cdtms.edcdatablind
net.bioknow.cdtms.edmFileOutput
net.bioknow.cdtms.esign
net.bioknow.cdtms.extdatabind
net.bioknow.cdtms.extdatagen
net.bioknow.cdtms.formMail
net.bioknow.cdtms.historyUntie
net.bioknow.cdtms.lightpdfSign
net.bioknow.cdtms.schedule
net.bioknow.cdtms.setApprover
net.bioknow.cdtms.setTableAuth
net.bioknow.cdtms.studyClose
net.bioknow.cdtms.studyFileOutput
net.bioknow.cdtms.wiki
net.bioknow.cdtms.wrokFileTmpl
net.bioknow.services.calendar
net.bioknow.services.core
net.bioknow.services.dynbuttonscfg
net.bioknow.services.dyntable
net.bioknow.services.passport.sysmaintenance
net.bioknow.services.passport.uiframe
net.bioknow.services.passport.webvar
net.bioknow.services.prjplug.biodata
net.bioknow.services.prjplug.getwordreport
net.bioknow.services.prjplug.groupextract
net.bioknow.services.uap.appplug.designer.pc
net.bioknow.services.uap.appplug.email_distribution
net.bioknow.services.uap.appplug.emailtimesend
net.bioknow.services.uap.appplug.embeddingindex
net.bioknow.services.uap.appplug.homepage
net.bioknow.services.uap.appplug.monitor
net.bioknow.services.uap.appplug.onlyoffice
net.bioknow.services.uap.appplug.ownerchange
net.bioknow.services.uap.appplug.pdfsign
net.bioknow.services.uap.appplug.publicdoc
net.bioknow.services.uap.appplug.publictable
net.bioknow.services.uap.appplug.remotebutontask
net.bioknow.services.uap.appplug.tabledoc
net.bioknow.services.uap.appplug.uapocr
net.bioknow.services.uap.appplug.urlredirect
net.bioknow.services.uap.appplug.wechatpush
net.bioknow.services.uap.cdl
net.bioknow.services.uap.dbdatamng.buttons
net.bioknow.services.uap.dbdatamng.dbcore
net.bioknow.services.uap.dbdatamng.fieldauth
net.bioknow.services.uap.dbdatamng.history
net.bioknow.services.uap.dbdatamng.listedit
net.bioknow.services.uap.dbdatamng.modifylock
net.bioknow.services.uap.dbdatamng.starmark
net.bioknow.services.uap.dbdatamng.tablejs
net.bioknow.services.uap.dbplug.batchadd
net.bioknow.services.uap.dbplug.batchappend
net.bioknow.services.uap.dbplug.batchreplace
net.bioknow.services.uap.dbplug.classify
net.bioknow.services.uap.dbplug.emailsend
net.bioknow.services.uap.dbplug.fastsearch
net.bioknow.services.uap.dbplug.helpcenter
net.bioknow.services.uap.dbplug.lvrelation
net.bioknow.services.uap.dbplug.output_linkshow
net.bioknow.services.uap.dbplug.repeatinput
net.bioknow.services.uap.dbplug.reportformattachout
net.bioknow.services.uap.dbplug.subscribe
net.bioknow.services.uap.dbplug.transcribe
net.bioknow.services.uap.dbplug.transcribeinner
net.bioknow.services.uap.dbplug.userui
net.bioknow.services.uap.dbplug.wordreport
net.bioknow.services.uap.dbplug.wxssobind
net.bioknow.services.uap.dbpubremote
net.bioknow.services.uap.fieldformat.abvview
net.bioknow.services.uap.fieldformat.countdown
net.bioknow.services.uap.fieldformat.dynamicmselect
net.bioknow.services.uap.fieldformat.fakeossfile
net.bioknow.services.uap.fieldformat.fieldJoin
net.bioknow.services.uap.fieldformat.linkview
net.bioknow.services.uap.fieldformat.linkviewbutton
net.bioknow.services.uap.fieldformat.linkviewtoweb
net.bioknow.services.uap.fieldformat.scinotation
net.bioknow.services.uap.fieldformat.showpdf2swf
net.bioknow.services.uap.fieldformat.smarthelp
net.bioknow.services.uap.schemaplug
net.bioknow.services.uap.signature
net.bioknow.services.uapplug.customfunctionformula
net.bioknow.services.uapplug.datasync
net.bioknow.services.uapplug.datasync.postprocess
net.bioknow.services.uapplug.dbview
net.bioknow.services.uapplug.developcolumn
net.bioknow.services.uapplug.fieldinputrule
net.bioknow.services.uapplug.fieldinputrule.query
net.bioknow.services.uapplug.fieldinputrule.verification
net.bioknow.services.uapplug.fileview
net.bioknow.services.uapplug.initialform
net.bioknow.services.uapplug.input
net.bioknow.services.uapplug.langtool
net.bioknow.services.uapplug.output
net.bioknow.services.uapplug.outputadv
net.bioknow.services.uapplug.publicworkflow
net.bioknow.services.uapplug.publicworkflowlinkfunc
net.bioknow.services.uapplug.reportform
net.bioknow.services.uapplug.sysparam
net.bioknow.services.uapplug.uireport
net.bioknow.services.uapplug.usersyn
net.bioknow.uap.dbinterface
net.bioknow.uap.dbjson
net.bioknow.uapplug.dtrefaddparent
net.bioknow.uapplug.langtool
net.bioknow.uapplug.meetingmail
net.bioknow.uapplug.publicnotice
net.bioknow.appplug.bioknowreg
net.bioknow.appplug.doubaoai
net.bioknow.appplug.edmdownload
net.bioknow.appplug.email_distribution
net.bioknow.appplug.embeddingindex
net.bioknow.appplug.ftpfile
net.bioknow.appplug.gantt
net.bioknow.appplug.ganttdefimpl
net.bioknow.appplug.httpsvalid
net.bioknow.appplug.kimiai
net.bioknow.appplug.messagesend
net.bioknow.appplug.networkworm
net.bioknow.appplug.onlyoffice
net.bioknow.appplug.ownerchange
net.bioknow.appplug.pdfsign
net.bioknow.appplug.publictable
net.bioknow.appplug.publictableimport
net.bioknow.appplug.remotebuttontask
net.bioknow.appplug.remoterelease
net.bioknow.appplug.smsautosend
net.bioknow.appplug.tabledoc
net.bioknow.appplug.wechatpush
net.bioknow.appplug.wpseditor
net.bioknow.dbplug.azad
net.bioknow.dbplug.baidOCRutil
net.bioknow.dbplug.classify_th
net.bioknow.dbplug.dingtalkintegration
net.bioknow.dbplug.mdbook
net.bioknow.dbplug.naturelanguage
net.bioknow.dbplug.object_scanner_pretreat
net.bioknow.dbplug.objectocr
net.bioknow.dbplug.output_linkshow
net.bioknow.dbplug.outputfile_zip
net.bioknow.dbplug.pdfprint
net.bioknow.dbplug.qywxintegrate
net.bioknow.dbplug.supportclient
net.bioknow.dbplug.tableau
net.bioknow.dbplug.tabletrigger
net.bioknow.dbplug.translate
net.bioknow.dbplug.wxqrconnect
net.bioknow.dbplug.wxsso
net.bioknow.dbplug.wxssobind
net.bioknow.fieldformat.editorview
net.bioknow.fieldformat.fieldossupload
net.bioknow.fieldformat.officeview
net.bioknow.fieldformat.showattach
net.bioknow.fieldformat.smarthelp
net.bioknow.prjplug.attachhistory
net.bioknow.prjplug.bioknow_regdown2
net.bioknow.prjplug.calculation
net.bioknow.prjplug.groupextract
net.bioknow.prjplug.wfproject
net.bioknow.prjplug.wfpsauth
net.bioknow.webio.restful
net.bioknow.webplug.mailclient
net.bioknow.webplug.transemail_office365
----------------------
Total: 342 packages.
Init packages done.
Init face handlers done.
18:06:32,526  INFO bioknow:21 - Load AdminMenu
  BioknowServices
  Bioknow设计器
  UAPPlug
18:06:32,563  INFO bioknow:21 - backup timer scheduleAtFixedRate
18:06:32,616  INFO bioknow:21 - Init DTDom cache...
18:06:32,617  INFO bioknow:21 -   1.cdtmsen_val
18:06:33,595  INFO MLog:80 - MLog clients using log4j logging.
18:06:33,717  INFO C3P0Registry:204 - Initializing c3p0-0.9.1.2 [built 21-May-2007 15:04:56; debug? true; trace: 10]
18:06:33,854  INFO AbstractPoolBackedDataSource:462 - Initializing c3p0 pool... com.mchange.v2.c3p0.PoolBackedDataSource@8debdb23 [ connectionPoolDataSource -> com.mchange.v2.c3p0.WrapperConnectionPoolDataSource@32b1451b [ acquireIncrement -> 1, acquireRetryAttempts -> 2, acquireRetryDelay -> 500, autoCommitOnClose -> false, automaticTestTable -> null, breakAfterAcquireFailure -> false, checkoutTimeout -> 10000, connectionCustomizerClassName -> null, connectionTesterClassName -> com.mchange.v2.c3p0.impl.DefaultConnectionTester, debugUnreturnedConnectionStackTraces -> false, factoryClassLocation -> null, forceIgnoreUnresolvedTransactions -> false, identityToken -> 2svc3pb988eeup1x96n1h|4d7efe2a, idleConnectionTestPeriod -> 120, initialPoolSize -> 0, maxAdministrativeTaskTime -> 0, maxConnectionAge -> 300, maxIdleTime -> 300, maxIdleTimeExcessConnections -> 60, maxPoolSize -> 100, maxStatements -> 100, maxStatementsPerConnection -> 0, minPoolSize -> 0, nestedDataSource -> com.mchange.v2.c3p0.DriverManagerDataSource@185364a [ description -> null, driverClass -> null, factoryClassLocation -> null, identityToken -> 2svc3pb988eeup1x96n1h|4e5a1bc2, jdbcUrl -> **************************************************************************************************, properties -> {autoReconnect=true, is-connection-validation-required=true, user=******, password=******, autoReconnectForPools=true} ], preferredTestQuery -> null, propertyCycle -> 0, testConnectionOnCheckin -> false, testConnectionOnCheckout -> false, unreturnedConnectionTimeout -> 0, usesTraditionalReflectiveProxies -> false; userOverrides: {} ], dataSourceName -> null, factoryClassLocation -> null, identityToken -> 2svc3pb988eeup1x96n1h|425318ab, numHelperThreads -> 2 ]
18:06:37,135  INFO bioknow:21 - Init reportform cache done.
18:06:37,257  INFO bioknow:21 - MQ Service not active!
18:06:37,355  INFO bioknow:21 - [ConfigFormError]/prjplug/studyclose/configform.xml is not existed!
18:06:37,413  INFO bioknow:21 - [ConfigFormError]/uap/dbjson/configform.xml is not existed!
18:06:37,707  INFO bioknow:21 - net.bioknow.uapplug.langtool.LangtoolUtil.reloadCache cdtmsen_val 3717
18:06:37,783  INFO bioknow:21 - net.bioknow.dbplug.wxsso.ThreadPubToRedis.run() do not has config
18:06:37,813  INFO bioknow:21 - Init restful URI:
18:06:37,817  INFO bioknow:21 -   /embeddingindexnew/showInfo -> /embeddingindexnew.showInfo.do(PRIVATE)
18:06:37,817  INFO bioknow:21 -   /smart/list -> /servicesmarthelp.list.do(PRIVATE)
18:06:37,818  INFO bioknow:21 -   /smart/save -> /servicesmarthelp.save.do(PRIVATE)
18:06:37,819  INFO bioknow:21 -   /redirect/goto -> /uapredirect.upload.do(PRIVATE)
18:06:37,819  INFO bioknow:21 -   /OutputFileZip/DownladFileByPath -> /outputFilezip.DownladFileByPath.do(PRIVATE)
18:06:37,820  INFO bioknow:21 -   /batch/list -> /services_dtplug_batchadd.list.do(PRIVATE)
18:06:37,820  INFO bioknow:21 -   /batch/tab -> /services_dtplug_batchadd.tab.do(PRIVATE)
18:06:37,821  INFO bioknow:21 -   /batch/dataList -> /services_dtplug_batchadd.dataList.do(PRIVATE)
18:06:37,821  INFO bioknow:21 -   /batch/showinfo -> /services_dtplug_batchadd.showinfo.do(PRIVATE)
18:06:37,821  INFO bioknow:21 -   /batch/getDataList -> /services_dtplug_batchadd.getDataList.do(PRIVATE)
18:06:37,822  INFO bioknow:21 -   /batch/rules -> /services_dtplug_batchadd.doAction.do(PRIVATE)
18:06:37,822  INFO bioknow:21 -   /batch/choose -> /services_dtplug_batchadd.choose.do(PRIVATE)
18:06:37,822  INFO bioknow:21 -   /servicesmonitor/cache -> /services_monitor.getcache.do(PRIVATE)
18:06:37,823  INFO bioknow:21 -   /servicesmonitor/faceHandler -> /services_monitor.faceHandler.do(PRIVATE)
18:06:37,823  INFO bioknow:21 -   /v3/3rd/files/*/permission -> /wpseditor.permission.do(PUBLIC)
18:06:37,823  INFO bioknow:21 -   /v3/3rd/files/*/download -> /wpseditor.fileDown.do(PUBLIC)
18:06:37,824  INFO bioknow:21 -   /v3/3rd/users -> /wpseditor.users.do(PUBLIC)
18:06:37,824  INFO bioknow:21 -   /v3/3rd/files/*/upload -> /wpseditor.upload.do(PUBLIC)
18:06:37,825  INFO bioknow:21 -   /v3/3rd/files/* -> /wpseditor.fileInfo.do(PUBLIC)
18:06:37,825  INFO bioknow:21 -   /langtool/showinfo -> /servicelang.input.do(PRIVATE)
18:06:37,826  INFO bioknow:21 -   /getWordReport/getWord -> /getWordReportVue.getWord.do(PRIVATE)
18:06:37,826  INFO bioknow:21 -   /naturelanguage/getjson -> /naturelanguage.getjson.do(PRIVATE)
18:06:37,827  INFO bioknow:21 -   /distribution/test -> /emailDistribution.test.do(PRIVATE)
18:06:37,827  INFO bioknow:21 -   /distribution/getBeforeSend -> /emailDistribution.getBeforeSend.do(PRIVATE)
18:06:37,828  INFO bioknow:21 -   /distribution/uap/* -> /emailDistribution.transmit.do(PRIVATE)
18:06:37,828  INFO bioknow:21 -   /distribution/taskInfo -> /emailDistribution.taskInfo.do(PUBLIC)
18:06:37,829  INFO bioknow:21 -   /distribution/getEmailInfo -> /emailDistribution.getEmailInfo.do(PRIVATE)
18:06:37,829  INFO bioknow:21 -   /distribution/emailCode -> /emailDistribution.emailCode.do(PUBLIC)
18:06:37,830  INFO bioknow:21 -   /distribution/sendEmail -> /emailDistribution.sendEmail.do(PRIVATE)
18:06:37,830  INFO bioknow:21 -   /distribution/getRules -> /emailDistribution.getRules.do(PUBLIC)
18:06:37,830  INFO bioknow:21 -   /distribution/verifyCode -> /emailDistribution.verifyCode.do(PUBLIC)
18:06:37,831  INFO bioknow:21 -   /distribution/sendCode -> /emailDistribution.sendCode.do(PUBLIC)
18:06:37,831  INFO bioknow:21 -   /fakeOssFile/info -> /fakeOssFile.info.do(PUBLIC)
18:06:37,832  INFO bioknow:21 -   /fakeOssFile/*/getOssToken -> /fakeOssFile.getOssTokenBySession.do(PUBLIC)
18:06:37,832  INFO bioknow:21 -   /fakeOssFile/getOssToken -> /fakeOssFile.getOssToken.do(PRIVATE)
18:06:37,832  INFO bioknow:21 -   /signatureHistory/getSignatureInfo -> /SignatureHistory.showsignature.do(PRIVATE)
18:06:37,833  INFO bioknow:21 -   /remoteSession/getsession -> /remotesession.getsession.do(PUBLIC)
18:06:37,833  INFO bioknow:21 -   /subscribe/code -> /services_subscribe.code.do(PRIVATE)
18:06:37,834  INFO bioknow:21 -   /subscribe/getData -> /services_subscribe.getData.do(PRIVATE)
18:06:37,834  INFO bioknow:21 -   /subscribe/saveData -> /services_subscribe.saveData.do(PRIVATE)
18:06:37,834  INFO bioknow:21 -   /subscribe/saveKey -> /services_subscribe.saveKey.do(PRIVATE)
18:06:37,834  INFO bioknow:21 -   /subscribe/listKey -> /services_subscribe.listKey.do(PRIVATE)
18:06:37,835  INFO bioknow:21 -   /loginuia/login -> /loginuia.login.do(PUBLIC)
18:06:37,835  INFO bioknow:21 -   /services/biodata/register -> /services/biodata.register.do(PRIVATE)
18:06:37,835  INFO bioknow:21 -   /services/biodata/chartdatalist -> /services/biodata.chatrdatalist.do(PUBLIC)
18:06:37,836  INFO bioknow:21 -   /services/biodata/datachartedit -> /services/biodata.datachartedit.do(PUBLIC)
18:06:37,836  INFO bioknow:21 -   /services/biodata/datachartdel -> /services/biodata.datachartdel.do(PUBLIC)
18:06:37,836  INFO bioknow:21 -   /deleteHistory/listdelete -> /deleteHistory.listdelete.do(PRIVATE)
18:06:37,836  INFO bioknow:21 -   /home_page/icons -> /new_homepage.icons.do(PRIVATE)
18:06:37,837  INFO bioknow:21 -   /home_page/showOfGroup -> /new_homepage.showOfGroup.do(PRIVATE)
18:06:37,837  INFO bioknow:21 -   /home_page/show -> /new_homepage.show.do(PRIVATE)
18:06:37,837  INFO bioknow:21 -   /classify/show -> /classifyServices.show.do(PRIVATE)
18:06:37,837  INFO bioknow:21 -   /classify/showCalendar -> /classifyServices.showcalendar.do(PRIVATE)
18:06:37,838  INFO bioknow:21 -   /schemaClobhtml/fileup -> /schemaClobhtml.fileup.do(PRIVATE)
18:06:37,838  INFO bioknow:21 -   /detail/workflow-history -> /detailServices.workflowHistory.do(PRIVATE)
18:06:37,838  INFO bioknow:21 -   /detail/modify-history -> /detailServices.modifyHistory.do(PRIVATE)
18:06:37,838  INFO bioknow:21 -   /detail/signature-history -> /detailServices.signatureHistory.do(PRIVATE)
18:06:37,839  INFO bioknow:21 -   /mobilelogin/loginbyapp -> /mobilelogin.loginbyapp.do(PUBLIC)
18:06:37,839  INFO bioknow:21 -   /mobilelogin/loginuiamobile -> /mobilelogin.loginuiamobile.do(PUBLIC)
18:06:37,839  INFO bioknow:21 -   /mobilelogin/bindmphnoe -> /mobilelogin.bindmphnoe.do(PRIVATE)
18:06:37,839  INFO bioknow:21 -   /mobilelogin/msmsend -> /mobilelogin.msmsend.do(PUBLIC)
18:06:37,839  INFO bioknow:21 -   /mobilelogin/relogin -> /mobilelogin.relogin.do(PUBLIC)
18:06:37,840  INFO bioknow:21 -   /mobilelogin/postlogin -> /mobilelogin.postlogin.do(PRIVATE)
18:06:37,840  INFO bioknow:21 -   /mobilelogin/registerlogin -> /mobilelogin.registerlogin.do(PUBLIC)
18:06:37,840  INFO bioknow:21 -   /schemaImage/fileup -> /schemaImage.fileup.do(PRIVATE)
18:06:37,840  INFO bioknow:21 -   /schemaImage/fileup2 -> /schemaImage.fileup2.do(PRIVATE)
18:06:37,841  INFO bioknow:21 -   /schemaImage/fileupjson -> /schemaImage.fileupjson.do(PRIVATE)
18:06:37,841  INFO bioknow:21 -   /services/publicTable/list -> /publictable/services.list.do(PUBLIC)
18:06:37,841  INFO bioknow:21 -   /services/publicTable/tableFunc -> /publictable/services.tableFunc.do(PUBLIC)
18:06:37,841  INFO bioknow:21 -   /services/publicTable/tableFuncList -> /publictable/services.tableFuncList.do(PUBLIC)
18:06:37,842  INFO bioknow:21 -   /reportformService/getTableCount -> /rfrefresh.getTableCount.do(PRIVATE)
18:06:37,842  INFO bioknow:21 -   /reportformService/getRefreshTableid -> /rfrefresh.getRefreshTableid.do(PRIVATE)
18:06:37,842  INFO bioknow:21 -   /ftattach/open/fileup -> /ftattach/open.fileup.do(PUBLIC)
18:06:37,843  INFO bioknow:21 -   /onlyOffice/test -> /onlyoffice.test.do(PUBLIC)
18:06:37,843  INFO bioknow:21 -   /onlyOffice/editParam -> /onlyoffice.editParam.do(PRIVATE)
18:06:37,844  INFO bioknow:21 -   /onlyOffice/editList -> /onlyoffice.editList.do(PRIVATE)
18:06:37,844  INFO bioknow:21 -   /onlyOffice/save/*/*/*/*/* -> /onlyoffice.fileSave.do(PUBLIC)
18:06:37,844  INFO bioknow:21 -   /onlyOffice/editParamList -> /onlyoffice.editParamList.do(PRIVATE)
18:06:37,844  INFO bioknow:21 -   /onlyOffice/forceSave -> /onlyoffice.forceSave.do(PRIVATE)
18:06:37,845  INFO bioknow:21 -   /onlyOffice/history/data -> /onlyoffice.historyData.do(PRIVATE)
18:06:37,845  INFO bioknow:21 -   /onlyOffice/edit -> /onlyoffice.edit.do(PUBLIC)
18:06:37,845  INFO bioknow:21 -   /onlyOffice/down/*/*/*/*/* -> /onlyoffice.fileDown.do(PUBLIC)
18:06:37,845  INFO bioknow:21 -   /onlyOffice/session -> /onlyoffice.session.do(PRIVATE)
18:06:37,846  INFO bioknow:21 -   /onlyOffice/history -> /onlyoffice.history.do(PRIVATE)
18:06:37,846  INFO bioknow:21 -   /reviewSave/list -> /reviewSave.list.do(PRIVATE)
18:06:37,846  INFO bioknow:21 -   /reviewSave/save -> /reviewSave.save.do(PRIVATE)
18:06:37,846  INFO bioknow:21 -   /usersyn/test -> /usersyn_publicinftester.test.do(PUBLIC)
18:06:37,847  INFO bioknow:21 -   /usersyn/testsubmit -> /usersyn_publicinftester.testsubmit.do(PUBLIC)
18:06:37,847  INFO bioknow:21 -   /services/fieldinputrule/check -> /services/fieldinputrule.check.do(PRIVATE)
18:06:37,847  INFO bioknow:21 -   /visit/getSubRecord -> /reportformservices.getVisitRecordid.do(PRIVATE)
18:06:37,848  INFO bioknow:21 -   /visit/subjectbase -> /reportformservices.subjectbase.do(PRIVATE)
18:06:37,848  INFO bioknow:21 -   /visit/visitlist -> /reportformservices.visitlist.do(PRIVATE)
18:06:37,848  INFO bioknow:21 -   /linkviewbutton/getMore -> /linkviewbutton.getMore.do(PRIVATE)
18:06:37,848  INFO bioknow:21 -   /linkviewbutton/setup -> /linkviewbutton.show.do(PRIVATE)
18:06:37,849  INFO bioknow:21 -   /filepdfanno/lockdoc -> /filepdfanno.lockdoc.do(PRIVATE)
18:06:37,849  INFO bioknow:21 -   /filepdfanno/releasedoc -> /filepdfanno.releasedoc.do(PRIVATE)
18:06:37,849  INFO bioknow:21 -   /workflowHistory/showHistoryLastTime -> /workflowHistory.showHistoryLastTime.do(PRIVATE)
18:06:37,849  INFO bioknow:21 -   /workflowHistory/showHistoryInfo -> /workflowHistory.showhistory.do(PRIVATE)
18:06:37,850  INFO bioknow:21 -   /schemaDtref/getTableList -> /schemaDtref.getTableList.do(PUBLIC)
18:06:37,850  INFO bioknow:21 -   /schemaDtref/getDtrefUrl -> /schemaDtref.getDtrefUrl.do(PRIVATE)
18:06:37,850  INFO bioknow:21 -   /schemaDtref/getTransfer -> /schemaDtref.getTransfer.do(PRIVATE)
18:06:37,850  INFO bioknow:21 -   /schemaDtref/getTableInfo -> /schemaDtref.getTableInfo.do(PUBLIC)
18:06:37,851  INFO bioknow:21 -   /schemaDtref/gettransvalue -> /schemaDtref.gettransvalue.do(PRIVATE)
18:06:37,851  INFO bioknow:21 -   /remoteButtonTask/notify -> /remotebuttontask.notify.do(PUBLIC)
18:06:37,851  INFO bioknow:21 -   /remoteButtonTask/test -> /remotebuttontask.test.do(PUBLIC)
18:06:37,852  INFO bioknow:21 -   /remoteButtonTask/getInfo -> /remotebuttontask.getInfo.do(PUBLIC)
18:06:37,852  INFO bioknow:21 -   /remoteButtonTask/upload -> /remotebuttontask.upload.do(PUBLIC)
18:06:37,852  INFO bioknow:21 -   /remoteButtonTask/download -> /remotebuttontask.download.do(PUBLIC)
18:06:37,853  INFO bioknow:21 -   /remoteButtonTask/doc -> /remotebuttontask.doc.do(PUBLIC)
18:06:37,853  INFO bioknow:21 -   /remoteButtonTask/testWeb -> /remotebuttontask.testWeb.do(PUBLIC)
18:06:37,853  INFO bioknow:21 -   /remoteButtonTask/dataSave -> /remotebuttontask.dataSave.do(PUBLIC)
18:06:37,854  INFO bioknow:21 -   /remoteButtonTask/formId -> /remotebuttontask.formId.do(PUBLIC)
18:06:37,854  INFO bioknow:21 -   /schemaString/getIcons -> /schemaString.getIcons.do(PRIVATE)
18:06:37,855  INFO bioknow:21 -   /schemaString/getManyFakeAsstSearch -> /schemaString.getManyFakeAsstSearch.do(PRIVATE)
18:06:37,855  INFO bioknow:21 -   /schemaString/asstShow -> /schemaString.asstShow.do(PRIVATE)
18:06:37,855  INFO bioknow:21 -   /schemaString/asstUrl -> /schemaString.asstUrl.do(PRIVATE)
18:06:37,855  INFO bioknow:21 -   /schemaString/asstSearch -> /schemaString.asstSearch.do(PRIVATE)
18:06:37,856  INFO bioknow:21 -   /modifyLock/judgeLock -> /servicesModLock.judgeLock.do(PRIVATE)
18:06:37,856  INFO bioknow:21 -   /uireport/help -> /uireport.help.do(PUBLIC)
18:06:37,856  INFO bioknow:21 -   /uireport/test -> /uireport.test.do(PUBLIC)
18:06:37,857  INFO bioknow:21 -   /uireport/getTableList -> /uireport.getTableList.do(PUBLIC)
18:06:37,857  INFO bioknow:21 -   /uireport/gettoken -> /uireport.gettoken.do(PUBLIC)
18:06:37,857  INFO bioknow:21 -   /uireport/getTableInfo -> /uireport.getTableInfo.do(PUBLIC)
18:06:37,857  INFO bioknow:21 -   /batch/replace/save -> /batchreplace.batchModificationsSave.do(PRIVATE)
18:06:37,858  INFO bioknow:21 -   /batch/replace/edit -> /batchreplace.batchModificationsEdit.do(PRIVATE)
18:06:37,858  INFO bioknow:21 -   /batch/replace -> /batchreplace.batchplace.do(PRIVATE)
18:06:37,858  INFO bioknow:21 -   /batch/replace/showinfo -> /batchreplace.showinfo.do(PRIVATE)
18:06:37,859  INFO bioknow:21 -   /help/list -> /helpCenter.list.do(PUBLIC)
18:06:37,859  INFO bioknow:21 -   /help/search -> /helpCenter.search.do(PUBLIC)
18:06:37,859  INFO bioknow:21 -   /help/addCount -> /helpCenter.addCount.do(PUBLIC)
18:06:37,859  INFO bioknow:21 -   /help/info -> /helpCenter.info.do(PUBLIC)
18:06:37,860  INFO bioknow:21 -   /help/custom -> /helpCenter.custom.do(PUBLIC)
18:06:37,860  INFO bioknow:21 -   /help/allow -> /helpCenter.allow.do(PUBLIC)
18:06:37,860  INFO bioknow:21 -   /help/typeList -> /helpCenter.typeList.do(PUBLIC)
18:06:37,860  INFO bioknow:21 -   /uapOcr/status -> /uapOcrAction.status.do(PRIVATE)
18:06:37,860  INFO bioknow:21 -   /uapOcr/test -> /uapOcrAction.test.do(PUBLIC)
18:06:37,861  INFO bioknow:21 -   /uapOcr/getResult -> /uapOcrAction.getResult.do(PRIVATE)
18:06:37,861  INFO bioknow:21 -   /uapOcr/addTask -> /uapOcrAction.addTask.do(PRIVATE)
18:06:37,861  INFO bioknow:21 -   /uapOcr/config -> /uapOcrAction.config.do(PRIVATE)
18:06:37,861  INFO bioknow:21 -   /uapOcr/submit -> /uapOcrAction.submit.do(PRIVATE)
18:06:37,861  INFO bioknow:21 -   /uapOcr/fileList -> /uapOcrAction.fileList.do(PRIVATE)
18:06:37,861  INFO bioknow:21 -   /uapOcr/upload -> /uapOcrAction.upload.do(PRIVATE)
18:06:37,862  INFO bioknow:21 -   /uapOcr/canalTask -> /uapOcrAction.canalTask.do(PRIVATE)
18:06:37,862  INFO bioknow:21 -   /uiframe_vue/jsonfirstpageinfo -> /uiframe_vue.jsonfirstpageinfo.do(PRIVATE)
18:06:37,862  INFO bioknow:21 -   /uiframe_vue/getToken -> /uiframe_vue.getToken.do(PUBLIC)
18:06:37,862  INFO bioknow:21 -   /uiframe_vue/userList -> /uiframe_vue.userList.do(PRIVATE)
18:06:37,862  INFO bioknow:21 -   /uiframe_vue/switchPro -> /uiframe_vue.switchPro.do(PRIVATE)
18:06:37,863  INFO bioknow:21 -   /uiframe_vue/changeLanguage -> /uiframe_vue.changeLanguage.do(PRIVATE)
18:06:37,863  INFO bioknow:21 -   /uiframe_vue/getEclMenu -> /uiframe_vue.getEclMenu.do(PUBLIC)
18:06:37,863  INFO bioknow:21 -   /uiframe_vue/checkLockingPwd -> /uiframe_vue.lockingcheckpwd.do(PUBLIC)
18:06:37,863  INFO bioknow:21 -   /uiframe_vue/firstpage -> /uiframe_vue.firstpage.do(PRIVATE)
18:06:37,864  INFO bioknow:21 -   /verification/getFieldCheck -> /actionVerification.getFieldCheck.do(PRIVATE)
18:06:37,864  INFO bioknow:21 -   /sysparam/getSysParam -> /sysparam.getSysParam.do(PRIVATE)
18:06:37,864  INFO bioknow:21 -   /a/b -> /restfultest.test.do(PRIVATE)
18:06:37,864  INFO bioknow:21 -   /a/*/user/add -> /restfultest.test3.do(PUBLIC)
18:06:37,864  INFO bioknow:21 -   /a/*/user/update -> /restfultest.test4.do(PUBLIC)
18:06:37,865  INFO bioknow:21 -   /a/b/c -> /restfultest.test2.do(PRIVATE)
18:06:37,865  INFO bioknow:21 -   /wordreport/previewWordReport -> /wordPreviewServices.previewWordReport.do(PRIVATE)
18:06:37,865  INFO bioknow:21 -   /wordreport/wordReportShowInfo -> /wordPreviewServices.wordReportShowInfo.do(PRIVATE)
18:06:37,865  INFO bioknow:21 -   /wordreport/ajaxmenu -> /wordPreviewServices.ajaxmenu.do(PRIVATE)
18:06:37,865  INFO bioknow:21 -   /dyntable/delete -> /dyntableservices.delete.do(PRIVATE)
18:06:37,866  INFO bioknow:21 -   /dyntable/list -> /dyntableservices.list.do(PRIVATE)
18:06:37,866  INFO bioknow:21 -   /dyntable/save -> /dyntableservices.save.do(PRIVATE)
18:06:37,866  INFO bioknow:21 -   /dyntable/extract -> /dyntableservices.extract.do(PRIVATE)
18:06:37,866  INFO bioknow:21 -   /dyntable/listSearchFieldsVue -> /dyntableservices.listSearchFieldsVue.do(PRIVATE)
18:06:37,866  INFO bioknow:21 -   /dyntable/getReportformBtns -> /dyntableservices.getReportformBtns.do(PRIVATE)
18:06:37,867  INFO bioknow:21 -   /dyntable/previewListParamCustom -> /dyntableservices.previewListParamCustom.do(PRIVATE)
18:06:37,867  INFO bioknow:21 -   /dyntable/edit -> /dyntableservices.edit.do(PRIVATE)
18:06:37,867  INFO bioknow:21 -   /dyntable/tableFunc -> /dyntableservices.tableFunc.do(PRIVATE)
18:06:37,867  INFO bioknow:21 -   /dyntable/recordFunc -> /dyntableservices.recordFunc.do(PRIVATE)
18:06:37,867  INFO bioknow:21 -   /dyntable/editTableTab -> /dyntableservices.editTableTab.do(PRIVATE)
18:06:37,867  INFO bioknow:21 -   /dyntable/deleteAllRecord -> /dyntableservices.deleteAllRecord.do(PRIVATE)
18:06:37,868  INFO bioknow:21 -   /dyntable/specialformat -> /dyntableservices.specialformat.do(PRIVATE)
18:06:37,868  INFO bioknow:21 -   /dyntable/tabList -> /dyntableservices.tablist.do(PRIVATE)
18:06:37,868  INFO bioknow:21 -   /dyntable/navigate -> /dyntableservices.navigate.do(PRIVATE)
18:06:37,868  INFO bioknow:21 -   /dyntable/ajaxGetValue -> /dyntableservices.ajaxgetfieldvalue.do(PRIVATE)
18:06:37,868  INFO bioknow:21 -   /dyntable/getRefTable -> /dyntableservices.getAllRefTable.do(PRIVATE)
18:06:37,869  INFO bioknow:21 -   /servicesContrast/editContrast -> /servicescontrast.getContrastTable.do(PRIVATE)
18:06:37,869  INFO bioknow:21 -   /servicesContrast/saveContrast -> /servicescontrast.saveContrastTable.do(PRIVATE)
18:06:37,869  INFO bioknow:21 -   /uapplugIn/output -> /uappluginput.output.do(PRIVATE)
18:06:37,869  INFO bioknow:21 -   /uapplugIn/input -> /uappluginput.input.do(PRIVATE)
18:06:37,869  INFO bioknow:21 -   /uapplugIn/getTableColumnInfoList -> /uappluginput.getTableColumnInfoList.do(PRIVATE)
18:06:37,870  INFO bioknow:21 -   /uapplugIn/getAdvancedImportInfo -> /uappluginput.getAdvancedImportInfo.do(PRIVATE)
18:06:37,870  INFO bioknow:21 -   /uapplugIn/evaluateData -> /uappluginput.evaluateData.do(PRIVATE)
18:06:37,870  INFO bioknow:21 -   /uapplugIn/pre2 -> /uappluginput.preview2.do(PRIVATE)
18:06:37,871  INFO bioknow:21 -   /uapplugIn/showInputInfo -> /uappluginput.showinput.do(PRIVATE)
18:06:37,871  INFO bioknow:21 -   /uapplugIn/pre -> /uappluginput.preview.do(PRIVATE)
18:06:37,871  INFO bioknow:21 -   /publictable/count -> /publictable.count.do(PUBLIC)
18:06:37,872  INFO bioknow:21 -   /publictable/list -> /publictable.list.do(PUBLIC)
18:06:37,872  INFO bioknow:21 -   /publictable/save -> /publictable.save.do(PUBLIC)
18:06:37,872  INFO bioknow:21 -   /publictable/lastmodify -> /publictable.lastmodify.do(PUBLIC)
18:06:37,872  INFO bioknow:21 -   /dtplugoutput/getExportInfo -> /uapplugoutput.getExportInfo.do(PRIVATE)
18:06:37,873  INFO bioknow:21 -   /dtplugoutput/downladFileZip -> /uapplugoutput.downladFileZip.do(PRIVATE)
18:06:37,873  INFO bioknow:21 -   /dtplugoutput/downExcel -> /uapplugoutput.downExcel.do(PRIVATE)
18:06:37,873  INFO bioknow:21 -   /dtplugoutput/getFilesPath -> /uapplugoutput.getFilesPath.do(PRIVATE)
18:06:37,873  INFO bioknow:21 -   /dtplugoutput/showDownExcel -> /uapplugoutput.showDownExcel.do(PRIVATE)
18:06:37,874  INFO bioknow:21 -   /vuePubRemote/extract -> /vueDbPubRemote.extract.do(PRIVATE)
18:06:37,874  INFO bioknow:21 -   /vuePubRemote/extractConfig -> /vueDbPubRemote.extractConfig.do(PRIVATE)
18:06:37,875  INFO bioknow:21 -   /usersyn/upload -> /usersyn_publicinf.upload.do(PUBLIC)
18:06:37,875  INFO bioknow:21 -   /usersyn/download -> /usersyn_publicinf.download.do(PUBLIC)
18:06:37,875  INFO bioknow:21 -   /usersyn/userlogin -> /usersyn_publicinf.userlogin.do(PUBLIC)
18:06:37,875  INFO bioknow:21 -   /usersyn/doc -> /usersyn_publicinf.doc.do(PUBLIC)
18:06:37,876  INFO bioknow:21 -   /usersyn/funcresult -> /usersyn_publicinf.funcresult.do(PUBLIC)
18:06:37,876  INFO bioknow:21 -   /usersyn/funcstart -> /usersyn_publicinf.funcstart.do(PUBLIC)
18:06:37,877  INFO bioknow:21 -   /usersyn/docfunc -> /usersyn_publicinf.docfunc.do(PUBLIC)
18:06:37,877  INFO bioknow:21 -   /usersyn/getformid -> /usersyn_publicinf.getformid.do(PUBLIC)
18:06:37,877  INFO bioknow:21 -   /usersyn/datalist -> /usersyn_publicinf.datalist.do(PUBLIC)
18:06:37,877  INFO bioknow:21 -   /usersyn/datasave -> /usersyn_publicinf.datasave.do(PUBLIC)
18:06:37,878  INFO bioknow:21 -   /usersyn/datasize -> /usersyn_publicinf.datasize.do(PUBLIC)
18:06:37,878  INFO bioknow:21 -   /usersyn/userchangepwd -> /usersyn_publicinf.userchangepwd.do(PUBLIC)
18:06:37,878  INFO bioknow:21 -   /usersyn/gettoken -> /usersyn_publicinf.gettoken.do(PUBLIC)
18:06:37,879  INFO bioknow:21 -   /usersyn/userregister -> /usersyn_publicinf.userregister.do(PUBLIC)
18:06:37,879  INFO bioknow:21 -   /usersyn/usersearch -> /usersyn_publicinf.usersearch.do(PUBLIC)
18:06:37,879  INFO bioknow:21 -   /api/test -> /services.test.do(PRIVATE)
18:06:37,879  INFO bioknow:21 -   /api/test2 -> /services.test2.do(PRIVATE)
18:06:37,879  INFO bioknow:21 -   /dyntableJs/ajaxGetValue -> /tablejs.ajaxGetValue.do(PUBLIC)
18:06:37,880  INFO bioknow:21 -   /listEdit/getValue -> /listEditServices.getValue.do(PRIVATE)
18:06:37,880  INFO bioknow:21 -   /listEdit/save -> /listEditServices.save.do(PRIVATE)
18:06:37,880  INFO bioknow:21 -   /wxpub/signature -> /wxpub.signature.do(PUBLIC)
18:06:37,881  INFO bioknow:21 -   /wxpub/sso -> /wxpub.sso.do(PUBLIC)
18:06:37,881  INFO bioknow:21 -   /wxpub/sso_redirect -> /wxpub.sso_redirect.do(PUBLIC)
18:06:37,881  INFO bioknow:21 -   /groupExtract/extract -> /servicesGroupExtract.extract.do(PRIVATE)
18:06:37,881  INFO bioknow:21 -   /batchAppendNew/list -> /services_dtplug_batchappend.list.do(PRIVATE)
18:06:37,882  INFO bioknow:21 -   /services/tablehistory/savemodifyreason -> /services/tablehistory.savemodifyreason.do(PRIVATE)
18:06:37,882  INFO bioknow:21 -   /calendar/getCalendar -> /calendar.getCalendar.do(PRIVATE)
18:06:37,882  INFO bioknow:21 -   /wxmini/getinfo -> /wxmini.getinfo.do(PUBLIC)
18:06:37,882  INFO bioknow:21 -   /workflow/process -> /workflowservices.process.do(PRIVATE)
18:06:37,882  INFO bioknow:21 -   /workflow/reload -> /workflowservices.reload.do(PRIVATE)
18:06:37,883  INFO bioknow:21 -   /workflow/workflowInfo -> /workflowservices.getWorkfolwInfo.do(PRIVATE)
18:06:37,883  INFO bioknow:21 -   /workflow/showHistoryInfo -> /workflowservices.showHistoryInfo.do(PRIVATE)
18:06:37,883  INFO bioknow:21 -   /workflow/batchProcessListBtn -> /workflowservices.batchProcessListBtn.do(PRIVATE)
18:06:37,883  INFO bioknow:21 -   /workflow/batchProcessvueHtml -> /workflowservices.batchProcessvueHtml.do(PRIVATE)
18:06:37,884  INFO bioknow:21 -   /workflow/batchProcessHtml -> /workflowservices.batchProcessHtml.do(PRIVATE)
18:06:37,884  INFO bioknow:21 -   /workflow/getProcessInfo -> /workflowservices.getProcessInfo.do(PRIVATE)
18:06:37,884  INFO bioknow:21 -   /workflow/batchProcess -> /workflowservices.batchProcess.do(PRIVATE)
18:06:37,884  INFO bioknow:21 -   /workflowgetwf -> /workflowservices.getwf.do(PRIVATE)
18:06:37,884  INFO bioknow:21 -   /reviewConfig/saveLink -> /onlineReviewConfig.saveLink.do(PRIVATE)
18:06:37,884  INFO bioknow:21 -   /reviewConfig/link -> /onlineReviewConfig.link.do(PRIVATE)
18:06:37,885  INFO bioknow:21 -   /onLineReview/rename -> /onlineReview.rename.do(PRIVATE)
18:06:37,885  INFO bioknow:21 -   /onLineReview/getSaveButton -> /onlineReview.getSaveButton.do(PRIVATE)
18:06:37,885  INFO bioknow:21 -   /onLineReview/saveDocument -> /onlineReview.saveDocument.do(PRIVATE)
18:06:37,885  INFO bioknow:21 -   /onLineReview/editParam -> /onlineReview.editParam.do(PRIVATE)
18:06:37,885  INFO bioknow:21 -   /onLineReview/pass -> /onlineReview.pass.do(PRIVATE)
18:06:37,886  INFO bioknow:21 -   /onLineReview/showDocument -> /onlineReview.showDocument.do(PRIVATE)
18:06:37,886  INFO bioknow:21 -   /onLineReview/review -> /onlineReview.review.do(PRIVATE)
18:06:37,886  INFO bioknow:21 -   /onLineReview/fileList -> /onlineReview.fileList.do(PRIVATE)
18:06:37,886  INFO bioknow:21 -   /onLineReview/show -> /onlineReview.show.do(PRIVATE)
18:06:37,886  INFO bioknow:21 -   /onlineReview/save -> /online/review.save.do(PRIVATE)
18:06:37,887  INFO bioknow:21 -   /onlineReview/edit -> /online/review.edit.do(PRIVATE)
18:06:37,887  INFO bioknow:21 -   /onlineReview/jump -> /online/review.jump.do(PRIVATE)
18:06:37,887  INFO bioknow:21 -   /onlineReview/workflow -> /online/review.getWorkFlow.do(PRIVATE)
18:06:37,887  INFO bioknow:21 -   /onlineReview/saveReview -> /online/review.saveReview.do(PRIVATE)
18:06:37,888  INFO bioknow:21 -   /btnservices/showinput -> /btnshowinput.showinput.do(PRIVATE)
18:06:37,888  INFO bioknow:21 -   /btnservices/outputparam -> /btnshowinput.outputparam.do(PRIVATE)
18:06:37,888  INFO bioknow:21 -   /outProcess/verifyCode -> /out_process.verifyCode.do(PUBLIC)
18:06:37,888  INFO bioknow:21 -   /outProcess/review -> /out_process.review.do(PUBLIC)
18:06:37,888  INFO bioknow:21 -   /outProcess/approved -> /out_process.approved.do(PUBLIC)
18:06:37,889  INFO bioknow:21 -   /outProcess/sendCode -> /out_process.sendCode.do(PUBLIC)
18:06:37,889  INFO bioknow:21 -   /outProcess/active -> /out_process.active.do(PUBLIC)
18:06:37,889  INFO bioknow:21 -   /outProcess/getConfig -> /out_process.getConfig.do(PUBLIC)
18:06:37,890  INFO bioknow:21 -   /userui/saveUserUi -> /services_userui.saveUserUi.do(PRIVATE)
18:06:37,890  INFO bioknow:21 -   /userui/showUserUi -> /services_userui.showUserUi.do(PRIVATE)
18:06:37,890  INFO bioknow:21 -   /userui/deleteAllUserUi -> /services_userui.deleteAllUserUi.do(PRIVATE)
18:06:37,890  INFO bioknow:21 -   /userui/showRefFields -> /services_userui.showRefFields.do(PRIVATE)
18:06:37,891  INFO bioknow:21 -   /userui/restoreDeault -> /services_userui.restoreDefault.do(PRIVATE)
18:06:37,891  INFO bioknow:21 -   /customFunctionFormula/getCustomFunctionFormula -> /customFunctionFormula.getCustomFunctionFormula.do(PRIVATE)
18:06:37,891  INFO bioknow:21 -   /customFunctionFormula/customizeToSave -> /customFunctionFormula.customizeToSave.do(PRIVATE)
18:06:37,892  INFO bioknow:21 -   /queryServices/save -> /actionQuery.save.do(PRIVATE)
18:06:37,892  INFO bioknow:21 -   /queryServices/setSaveForm -> /actionQuery.AnnotionsSave.do(PRIVATE)
18:06:37,892  INFO bioknow:21 -   /queryServices/getEditForm -> /actionQuery.AnnotionsEdit.do(PRIVATE)
18:06:37,892  INFO bioknow:21 -   /dataSync/callback -> /dataSync.callback.do(PRIVATE)
18:06:37,893  INFO bioknow:21 -   /dataSync/createTargetTable -> /dataSync.createTargetTable.do(PUBLIC)
18:06:37,893  INFO bioknow:21 -   /dataSync/initDataSyncStart -> /dataSync.initDataSyncStart.do(PRIVATE)
18:06:37,893  INFO bioknow:21 -   /dataSync/assistOfDataSyncPage -> /dataSync.assistOfDataSyncPage.do(PRIVATE)
18:06:37,893  INFO bioknow:21 -   /dataSync/tableMappingPlatform -> /dataSync.tableMappingPlatform.do(PRIVATE)
18:06:37,894  INFO bioknow:21 -   /dataSync/getRemoteDbConfig -> /dataSync.getRemoteDbConfig.do(PUBLIC)
18:06:37,894  INFO bioknow:21 -   /dataSync/getAuxiliaryEntry -> /dataSync.getAuxiliaryEntry.do(PRIVATE)
18:06:37,894  INFO bioknow:21 -   /dataSync/showAuxiliaryEntry -> /dataSync.showAuxiliaryEntry.do(PRIVATE)
18:06:37,894  INFO bioknow:21 -   /dataSync/initTableMappingData -> /dataSync.initTableMappingData.do(PRIVATE)
18:06:37,895  INFO bioknow:21 -   /dataSync/dataSyncTableManualPull -> /dataSync.dataSyncTableManualPull.do(PRIVATE)
18:06:37,895  INFO bioknow:21 -   /dataSync/getUapTableXml -> /dataSync.getUapTableXml.do(PUBLIC)
18:06:37,896  INFO bioknow:21 -   /dataSync/findValueByCode -> /dataSync.findValueByCode.do(PUBLIC)
18:06:37,896  INFO bioknow:21 -   /dataSync/assistOfDataSync -> /dataSync.assistOfDataSync.do(PRIVATE)
18:06:37,896  INFO bioknow:21 -   /dataSync/dataBasePlatform -> /dataSync.dataBasePlatform.do(PRIVATE)
18:06:37,896  INFO bioknow:21 -   /dataSync/registerPlatform -> /dataSync.registerPlatform.do(PRIVATE)
18:06:37,896  INFO bioknow:21 -   /fileToPdfStatus/getFile2PdfStatus -> /fileToPdfStatus.getFile2PdfStatus.do(PRIVATE)
18:06:37,897  INFO bioknow:21 -   /schedule/getRespon -> /scheduleGanttjson.getRespon.do(PRIVATE)
18:06:37,897  INFO bioknow:21 -   /schedule/finishSchedule -> /scheduleGanttjson.finishSchedule.do(PRIVATE)
18:06:37,897  INFO bioknow:21 -   /schedule/init -> /scheduleGanttjson.initSchedulePage.do(PRIVATE)
18:06:37,897  INFO bioknow:21 -   /schedule/getScheduleTime -> /scheduleGanttjson.getScheduleTime.do(PRIVATE)
18:06:37,897  INFO bioknow:21 -   /email_send/send_mail -> /services_email.sendmail.do(PRIVATE)
18:06:37,898  INFO bioknow:21 -   /email_send/send -> /services_email.send.do(PRIVATE)
18:06:37,898  INFO bioknow:21 -   /email_send/show_mail -> /services_email.showmail.do(PRIVATE)
18:06:37,898  INFO bioknow:21 -   /email_send/refresh_time -> /services_email.refreshtime.do(PRIVATE)
18:06:37,898  INFO bioknow:21 -   /email_send/show_detail -> /services_email.showdetail.do(PRIVATE)
18:06:37,898  INFO bioknow:21 -   /datamng/appRoleSync -> /datamng.appRoleSync.do(PUBLIC)
18:06:37,898  INFO bioknow:21 -   /datamng/ajaxgetfields -> /datamng.ajaxgetfields.do(PRIVATE)
18:06:37,899  INFO bioknow:21 -   /datamng/navigate -> /datamng.navigate.do(PRIVATE)
18:06:37,899  INFO bioknow:21 -   /dbview/status -> /dbviewservices.status.do(PRIVATE)
18:06:37,899  INFO bioknow:21 -   /dbview/export -> /dbviewservices.export.do(PRIVATE)
18:06:37,899  INFO bioknow:21 -   /dbview/show -> /dbviewservices.showData.do(PRIVATE)
18:06:37,899  INFO bioknow:21 -   /dbview/tab -> /dbviewservices.showTab.do(PRIVATE)
18:06:37,899  INFO bioknow:21 -   /dbview/viewList -> /dbviewservices.getViewList.do(PRIVATE)
18:06:37,900  INFO bioknow:21 -   /dtplugoutput/getExportInfo -> /uaplugoutput.getExportInfo.do(PRIVATE)
18:06:37,900  INFO bioknow:21 -   /dtplugoutput/downladFileZip -> /uaplugoutput.downladFileZip.do(PRIVATE)
18:06:37,900  INFO bioknow:21 -   /dtplugoutput/downExcel -> /uaplugoutput.downExcel.do(PRIVATE)
18:06:37,900  INFO bioknow:21 -   /dtplugoutput/getFilesPath -> /uaplugoutput.getFilesPath.do(PRIVATE)
18:06:37,900  INFO bioknow:21 -   /dtplugoutput/showDownExcel -> /uaplugoutput.showDownExcel.do(PRIVATE)
18:06:37,900  INFO bioknow:21 -   /modifyHistory/listBatch -> /detailHistory.listBatch.do(PRIVATE)
18:06:37,901  INFO bioknow:21 -   /modifyHistory/listField -> /detailHistory.listField.do(PRIVATE)
18:06:37,901  INFO bioknow:21 -   /mail/send -> /noticetransemail.sendmail.do(PUBLIC)
18:06:37,901  INFO bioknow:21 -   /dbjson/save -> /dbjson.save.do(PRIVATE)
18:06:37,901  INFO bioknow:21 -   /dbjson/savepub -> /dbjson.savepub.do(PUBLIC)
18:06:37,901  INFO bioknow:21 -   /dbjson/doc -> /dbjson.doc.do(PRIVATE)
18:06:37,902  INFO bioknow:21 -   /schemaRadio/ajaxgetdic -> /schemaRadio.ajaxgetdic.do(PRIVATE)
18:06:37,902  INFO bioknow:21 -   /outputadv/ajaxmenu -> /outputadv.ajaxmenu.do(PRIVATE)
18:06:37,902  INFO bioknow:21 -   /buttonsConfig/getConfig -> /buttonsCfg.setup.do(PUBLIC)
18:06:37,902  INFO bioknow:21 -   /buttonsConfig/reSet -> /buttonsCfg.reSet.do(PUBLIC)
18:06:37,902  INFO bioknow:21 -   /buttonsConfig/saveConfig -> /buttonsCfg.saveConfig.do(PUBLIC)
AppStartFace.onStart() done.
18:06:38,224  INFO bioknow:21 - Dailywork: System.gc()
Load TimerTask done.
--------------------------------------------
App Initialized (8s).
[2025-02-19 06:06:38,339] Artifact Test:war exploded: Artifact is deployed successfully
[2025-02-19 06:06:38,340] Artifact Test:war exploded: Deploy took 15,056 milliseconds
18:06:38,667  INFO AbstractPoolBackedDataSource:462 - Initializing c3p0 pool... com.mchange.v2.c3p0.PoolBackedDataSource@adb9bd1c [ connectionPoolDataSource -> com.mchange.v2.c3p0.WrapperConnectionPoolDataSource@9cfb633d [ acquireIncrement -> 1, acquireRetryAttempts -> 2, acquireRetryDelay -> 500, autoCommitOnClose -> false, automaticTestTable -> null, breakAfterAcquireFailure -> false, checkoutTimeout -> 10000, connectionCustomizerClassName -> null, connectionTesterClassName -> com.mchange.v2.c3p0.impl.DefaultConnectionTester, debugUnreturnedConnectionStackTraces -> false, factoryClassLocation -> null, forceIgnoreUnresolvedTransactions -> false, identityToken -> 2svc3pb988eeup1x96n1h|45e91c03, idleConnectionTestPeriod -> 120, initialPoolSize -> 0, maxAdministrativeTaskTime -> 0, maxConnectionAge -> 300, maxIdleTime -> 300, maxIdleTimeExcessConnections -> 60, maxPoolSize -> 100, maxStatements -> 100, maxStatementsPerConnection -> 0, minPoolSize -> 0, nestedDataSource -> com.mchange.v2.c3p0.DriverManagerDataSource@eb2f231b [ description -> null, driverClass -> null, factoryClassLocation -> null, identityToken -> 2svc3pb988eeup1x96n1h|2b093bd, jdbcUrl -> **************************************************************************************************, properties -> {autoReconnect=true, is-connection-validation-required=true, user=******, password=******, autoReconnectForPools=true} ], preferredTestQuery -> null, propertyCycle -> 0, testConnectionOnCheckin -> false, testConnectionOnCheckout -> false, unreturnedConnectionTimeout -> 0, usesTraditionalReflectiveProxies -> false; userOverrides: {} ], dataSourceName -> null, factoryClassLocation -> null, identityToken -> 2svc3pb988eeup1x96n1h|7df1a455, numHelperThreads -> 2 ]
18:06:40,026  INFO bioknow:21 - Cannot find server.xml
