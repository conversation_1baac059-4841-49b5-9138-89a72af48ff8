import airflow
from airflow.models import DAG
from airflow.operators.bash import Ba<PERSON><PERSON>perator 
from airflow.utils.dates import days_ago
import pymysql

args = {
    'owner': 'hengrui',
    'start_date': days_ago(1)
}

dag = DAG(dag_id='mysql_to_scp', default_args=args, schedule_interval=None)

# 任务1:使用PyMySQL查询报告文件名和文件路径 
task1 = BashOperator(
    task_id='mysql_query',
    bash_command='python /path/to/mysql_query.py', 
    dag=dag,
)

# 任务2:使用SSH下载文件并重命名
# (同上)

# mysql_query.py内容
import pymysql

conn = pymysql.connect(host='127.0.0.1',port=3306,database='root',user='hengrui',password='Hr_airflow0509')
cursor = conn.cursor()
sql = "SELECT dag_id, fileloc FROM dag"
cursor.execute(sql)
results = cursor.fetchall()

for row in results:
    filename = row[0]
    filepath = row[1]

print(f"{filename},{filepath}")
cursor.close() 
conn.close()