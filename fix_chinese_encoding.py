#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON><PERSON> to fix Chinese character encoding in JSON files
Python 2.7 compatible version
"""

import json
import codecs

def fix_chinese_encoding(input_file, output_file=None):
    """
    Fix Chinese character encoding in JSON file
    """
    if output_file is None:
        output_file = input_file.replace('.txt', '_fixed.json')

    # Try different encoding combinations
    encodings_to_try = [
        ('utf-8', 'utf-8'),
        ('gbk', 'utf-8'),
        ('gb2312', 'utf-8'),
        ('gb18030', 'utf-8'),
        ('big5', 'utf-8'),
        ('latin1', 'utf-8'),
        ('cp1252', 'utf-8'),
    ]

    print("Attempting to fix encoding for: " + input_file)

    for read_encoding, write_encoding in encodings_to_try:
        try:
            print("Trying to read as " + read_encoding + " and write as " + write_encoding + "...")

            # Read the file with the specified encoding
            with codecs.open(input_file, 'r', encoding=read_encoding, errors='ignore') as f:
                content = f.read()

            # Try to parse as JSON to validate
            try:
                data = json.loads(content)
                print("Successfully parsed JSON with " + read_encoding + " encoding")

                # Write the fixed file
                with codecs.open(output_file, 'w', encoding=write_encoding, errors='ignore') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                print("Fixed file saved as: " + output_file)
                return True

            except ValueError as e:  # JSONDecodeError doesn't exist in Python 2.7
                print("JSON parsing failed with " + read_encoding + ": " + str(e))
                continue

        except Exception as e:
            print("Failed with " + read_encoding + ": " + str(e))
            continue

    print("Could not fix encoding automatically. Manual intervention may be required.")
    return False

def manual_fix_garbled_text(input_file, output_file=None):
    """
    Manually fix some common garbled Chinese text patterns
    """
    if output_file is None:
        output_file = input_file.replace('.txt', '_manual_fixed.json')

    # Common garbled text mappings (you may need to add more based on your specific data)
    replacements = {
        # Add specific mappings if you know what the garbled text should be
        # Example: "缂冩垹鐝崘鍛啇": "网站内容",
    }

    try:
        with codecs.open(input_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Apply replacements
        for garbled, correct in replacements.items():
            content = content.replace(garbled, correct)

        # Try to parse and reformat JSON
        data = json.loads(content)

        with codecs.open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        print("Manually fixed file saved as: " + output_file)
        return True

    except Exception as e:
        print("Manual fix failed: " + str(e))
        return False

if __name__ == "__main__":
    input_file = "json.txt"

    # Try automatic fix first
    if not fix_chinese_encoding(input_file):
        # If automatic fix fails, try manual fix
        manual_fix_garbled_text(input_file)
