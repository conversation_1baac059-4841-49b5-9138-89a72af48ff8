/*
 Navicat MySQL Data Transfer

 Source Server         : 测试环境
 Source Server Type    : MySQL
 Source Server Version : 80028 (8.0.28)
 Source Host           : ***********:3306
 Source Schema         : susar_auto_mail

 Target Server Type    : MySQL
 Target Server Version : 80028 (8.0.28)
 File Encoding         : 65001

 Date: 20/04/2023 23:01:23
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_site
-- ----------------------------
DROP TABLE IF EXISTS `t_site`;
CREATE TABLE `t_site`  (
  `ID` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `compound_no` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `compound_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `study_no` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `study_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `site_no` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `site_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_site
-- ----------------------------
INSERT INTO `t_site` VALUES ('SHR-6390-III-301CN001', 'SHR6390', 'SHR6390', 'SHR-6390-III-301', '用于SUSAR报告平台内部人员测试', 'CN001', '中国医学科学院肿瘤医院');
INSERT INTO `t_site` VALUES ('SHR-6390-III-301CN007', 'SHR6390', 'SHR6390', 'SHR-6390-III-301', '用于SUSAR报告平台内部人员测试', 'CN007', '四川大学华西医院');
INSERT INTO `t_site` VALUES ('SHR4640-301CN001', 'SHR4640', 'SHR4640', 'SHR4640-301', '用于SUSAR报告平台内部人员测试01，多中心添加研究者', 'CN001', ' 上海交通大学医学院附属仁济医院');
INSERT INTO `t_site` VALUES ('SHR4640-301CN048', 'SHR4640', 'SHR4640', 'SHR4640-301', '用于SUSAR报告平台内部人员测试01，多中心添加研究者', 'CN048', '江西省人民医院');
INSERT INTO `t_site` VALUES ('HRS-1358-I-101CN001', 'HRS-1358', 'HRS-1358', 'HRS-1358-I-101', '用于SUSAR报告平台PV&CTA人员测试用001研究项目', 'CN001', '中国医学科学院肿瘤医院');
INSERT INTO `t_site` VALUES ('HRS-1358-I-101CN002', 'HRS-1358', 'HRS-1358', 'HRS-1358-I-101', '用于SUSAR报告平台PV&CTA人员测试用001研究项目', 'CN002', '天津市肿瘤医院');
INSERT INTO `t_site` VALUES ('HRS9531-102CN001', 'HRS9531', 'HRS9531', 'HRS9531-102', '用于SUSAR报告平台PV&CTA人员测试用002研究项目', 'CN001', '复旦大学附属中山医院');
INSERT INTO `t_site` VALUES ('HRS9531-102CN004', 'HRS9531', 'HRS9531', 'HRS9531-102', '用于SUSAR报告平台PV&CTA人员测试用002研究项目', 'CN004', '重庆医科大学附属第一医院');
INSERT INTO `t_site` VALUES ('INS068-301CN001', 'INS068', 'INS068', 'INS068-301', '用于SUSAR报告平台PV&CTA人员测试用003研究项目', 'CN001', '复旦大学附属中山医院');
INSERT INTO `t_site` VALUES ('INS068-301CN007', 'INS068', 'INS068', 'INS068-301', '用于SUSAR报告平台PV&CTA人员测试用003研究项目', 'CN007', '安徽省立医院');
INSERT INTO `t_site` VALUES ('SHR-A1811-II-203CN001', 'SHR-A1811,SHR-1316', 'SHR-A1811,SHR-1316', 'SHR-A1811-II-203', '用于SUSAR报告平台内部人员测试04，联合用药', 'CN001', '南京医科大学第一附属医院');
INSERT INTO `t_site` VALUES ('SHR-A1811-II-203CN003', 'SHR-A1811,SHR-1316', 'SHR-A1811,SHR-1316', 'SHR-A1811-II-203', '用于SUSAR报告平台内部人员测试04，联合用药', 'CN003', '安徽省立医院');
INSERT INTO `t_site` VALUES ('SHR-1701-209CN001', 'SHR-1701,HRS-1358', 'SHR-1701,HRS-1358', 'SHR-1701-209', 'SUSAR平台测试（PV）', 'CN001', '中国医学科学院肿瘤医院');
INSERT INTO `t_site` VALUES ('SHR-1701-209CN002', 'SHR-1701,HRS-1358', 'SHR-1701,HRS-1358', 'SHR-1701-209', 'SUSAR平台测试（PV）', 'CN002', '天津市肿瘤医院');
INSERT INTO `t_site` VALUES ('SHR-1316-322CN001', 'SHR-1316', 'SHR-1316', 'SHR-1316-322', 'SUSAR平台测试（PV）', 'CN001', '复旦大学附属中山医院');

SET FOREIGN_KEY_CHECKS = 1;
