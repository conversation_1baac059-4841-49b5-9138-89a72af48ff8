1.新增随机申请表版本记录 
POST
https://sas-online-tst.hengrui.com/sas_online/addBatchRecord?studyId=HRS-5635-101&userName=周辉&applyDate=2024-11-25
body:
{
    "applyType": "1",
    "rmdm": "1",
    "levelFactor": "0",
    "studyDesign": "2",
    "secondRand": "0",
    "subjectReplace": "1",
    "account": "test",
    "medDesignNum": "3",
    "queueNum": "2"
}

response:
{
    "code": 200,
    "msg": "OK",
    "data": "success"
}


2.编辑随机申请表版本记录
POST
https://sas-online-tst.hengrui.com/sas_online/editFormInfo?studyId=HRS-5635-101&batchNum=4d9fe92b79fd6210&userName=周辉
body:
{
    "applyType": "1",
    "rmdm": "1",
    "levelFactor": "0",
    "studyDesign": "2",
    "secondRand": "0",
    "subjectReplace": "1",
    "account": "test",
    "medDesignNum": "0",
    "queueNum": "0"
}

response:
{
    "code": 200,
    "msg": "OK",
    "data": "success"
}



3.删除随机申请表记录
GET
https://sas-online-tst.hengrui.com/sas_online/deleteBatchRecord?studyId=HRS-5635-101&batchNum=4d9fe92b79fd6210&userName=张三

response:
{
    "code": 200,
    "msg": "OK",
    "data": "success"
}