from airflow import DAG
from airflow.operators.python import PythonOperator
from datetime import datetime
import os

# DAG参数
DAG_DEFAULT_ARGS = {
    'start_date': datetime(2023, 05, 23)
}

# 下载文件函数
def download_files():
    download_cmds = [
        'mc cp minios3/airflow-dags/kkFileView.rar /tmp/hsperfdata_root/sas_script/kkFileView.rar',
        'mc cp minios3/raw/pgm/m_post2s3.sas /tmp/hsperfdata_root/sas_script'
    ]
    for cmd in download_cmds:
        os.system(cmd)
    print(f'Files downloaded at {datetime.now()}')

# 打印当前时间函数    
def print_time(): 
    print(f'Current time is {datetime.now()}')

# 定义DAG    
with DAG('download_files', schedule_interval='@once', default_args=DAG_DEFAULT_ARGS) as dag:

    # 任务1:下载文件
    task1 = PythonOperator(
        task_id='download_files',
        python_callable=download_files,
        dag=dag
    )

    # 任务2:打印当前时间
    task2 = PythonOperator(
        task_id='print_time',
        python_callable=print_time,
        dag=dag
    )

# 设置依赖关系 
task1 >> task2
