




@reboot mount.cifs //SHNVWCDTMST01/edm_uap/dtattach /home/<USER>/edm_uap_file -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root

@reboot mount.cifs //SHNVWSASECT01/EDMtst/EDM/blind_sas_log /home/<USER>/blind_sas_log -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root

@reboot mount.cifs //SHNVWSASECT01/EDMtst/EDM/csv_raw /home/<USER>/ex_original_csv -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root

@reboot mount.cifs //SHNVWSASECT01/EDMtst/EDM/json_blind /home/<USER>/ex_json_file -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root

@reboot /usr/local/nginx/sbin/nginx


@reboot nohup java -jar /home/<USER>/blind_back-0.0.1-SNAPSHOT.jar >/home/<USER>/log.txt & 

@reboot sh /home/<USER>/kkFileView-4.3.0/bin/shutdown.sh

@reboot sh /home/<USER>/kkFileView-4.3.0/bin/startup.sh





nohup java -jar /home/<USER>/home/<USER>/medcodingDownload_$(date +%Y%m%d).log 





\tmp\hsperfdata_root\SusarFolderTest\file\H2023001201118 SHR-TEST-216_SAE03_FU11_TEST9.pdf

/tmp/hsperfdata_root/SusarFolderTest/file/


 getUserNoPage	
 
 DATE_FORMAT( update_time, '%Y-%m-%d %H:%i:%s' )  as updateTime
 1.VUE admin template
2.Java SpringBoot后端框架+mybatis+mysql+shiro+JWT
3.xxl_job分布式调度工具
4.Redis
5.Nginx
6.kkfileview
