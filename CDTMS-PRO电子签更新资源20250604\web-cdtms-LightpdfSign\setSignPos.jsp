<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=utf-8" %>
<%@ page language="java" pageEncoding="utf-8" %>
<%@ taglib uri="/webutil/tlib/bioknow-tlib.tld" prefix="bt" %>

<!DOCTYPE html>
<html lang="en" class="hidden-page">
<head>
    <head>
        <head>
            <script>
                // 页面加载前立即执行，尝试隐藏父窗口的dialog和遮罩层
                (function() {
                    try {
                        // 尝试获取并隐藏父窗口中的dialog - 使用原生JavaScript
                        if (window.parent && window.parent.document) {
                            // 如果当前页面在iframe中
                            if (window.frameElement) {
                                // 查找包含当前iframe的dialog或modal
                                var frame = window.frameElement;
                                var parentDoc = window.parent.document;
                                   // 处理Element UI的对话框
                        var elDialogs = parentDoc.querySelectorAll('.el-dialog__wrapper');
                        if (elDialogs.length > 0) {
                            for (var i = 0; i < elDialogs.length; i++) {
                                elDialogs[i].style.display = 'none';
                                elDialogs[i].style.visibility = 'hidden';
                                elDialogs[i].style.opacity = '0';
                                elDialogs[i].style.pointerEvents = 'none';
                            }
                        }
                        
                        // 处理Element UI的遮罩层
                        var elMasks = parentDoc.querySelectorAll('.v-modal');
                        if (elMasks.length > 0) {
                            for (var i = 0; i < elMasks.length; i++) {
                                elMasks[i].style.display = 'none';
                            }
                        }
                                
                                // 尝试查找父元素中的对话框
                                var findParentDialog = function(element) {
                                    if (!element || element === parentDoc.body) return null;
                                    
                                    // 检查当前元素是否是对话框
                                    if (element.classList && 
                                        (element.classList.contains('modal') || 
                                         element.classList.contains('dialog') || 
                                         element.getAttribute('role') === 'dialog')) {
                                        return element;
                                    }
                                    
                                    // 递归查找父元素
                                    return findParentDialog(element.parentNode);
                                };
                                
                                var parentDialog = findParentDialog(frame);
                                
                                if (parentDialog) {
                                    // 隐藏dialog但不关闭它
                                    parentDialog.style.visibility = 'hidden';
                                    parentDialog.style.opacity = '0';
                                    parentDialog.style.pointerEvents = 'none';
                                    parentDialog.style.position = 'absolute';
                                    parentDialog.style.width = '0';
                                    parentDialog.style.height = '0';
                                    parentDialog.style.overflow = 'hidden';
                                    
                                    // 同时处理遮罩层
                                    var modalBackdrops = parentDoc.querySelectorAll('.modal-backdrop');
                                    if (modalBackdrops.length > 0) {
                                        for (var i = 0; i < modalBackdrops.length; i++) {
                                            modalBackdrops[i].style.display = 'none';
                                            modalBackdrops[i].style.opacity = '0';
                                            modalBackdrops[i].style.pointerEvents = 'none';
                                        }
                                    }
                                    
                                    // 移除body上的modal-open类，防止滚动被禁用
                                    if (parentDoc.body.classList.contains('modal-open')) {
                                        parentDoc.body.classList.remove('modal-open');
                                    }
                                } else {
                                    // 如果找不到特定的对话框，尝试隐藏iframe本身
                                    frame.style.display = 'none';
                                    
                                    // 同时处理遮罩层
                                    var modalBackdrops = parentDoc.querySelectorAll('.modal-backdrop');
                                    if (modalBackdrops.length > 0) {
                                        for (var i = 0; i < modalBackdrops.length; i++) {
                                            modalBackdrops[i].style.display = 'none';
                                        }
                                    }
                                    
                                    // 移除body上的modal-open类
                                    if (parentDoc.body.classList.contains('modal-open')) {
                                        parentDoc.body.classList.remove('modal-open');
                                    }
                                }
                            }
                        }
                    } catch (e) {
                        console.error("隐藏父窗口dialog失败:", e);
                    }
                })();
            </script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加签署人</title>
    <link href="/cdtms/LightpdfSign/js/bootstrap.min.css" rel="stylesheet">
    <script type="text/javascript" src="/cdtms/LightpdfSign/js/jquery.min.js"></script>

    <link href="/cdtms/LightpdfSign/js/select2.min.css" rel="stylesheet"/>
    <script src="/cdtms/LightpdfSign/js/select2.min.js"></script>

    <link href="/cdtms/LightpdfSign/js/style.css" rel="stylesheet">



    <link rel=stylesheet type="text/css" href="/public/css/public.css"></link>
    <style>
              /* 添加隐藏页面的样式 */
           /* 添加隐藏页面的样式 - 更强的隐藏效果 */
           html.hidden-page, 
        html.hidden-page body {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            height: 0 !important;
            width: 0 !important;
            overflow: hidden !important;
            position: absolute !important;
            pointer-events: none !important;
            margin: 0 !important;
            padding: 0 !important;
        }
        body {
            /*background-color: #f8f9fa;*/
        }

        .container {
            /*margin-top: 50px;*/
            /*width: 400px;*/
        }

        label {
            font-weight: bold;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .editor-wrapper {
            border: 1px solid #ccc;
        }

        #toolbar-container {
            border-bottom: 1px solid #ccc;
        }

        #editor-container {
            height: 200px;
        }

        button[type="submit"] {
            display: block;
            width: 100%;
            padding: 10px;
            font-size: 18px;
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        /* Custom styles for Select2 and Bootstrap integration */
        .select2-container {
            width: 100% !important;
        }
        .select2-search__field{
            /* height: 28px; */
             border:none !important;
             padding-right:0px !important;
            /* background-color: #ffffff; */
            /* border-radius: 4px; */
            /* outline: none; */
            /* padding-left: 5px; */
            /* padding-right: 5px; */
            /* font-size: 12px; */
            /* color: #606266; */
            /* transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1); */
        }
        #overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        #iframeContainer {
            position: relative;
            width: 80%;
            max-width: 500px; /* Adjust the maximum width as needed */
            height: 250px;
            background-color: #fff; /* Adjust the background color as needed */
            border-radius: 8px;
            overflow: hidden;
            padding: 20px
        }

        #closeButton {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
        }

    </style>
</head>
<body>
        <!-- 在页面顶部获取 setUrl -->
        <c:if test="${not empty setUrl}">
            <script>
                // 定义一个可以重复调用的函数来处理签字位置设置
                function openSignPositionPage(url) {
                    // 尝试隐藏父窗口的dialog
                    try {
                        if (window.parent && window.parent.document) {
                            if (window.frameElement) {
                                var frame = window.frameElement;
                                var parentDoc = window.parent.document;
                                
                                // 处理Element UI的对话框
                                var elDialogs = parentDoc.querySelectorAll('.el-dialog__wrapper');
                                if (elDialogs.length > 0) {
                                    for (var i = 0; i < elDialogs.length; i++) {
                                        elDialogs[i].style.display = 'none';
                                        elDialogs[i].style.visibility = 'hidden';
                                        elDialogs[i].style.opacity = '0';
                                        elDialogs[i].style.pointerEvents = 'none';
                                    }
                                }
                                
                                // 处理Element UI的遮罩层
                                var elMasks = parentDoc.querySelectorAll('.v-modal');
                                if (elMasks.length > 0) {
                                    for (var i = 0; i < elMasks.length; i++) {
                                        elMasks[i].style.display = 'none';
                                    }
                                }
                                
                                // 尝试查找父元素中的对话框
                                var findParentDialog = function(element) {
                                    if (!element || element === parentDoc.body) return null;
                                    
                                    // 检查当前元素是否是对话框
                                    if (element.classList && 
                                        (element.classList.contains('modal') || 
                                         element.classList.contains('dialog') || 
                                         element.getAttribute('role') === 'dialog')) {
                                        return element;
                                    }
                                    
                                    // 递归查找父元素
                                    return findParentDialog(element.parentNode);
                                };
                                
                                var parentDialog = findParentDialog(frame);
                                
                                if (parentDialog) {
                                    // 隐藏dialog但不关闭它
                                    parentDialog.style.visibility = 'hidden';
                                    parentDialog.style.opacity = '0';
                                    parentDialog.style.pointerEvents = 'none';
                                    parentDialog.style.position = 'absolute';
                                    parentDialog.style.width = '0';
                                    parentDialog.style.height = '0';
                                    parentDialog.style.overflow = 'hidden';
                                    
                                    // 同时处理遮罩层
                                    var modalBackdrops = parentDoc.querySelectorAll('.modal-backdrop');
                                    if (modalBackdrops.length > 0) {
                                        for (var i = 0; i < modalBackdrops.length; i++) {
                                            modalBackdrops[i].style.display = 'none';
                                            modalBackdrops[i].style.opacity = '0';
                                            modalBackdrops[i].style.pointerEvents = 'none';
                                        }
                                    }
                                    
                                    // 移除body上的modal-open类，防止滚动被禁用
                                    if (parentDoc.body.classList.contains('modal-open')) {
                                        parentDoc.body.classList.remove('modal-open');
                                    }
                                } else {
                                    // 如果找不到特定的对话框，尝试隐藏iframe本身
                                    frame.style.display = 'none';
                                    
                                    // 同时处理遮罩层
                                    var modalBackdrops = parentDoc.querySelectorAll('.modal-backdrop');
                                    if (modalBackdrops.length > 0) {
                                        for (var i = 0; i < modalBackdrops.length; i++) {
                                            modalBackdrops[i].style.display = 'none';
                                        }
                                    }
                                    
                                    // 移除body上的modal-open类
                                    if (parentDoc.body.classList.contains('modal-open')) {
                                        parentDoc.body.classList.remove('modal-open');
                                    }
                                }
                            }
                        }
                    } catch (e) {
                        console.error("隐藏父窗口dialog失败:", e);
                    }
                    
                    // 尝试关闭当前弹窗
                    if (window.top && window.top.webmask_close_modal) {
                        window.top.webmask_close_modal();
                    } else if (window.parent && window.parent.webmask_close_modal) {
                        window.parent.webmask_close_modal();
                    } else if (window.opener && typeof window.opener.closeModal === 'function') {
                        window.opener.closeModal();
                    } else {
                        // 如果是在iframe中
                        try {
                            if (window.frameElement) {
                                var parentDoc = window.parent.document;
                                var frame = window.frameElement;
                                var parentModal = $(frame).closest('.modal');
                                if (parentModal.length) {
                                    parentModal.modal('hide');
                                } else {
                                    frame.style.display = 'none';
                                }
                            }
                        } catch (e) {
                            console.error("关闭弹窗失败:", e);
                        }
                    }
                    
                    // 打开新窗口
                    var newWindow = window.open(url);
                    
                    // 添加监听器，当新窗口关闭时刷新父页面
                    if (newWindow) {
                                // 尝试刷新父窗口
                                try {
                                    // 如果当前页面是在iframe中
                                    if (window.parent && window.parent !== window) {
                                        window.parent.location.reload();
                                    }
                                } catch (e) {
                                    console.error("刷新父页面失败:", e);
                                }
                            } 
                    }
                
                // 存储URL以便后续使用
                window.signPositionUrl = '${setUrl}';
                
                // 页面加载时自动执行一次
                window.onload = function() {
                    openSignPositionPage(window.signPositionUrl);
                    
                    // 添加一个按钮到页面上，允许用户再次打开签字位置设置
                    document.body.insertAdjacentHTML('beforeend', 
                        '<button id="openSignPositionBtn">重新打开签字位置设置</button>');
                    
                    // 为按钮添加点击事件
                    document.getElementById('openSignPositionBtn').addEventListener('click', function() {
                        if (window.signPositionUrl) {
                            openSignPositionPage(window.signPositionUrl);
                        }
                    });
                }
            </script>
        </c:if>
<div class="container">


    <form id="signForm">

        <div class="form-group row">
<%--            <label for="recipients" class="col-sm-2 col-form-label">收件人:</label>--%>
<%--            <div class="col-sm-10">--%>
                <select id="recipients" required class="form-control" multiple="multiple" style="height: 400px"></select>
<%--            </div>--%>
        </div>

        <p id="errorMessage" style="color: red;"></p>
        <p id="successMessage" style="color: green;"></p>
        <button type="submit" id="submitbtn" class="btn btn-primary">提交</button>

    </form>
</div>
<div id="overlay">
    <div id="iframeContainer">
        <div id="closeButton" onclick="closeForgetPsw()">X</div>
        <form id="signer">

            <div class="form-group row">
                <label for="type" class="col-sm-2 col-form-label">姓名：</label>

                <div class="col-sm-10">
                    <input class="input-field" type="text" style="width: 100%" required="" id="signer_name">
                </div>

            </div>
            <div class="form-group row">
                <label for="type" class="col-sm-2 col-form-label">邮箱：</label>

                <div class="col-sm-10">
                    <input class="input-field" type="text"  style="width: 100%" required="" id="signer_mail">
                </div>

            </div>

            <button type="submit" id="signerbtn" class="btn btn-primary">确认</button>
        </form>
    </div>
</div>


<script>
   $recipients=$('#recipients').select2({
        multiple: true,
        // tags: true,
       height : '400px',
        ajax: {
            url: "/lightpdfSign.signerSearch.do",
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    q: params.term, // search term
                    page: params.page
                };
            },
            processResults: function (data, params) {
                // parse the results into the format expected by Select2
                // since we are using custom formatting functions we do not need to
                // alter the remote JSON data, except to indicate that infinite
                // scrolling can be used
                params.page = params.page || 1;

                return {
                    results: data.data.results,
                    pagination: {
                        more: (params.page * 30) < data.total_count
                    }
                }
            },
            cache: true


        },
       tags: true,
       createTag: function (params) {
           var term = $.trim(params.term);

           if (term === '') {
               return null;
           }

           return {
               id: term,
               text: term,
               newTag: true
           };
       },
       templateResult: function (data) {
           // Customize the display of results
           if (data.newTag) {
               return $('<a style="color: red; ">没有想要的结果，去添加: ' + data.text + '</a>');
           }
           return data.text;
       }
   }).on('select2:select', function (e) {
       // Prevent selecting the "New" tag
       var isNewTag = e.params.data.newTag;

       if (isNewTag) {
           // Unselect the "New" tag
           // $(this).val(null).trigger('change');

           // alert(JSON.stringify(e.params.data.id));

           // Handle the click event, e.g., show a modal
           // openModal(e.params.data.id);

           var selectedOptions = $(this).val() || [];
           selectedOptions.pop(); // 移除最后一个元素，即“New”标签
           $(this).val(selectedOptions).trigger('change');
           $('#signer_name').val(e.params.data.id);
           $('#signer_mail').val(e.params.data.id);
           showForgetPsw();


       }
        // placeholder: 'Search for a repository',
        // minimumInputLength: 1
        // createSearchChoice: function (term) {
        //     return {
        //         id: $.trim(term),
        //         text: $.trim(term) + ' (new tag)'
        //     };
        // },

       // data: [{ id: 'defaultOption', text: '默认选项' }]



       // Some nice improvements:

        // max tags is 3
        // maximumSelectionSize: 3

    });
   //再次加载默认值. 赋值.
   var data='${selectedSignerJson}';

if(data){
   for (var d = 0; d < data.length; d++) {
       var item = data[d];
       var option = new Option(item.Name, item.Value, true, true);
       $recipients.append(option);
   }

   $recipients.trigger('change');//使用这个方法显示到select2上.
}

    // $recipients.val(["<EMAIL>", "AL"]).trigger("change");

</script>






</script>
<script src="/cdtms/LightpdfSign/js/index.js"></script>


<script>


    const errorMessage = document.getElementById("errorMessage");
    const successMessage = document.getElementById("successMessage");


    signForm.addEventListener("submit", async (e) => {

        $('#submitbtn').attr('disabled',true)

        e.preventDefault();
        // console.log($('#recipients').select2('data'));
        // const labels = [].map.call($("#recipients").find(":selected"), el =>
        //     $(el).text()
        // );
        // console.log(labels);
       var recipients = $('#recipients').select2('val')?$('#recipients').select2('val').join(";"):"";
        const response = await fetch("lightpdfSign.setSigner.do", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                recipients:recipients,
                id:0

            })
        });

        const data = await response.json();
        if (data.status == 200) {
            // alert( "成功！");
            // window.open(data.data.setUrl);
            // window.location
            // window.parent.close();
            // window.close();
            if(data.data){
                window.open(data.data.setUrl);
            }
            // window.parent.closeFromInnerWindow();

            window.top.notifySuccess("设置成功",false);
            window.top.webmask_close_modal();
            // window.re
            // window.
        } else {
            $('#submitbtn').attr('disabled',true)
            // errorMessage.textContent = "验证码或邮箱错误";
            successMessage.textContent = "";
        }
    });

    signer.addEventListener("submit", async (e) => {


        e.preventDefault();

        if (!isValidName($('#signer_name').val())) {
            alert('姓名仅允许包含大小写字母、空格或只包含中文字符');
            return;
        }

        if (!isEmailValid($('#signer_mail').val())) {
            alert('邮箱格式错误！');
            return;
        }
        var selectedValues = $('#recipients').select2('val');
        // alert(JSON.stringify(selectedValues));

// 要检查的目标值
        var targetValue = '<'+$('#signer_mail').val()+'>';
        // alert(JSON.stringify(targetValue));

// 判断目标值是否部分匹配已选中的值中的任何一个
        var isValueSelected=false;
        if(selectedValues) {
            isValueSelected = selectedValues.some(function (value) {
                // 使用indexOf进行部分匹配（不区分大小写）
                return value.toLowerCase().includes(targetValue.toLowerCase());
            });

        }

        if(isValueSelected){
            alert('邮箱已存在！');
            return;
        }

        var item=$('#signer_name').val()+'<'+$('#signer_mail').val()+'>';
        var option = new Option(item, item, true, true);
        $recipients.append(option);
        $recipients.trigger('change');
        closeForgetPsw();
    });

    function isEmailValid(email) {
        // 使用正则表达式匹配邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    function isValidName(name) {
        // 正则表达式：只包含大小写字母、空格或只包含中文字符
        const regex = /^([\u4e00-\u9fa5]+|[A-Za-z\s]+)$/;
        return regex.test(name);
    }
    function showForgetPsw() {
        // var overlay = document.getElementById("overlay");
        // var iframe = document.getElementById("forgetPswIframe");
        //
        // Set the iframe src to your forget password page URL
        <%--iframe.src = "lightpdfSign.changepassword.do?email=${currUserInfoMap.email}"; // Replace with the actual URL of your forget password page--%>

        // Display the overlay
        overlay.style.display = "flex";
    }

    function closeForgetPsw() {
        // var overlay = document.getElementById("overlay");
        // var iframe = document.getElementById("forgetPswIframe");

        // Hide the overlay
        overlay.style.display = "none";

        // Reset the iframe src
        // iframe.src = "";
    }



</script>


</body>
</html>
