1.服务器申请
	数据仓库服务器开发、测试、生产各一套：
	操作系统：Linux Centos7 ；
	配置需求：jdk1.8以上，tomcat8.5.85以上，mysql8.0版本，nginx1.22.0,python 3.6.8
	网络要求：满足应用程序协议，（http，SMB，ftp，smtp，TCP/IP一系列协议）；
	内存要求：8GB
	硬盘要求：500G
	开通端口:3306 5432


2.端口访问权限申请：
***********:9000/8000/8001/8003   互通  **********:9000
**********:8000  互通  **********：9000
**********:8000、9000/***********：8000、9000/***********：8000、9000  互通 **********:3306
**********:443/***********：443/***********：443 互通 **********:8090  
mail.hengrui.com:587 互通 **********:8090  
***********:3306 互通 **********:8090 
***********:3306 互通 **********:8090 
***********:9000/8000/8001/8003 互通 数据仓库服务器 3306 5432 
**********：8000、9000、443/***********：8000、9000、443/***********：8000、9000、443 互通 数据仓库服务器 3306 5432
**********：8000 互通 数据仓库服务器 3306 5432