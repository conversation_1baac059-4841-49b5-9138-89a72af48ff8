%global system studyid root version dir lang m_minio repdat is_add;
proc datasets lib=work nolist kill; quit;
%let dir=%sysfunc(getoption(work));
%let root=&dir.;
x "cd &dir";
%put &dir;
%put &root;

/*指定minio宏参数*/

%let m_minio=minios3-t;
%let repdat=&sysdate.;

%let studyid=&studyid.;
%let lang=&lang.;
%let jsonMinioPath=&jsonPath.;

/*%let lang="CH";*/
/*%let studyid=HRS-4357-101;*/
/*%let studyid=SHR-A1811-208;*/


x "mkdir ./pgm";
x 'mc find minios3-t/pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';
/*x 'mc find minios3-t/pgm/ --name "m_post2s3.sas" --exec "mc cp {} ./pgm/"';*/
/*x 'mc find minios3-t/pgm/ --name "m_gets3data.sas" --exec "mc cp {} ./pgm/"';*/
/*x 'mc find minios3-t/pgm/ --name "m_exportxlsx_dmreview.sas" --exec "mc cp {} ./pgm/"';*/
/*x 'mc find minios3-t/pgm/ --name "m_Tu_Checks.sas" --exec "mc cp {} ./pgm/"';*/
/*x 'mc find minios3-t/pgm/ --name "m_Tu_CLesLoc.sas" --exec "mc cp {} ./pgm/"';*/
/*x 'mc find minios3-t/pgm/ --name "m_Tu_CResEval.sas" --exec "mc cp {} ./pgm/"';*/
/*x 'mc find minios3-t/pgm/ --name "m_Tu_CTotEval.sas" --exec "mc cp {} ./pgm/"';*/
/*x 'mc find minios3-t/pgm/ --name "m_Tu_CWinDef.sas" --exec "mc cp {} ./pgm/"';*/
/*x 'mc find minios3-t/pgm/ --name "m_Tu_GlobalVar.sas" --exec "mc cp {} ./pgm/"';*/
/*x 'mc find minios3-t/pgm/ --name "m_Tu_PreData.sas" --exec "mc cp {} ./pgm/"';*/
/*x 'mc find minios3-t/pgm/ --name "m_Tu_PreData_add.sas" --exec "mc cp {} ./pgm/"';*/
/*x 'mc find minios3-t/pgm/ --name "m_Tu_PreData_judge.sas" --exec "mc cp {} ./pgm/"';*/
%include "&root./pgm/*.sas";


%let querul=edcserver/query_summary_protocol_violation;
option mprint symbolgen validvarname=v7;


/*%let jsonMinioPath=minios3-t/sdv/json/SHR-A1811-307_Recist1.1_69b57f9f72f78684.json;*/

data _null_;
call symput('file_name',scan("&jsonMinioPath.",-1,'/'));
run;

%put &file_name;

x "mkdir ./doc/sdv";
x "mc find minios3-t/sdv/json --name ""&file_name."" | xargs -I{} mc cp {} ./doc/sdv/";
filename y "./doc/sdv/&file_name.";

libname jsonrec json fileref=y;
proc copy in=jsonrec out=work;
run;

/*处理jason以及自定义*/

data alldata_;
set alldata;
if P1 eq 'parm' then do;
is_add=compress(scan(Value,2,'='),'<>');
/*	if Value="多套" then is_add='N'; */
/*	else if Value="单套" then is_add='Y'; */
end;
run;

/*data alldata_;*/
/*set alldata;*/
/*if Value="多套" then is_add='N'; */
/*else if Value="单套" then is_add='Y'; */
/*run;*/

proc sql noprint;
	select is_add into :is_add from alldata_ where P1='parm';
	select Value into :system from alldata_ where P1='system';
	select Value into :sascode from alldata_ where P1='sascode';

quit;
%put &is_add. &system.;

%let outrul=minios3-t/sdv/output/;


%m_post2s3(studyid=&studyid.,env=uat);
%m_gets3data(studyid=&studyid.,data=@,env=uat);

&sascode;

%m_Tu_Checks(is_add=&is_add.);



