import socket
import ssl

class FTPSClient:
    def __init__(self, host, port=990, username='', password=''):
        self.host = host
        self.port = port
        self.username = username
        self.password = password

    def connect(self):
        # 创建socket连接
        sock = socket.create_connection((self.host, self.port))
        # 封装TLS
        context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        sock = context.wrap_socket(sock, server_hostname=self.host)
        # 接收欢迎消息
        resp = self.get_resp(sock) 
        print(resp)

        # 发送验证命令   
        cmd = 'USER ' + self.username + '\r\n'
        sock.send(cmd.encode('utf-8'))
        resp = self.get_resp(sock)
        print(resp)

        cmd = 'PASS ' + self.password + '\r\n'
        sock.send(cmd.encode('utf-8'))
        resp = self.get_resp(sock)
        print(resp)

        # 进入FTP根目录
        cmd = 'PWD\r\n'
        sock.send(cmd.encode('utf-8'))
        resp = self.get_resp(sock)
        print(resp)

        # 进入子目录
        cmd = 'CWD /hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand/\r\n'  
        sock.send(cmd.encode('utf-8'))
        resp = self.get_resp(sock)
        print(resp)

        # 列出文件列表
        cmd = 'LIST\r\n'
        sock.send(cmd.encode('utf-8')) 
        resp = self.get_multiline_resp(sock)
        print(resp)

        # 关闭连接
        sock.close()

    def get_resp(self, sock):
        resp = sock.recv(1024).decode('utf-8')
        if resp.startswith('3'):
            resp += sock.recv(1024).decode('utf-8')
        return resp
    def get_multiline_resp(self, sock):
        resp = ''
        while True:
            line = sock.recv(1024).decode('utf-8')
            if line.startswith('226'):
                break
            resp += line 
        return resp 

if __name__ == '__main__':
    ftp_client = FTPSClient('ftp01.ftp.mdsol.com', username='erkang.zhou.hengrui.com', password='Zero2One4?')
    ftp_client.__init__('ftp01.ftp.mdsol.com',990,'erkang.zhou.hengrui.com','Zero2One4?')
    ftp_client.connect() 