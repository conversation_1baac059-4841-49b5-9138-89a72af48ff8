/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80032
 Source Host           : localhost:3306
 Source Schema         : edm_uap

 Target Server Type    : MySQL
 Target Server Version : 80032
 File Encoding         : 65001

 Date: 24/01/2024 15:11:45
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for hibernate_unique_key
-- ----------------------------
DROP TABLE IF EXISTS `hibernate_unique_key`;
CREATE TABLE `hibernate_unique_key`  (
  `next_hi` int(0) NULL DEFAULT NULL
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_action_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_action_log`;
CREATE TABLE `sys_action_log`  (
  `P_ID` bigint(0) NOT NULL,
  `DATE_CREATED` datetime(0) NULL DEFAULT NULL,
  `IP` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `SESSIONID` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `URI` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `USERID` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `USERNAME` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  PRIMARY KEY (`P_ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dt_dataw_history
-- ----------------------------
DROP TABLE IF EXISTS `sys_dt_dataw_history`;
CREATE TABLE `sys_dt_dataw_history`  (
  `P_ID` varchar(50) CHARACTER SET gbk COLLATE gbk_chinese_ci NOT NULL,
  `DATE_CREATED` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`P_ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dt_history
-- ----------------------------
DROP TABLE IF EXISTS `sys_dt_history`;
CREATE TABLE `sys_dt_history`  (
  `P_ID` bigint(0) NOT NULL,
  `AFTER_VALUE` mediumtext CHARACTER SET gbk COLLATE gbk_chinese_ci NULL,
  `BEFORE_VALUE` mediumtext CHARACTER SET gbk COLLATE gbk_chinese_ci NULL,
  `DATE_CREATED` datetime(0) NULL DEFAULT NULL,
  `IP_ADDR` varchar(16) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `M_OR_D` varchar(1) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `RECORD_ID` bigint(0) NULL DEFAULT NULL,
  `TABLE_ID` varchar(50) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `USER_ID` bigint(0) NULL DEFAULT NULL,
  `VALUE_KEY` text CHARACTER SET gbk COLLATE gbk_chinese_ci NULL,
  PRIMARY KEY (`P_ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dt_hitcount
-- ----------------------------
DROP TABLE IF EXISTS `sys_dt_hitcount`;
CREATE TABLE `sys_dt_hitcount`  (
  `P_ID` bigint(0) NOT NULL,
  `HIT_COUNT` int(0) NULL DEFAULT NULL,
  `TABLE_ID` varchar(50) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  PRIMARY KEY (`P_ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dt_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_dt_notice`;
CREATE TABLE `sys_dt_notice`  (
  `P_ID` bigint(0) NOT NULL,
  `DATE_CREATED` datetime(0) NULL DEFAULT NULL,
  `FUNCNAME` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `IMPORTANCE` int(0) NULL DEFAULT NULL,
  `MESSAGEID` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `NOTE` text CHARACTER SET gbk COLLATE gbk_chinese_ci NULL,
  `TIME_READ` datetime(0) NULL DEFAULT NULL,
  `TIME_RECV_DELETE` datetime(0) NULL DEFAULT NULL,
  `RECEIVE_USERID` bigint(0) NULL DEFAULT NULL,
  `RECORD_ID` bigint(0) NULL DEFAULT NULL,
  `TIME_SEND_DELETE` datetime(0) NULL DEFAULT NULL,
  `SEND_USERID` bigint(0) NULL DEFAULT NULL,
  `TABLE_ID` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  PRIMARY KEY (`P_ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dt_recordinfo
-- ----------------------------
DROP TABLE IF EXISTS `sys_dt_recordinfo`;
CREATE TABLE `sys_dt_recordinfo`  (
  `P_ID` bigint(0) NOT NULL,
  `DATE_CREATED` datetime(0) NULL DEFAULT NULL,
  `NOTE_INFO` text CHARACTER SET gbk COLLATE gbk_chinese_ci NULL,
  `RECORD_ID` bigint(0) NULL DEFAULT NULL,
  `TABLE_ID` varchar(50) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `INFO_TYPE` bigint(0) NULL DEFAULT NULL,
  `USER_ID` bigint(0) NULL DEFAULT NULL,
  PRIMARY KEY (`P_ID`) USING BTREE,
  INDEX `Index_2`(`TABLE_ID`) USING BTREE,
  INDEX `Index_3`(`RECORD_ID`) USING BTREE,
  INDEX `Index_4`(`INFO_TYPE`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dt_reply
-- ----------------------------
DROP TABLE IF EXISTS `sys_dt_reply`;
CREATE TABLE `sys_dt_reply`  (
  `P_ID` bigint(0) NOT NULL,
  `REPLY_CONTENT` text CHARACTER SET gbk COLLATE gbk_chinese_ci NULL,
  `DATE_CREATED` datetime(0) NULL DEFAULT NULL,
  `RECORD_ID` bigint(0) NULL DEFAULT NULL,
  `TABLE_ID` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `USERID` bigint(0) NULL DEFAULT NULL,
  PRIMARY KEY (`P_ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dt_starmark
-- ----------------------------
DROP TABLE IF EXISTS `sys_dt_starmark`;
CREATE TABLE `sys_dt_starmark`  (
  `P_ID` bigint(0) NOT NULL,
  `RECORD_ID` bigint(0) NULL DEFAULT NULL,
  `STAR_NUM` int(0) NULL DEFAULT NULL,
  `TABLE_ID` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `USER_ID` bigint(0) NULL DEFAULT NULL,
  PRIMARY KEY (`P_ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dt_wfprocess
-- ----------------------------
DROP TABLE IF EXISTS `sys_dt_wfprocess`;
CREATE TABLE `sys_dt_wfprocess`  (
  `P_ID` bigint(0) NOT NULL,
  `DATE_CREATED` datetime(0) NULL DEFAULT NULL,
  `FUNCNAME` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `NOTE` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `RECORD_ID` bigint(0) NULL DEFAULT NULL,
  `STATUS_BEGIN` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `STATUS_END` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `TABLE_ID` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `USERID` bigint(0) NULL DEFAULT NULL,
  PRIMARY KEY (`P_ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_permission
-- ----------------------------
DROP TABLE IF EXISTS `sys_permission`;
CREATE TABLE `sys_permission`  (
  `P_ID` bigint(0) NOT NULL,
  `DATA_GROUPID` varchar(50) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `DATAID` varchar(50) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `FUNCTION_TABLE_ID` varchar(50) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `FUNCTIONID` varchar(50) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `PERMISSIONID` bigint(0) NULL DEFAULT NULL,
  `SCOPE_ID` varchar(50) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `ITEM_VALUE` int(0) NULL DEFAULT NULL,
  PRIMARY KEY (`P_ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_restemplate
-- ----------------------------
DROP TABLE IF EXISTS `sys_restemplate`;
CREATE TABLE `sys_restemplate`  (
  `P_ID` bigint(0) NOT NULL,
  `R_REFTABLE_ID` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `R_RES_NAME` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `R_SELF_STATUS` int(0) NULL DEFAULT NULL,
  PRIMARY KEY (`P_ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_roles
-- ----------------------------
DROP TABLE IF EXISTS `sys_roles`;
CREATE TABLE `sys_roles`  (
  `P_ID` bigint(0) NOT NULL,
  `ROLE_DESCRIPTION` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `ROLE_NAME` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  PRIMARY KEY (`P_ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_thread_task
-- ----------------------------
DROP TABLE IF EXISTS `sys_thread_task`;
CREATE TABLE `sys_thread_task`  (
  `P_ID` bigint(0) NOT NULL,
  `ADD_TIME` datetime(0) NULL DEFAULT NULL,
  `CLASS_DESC` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `CLASS_NAME` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `END_TIME` datetime(0) NULL DEFAULT NULL,
  `TASK_PARAM` text CHARACTER SET gbk COLLATE gbk_chinese_ci NULL,
  `RETRUN_INFO` text CHARACTER SET gbk COLLATE gbk_chinese_ci NULL,
  `RUN_TYPE` int(0) NULL DEFAULT NULL,
  `START_TIME` datetime(0) NULL DEFAULT NULL,
  `TASK_STAGE` int(0) NULL DEFAULT NULL,
  `USER_NAME` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  PRIMARY KEY (`P_ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_units
-- ----------------------------
DROP TABLE IF EXISTS `sys_units`;
CREATE TABLE `sys_units`  (
  `P_ID` bigint(0) NOT NULL,
  `UNIT_DESCRIPTION` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `UNIT_NAME` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  PRIMARY KEY (`P_ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `USERID` bigint(0) NOT NULL,
  `ROLEID` bigint(0) NOT NULL,
  PRIMARY KEY (`ROLEID`, `USERID`) USING BTREE,
  INDEX `FKAABB7D58A1755045`(`USERID`) USING BTREE,
  INDEX `FKAABB7D589C1FFADB`(`ROLEID`) USING BTREE,
  INDEX `FKAABB7D588E6B6744`(`USERID`) USING BTREE,
  INDEX `FKAABB7D58891611DA`(`ROLEID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_users
-- ----------------------------
DROP TABLE IF EXISTS `sys_users`;
CREATE TABLE `sys_users`  (
  `P_ID` bigint(0) NOT NULL,
  `BIRTHDAY` datetime(0) NULL DEFAULT NULL,
  `CONTACT` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `EMAIL` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `GENDER` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `ORDERID` int(0) NULL DEFAULT NULL,
  `PASSWORD` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `PYNAME` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `REALNAME` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `UNIT` bigint(0) NULL DEFAULT NULL,
  `USERNAME` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `USER_ACTIVE` bit(1) NULL DEFAULT NULL,
  `VALIDATETAG` bit(1) NULL DEFAULT NULL,
  PRIMARY KEY (`P_ID`) USING BTREE,
  UNIQUE INDEX `USERNAME`(`USERNAME`) USING BTREE,
  INDEX `FKFD0EC5D6D380E55C`(`UNIT`) USING BTREE,
  INDEX `FKFD0EC5D6C076FC5B`(`UNIT`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_analyst_list
-- ----------------------------
DROP TABLE IF EXISTS `tbl_analyst_list`;
CREATE TABLE `tbl_analyst_list`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_NAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_EMAIL` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DEPT` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SSDQ` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ACTIVE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_STATR_DATE` datetime(0) NULL DEFAULT NULL,
  `COL_END_DATE` datetime(0) NULL DEFAULT NULL,
  `COL_COMMENTS` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_PHONE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_codelist
-- ----------------------------
DROP TABLE IF EXISTS `tbl_codelist`;
CREATE TABLE `tbl_codelist`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_CODELIST_GROUP` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CODELIST_CGROUP` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CODELIST_NAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CODELIST_CNAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CODE_VALUE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CODE_DESCRIPTION` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CODE_CDESCRIPTION` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ACTIVE` bigint(0) NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_conpub
-- ----------------------------
DROP TABLE IF EXISTS `tbl_conpub`;
CREATE TABLE `tbl_conpub`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_TABLEID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TABLENAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_UNFIELD` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TYPE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_contable
-- ----------------------------
DROP TABLE IF EXISTS `tbl_contable`;
CREATE TABLE `tbl_contable`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_CODE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_UPLOADER` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_UPLOADDAT` datetime(0) NULL DEFAULT NULL,
  `COL_CONFILE` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_HASH` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_STATUS` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PUBLISHER` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PUBDAT` datetime(0) NULL DEFAULT NULL,
  `COL_PUBDATFINISH` datetime(0) NULL DEFAULT NULL,
  `COL_CONFILELOCAL` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_CONFILELOCALPUB` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_CONFIGLOG` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_DBDATALOG` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_DBMETACOMP` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_UPIP` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_UPPROID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_OBJPROID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SYSVERSION` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERVERSION` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PERFORMER` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_NOTE` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_dcpzb
-- ----------------------------
DROP TABLE IF EXISTS `tbl_dcpzb`;
CREATE TABLE `tbl_dcpzb`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_FILE_TYPE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_FILE_TYPE_VALUE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_OUTPUTFOLDER` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_FILE_NAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_FILENAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TNAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PTID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_FID_ATTACH` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_MENUID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_OUT_TYPE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_div_layout_editor
-- ----------------------------
DROP TABLE IF EXISTS `tbl_div_layout_editor`;
CREATE TABLE `tbl_div_layout_editor`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_LAY_EDI_NAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ENG_NAME_LAY` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LAYOUT_DESC` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_LAY_HTML_CON` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_LAY_JSON_OBJ` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_CLASSIFIER` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SN` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_downhistory
-- ----------------------------
DROP TABLE IF EXISTS `tbl_downhistory`;
CREATE TABLE `tbl_downhistory`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_REFID` bigint(0) NULL DEFAULT NULL,
  `COL_LOGINID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_STARTTIME` datetime(0) NULL DEFAULT NULL,
  `COL_ENDTIME` datetime(0) NULL DEFAULT NULL,
  `COL_IPADDR` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_NOTE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_UPLOAD_TAG` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `sysindex_refid`(`COL_REFID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_download_list
-- ----------------------------
DROP TABLE IF EXISTS `tbl_download_list`;
CREATE TABLE `tbl_download_list`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_DOWNLOAD_DATE` datetime(0) NULL DEFAULT NULL,
  `COL_USER` bigint(0) NULL DEFAULT NULL,
  `COL_OUTBOARD_DATA_MANAGER` bigint(0) NULL DEFAULT NULL,
  `COL_EMAIL` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `sysindex_outboard_data_manager`(`COL_OUTBOARD_DATA_MANAGER`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_eclinichistory
-- ----------------------------
DROP TABLE IF EXISTS `tbl_eclinichistory`;
CREATE TABLE `tbl_eclinichistory`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_FID_OPERATOR` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_FID_DATE` datetime(0) NULL DEFAULT NULL,
  `COL_CLRZ` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_FID_XML` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_eda
-- ----------------------------
DROP TABLE IF EXISTS `tbl_eda`;
CREATE TABLE `tbl_eda`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_MEMBER` bigint(0) NULL DEFAULT NULL,
  `COL_DEPT` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SSDQ` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ACTIVE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_STATR_DATE` datetime(0) NULL DEFAULT NULL,
  `COL_END_DATE` datetime(0) NULL DEFAULT NULL,
  `COL_COMMENTS` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_EXT_DATA` bigint(0) NULL DEFAULT NULL,
  `COL_TYPE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_STUDYID` bigint(0) NULL DEFAULT NULL,
  `COL_EMAIL` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LOGINID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `sysindex_member`(`COL_MEMBER`) USING BTREE,
  INDEX `sysindex_ext_data`(`COL_EXT_DATA`) USING BTREE,
  INDEX `sysindex_studyid`(`COL_STUDYID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_email_output_list
-- ----------------------------
DROP TABLE IF EXISTS `tbl_email_output_list`;
CREATE TABLE `tbl_email_output_list`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_ADDRESSEE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PHONE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DATE` datetime(0) NULL DEFAULT NULL,
  `COL_MD5` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TIME_LIMIT` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TYPE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_NUMBER` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_FILENAME` bigint(0) NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_STATUS` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `sysindex_filename`(`COL_FILENAME`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_ext_data
-- ----------------------------
DROP TABLE IF EXISTS `tbl_ext_data`;
CREATE TABLE `tbl_ext_data`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_STUDYID` bigint(0) NULL DEFAULT NULL,
  `COL_EXT_DATA_TYPE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DM_CONTACT` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LAB_NAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LAB_ADDRESS` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LAB_CONTACT` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LAB_PHONE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_FILE_FORMAT` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_FORMAT_DETAIL` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TRANS_FREQ` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DTA_DOC` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_IS_BINDING` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_EXT_DATA_TEXT` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_ACTIVE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SBF` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CSPLQT` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LAB_ORG` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_STUDYID_NOTE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CDTMS_DTA_ID` bigint(0) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `sysindex_studyid`(`COL_STUDYID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_ext_data_unit
-- ----------------------------
DROP TABLE IF EXISTS `tbl_ext_data_unit`;
CREATE TABLE `tbl_ext_data_unit`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_EXT_DATA_TYPE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DM_CONTACT` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LAB_ORG` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LAB_NAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LAB_ADDRESS` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LAB_CONTACT` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LAB_PHONE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_EXT_DATA_TEXT` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_ACTIVE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_STUDYID` bigint(0) NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `sysindex_studyid`(`COL_STUDYID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_ggsyr
-- ----------------------------
DROP TABLE IF EXISTS `tbl_ggsyr`;
CREATE TABLE `tbl_ggsyr`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_PXZD` bigint(0) NULL DEFAULT NULL,
  `COL_BM` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DM` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_JSMC` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_XTJSMC` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_gzlgzb
-- ----------------------------
DROP TABLE IF EXISTS `tbl_gzlgzb`;
CREATE TABLE `tbl_gzlgzb`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_LCMC` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_MBB` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_ZTWZD` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CSZTWZD` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ZZZTW` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SFJH` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_GZLTJLX` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SFLBXSBL` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LBXSDBR` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_HTFS` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_BLYJTXFS` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DBSXXSMB` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SFZDTJSYR` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CSZTWJBRTJ` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TJSXDBRFSTZZD` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TJSXSDBRZD` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_XMNWDLZXMB` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_GRDBSXYXPLBL` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_GRDBSXLBXS` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TZBTMB` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TZZWMB` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_CHART_RULE_ACCORD` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_gzlmxb
-- ----------------------------
DROP TABLE IF EXISTS `tbl_gzlmxb`;
CREATE TABLE `tbl_gzlmxb`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_GNMC` bigint(0) NULL DEFAULT NULL,
  `COL_MBB` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_GNMC1` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PX` double NULL DEFAULT NULL,
  `COL_KSZTW` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_JSZTW` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SQFS` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_JSZD` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_BMNSP` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ZDYH` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SYYHSDFS` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TJTJSD` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_GLYHHSZD` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_GLYHHSSSZD` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_GLTX` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_ZDCFZB` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ZDCFZBCXTJ` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_BLYJTXFS` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_BLYJSD` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ZDTJSYRJS` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TZSD` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_XMFZR` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SJSX` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TRANSACT_OPINION_REQUIRED` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `sysindex_gnmc`(`COL_GNMC`) USING BTREE,
  INDEX `sysindex_gnmc1`(`COL_GNMC1`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_help
-- ----------------------------
DROP TABLE IF EXISTS `tbl_help`;
CREATE TABLE `tbl_help`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_WZBH` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LMMC` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LMXSCX` double NULL DEFAULT NULL,
  `COL_WZMC` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_NR` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_MRSYXS` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_NRXSCX` double NULL DEFAULT NULL,
  `COL_JSMC` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TABLEID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PDFFJ` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_SWFFJ` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_LMMCYW` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_WZMCYW` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_NRYW` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_PDFFJYW` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_FJYW` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_jssj
-- ----------------------------
DROP TABLE IF EXISTS `tbl_jssj`;
CREATE TABLE `tbl_jssj`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_JSMC` bigint(0) NULL DEFAULT NULL,
  `COL_MENUID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SFKJ` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SJFW` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SJQX` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SYSJQX` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `sysindex_jsmc`(`COL_JSMC`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_jsxx
-- ----------------------------
DROP TABLE IF EXISTS `tbl_jsxx`;
CREATE TABLE `tbl_jsxx`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_ROLE_NAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DESCRIPTION` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_kbmdrjssdb
-- ----------------------------
DROP TABLE IF EXISTS `tbl_kbmdrjssdb`;
CREATE TABLE `tbl_kbmdrjssdb`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_XM` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LOGINID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DW` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_JS` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_langmanage
-- ----------------------------
DROP TABLE IF EXISTS `tbl_langmanage`;
CREATE TABLE `tbl_langmanage`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_TERMID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CATEGORY` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_NOTE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LOCALE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_STATUS` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TERM` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_TRANS` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_ljszb
-- ----------------------------
DROP TABLE IF EXISTS `tbl_ljszb`;
CREATE TABLE `tbl_ljszb`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_JSMC` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PX` double NULL DEFAULT NULL,
  `COL_GD` bigint(0) NULL DEFAULT NULL,
  `COL_LJ` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_BZ` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_PBYS` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_BQMC` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_GZTXS` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_log_comesback
-- ----------------------------
DROP TABLE IF EXISTS `tbl_log_comesback`;
CREATE TABLE `tbl_log_comesback`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_PARENT_FILE` bigint(0) NULL DEFAULT NULL,
  `COL_DOWNLOAD_USER` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LOG_FILE` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_DOWNLOAD_CONTENT` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `sysindex_parent_file`(`COL_PARENT_FILE`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_log_event
-- ----------------------------
DROP TABLE IF EXISTS `tbl_log_event`;
CREATE TABLE `tbl_log_event`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_EVENTNAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_FUNCTION_NAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SESSID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_URI` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_IP` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USER` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TABLEID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_RID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_NOTE` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_QSTRING` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_INPUTFILE` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_OUTPUTFILE` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_modify_history
-- ----------------------------
DROP TABLE IF EXISTS `tbl_modify_history`;
CREATE TABLE `tbl_modify_history`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_SAVE_FIELD` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_SAVEQ_FIELD` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_RECORDID` bigint(0) NULL DEFAULT NULL,
  `COL_TABLENAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_FIELDNAME` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_USERNAME` bigint(0) NULL DEFAULT NULL,
  `COL_CREATEDATE` datetime(0) NULL DEFAULT NULL,
  `COL_IP_ADDRESS` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TYPE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PRECORDID` bigint(0) NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_nr
-- ----------------------------
DROP TABLE IF EXISTS `tbl_nr`;
CREATE TABLE `tbl_nr`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_WZBH` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_WZMC` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LMMC` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LMBM` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_XSCX` bigint(0) NULL DEFAULT NULL,
  `COL_NR` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_outboard_data_manager
-- ----------------------------
DROP TABLE IF EXISTS `tbl_outboard_data_manager`;
CREATE TABLE `tbl_outboard_data_manager`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_EXT_DATA_ID` bigint(0) NULL DEFAULT NULL,
  `COL_STUDYID` bigint(0) NULL DEFAULT NULL,
  `COL_XTBH` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_STATUS` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SBF` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_OUTBOARD_DATA_CATEGORY` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_OUTBOARD_DATA_DEPARTMENT` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_OUTBOARD_DATA_DOC` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_UPLOADER` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_UPLOAD_TIME` datetime(0) NULL DEFAULT NULL,
  `COL_MD5` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_2MD5` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_OUTBOARD_DATA_MANAGER_TEXT` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_MSFYZ` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_BLIND_DATA` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `sysindex_ext_data_id`(`COL_EXT_DATA_ID`) USING BTREE,
  INDEX `sysindex_studyid`(`COL_STUDYID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_pages
-- ----------------------------
DROP TABLE IF EXISTS `tbl_pages`;
CREATE TABLE `tbl_pages`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_BH` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TITLE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LB` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ENGLISH_TITLE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PAGE_TEMPLATE` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_PAGE_DATA_CONFIG` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_XMIDE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `sysindex_bh`(`COL_BH`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_password_history
-- ----------------------------
DROP TABLE IF EXISTS `tbl_password_history`;
CREATE TABLE `tbl_password_history`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_DL` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_MD5` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PCHANGED` datetime(0) NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_project_user
-- ----------------------------
DROP TABLE IF EXISTS `tbl_project_user`;
CREATE TABLE `tbl_project_user`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_LOGINID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_STUDYID` bigint(0) NULL DEFAULT NULL,
  `COL_NAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_EMAIL` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_COMPANY` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_STATUS` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PRJROLE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SYSROLE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_STARTDATE` datetime(0) NULL DEFAULT NULL,
  `COL_EXITDATE` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `sysindex_studyid`(`COL_STUDYID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_pubhis
-- ----------------------------
DROP TABLE IF EXISTS `tbl_pubhis`;
CREATE TABLE `tbl_pubhis`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_CODE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PUBLISHER` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PUBDAT` datetime(0) NULL DEFAULT NULL,
  `COL_CONFILE` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_FILESIZE` bigint(0) NULL DEFAULT NULL,
  `COL_HASH` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_UPSTATUS_T` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_UPLOADDAT_T` datetime(0) NULL DEFAULT NULL,
  `COL_TESTSERVER` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TESTSUBJECT` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_UPSTATUS` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_UPLOADDAT` datetime(0) NULL DEFAULT NULL,
  `COL_SERVERIP` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TARGETPRJID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SYSVERSION` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERVERSION` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_OPERATION_USER` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_NOTES` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_roles
-- ----------------------------
DROP TABLE IF EXISTS `tbl_roles`;
CREATE TABLE `tbl_roles`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_MEMBER` bigint(0) NULL DEFAULT NULL,
  `COL_DEPT` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SSDQ` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ACTIVE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_STATR_DATE` datetime(0) NULL DEFAULT NULL,
  `COL_END_DATE` datetime(0) NULL DEFAULT NULL,
  `COL_DQRQ` datetime(0) NULL DEFAULT NULL,
  `COL_COMMENTS` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_EMAIL` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LIMITNUM` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DFTYPE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_EXT_DATA` bigint(0) NULL DEFAULT NULL,
  `COL_STUDYID` bigint(0) NULL DEFAULT NULL,
  `COL_LOGINID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `sysindex_member`(`COL_MEMBER`) USING BTREE,
  INDEX `sysindex_ext_data`(`COL_EXT_DATA`) USING BTREE,
  INDEX `sysindex_studyid`(`COL_STUDYID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_rwzz
-- ----------------------------
DROP TABLE IF EXISTS `tbl_rwzz`;
CREATE TABLE `tbl_rwzz`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_XH` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_JSMC` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_RWZZ` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_SFSX` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SXRQ` datetime(0) NULL DEFAULT NULL,
  `COL_SSRQ` datetime(0) NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_ryjbzl
-- ----------------------------
DROP TABLE IF EXISTS `tbl_ryjbzl`;
CREATE TABLE `tbl_ryjbzl`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_LOGINID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_XM` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DWMC` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_BM` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_RYJS` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ZT` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LZRQ` datetime(0) NULL DEFAULT NULL,
  `COL_DLCS` bigint(0) NULL DEFAULT NULL,
  `COL_SCXGMMSJ` datetime(0) NULL DEFAULT NULL,
  `COL_MMCWCS` bigint(0) NULL DEFAULT NULL,
  `COL_EMAIL` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DH` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_STATUS` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DEFAULT_PROJECT` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CURRENT_STUDY_ID` bigint(0) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `sysindex_loginid`(`COL_LOGINID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_seltable
-- ----------------------------
DROP TABLE IF EXISTS `tbl_seltable`;
CREATE TABLE `tbl_seltable`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_SXPX` bigint(0) NULL DEFAULT NULL,
  `COL_SCRNUM` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PAGE` bigint(0) NULL DEFAULT NULL,
  `COL_FIELDNAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_E_FIELDNAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_FIELDID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SELMETHOD` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SELECTOR_SOUR` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_FIX_SELECTOR` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DYN_SELECTOR` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ISVISIBLE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TYPE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `sysindex_scrnum`(`COL_SCRNUM`) USING BTREE,
  INDEX `sysindex_page`(`COL_PAGE`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_spjlb
-- ----------------------------
DROP TABLE IF EXISTS `tbl_spjlb`;
CREATE TABLE `tbl_spjlb`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_SPBH` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_GZLMC` bigint(0) NULL DEFAULT NULL,
  `COL_SJB` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_JL` bigint(0) NULL DEFAULT NULL,
  `COL_SSBM` bigint(0) NULL DEFAULT NULL,
  `COL_DQZT` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DQZTMC` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_WJZT` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DQJS` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DQYH` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_JDQYHBLZD` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `sysindex_gzlmc`(`COL_GZLMC`) USING BTREE,
  INDEX `sysindex_sjb`(`COL_SJB`) USING BTREE,
  INDEX `sysindex_jl`(`COL_JL`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_spmxb
-- ----------------------------
DROP TABLE IF EXISTS `tbl_spmxb`;
CREATE TABLE `tbl_spmxb`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_SPBH` bigint(0) NULL DEFAULT NULL,
  `COL_BLR` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_BLSJ` datetime(0) NULL DEFAULT NULL,
  `COL_GNMC` bigint(0) NULL DEFAULT NULL,
  `COL_SJB` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_JL` bigint(0) NULL DEFAULT NULL,
  `COL_SPSC` bigint(0) NULL DEFAULT NULL,
  `COL_BZ` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_FJ` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `sysindex_spbh`(`COL_SPBH`) USING BTREE,
  INDEX `sysindex_gnmc`(`COL_GNMC`) USING BTREE,
  INDEX `sysindex_jl`(`COL_JL`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_statistician
-- ----------------------------
DROP TABLE IF EXISTS `tbl_statistician`;
CREATE TABLE `tbl_statistician`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_STUDYID` bigint(0) NULL DEFAULT NULL,
  `COL_NAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_EMAIL` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DEPT` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SSDQ` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ACTIVE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_STATR_DATE` datetime(0) NULL DEFAULT NULL,
  `COL_END_DATE` datetime(0) NULL DEFAULT NULL,
  `COL_COMMENTS` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_EXT_DATA` bigint(0) NULL DEFAULT NULL,
  `COL_PHONE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LOGINID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `sysindex_studyid`(`COL_STUDYID`) USING BTREE,
  INDEX `sysindex_ext_data`(`COL_EXT_DATA`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_sub_datesource
-- ----------------------------
DROP TABLE IF EXISTS `tbl_sub_datesource`;
CREATE TABLE `tbl_sub_datesource`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_BH` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_FUNCNAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CHARTTYPE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CHART_TITLE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_E_CHART_TITLE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PAGES` bigint(0) NULL DEFAULT NULL,
  `COL_TYPE` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SQL` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_OSQL` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_COLNAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_E_COLNAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_FIELD_REPLACE` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_E_FIELD_REPLACE` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_C_DYNAMIC_REPLACE` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_E_DYNAMIC_REPLACE` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_PARAM` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_TABLELINK` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SELECT_CONFIG` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `sysindex_pages`(`COL_PAGES`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_systemcode
-- ----------------------------
DROP TABLE IF EXISTS `tbl_systemcode`;
CREATE TABLE `tbl_systemcode`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_CODELIST_NAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CODELIST_CNAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CODE_VALUE` bigint(0) NULL DEFAULT NULL,
  `COL_CODE_DESCRIPTION` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CODE_CDESCRIPTION` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ACTIVE` bigint(0) NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_tjszb
-- ----------------------------
DROP TABLE IF EXISTS `tbl_tjszb`;
CREATE TABLE `tbl_tjszb`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_JSMC` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ZYJS` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_TJFL` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_XSMC` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PX` double NULL DEFAULT NULL,
  `COL_SL` bigint(0) NULL DEFAULT NULL,
  `COL_B` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_CXYJ` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ORACLESQL` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DKFS` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LJ` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_XSMCYW` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_LJYW` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_unit_info
-- ----------------------------
DROP TABLE IF EXISTS `tbl_unit_info`;
CREATE TABLE `tbl_unit_info`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_UNIT_NO` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_KIND` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_UNIT_NAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_UNIT_ID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_view_pub_log
-- ----------------------------
DROP TABLE IF EXISTS `tbl_view_pub_log`;
CREATE TABLE `tbl_view_pub_log`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_VIEWUUID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_VIEW_JSON_OBJ` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_VIEW_SQL_STATE` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_xmjs
-- ----------------------------
DROP TABLE IF EXISTS `tbl_xmjs`;
CREATE TABLE `tbl_xmjs`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_JSMC` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_JSCJ` bigint(0) NULL DEFAULT NULL,
  `COL_BM` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_DM` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SSZT` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ZB` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SFGGJS` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_xsht
-- ----------------------------
DROP TABLE IF EXISTS `tbl_xsht`;
CREATE TABLE `tbl_xsht`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_ZT` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_SBF` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_XMDM` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PROJECT_NAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_STUDYID` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_PROJECT_TITLE` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_THERA_AREA` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_INDICATIO` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_BZ` mediumtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `COL_STUDY_PHASE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_ztzd
-- ----------------------------
DROP TABLE IF EXISTS `tbl_ztzd`;
CREATE TABLE `tbl_ztzd`  (
  `ID` bigint(0) NOT NULL,
  `VERSION` int(0) NOT NULL,
  `COL_MBBM` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_MBB` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_BH` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_ZTMC` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_EN_NAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `COL_USERID` bigint(0) NULL DEFAULT NULL,
  `COL_UNITID` bigint(0) NULL DEFAULT NULL,
  `COL_CREATETIME` datetime(0) NULL DEFAULT NULL,
  `COL_MODIFYUSERID` bigint(0) NULL DEFAULT NULL,
  `COL_LASTMODIFYTIME` datetime(0) NULL DEFAULT NULL,
  `COL_UUID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- View structure for v_1
-- ----------------------------
DROP VIEW IF EXISTS `v_1`;
CREATE ALGORITHM = UNDEFINED DEFINER = `root`@`localhost` SQL SECURITY DEFINER VIEW `v_1` AS select `tbl_xsht`.`COL_ZT` AS `ali0`,`tbl_xsht`.`COL_SBF` AS `ali1`,`tbl_xsht`.`COL_XMDM` AS `ali2`,`tbl_xsht`.`COL_PROJECT_NAME` AS `ali3`,`tbl_xsht`.`COL_STUDY_PHASE` AS `ali4` from `tbl_xsht` where (1 = 1);

-- ----------------------------
-- View structure for v_10
-- ----------------------------
DROP VIEW IF EXISTS `v_10`;
CREATE ALGORITHM = UNDEFINED DEFINER = `root`@`localhost` SQL SECURITY DEFINER VIEW `v_10` AS select `tbl_outboard_data_manager`.`COL_STUDYID` AS `COL_STUDYID`,count(1) AS `wjzs`,`tbl_outboard_data_manager`.`COL_OUTBOARD_DATA_CATEGORY` AS `COL_OUTBOARD_DATA_CATEGORY`,`tbl_outboard_data_manager`.`COL_SBF` AS `COL_SBF`,`tbl_outboard_data_manager`.`COL_BLIND_DATA` AS `COL_BLIND_DATA` from `tbl_outboard_data_manager` group by `tbl_outboard_data_manager`.`COL_STUDYID`,`tbl_outboard_data_manager`.`COL_SBF`,`tbl_outboard_data_manager`.`COL_OUTBOARD_DATA_CATEGORY`,`tbl_outboard_data_manager`.`COL_BLIND_DATA`;

-- ----------------------------
-- View structure for v_2
-- ----------------------------
DROP VIEW IF EXISTS `v_2`;
CREATE ALGORITHM = UNDEFINED DEFINER = `root`@`localhost` SQL SECURITY DEFINER VIEW `v_2` AS select `tableali3`.`COL_STATR_DATE` AS `ali5`,`tableali3`.`COL_LIMITNUM` AS `ali6`,`tableali4`.`COL_XM` AS `ali7`,`tableali5`.`COL_EXT_DATA_TYPE` AS `ali8`,`tableali5`.`COL_LAB_ORG` AS `ali9` from ((`tbl_roles` `tableali3` left join `tbl_ryjbzl` `tableali4` on((`tableali3`.`COL_MEMBER` = `tableali4`.`ID`))) left join `tbl_ext_data` `tableali5` on((`tableali3`.`COL_EXT_DATA` = `tableali5`.`ID`))) where (1 = 1);

-- ----------------------------
-- View structure for v_3
-- ----------------------------
DROP VIEW IF EXISTS `v_3`;
CREATE ALGORITHM = UNDEFINED DEFINER = `root`@`localhost` SQL SECURITY DEFINER VIEW `v_3` AS select `xsht`.`COL_SBF` AS `xxx`,`xsht`.`COL_STUDYID` AS `b`,`extdate`.`COL_EXT_DATA_TYPE` AS `g`,`extdate`.`COL_LAB_NAME` AS `dd`,`extdate`.`COL_IS_BINDING` AS `j`,`ryjbzl`.`COL_XM` AS `l`,date_format(`eda`.`COL_STATR_DATE`,'%Y-%m-%d') AS `n`,date_format(`eda`.`COL_END_DATE`,'%Y-%m-%d') AS `o` from (((`tbl_eda` `eda` left join `tbl_xsht` `xsht` on((`eda`.`COL_STUDYID` = `xsht`.`ID`))) left join `tbl_ryjbzl` `ryjbzl` on((`ryjbzl`.`ID` = `eda`.`COL_MEMBER`))) left join `tbl_ext_data` `extdate` on((`extdate`.`ID` = `eda`.`COL_EXT_DATA`)));

-- ----------------------------
-- View structure for v_4
-- ----------------------------
DROP VIEW IF EXISTS `v_4`;
CREATE ALGORITHM = UNDEFINED DEFINER = `root`@`localhost` SQL SECURITY DEFINER VIEW `v_4` AS select `ryjbzl`.`COL_XM` AS `l`,`xsht`.`COL_SBF` AS `xxx`,`xsht`.`COL_STUDYID` AS `b`,`extdate`.`COL_EXT_DATA_TYPE` AS `g`,`extdate`.`COL_LAB_NAME` AS `dd`,`extdate`.`COL_IS_BINDING` AS `j`,date_format(`eda`.`COL_STATR_DATE`,'%Y-%m-%d') AS `n`,date_format(`eda`.`COL_END_DATE`,'%Y-%m-%d') AS `o` from (((`tbl_roles` `eda` left join `tbl_xsht` `xsht` on((`eda`.`COL_STUDYID` = `xsht`.`ID`))) left join `tbl_ryjbzl` `ryjbzl` on((`ryjbzl`.`ID` = `eda`.`COL_MEMBER`))) left join `tbl_ext_data` `extdate` on((`extdate`.`ID` = `eda`.`COL_EXT_DATA`)));

-- ----------------------------
-- View structure for v_7
-- ----------------------------
DROP VIEW IF EXISTS `v_7`;
CREATE ALGORITHM = UNDEFINED DEFINER = `root`@`localhost` SQL SECURITY DEFINER VIEW `v_7` AS select `b`.`COL_STUDYID` AS `d`,`b`.`COL_ZT` AS `e`,date_format(`b`.`COL_CREATETIME`,'%Y-%m-%d') AS `f`,`c`.`COL_EXT_DATA_TYPE` AS `lx`,`ry`.`COL_XM` AS `g`,date_format(`a`.`COL_STATR_DATE`,'%Y-%m-%d') AS `h`,`a`.`COL_ACTIVE` AS `i` from (((`tbl_xsht` `b` left join `tbl_ext_data` `c` on((`b`.`ID` = `c`.`COL_STUDYID`))) left join `tbl_roles` `a` on((`c`.`ID` = `a`.`COL_EXT_DATA`))) left join `tbl_ryjbzl` `ry` on((`a`.`COL_MEMBER` = `ry`.`ID`)));

-- ----------------------------
-- View structure for v_8
-- ----------------------------
DROP VIEW IF EXISTS `v_8`;
CREATE ALGORITHM = UNDEFINED DEFINER = `root`@`localhost` SQL SECURITY DEFINER VIEW `v_8` AS select `study_edm`.`ID` AS `id`,`study_edm`.`COL_EXT_DATA_ID` AS `ext_data_id`,`study`.`COL_STUDYID` AS `studyid`,`study_edm`.`COL_XTBH` AS `xtbh`,`study_edm`.`COL_STATUS` AS `status`,`study_edm`.`COL_SBF` AS `sbf`,`study_edm`.`COL_OUTBOARD_DATA_CATEGORY` AS `outboard_data_category`,`study_edm`.`COL_OUTBOARD_DATA_DEPARTMENT` AS `outboard_data_department`,`study_edm`.`COL_OUTBOARD_DATA_DOC` AS `outboard_data_doc`,`study_edm`.`COL_UPLOADER` AS `uploader`,`study_edm`.`COL_UPLOAD_TIME` AS `upload_time`,`study_edm`.`COL_MD5` AS `md5`,`study_edm`.`COL_2MD5` AS `2md5`,`study_edm`.`COL_OUTBOARD_DATA_MANAGER_TEXT` AS `outboard_data_manager_text`,`study_edm`.`COL_MSFYZ` AS `msfyz`,`study_edm`.`COL_BLIND_DATA` AS `blind_data`,`study_edm`.`COL_CREATETIME` AS `createtime`,6 AS `zq`,`study_edm`.`COL_LASTMODIFYTIME` AS `lastmodifytime` from (`tbl_outboard_data_manager` `study_edm` left join `tbl_xsht` `study` on((`study_edm`.`COL_STUDYID` = `study`.`ID`)));

-- ----------------------------
-- View structure for v_9
-- ----------------------------
DROP VIEW IF EXISTS `v_9`;
CREATE ALGORITHM = UNDEFINED DEFINER = `root`@`localhost` SQL SECURITY DEFINER VIEW `v_9` AS select `b`.`COL_STUDYID` AS `d`,`b`.`COL_ZT` AS `e`,date_format(`b`.`COL_CREATETIME`,'%Y-%m-%d') AS `f`,`c`.`COL_EXT_DATA_TYPE` AS `lx`,`ry`.`COL_XM` AS `g`,date_format(`a`.`COL_STATR_DATE`,'%Y-%m-%d') AS `h`,`a`.`COL_ACTIVE` AS `i` from (((`tbl_xsht` `b` left join `tbl_ext_data` `c` on((`b`.`ID` = `c`.`COL_STUDYID`))) left join `tbl_roles` `a` on((`c`.`ID` = `a`.`COL_EXT_DATA`))) left join `tbl_ryjbzl` `ry` on((`a`.`COL_MEMBER` = `ry`.`ID`)));

SET FOREIGN_KEY_CHECKS = 1;
