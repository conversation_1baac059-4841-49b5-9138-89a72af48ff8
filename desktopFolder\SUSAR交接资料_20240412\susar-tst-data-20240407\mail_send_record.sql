INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (1, '', '20230418 H2023001201347 SHR-A1811-I-102_SAE46_FU2_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR-A1811', '2023-04-18 11:45:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (2, '', '20230417 H2023001201449 SHR-1316-III-302_SAE263_FU2_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR-1316', '2023-04-18 11:45:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (3, '', '20230413 H2022001203970 SHR6390-III-302_SAE65_FU6_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-04-18 11:45:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (4, '', 'H2023001201117 SHR-1316-III-302_SAE246_FU2_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR-1316', '2023-04-18 17:45:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (5, '', '20230419 H2022001204678 SHR-1701-III-307_SAE105_FU11_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR-1701', '2023-04-19 11:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (6, '', '20230202 H2022001203227 SHR-1701-III-307_SAE35_FU5_NHC.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR-1701', '2023-04-19 15:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (7, '', '20230306 H2022001204252 SHR-1316-III-302_SAE180_FU3FU4_NHC.pdf/20230302 H2022001203477 SHR-1316-III-302_SAE156_FU5FU6_NHC.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR-1316', '2023-04-19 15:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (8, '', '20230307 H2022001204676 SHR6390-III-303_SAE123_FU2_NHC.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-04-19 15:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (9, '', '20230419 H2023001201395 ES-SCLC-3rd-IIT-SHR1316-FMTN_SAE07_FU1_INV.pdf/20230419 H2023001200891 SHR-1316-III-302_SAE239_FU1_INV.pdf/20230419 H2023001201448 SHR-1316-III-302_SAE262_FU2FU3_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR-1316', '2023-04-19 17:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (10, '', 'H2023001201513 SHR-A1811-I-101_SAE132_INI_INV  .pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR-A1811', '2023-04-19 17:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (11, '', 'H2023001201540 SHR-1316-III-303_SAE99_FU1FU2_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR-1316', '2023-04-19 17:30:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (12, '', 'H2023001201513 SHR-A1811-I-101_SAE132_INI_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR-A1811', '2023-04-19 17:30:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (13, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR-1316', '2023-04-20 15:30:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (14, '', 'SUSAR test_1.pdf/SUSAR平台收件人测试_1.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-04-21 10:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (15, '', 'SUSAR test_3.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-04-21 10:15:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (16, '', 'SUSAR test_6.pdf/SUSAR test_5.pdf/SUSAR test_4.pdf/SUSAR test_3.pdf/SUSAR test_2.pdf/SUSAR test_7.pdf/SUSAR test_1.pdf/', '<EMAIL>', '843273033@com', 'system', 'N', 'Compound A', '2023-04-21 10:45:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (17, '', 'SUSAR test_6.pdf/SUSAR test_5.pdf/SUSAR test_4.pdf/SUSAR test_3.pdf/SUSAR test_2.pdf/SUSAR test_7.pdf/SUSAR test_1.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'Compound A', '2023-04-21 10:45:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (18, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-04-21 11:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (19, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR4640', '2023-04-27 10:15:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (20, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_1.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-04-27 13:30:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (21, '', 'QAQ1.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-04-27 15:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (22, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_2.pdf/Susar_test1.pdf/', '<EMAIL>', '', 'system', 'N', 'SHR6390', '2023-04-28 11:30:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (23, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_2.pdf/Susar_test1.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-04-28 11:30:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (24, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_4.pdf/H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_3.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-04-28 21:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (25, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_5.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-05-04 09:45:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (26, '', 'QAQ2.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-05-04 10:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (27, '', 'QAQ2.pdf', '<EMAIL>', '<EMAIL>', 'system', 'Y', 'SHR4640', '2023-05-04 10:15:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (28, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_6.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-05-04 10:45:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (29, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_8.pdf/H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_7.pdf/', '<EMAIL>', '<EMAIL>', 'system', 'Y', 'SHR-1802', '2023-05-04 13:15:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (30, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_08.pdf/H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_07.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-05-04 13:15:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (31, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_008.pdf/H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_007.pdf/', '<EMAIL>', '<EMAIL>', 'system', 'Y', 'SHR4640', '2023-05-04 13:15:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (32, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_008.pdf/H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_007.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-05-04 14:30:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (33, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_001.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-05-05 13:15:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (34, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_002.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-05-05 20:30:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (35, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_003.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-05-05 22:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (36, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_003.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR-1802', '2023-05-05 22:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (37, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_004.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR-1802', '2023-05-06 09:15:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (38, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_004.pdf', '<EMAIL>', '', 'system', 'Y', 'YN968D1', '2023-05-06 09:15:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (39, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_004.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-05-06 10:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (40, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_004.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'APTN(YN968D1)', '2023-05-06 10:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (41, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_005.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR-1802', '2023-05-06 10:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (42, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_005.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'APTN(YN968D1)', '2023-05-06 10:30:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (43, '', '（降级）H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_005.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'APTN(YN968D1)', '2023-05-06 14:15:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (44, '', 'SUSAR平台测试文件_02.pdf/SUSAR平台测试文件_01.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-05-21 21:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (45, '', 'SUSAR平台测试文件_05.pdf/SUSAR平台测试文件_04.pdf/SUSAR平台测试文件_03.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-05-23 10:15:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (46, '', 'Susar_test20.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-06-13 10:15:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (47, '', 'Susar_test20.pdf', '<EMAIL>', '<EMAIL>', '<EMAIL>', 'Y', 'HRS001', '2023-06-16 17:55:14', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (48, '', 'H2023001201118 SHR-TEST-215_SAE03_FU4_INV(降级).pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-19 13:31:10', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (49, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_06(降级).pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-19 13:33:19', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (50, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_100.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-19 13:36:23', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (51, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU1_INV_101.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-19 13:37:37', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (52, '', 'H2023001201118 SHR-TEST-215_SAE03_FU34_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-19 13:38:37', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (53, '', 'H2023001201118 SHR-TEST-215_SAE03_FU34_INV(降级).pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-19 13:41:14', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (54, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU4_INV_101.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR4640', '2023-06-19 14:24:13', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (55, '', 'H2023001201490 SHR-1316-III-302_SAE266_INIFU4_INV_101.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-06-19 14:30:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (56, '', 'test_biu.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-06-19 14:45:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (57, '', 'test_biu.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR4640', '2023-06-19 14:45:02', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (58, '', 'H2023001201490 SHR-1314-III-302_SAE266_INIFU1_INV_008.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-06-20 10:45:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (59, '', '20230620 H2023001201492 SHR6390-III-302_SAE260_INIFU1_INV_002.pdf.pdf/H2023001201493 SHR6390-III-302_SAE260_INIFU1_INV_003(降级).pdf.pdf/H2023001201491 SHR6390-III-302_SAE260_INIFU1_INV_001.pdf.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'APTN(YN968D1)', '2023-06-20 11:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (60, '', 'H2023001201491 SHR6390-III-302_SAE260_INIFU1_INV_002.pdf.pdf/H2023001201493 SHR6390-III-302_SAE260_INIFU1_INV_003.pdf.pdf/20230620 H2023001201492 SHR6390-III-302_SAE260_INIFU1_INV_003（降级）.pdf.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'APTN(YN968D1)', '2023-06-20 14:35:51', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (61, '', 'Susartest2.pdf/Susartest1.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-20 14:59:31', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (62, '', 'H2023001201490 SHR-6789-III-302_SAE266_INIFU1_INV_008.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-20 15:08:41', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (63, '', 'H2023001201490 SHR-6789-III-302_SAE266_INIFU1_INV_009(降级).pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-20 15:09:18', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (64, '', 'H2023001201490 SHR-6789-III-302_SAE266_INIFU1_INV_010.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-20 15:10:51', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (65, '', 'H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_103.pdf/H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_102.pdf/H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_101.pdf/H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_100.pdf/H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_009.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'N', 'SHR6390', '2023-06-20 15:37:24', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (66, '', 'H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_103.pdf/H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_102.pdf/H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_101.pdf/H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_100.pdf/H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_009.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'N', 'SHR6390', '2023-06-20 15:45:03', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (67, '', 'H2023001201120 SHR-TESTSSSS-212_SAE3_FU5_TEST.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-20 17:34:57', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (68, '', 'H2023001201120 SHR-TESTSSSS-212_SAE3_FU5_TEST1.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-20 17:36:32', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (69, '', 'H2023001201120 SHR-TESTSSSS-212_SAE3_FU5_TEST2.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-20 17:52:43', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (70, '', 'H2023001201120 SHR-TESTSSSS-212_SAE3_FU5_TEST3.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-20 17:53:55', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (71, '', 'H2022001200644 SHR-1210-III-325_SAE39_FU3_INV.pdf/H2023001202517 SHR-1210-III-336_SAE14_FU1_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'APTN(YN968D1)', '2023-06-21 09:24:34', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (72, '', 'H2023001202517 SHR-1210-III-336_SAE14_FU1_INV.pdf/H2022001200644 SHR-1210-III-325_SAE39_FU3_INV.pdf/', '<EMAIL>', '<EMAIL>', 'system', 'Y', 'SHR1210', '2023-06-21 09:30:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (73, '', 'H2023001201490 SHR6390-306_SAE266_INIFU1_INV_2.pdf/H2023001201490 SHR6390-306_SAE266_INIFU1_INV_1.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-06-21 13:45:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (74, '', 'H2023001201490 SHR6390-306_SAE266_INIFU1_INV_1.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-21 13:45:58', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (75, '', 'H2023001201490 SHR6390-306_SAE266_INIFU1_INV_4.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-21 13:54:11', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (76, '', 'H2023001201490 SHR6390-306_SAE266_INIFU1_INV_3.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-21 13:55:59', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (77, '', 'H2023001201490 SHR6390-306_SAE266_INIFU1_INV_6.pdf/H2023001201490 SHR6390-306_SAE266_INIFU1_INV_5.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-06-21 14:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (78, '', 'H2023001201490 SHR6390-306_SAE266_INIFU1_INV_7.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-06-21 14:15:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (79, '', 'H2023001201490 SHR6390-306_SAE266_INIFU1_INV_7.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-21 14:15:10', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (80, '', 'H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_000.pdf/H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_000 - 副本.pdf/H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_000 - 副本 (4).pdf/H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_000 - 副本 (3).pdf/H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_000 - 副本 (2).pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'N', 'SHR6390', '2023-06-21 14:33:29', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (81, '', 'INV_7.pdf/H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_000 - 副本 (2).pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-21 15:07:12', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (82, '', 'H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_000 - 副本 (3).pdf/H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_降级.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-21 15:08:45', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (83, '', 'H2023001201490 SHR-777-III-302_SAE266_INIFU1_降级.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-21 15:09:41', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (84, '', 'H2023001201490 SHR-777-III-302_SAE266_INIFU1_INV_000 - 副本 (4).pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-06-21 15:10:51', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (85, '', 'H2023001202291 SHR-1210-III-329_SAE107_FU9FU10_INV.pdf', '<EMAIL>', '<EMAIL>', 'system', 'Y', 'SHR1210', '2023-06-25 11:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (86, '', 'H2023001202291 SHR-1210-III-329_SAE107_FU9FU10_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>', 'system', 'Y', '1111', '2023-06-25 11:15:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (87, '', 'H2023001202291 SHR-1210-III-329_SAE107_FU9FU10_INV-1.pdf', '<EMAIL>', '<EMAIL>', 'system', 'Y', 'SHR1210', '2023-06-25 11:15:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (88, '', 'H2023001202346 SHR-A1811-I-101_SAE153_FU1FU2_INV.pdf', '<EMAIL>', '<EMAIL>', 'system', 'Y', 'SHR-1701', '2023-06-25 16:30:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (89, '', 'H2023001202570 SHR-A1921-I-101_SAE50_INI_INV.pdf/2023-00000053 HS-10296-306_SAE37_FU6_INV.pdf/2022-00000063 HS-10296-306_SAE13_FU9_INV.pdf/H2023001202525 HRS7415-I-101_SAE13_INI_INV.pdf/H2023001202384 SHR-1701-II-207_SAE54_FU2FU3_INV（降级）.pdf/H2022001202898 SHR-1701-III-310_SAE06_FU3_INV.pdf/', '<EMAIL>', '<EMAIL>', 'system', 'Y', '1209', '2023-06-25 18:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (90, '', 'H2022001202835 SHR-A1921-I-101_SAE06_FU4_INV.pdf/H2023001202214 SHR-A1811-I-101_SAE150_FU2_INV.pdf/H2023001202288 SHR-8068-II-201-NSCLC_SAE09_FU1FU2_INV.pdf/H2023001201256 SHR6390-III-302_SAE78_FU2_INV.pdf/H2023001202585 SHR-1701-III-307_SAE244_INI_INV.pdf/H2022001200523 SHR-1701-II-205_SAE79_FU3_INV.pdf/H2023001200586 SHR-1316-III-302_SAE224_FU4_INV.pdf/RS202300033 RSJ10535_SAE08_FU1_INV（H2023001202413中文CIOMS）.pdf/H2023001202272 SHR-1210-III-329_SAE106_FU2FU3_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>', 'system', 'Y', '1111', '2023-06-26 14:45:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (91, '', 'H2023001202634 SHR-A1921-201_SAE02_INI_INV.pdf/H2023001202214 SHR-A1811-I-101_SAE150_FU2_INV.pdf/H2023001202288 SHR-8068-II-201-NSCLC_SAE09_FU1FU2_INV.pdf/TGT002798 TG-1701-101_SAE121_FU2_INV (H2023001202254 英文CIOMS).pdf/H2023001202678 SHR-1316-III-302_SAE332_INI_INV.pdf/H2023001201540 SHR-1316-III-303_SAE99_FU5_INV.pdf/2022-00000153 HS-10296-306_SAE29_FU14_INV.pdf/HANSOH-2023-000004 HS-10296-302_SAE03_FU3_INV.pdf/H2023001201408 SHR-1210-III-329_SAE97_FU4_INV.pdf/H2022001202925 SHR-1701-III-310_SAE07_FU3_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>', 'system', 'Y', '1104', '2023-06-26 17:15:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (92, '', 'H2023001202809 SHR-A2009-I-101_SAE28_INIFU1FU2_INV.pdf/H2023001202738 SHR-A2009-I-101_SAE27_INIFU1_INV.pdf/H2023001202394 SHR-A2009-I-101_SAE24_FU2_INV.pdf/H2023001202216 SHR-1701-III-307_SAE224_FU2_INV.pdf/H2023001202817 SHR-1701-III-307_SAE263_INI_INV.pdf/H2023001201490 SHR-1316-III-302_SAE266_FU4_INV.pdf/H2023001202758 SHR-8068-II-201-HCC_SAE05_INI_INV.pdf/H2023001202115 SHR-1316-III-302_SAE300_FU3_INV.pdf/H2023001201982 SHR-1210-II-218_SAE08_FU6_INV.pdf/2022-00000153 HS-10296-306_SAE29_FU16_INV.pdf/H2023001202754 SHR-1210-II-218_SAE17_INI_INV.pdf/', '<EMAIL>', '843273033@com', 'system', 'N', 'Compound  C', '2023-07-06 09:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (93, '', 'H2023001202809 SHR-A2009-I-101_SAE28_INIFU1FU2_INV.pdf/H2023001202738 SHR-A2009-I-101_SAE27_INIFU1_INV.pdf/H2023001202394 SHR-A2009-I-101_SAE24_FU2_INV.pdf/H2023001202216 SHR-1701-III-307_SAE224_FU2_INV.pdf/H2023001202817 SHR-1701-III-307_SAE263_INI_INV.pdf/H2023001201490 SHR-1316-III-302_SAE266_FU4_INV.pdf/H2023001202758 SHR-8068-II-201-HCC_SAE05_INI_INV.pdf/H2023001202115 SHR-1316-III-302_SAE300_FU3_INV.pdf/H2023001201982 SHR-1210-II-218_SAE08_FU6_INV.pdf/2022-00000153 HS-10296-306_SAE29_FU16_INV.pdf/H2023001202754 SHR-1210-II-218_SAE17_INI_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'Compound  C', '2023-07-06 09:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (94, '', 'test_frequency.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'SHR6390', '2023-07-06 21:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (95, '', 'H2023001202788 SHR-A1921-I-101_SAE57_INIFU1_INV.pdf/H2023001202570 SHR-A1921-I-101_SAE50_FU1FU2_INV.pdf/H2023001201754 SHR-A1811-I-102_SAE49_FU3FU4_INV.pdf/H2023001201538 SHR-A1811-I-101_SAE135_FU3_INV（降级）.pdf/H2023001201174 SHR-A1811-I-101_SAE137_FU2_INV.pdf/H2023001202423 SHR-1701-215_SAE06_FU3_INV.pdf/H2021001203753 SHR1459-I-101_SAE25_FU2_INV.pdf/H2023001202218 SHR-1316-III-302_SAE306_FU3_INV.pdf/H2023001202102 SHR-1316-III-302_SAE299_FU2_INV.pdf/H2023001201708 SHR-1316-III-302_SAE273_FU2_INV.pdf/H2022001202642 SHR-1316-III-302_SAE121_FU3_INV.pdf/H2022001201315 SHR-1316-III-302_SAE67_FU8_INV.pdf/Compound  C_(5)_H2023001202291 SHR-1210-III-329_SAE107_FU11_INV.pdf/H2023001202469 MA-BC-II-043_SAE01_FU2_INV.pdf/H2023001202291 SHR-1210-III-329_SAE107_FU11_INV.pdf/H2022001203936 SHR-1701-III-309_SAE23_FU3_INV.pdf/H2023001200791 HR-BLTN-III-MBC-C_SAE184_FU5_INV.pdf/H2023001202635 SHR-1210-II-218_SAE15_FU1FU2_INV.pdf/', '<EMAIL>', '843273033@com', 'system', 'N', 'Compound  C', '2023-07-06 21:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (96, '', 'H2023001202788 SHR-A1921-I-101_SAE57_INIFU1_INV.pdf/H2023001202570 SHR-A1921-I-101_SAE50_FU1FU2_INV.pdf/H2023001201754 SHR-A1811-I-102_SAE49_FU3FU4_INV.pdf/H2023001201538 SHR-A1811-I-101_SAE135_FU3_INV（降级）.pdf/H2023001201174 SHR-A1811-I-101_SAE137_FU2_INV.pdf/H2023001202423 SHR-1701-215_SAE06_FU3_INV.pdf/H2021001203753 SHR1459-I-101_SAE25_FU2_INV.pdf/H2023001202218 SHR-1316-III-302_SAE306_FU3_INV.pdf/H2023001202102 SHR-1316-III-302_SAE299_FU2_INV.pdf/H2023001201708 SHR-1316-III-302_SAE273_FU2_INV.pdf/H2022001202642 SHR-1316-III-302_SAE121_FU3_INV.pdf/H2022001201315 SHR-1316-III-302_SAE67_FU8_INV.pdf/Compound  C_(5)_H2023001202291 SHR-1210-III-329_SAE107_FU11_INV.pdf/H2023001202469 MA-BC-II-043_SAE01_FU2_INV.pdf/H2023001202291 SHR-1210-III-329_SAE107_FU11_INV.pdf/H2022001203936 SHR-1701-III-309_SAE23_FU3_INV.pdf/H2023001200791 HR-BLTN-III-MBC-C_SAE184_FU5_INV.pdf/H2023001202635 SHR-1210-II-218_SAE15_FU1FU2_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'Compound  C', '2023-07-06 21:00:02', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (97, '', 'H2023001201491 发送频率.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'APTN(YN968D1)', '2023-07-08 09:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (98, '', '2023-00000106 HS-10296-306_SAE48_INI_INV.pdf/2023-00000053 HS-10296-306_SAE37_FU9_INV.pdf/H2023001202523 SHR-A2009-I-101_SAE26_FU2_INV.pdf/H2023001202920 SHR-A1811-III-301_SAE14_INI_INV.pdf/H2023001202399 SHR6390-III-303_SAE225_FU1FU2_INV.pdf/H2023001202373 SHR4640-303_SAE19_FU6FU7_INV.pdf/H2023001202103 SHR3680-III-302_SAE60_FU2FU3_INV.pdf/H2023001201294 SHR-1701-III-307_SAE167_FU3_INV(降级).pdf/H2023001201887 RSJ10135_SAE21_FU2_INV.pdf/H2023001202642 HR18034-203_SAE02_FU3_INV.pdf/H2023001201453 SHR-1210-III-329_SAE98_FU4_INV.pdf/', '<EMAIL>', '843273033@com', 'system', 'N', 'Compound A', '2023-07-10 18:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (99, '', '2023-00000106 HS-10296-306_SAE48_INI_INV.pdf/2023-00000053 HS-10296-306_SAE37_FU9_INV.pdf/H2023001202523 SHR-A2009-I-101_SAE26_FU2_INV.pdf/H2023001202920 SHR-A1811-III-301_SAE14_INI_INV.pdf/H2023001202399 SHR6390-III-303_SAE225_FU1FU2_INV.pdf/H2023001202373 SHR4640-303_SAE19_FU6FU7_INV.pdf/H2023001202103 SHR3680-III-302_SAE60_FU2FU3_INV.pdf/H2023001201294 SHR-1701-III-307_SAE167_FU3_INV(降级).pdf/H2023001201887 RSJ10135_SAE21_FU2_INV.pdf/H2023001202642 HR18034-203_SAE02_FU3_INV.pdf/H2023001201453 SHR-1210-III-329_SAE98_FU4_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'Compound A', '2023-07-10 18:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (100, '', '2023-00000106 HS-10296-306_SAE48_INI_INV.pdf/2023-00000053 HS-10296-306_SAE37_FU9_INV.pdf/H2023001202523 SHR-A2009-I-101_SAE26_FU2_INV.pdf/H2023001202920 SHR-A1811-III-301_SAE14_INI_INV.pdf/H2023001202399 SHR6390-III-303_SAE225_FU1FU2_INV.pdf/H2023001202373 SHR4640-303_SAE19_FU6FU7_INV.pdf/H2023001202103 SHR3680-III-302_SAE60_FU2FU3_INV.pdf/H2023001201294 SHR-1701-III-307_SAE167_FU3_INV(降级).pdf/H2023001201887 RSJ10135_SAE21_FU2_INV.pdf/H2023001202642 HR18034-203_SAE02_FU3_INV.pdf/H2023001201453 SHR-1210-III-329_SAE98_FU4_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'Compound A', '2023-07-10 18:00:02', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (101, '', 'H2023001202920 SHR-A1811-III-301_SAE14_FU1_INV.pdf/H2023001202809 SHR-A2009-I-101_SAE28_FU3_INV.pdf/H2023001202721 SHR-A2009-I-102_SAE04_FU3FU4_INV.pdf/H2023001201433 SHR-A2009-I-102_SAE03_FU2_INV（降级）.pdf/H2022001202798 SHR6390-III-303_SAE79_FU2_INV.pdf/H2023001202585 SHR-1701-III-307_SAE244_FU2_INV.pdf/H2022001203437 SHR-1701-III-307_SAE44_FU4_INV.pdf/', '<EMAIL>', '843273033@com', 'system', 'N', 'Compound A', '2023-07-11 18:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (102, '', 'H2023001202920 SHR-A1811-III-301_SAE14_FU1_INV.pdf/H2023001202809 SHR-A2009-I-101_SAE28_FU3_INV.pdf/H2023001202721 SHR-A2009-I-102_SAE04_FU3FU4_INV.pdf/H2023001201433 SHR-A2009-I-102_SAE03_FU2_INV（降级）.pdf/H2022001202798 SHR6390-III-303_SAE79_FU2_INV.pdf/H2023001202585 SHR-1701-III-307_SAE244_FU2_INV.pdf/H2022001203437 SHR-1701-III-307_SAE44_FU4_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'Compound A', '2023-07-11 18:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (103, '', 'H2023001202920 SHR-A1811-III-301_SAE14_FU1_INV.pdf/H2023001202809 SHR-A2009-I-101_SAE28_FU3_INV.pdf/H2023001202721 SHR-A2009-I-102_SAE04_FU3FU4_INV.pdf/H2023001201433 SHR-A2009-I-102_SAE03_FU2_INV（降级）.pdf/H2022001202798 SHR6390-III-303_SAE79_FU2_INV.pdf/H2023001202585 SHR-1701-III-307_SAE244_FU2_INV.pdf/H2022001203437 SHR-1701-III-307_SAE44_FU4_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'Compound A', '2023-07-11 18:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (104, '', 'H2023001202634 SHR-A1921-201_SAE02_FU1FU2_INV.pdf/H2023001202204 SHR-A1811-I-101_SAE148_FU4_INV.pdf/H2023001202787 SHR4640-303_SAE20_FU1FU2_INV.pdf/H2021001205055 SHR3680-II-203_SAE21_FU5_INV.pdf/H2023001202928 SHR-1701-III-307_SAE275_INI_INV.pdf/H2023001202099 SHR-1701-III-307_SAE219_FU2_INV.pdf/2023-00000074 HS-10296-306_SAE44_FU3_INV.pdf/2023-00000065 HS-10296-306_SAE41_FU4_INV.pdf/H2023001201441 HRS-4642-I-101_SAE02_FU3_INV.pdf/H2023001202506 SHR-1210-III-329_SAE113_FU2_INV.pdf/', '<EMAIL>', '843273033@com', 'system', 'N', 'Compound A', '2023-07-12 18:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (105, '', 'H2023001202634 SHR-A1921-201_SAE02_FU1FU2_INV.pdf/H2023001202204 SHR-A1811-I-101_SAE148_FU4_INV.pdf/H2023001202787 SHR4640-303_SAE20_FU1FU2_INV.pdf/H2021001205055 SHR3680-II-203_SAE21_FU5_INV.pdf/H2023001202928 SHR-1701-III-307_SAE275_INI_INV.pdf/H2023001202099 SHR-1701-III-307_SAE219_FU2_INV.pdf/2023-00000074 HS-10296-306_SAE44_FU3_INV.pdf/2023-00000065 HS-10296-306_SAE41_FU4_INV.pdf/H2023001201441 HRS-4642-I-101_SAE02_FU3_INV.pdf/H2023001202506 SHR-1210-III-329_SAE113_FU2_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'Compound A', '2023-07-12 18:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (106, '', 'H2023001202634 SHR-A1921-201_SAE02_FU1FU2_INV.pdf/H2023001202204 SHR-A1811-I-101_SAE148_FU4_INV.pdf/H2023001202787 SHR4640-303_SAE20_FU1FU2_INV.pdf/H2021001205055 SHR3680-II-203_SAE21_FU5_INV.pdf/H2023001202928 SHR-1701-III-307_SAE275_INI_INV.pdf/H2023001202099 SHR-1701-III-307_SAE219_FU2_INV.pdf/2023-00000074 HS-10296-306_SAE44_FU3_INV.pdf/2023-00000065 HS-10296-306_SAE41_FU4_INV.pdf/H2023001201441 HRS-4642-I-101_SAE02_FU3_INV.pdf/H2023001202506 SHR-1210-III-329_SAE113_FU2_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'Compound A', '2023-07-12 18:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (107, '', 'H2023001201492 发送频率.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'APTN(YN968D1)', '2023-07-13 18:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (108, '', 'H2023001202394 SHR-A2009-I-101_SAE24_FU3_INV.pdf/H2023001201961 SHR-A1921-I-101_SAE40_FU2_INV.pdf/H2023001202908 SHR6390-III-303_SAE246_INIFU1_INV.pdf/H2023001202942 SHR3680-III-302_SAE70_INI_INV.pdf/H2023001202537 SHR3680-III-302_SAE65_FU1_INV.pdf/H2023001202882 SHR-1701-III-307_SAE270_FU2_INV.pdf/H2023001201949 SHR-1701-III-307_SAE203_FU3_INV.pdf/H2023001202937 SHR-1316-III-302_SAE345_INI_INV.pdf/H2023001202905 SHR-1316-III-302_SAE344_INIFU1_INV.pdf/H2023001202698 SHR-1316-III-302_SAE333_FU1_INV.pdf/H2023001202678 SHR-1316-III-302_SAE332_FU2_INV.pdf/H2023001202754 SHR-1210-II-218_SAE17_FU1_INV.pdf/H2023001202715 MA-EC-II-011_SAE16_FU7_INV.pdf/H2022001204292 DRAGON-IV-Ahead-G208_SAE155_FU2_INV（降级）.pdf/test1测试.pdf/', '<EMAIL>', '843273033@com', 'system', 'N', 'Compound A', '2023-07-13 18:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (109, '', 'H2023001202394 SHR-A2009-I-101_SAE24_FU3_INV.pdf/H2023001201961 SHR-A1921-I-101_SAE40_FU2_INV.pdf/H2023001202908 SHR6390-III-303_SAE246_INIFU1_INV.pdf/H2023001202942 SHR3680-III-302_SAE70_INI_INV.pdf/H2023001202537 SHR3680-III-302_SAE65_FU1_INV.pdf/H2023001202882 SHR-1701-III-307_SAE270_FU2_INV.pdf/H2023001201949 SHR-1701-III-307_SAE203_FU3_INV.pdf/H2023001202937 SHR-1316-III-302_SAE345_INI_INV.pdf/H2023001202905 SHR-1316-III-302_SAE344_INIFU1_INV.pdf/H2023001202698 SHR-1316-III-302_SAE333_FU1_INV.pdf/H2023001202678 SHR-1316-III-302_SAE332_FU2_INV.pdf/H2023001202754 SHR-1210-II-218_SAE17_FU1_INV.pdf/H2023001202715 MA-EC-II-011_SAE16_FU7_INV.pdf/H2022001204292 DRAGON-IV-Ahead-G208_SAE155_FU2_INV（降级）.pdf/test1测试.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'Compound A', '2023-07-13 18:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (110, '', 'H2023001202394 SHR-A2009-I-101_SAE24_FU3_INV.pdf/H2023001201961 SHR-A1921-I-101_SAE40_FU2_INV.pdf/H2023001202908 SHR6390-III-303_SAE246_INIFU1_INV.pdf/H2023001202942 SHR3680-III-302_SAE70_INI_INV.pdf/H2023001202537 SHR3680-III-302_SAE65_FU1_INV.pdf/H2023001202882 SHR-1701-III-307_SAE270_FU2_INV.pdf/H2023001201949 SHR-1701-III-307_SAE203_FU3_INV.pdf/H2023001202937 SHR-1316-III-302_SAE345_INI_INV.pdf/H2023001202905 SHR-1316-III-302_SAE344_INIFU1_INV.pdf/H2023001202698 SHR-1316-III-302_SAE333_FU1_INV.pdf/H2023001202678 SHR-1316-III-302_SAE332_FU2_INV.pdf/H2023001202754 SHR-1210-II-218_SAE17_FU1_INV.pdf/H2023001202715 MA-EC-II-011_SAE16_FU7_INV.pdf/H2022001204292 DRAGON-IV-Ahead-G208_SAE155_FU2_INV（降级）.pdf/test1测试.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'Compound A', '2023-07-13 18:00:02', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (111, '', 'H2023001202394 SHR-A2009-I-101_SAE24_FU3_test.pdf', '<EMAIL>', '843273033@com', 'system', 'N', 'Compound A', '2023-07-14 14:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (112, '', 'H2023001202394 SHR-A2009-I-101_SAE24_FU3_test.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'Compound A', '2023-07-14 14:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (113, '', 'H2023001202394 SHR-A2009-I-101_SAE24_FU3_test.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'Compound A', '2023-07-14 14:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (114, '', 'H2023001201956 SHR-A1921-I-101_SAE39_FU4_INV.pdf/H2023001201886 SHR-A1921-201_SAE01_FU3_INV.pdf/H2023001203012 SHR-A1811-I-101_SAE170_INI_INV.pdf/H2023001201255 SHR-A1811-I-102_SAE43_FU2_INV.pdf/H2023001202288 SHR-8068-II-201-NSCLC_SAE09_FU3_INV.pdf/H2023001202933 MA-BC-II-029_SAE03_INIFU1_INV.pdf/H2023001202423 SHR-1701-215_SAE06_FU4_INV.pdf/H2023001201378 SHR-1701-III-308_SAE33_FU11_INV.pdf/H2022001202448 SHR-1701-III-307_SAE15_FU8_INV.pdf/H2022001201079 SHR-1701-II-205_SAE93_FU3_INV.pdf/H2021001205492 SHR-1701-I-103_SAE20_FU3_INV.pdf/2023-00000112 HS-10296-306_SAE49_INI_INV.pdf/H2023001202959 HR19024-101_SAE11_INI_INV.pdf/H2023001202983 SHR-1210-III-329_SAE122_INI_INV.pdf/H2023001203010 SHR-1701-III-309_SAE41_INI_INV.pdf/H2023001202958 SHR-1701-III-309_SAE40_INI_INV.pdf/H2023001202754 SHR-1210-II-218_SAE17_FU2FU3_INV.pdf/H2023001202517 SHR-1210-III-336_SAE14_FU2_INV.pdf/', '<EMAIL>', '', 'system', 'Y', 'Compound  C', '2023-07-15 14:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (115, '', 'H2023001201491 TEST2.pdf/H2023001201490 TEST1.pdf/', '<EMAIL>', '<EMAIL>', 'system', 'Y', 'HRS001', '2023-07-18 15:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (116, '', 'H2023001201491 TEST1.pdf/H2023001201493 TEST3.pdf/H2023001201492 TEST2.pdf/', '<EMAIL>', '', 'system', 'Y', 'HRS001', '2023-07-19 14:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (117, '', 'H2023001201491 SHR1001-III-302_SAE201_INIFU2_INV.pdf.pdf/H2023001201490 SHR1001-III-301_SAE260_INIFU1_INV.pdf.pdf/', '<EMAIL>', '<EMAIL>', 'system', 'Y', 'HRS001', '2023-07-20 14:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (118, '', 'H202300120133 SHR-6789-III-302_SAE266_INIFU1_INV_010.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2023-07-25 10:59:02', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (119, '', 'H2023001201493 TEST3.pdf/H2023001201492 TEST2.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'APTN(YN968D1)', '2023-07-26 10:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (120, '', 'H2023001201494 TEST4.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'APTN(YN968D1)', '2023-07-27 14:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (121, '', 'H2023001201495 TEST5.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'APTN(YN968D1)', '2023-07-28 16:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (122, '', 'H2023001203157 SHR6390-III-303_SAE262_INIFU1_INV.pdf/H2023001203214 SHR4640-303_SAE23_INI_INV.pdf/H2023001202299 SHR4640-303_SAE12_FU3_INV.pdf/H2021001203509 SHR-1701-II-205_SAE26_FU7_INV.pdf/TGT002867 TG-1701-101_SAE126_INI_INV (H2023001203302 中文CIOMS).pdf/TGT002880 TG-1701-101_SAE129_INI_INV (H2023001203360英文CIOMS).pdf/H2023001203099 SHR-8068-II-201-NSCLC_SAE15_FU2_INV.pdf/H2023001202835 SHR-8068-II-201-NSCLC_SAE13_FU2_INV.pdf/H2023001202243 SHR-1316-III-302_SAE307_FU1_INV.pdf/H2023001203199 SHR-1210-II-218_SAE23_INI_INV.pdf/H2023001202744 GVHD-1st-IIT-SHR0302-PED-SHSY_SAE04_FU1_INV.pdf/2023219004607 HS-10241-102_SAE12_FU3_INV.pdf/H2023001203195 SHR-1210-II-218_SAE24_INI_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1104', '2023-08-01 09:00:02', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (123, '', '一体化平台用户手册_20220624 - 副本.pdf/一体化平台用户手册_20220624 - 副本 (8).pdf/一体化平台用户手册_20220624 - 副本 (7).pdf/一体化平台用户手册_20220624 - 副本 (6).pdf/一体化平台用户手册_20220624 - 副本 (5).pdf/一体化平台用户手册_20220624 - 副本 (4).pdf/一体化平台用户手册_20220624 - 副本 (3).pdf/一体化平台用户手册_20220624 - 副本 (2).pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'N', 'TEST-1', '2023-08-01 17:02:42', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (124, '', 'AE分级记录工具技术文件_V1.0_20221209.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'TEST-1', '2023-08-08 15:05:53', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (125, '', '20230830 H2023001203157 SHR6390-III-303_SAE262_INIFU1_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', '1104', '2023-08-30 17:28:52', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (126, '', '20230830 TGT002880 TG-1701-101_SAE129_INI_INV (H2023001203360英文CIOMS).pdf.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', '1104', '2023-08-30 17:30:30', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (127, '', '20230831 TGT002880 TG-1701-101_SAE129_INI_INV (H2023001203360).pdf.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', '1104', '2023-08-30 17:31:31', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (128, '', 'SOP-PV-001F1_不良事件报告表_V2.0_final_培训用.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'EDCTEST', '2023-09-08 15:52:52', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (129, '', 'H2021001203509 SHR-TEST-II-205_SAE26_FU7_INV.pdf', '<EMAIL>', '<EMAIL>', '<EMAIL>', 'Y', '1114', '2023-09-19 09:14:23', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (130, '', 'Argus Admin培训_20230404.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'TEST-1', '2023-09-19 15:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (131, '', '20230919 H2021002203510 SHR-TEST-II-201_SAE26_FU7_INV.pdf/20230919 H2021001203509 SHR-TEST-II-203_SAE31_FU3_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'EDCTEST', '2023-09-19 17:20:38', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (132, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU6_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU7_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'HR7777', '2023-09-19 18:19:12', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (133, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU5_INV(降级).pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'HR7777', '2023-09-19 18:25:50', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (134, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU26_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU25_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU24_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU22_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU23_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU21_INV.pdf/', '<EMAIL>', '<EMAIL>', '<EMAIL>', 'N', 'HRS001', '2023-09-19 18:32:54', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (135, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU1_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU3_INV.pdf/20230919 H2021009203509 SHR-TEST-II-202_SAE10_FU2_INV.pdf/20230919 H2021002203510 SHR-TEST-II-201_SAE26_FU7_INV.pdf/20230919 H2021001203509 SHR-TEST-II-203_SAE31_FU3_INV.pdf/', '<EMAIL>', '<EMAIL>', 'system', 'Y', 'HRS001', '2023-09-19 19:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (136, '', '20230919 H2021009203509 SHR-TEST-II-202_SAE10_FU2_INV.pdf/20230919 H2021002203510 SHR-TEST-II-201_SAE26_FU7_INV.pdf/20230919 H2021001203509 SHR-TEST-II-203_SAE31_FU3_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>', 'system', 'Y', 'HR20014', '2023-09-19 19:00:02', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (137, '', '20230919 H2021009203509 SHR-TEST-II-202_SAE10_FU2_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'EDCTEST', '2023-09-19 19:00:02', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (138, '', '20230919 H2021009203509 SHR-TEST-II-202_SAE10_FU2_INV.pdf/20230919 H2021002203510 SHR-TEST-II-201_SAE26_FU7_INV.pdf/20230919 H2021001203509 SHR-TEST-II-203_SAE31_FU3_INV.pdf/', '<EMAIL>', '<EMAIL>', 'system', 'Y', 'EDC-20220302', '2023-09-19 19:00:03', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (139, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU26_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU25_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU24_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU22_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU23_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU21_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'N', 'HR7777', '2023-09-19 19:00:07', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (140, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU26_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU25_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU24_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU22_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU23_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU21_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'N', 'HR7777', '2023-09-19 21:00:05', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (141, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU9_INV.pdf', '<EMAIL>', '<EMAIL>', 'system', 'Y', 'HRS001', '2023-09-19 21:00:05', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (142, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU32_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'TEST-2', '2023-09-19 21:22:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (143, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU26_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU25_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU24_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU22_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU23_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU21_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'N', 'HR7777', '2023-09-20 09:00:05', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (144, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU10_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'HRS001', '2023-09-20 09:00:05', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (145, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU31_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'TEST-1', '2023-09-20 09:00:06', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (146, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU26_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU25_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU24_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU22_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU23_INV.pdf/H2023001202925 SHR-1701-III-310_SAE07_FU21_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'N', 'HR7777', '2023-09-20 10:00:05', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (147, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU41_INV(降级).pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'HRS001', '2023-09-20 10:00:06', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (148, '', '20230919 H2021001203509 SHR-TEST-II-203_SAE31_FU3_INV.pdf', '<EMAIL>', '<EMAIL>', 'system', 'Y', '1114', '2023-09-20 10:00:06', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (149, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU41_INV(邮箱).pdf', '<EMAIL>', 'gzh324@qqcom,gzh324.com', '<EMAIL>', 'N', 'HRS001', '2023-09-20 10:22:27', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (150, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU41_INV(邮箱).pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'HRS001', '2023-09-20 10:22:27', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (151, '', '20230920 H2023001202335 SHR-1210-III-324 _FU7_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', '1104', '2023-09-20 14:57:51', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (152, '', '20230920 H2023001202335 SHR-1210-III-324 _FU7_INV.pdf', '<EMAIL>', 'err02hr.com', '<EMAIL>', 'N', '1213', '2023-09-21 10:52:54', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (153, '', '20230920 H2023001202335 SHR-1210-III-324 _FU7_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', '1213', '2023-09-21 10:52:55', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (154, '', '20230920 H2023001202335 1213_FU7_INV.pdf', '<EMAIL>', 'err02hr.com', '<EMAIL>', 'N', '1213', '2023-09-21 10:57:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (155, '', '20230920 H2023001202335 1213_FU7_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', '1213', '2023-09-21 10:57:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (156, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU61_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'HR7777', '2023-09-21 11:00:02', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (157, '', '20230920 H2023001202335 SHR-1210-III-324 _FU8_INV（降级）.pdf/20230920 H2023001202291 SHR-1210-III-329_FU14_INV.pdf/', '<EMAIL>', 'err02hr.com', 'system', 'N', '1213', '2023-09-21 11:00:02', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (158, '', '20230920 H2023001202335 SHR-1210-III-324 _FU8_INV（降级）.pdf/20230920 H2023001202291 SHR-1210-III-329_FU14_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1213', '2023-09-21 11:00:03', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (159, '', '20230920 H2023001202335 1213_FU8_INV.pdf', '<EMAIL>', 'err02hr.com', '<EMAIL>', 'N', '1213', '2023-09-21 11:18:41', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (160, '', '20230920 H2023001202335 1213_FU8_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', '1213', '2023-09-21 11:18:41', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (161, '', '20230921 H2023001204086 SHR-A1921-I-101_FU1_INV.pdf', '<EMAIL>', 'err02hr.com', 'system', 'N', '1213', '2023-09-21 17:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (162, '', '20230921 H2023001204086 SHR-A1921-I-101_FU1_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1213', '2023-09-21 17:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (163, '', '20230921 H2023001203149 SHR-1316-III-303_FU1_INV.pdf', '<EMAIL>', 'err02hr.com', 'system', 'N', '1213', '2023-09-22 09:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (164, '', '20230921 H2023001203149 SHR-1316-III-303_FU1_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1213', '2023-09-22 09:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (165, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU62_INV.pdf', '<EMAIL>', 'gzh324@qqcom,gzh324.com', '<EMAIL>', 'N', 'HRS001', '2023-09-22 09:31:37', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (166, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU62_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'N', 'HRS001', '2023-09-22 09:31:42', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (167, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU62_INV.pdf', '<EMAIL>', 'gzh324@qqcom,gzh324.com', '<EMAIL>', 'N', 'HRS001', '2023-09-22 09:32:08', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (168, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU62_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'N', 'HRS001', '2023-09-22 09:32:13', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (169, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU63_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'N', 'HR7777', '2023-09-22 09:33:27', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (170, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU63_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'HR7777', '2023-09-22 09:35:29', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (171, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU62_INV.pdf', '<EMAIL>', 'gzh324@qqcom,gzh324.com', '<EMAIL>', 'N', 'HRS001', '2023-09-22 09:35:43', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (172, '', 'H2023001202925 SHR-1701-III-310_SAE07_FU62_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'HRS001', '2023-09-22 09:35:44', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (173, '', '20230524 H2023001201720 SHR-1701-III-307_FU1_INV.pdf', '<EMAIL>', 'err02hr.com', '<EMAIL>', 'N', '1213', '2023-09-22 09:43:29', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (174, '', '20230524 H2023001201720 SHR-1701-III-307_FU1_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', '1213', '2023-09-22 09:43:29', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (175, '', '20230922 H2023001204222 RSJ10135_INI_INV.pdf/20230922 H2023001204332 SHR-A2009-I-101_INI_INV.pdf/20230922 H2023001203889 SHR-A2009-I-101_FU4_INV.pdf/20230922 H2021001205508 SHR0302-301_FU7_INV.pdf/', '<EMAIL>', 'err01@hrcom', 'system', 'N', '1114', '2023-09-23 09:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (176, '', '20230922 H2023001204222 RSJ10135_INI_INV.pdf/20230922 H2023001204332 SHR-A2009-I-101_INI_INV.pdf/20230922 H2023001203889 SHR-A2009-I-101_FU4_INV.pdf/20230922 H2021001205508 SHR0302-301_FU7_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1114', '2023-09-23 09:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (177, '', '20230925 H2023001202202 SHR-A2009-I-101_FU3_INV.pdf/20230925 H2023001203560 SHR-8068-II-201-NSCLC_FU3_INV.pdf/20230925 H2023001204246 SHR-8068-II-201-NSCLC_INI_INV.pdf/20230925 H2023219004607 HS-10241-102_FU4_INV.pdf/20230925 H2023-00000013 HS-10296-304_FU6_INV.pdf/20230925 H2023001203534 HRS-1167-I-101_FU6_INV.pdf/20230925 H2023001204254 HR19024-101 _INIFU1_INV.pdf/20230925 H2023001204089 HR19024-101_FU2_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1104', '2023-09-26 09:00:02', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (178, '', '20230926 H2023001204329 SHR-A2009-I-101_INI_INV.pdf/20230926 H2023001204219 SHR-A2009-I-101_FU2_INV.pdf/20230926 H2023001204332 SHR-A2009-I-101_FU1_INV.pdf/20230926 H2023001204285 SHR-8068-II-201-NSCLC_INI_INV.pdf/20230926 H2023001204410 SHR-8068-II-201-HCC_INI_INV.pdf/20230926 H2023219007819 HS-10296-12-01_FU4_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1104', '2023-09-27 09:00:04', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (179, '', '20230927 H2023001204423 SHR-8068-II-201-HCC_INI_INV.pdf/20230927 H2023001204124 SHR2554-I-101_FU1_INV（降级）.pdf/20230927 H2023001203342 SHR-1707-102_FU4_INV.pdf/20230927 H2022-00000153 HS-10296-306_FU23_INV.pdf/20230927 H2023-00000053 HS-10296-306_FU16_INV.pdf/20230927 H2023001201419 HRS2398-I-101_FU10_INV.pdf/20230927 H2023001203600 HRS-2189-I-101_FU3FU4_INV.pdf/20230927 H2023001204147 HRS-1167-I-101_FU2_INV.pdf/20230927 H2023001204322 HRS-1167-I-101_INIFU1FU2_INV.pdf/20230927 H2023001204179 SHR-A1811-II-203_FU1_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1104', '2023-09-28 09:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (180, '', '20230927 H2023001204193 SHR-A2102-I-101_FU1_INV.pdf/20230927 H2023001204227 SHR-A2009-I-101_FU1FU2_INV（降级）.pdf/', '<EMAIL>', 'err01@hrcom', 'system', 'N', '1114', '2023-09-28 09:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (181, '', '20230927 H2023001204193 SHR-A2102-I-101_FU1_INV.pdf/20230927 H2023001204227 SHR-A2009-I-101_FU1FU2_INV（降级）.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1114', '2023-09-28 09:00:02', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (182, '', '20230928 H2023001204332 SHR-A2009-I-101_FU2_INV.pdf/20230928 H2023001204339 SHR-A2009-I-101_INI_INV.pdf/20230928 H2023001204350 SHR-8068-II-201-NSCLC_INIFU1_INV.pdf/20230928  H2023001203273 SHR2554-I-101_FU6_INV.pdf/20230928 H2023001203450 SHR-1901-I-101_FU5FU6_INV.pdf/20230928 H2023001203600 HRS-2189-I-101_FU5_INV.pdf/20230928 H2022001203829 SHR-1701-III-309_FU12FU13_INV.pdf/20230928 H2023001204313 MA-OC-II-007_INI_INV.pdf/20230928 H2023001203908 SHR-1701-III-309_FU1_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1104', '2023-09-28 17:00:02', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (183, '', '20231006 H2023001204322 HRS-1167-I-101_FU3_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1104', '2023-10-07 10:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (184, '', '20231001 H2023219012093 HS-10241-102_FU3_INV.pdf/20231001 H2023219010306-004 HS-10241-102_FU5_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1104', '2023-10-07 17:00:02', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (185, '', '20231007 H2023001204459 SHR-A1912-I-101 _INI_INV.pdf/20231007 H2023001204391 SHR-8068-II-201-NSCLC_FU1_INV.pdf/20231007 H2023001204470 SHR-1802-II-202_INI_INV.pdf/20231007 H2022219012263 HS-10296-304_FU3_INV.pdf/20231007 H2023-00000152 HS-10296-305_INI_INV.pdf/20231007 H2023001201665 HRS7415-I-101_FU2_INV.pdf/20231007 H2023001203600 HRS-2189-I-101_FU6_INV.pdf/20231007 H2023001204180 HRS-1167-I-101_FU2FU3_INV.pdf/20231007  H2023001203979 SHR-1701-III-309_FU2_INV.pdf/', '<EMAIL>', 'err01@hrcom', 'system', 'N', '1114', '2023-10-08 09:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (186, '', '20231007 H2023001204459 SHR-A1912-I-101 _INI_INV.pdf/20231007 H2023001204391 SHR-8068-II-201-NSCLC_FU1_INV.pdf/20231007 H2023001204470 SHR-1802-II-202_INI_INV.pdf/20231007 H2022219012263 HS-10296-304_FU3_INV.pdf/20231007 H2023-00000152 HS-10296-305_INI_INV.pdf/20231007 H2023001201665 HRS7415-I-101_FU2_INV.pdf/20231007 H2023001203600 HRS-2189-I-101_FU6_INV.pdf/20231007 H2023001204180 HRS-1167-I-101_FU2FU3_INV.pdf/20231007  H2023001203979 SHR-1701-III-309_FU2_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1114', '2023-10-08 09:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (187, '', '20231008 H2023001204219 SHR-A2009-I-101_FU3_INV.pdf/20231008 H2023-00000152 HS-10296-305_FU1_INV.pdf/20230926 H2023-00000053 HS-10296-306_FU17_INV.pdf/20231008 H2023001204147 HRS-1167-I-101_FU3_INV.pdf/20231008 H2023001204254 HR19024-101 _FU2_INV.pdf/20231008 H2023001202642 HR18034-203_FU5_INV.pdf/20231008 H2022001201404 HR-BLTN-III-MBC-C_FU7_INV.pdf/', '<EMAIL>', 'err01@hrcom', 'system', 'N', '1114', '2023-10-09 09:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (188, '', '20231008 H2023001204219 SHR-A2009-I-101_FU3_INV.pdf/20231008 H2023-00000152 HS-10296-305_FU1_INV.pdf/20230926 H2023-00000053 HS-10296-306_FU17_INV.pdf/20231008 H2023001204147 HRS-1167-I-101_FU3_INV.pdf/20231008 H2023001204254 HR19024-101 _FU2_INV.pdf/20231008 H2023001202642 HR18034-203_FU5_INV.pdf/20231008 H2022001201404 HR-BLTN-III-MBC-C_FU7_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1114', '2023-10-09 09:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (189, '', '20231009 H2023219014916 HS-10241-102_INI_INV.pdf/20230921 H2023219014918 HS-10241-102_INI_INV.pdf/20231009 H2023001204551 SHR-A2009-I-101_INI_INV.pdf/20231009 H2023001204329 SHR-A2009-I-101_FU1_INV.pdf/20231009 H2023001204423 SHR-8068-II-201-HCC_FU1FU2_INV.pdf/20231009 H2023001204538 SHR-3680-III-HSPC_INI_INV.pdf/20231009 H2023001204485 SHR-A1904-I-101_INI_INV.pdf/20231009 H2023001204431 HR19042-202_FU1_INV.pdf/20231009 H2023001200461 SHR-1701-III-301_FU4_INV.pdf/20231009 H2023001204463 HR-BLTN-III-MBC-C_INI_INV.pdf/', '<EMAIL>', 'err01@hrcom', 'system', 'N', '1114', '2023-10-10 09:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (190, '', '20231009 H2023219014916 HS-10241-102_INI_INV.pdf/20230921 H2023219014918 HS-10241-102_INI_INV.pdf/20231009 H2023001204551 SHR-A2009-I-101_INI_INV.pdf/20231009 H2023001204329 SHR-A2009-I-101_FU1_INV.pdf/20231009 H2023001204423 SHR-8068-II-201-HCC_FU1FU2_INV.pdf/20231009 H2023001204538 SHR-3680-III-HSPC_INI_INV.pdf/20231009 H2023001204485 SHR-A1904-I-101_INI_INV.pdf/20231009 H2023001204431 HR19042-202_FU1_INV.pdf/20231009 H2023001200461 SHR-1701-III-301_FU4_INV.pdf/20231009 H2023001204463 HR-BLTN-III-MBC-C_INI_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1114', '2023-10-10 09:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (191, '', 'H2023001202926 SHR-TEST-310_SAE07_FU4(降级).pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'HR7777', '2023-10-10 09:54:20', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (192, '', '20231010 H2023001204514 SHR3680-III-302_INIFU1_INV.pdf/20231010 H2023001204222 RSJ10135_FU1_INV.pdf/20231010 H2023001204105 INS068-302_FU2_INV.pdf/', '<EMAIL>', 'err01@hrcom', 'system', 'N', '1114', '2023-10-11 09:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (193, '', '20231010 H2023001204514 SHR3680-III-302_INIFU1_INV.pdf/20231010 H2023001204222 RSJ10135_FU1_INV.pdf/20231010 H2023001204105 INS068-302_FU2_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1114', '2023-10-11 09:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (194, '', '20231011 H2023001204057 SHR-A2102-I-101_FU2_INV（降级）.pdf/20231011 H2023219002711 HS-10241-102_FU7_INV.pdf/', '<EMAIL>', 'err01@hrcom', 'system', 'N', '1114', '2023-10-11 17:00:02', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (195, '', '20231011 H2023001204057 SHR-A2102-I-101_FU2_INV（降级）.pdf/20231011 H2023219002711 HS-10241-102_FU7_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1114', '2023-10-11 17:00:03', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (196, '', '20231012 H2023001204339 SHR-A2009-I-101_FU1_INV.pdf/20231012 H2023001204410 SHR-8068-II-201-HCC_FU1_INV.pdf/20231012 H2023001204514 SHR3680-III-302_FU2_INV.pdf/20231012 H2023-00000088 HS-10296-306_FU5_INV.pdf/', '<EMAIL>', 'err01@hrcom', 'system', 'N', '1114', '2023-10-13 09:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (197, '', '20231012 H2023001204339 SHR-A2009-I-101_FU1_INV.pdf/20231012 H2023001204410 SHR-8068-II-201-HCC_FU1_INV.pdf/20231012 H2023001204514 SHR3680-III-302_FU2_INV.pdf/20231012 H2023-00000088 HS-10296-306_FU5_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1114', '2023-10-13 09:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (198, '', '20231013 H2023001204329 SHR-A2009-I-101_FU2_INV.pdf/20231013 H2023001204459 SHR-A1912-I-101_FU1_INV.pdf/20231013 H2022001204329 SHR-1701-III-309_FU6_INV.pdf/20231013 H2022001203829 SHR-1701-III-309_FU14_INV.pdf/', '<EMAIL>', 'err01@hrcom', 'system', 'N', '1114', '2023-10-14 09:00:00', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (199, '', '20231013 H2023001204329 SHR-A2009-I-101_FU2_INV.pdf/20231013 H2023001204459 SHR-A1912-I-101_FU1_INV.pdf/20231013 H2022001204329 SHR-1701-III-309_FU6_INV.pdf/20231013 H2022001203829 SHR-1701-III-309_FU14_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1114', '2023-10-14 09:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (200, '', 'H2023001202826 SHR-TEST-303_SAE20_FU9.pdf/H2023001202826 SHR-TEST-302_SAE17_FU3.pdf/H2023001202825 SHR-TEST-301_SAE12_FU6.pdf/', '<EMAIL>', 'gzh324@qqcom,gzh324.com', '<EMAIL>', 'N', 'APTN(YN968D1)', '2023-10-17 17:10:10', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (201, '', 'H2023001202826 SHR-TEST-303_SAE20_FU9.pdf/H2023001202826 SHR-TEST-302_SAE17_FU3.pdf/H2023001202825 SHR-TEST-301_SAE12_FU6.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'APTN(YN968D1)', '2023-10-17 17:10:10', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (202, '', '20231017 H2023001202825 SHR-TEST-301_SAE12_FU6.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', '1104', '2023-10-18 07:21:19', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (203, '', '20231018 H2023001202825 SHR-TEST-301_SAE12_FU6.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1104', '2023-10-18 09:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (204, '', '20231018 H2023001202826 SHR-TEST-301_SAE12_FU6.pdf', '<EMAIL>', 'err02hr.com', '<EMAIL>', 'N', '1213', '2023-10-18 10:05:24', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (205, '', '20231018 H2023001202826 SHR-TEST-301_SAE12_FU6.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', '1213', '2023-10-18 10:05:24', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (206, '', '20231018 H2023001202826 SHR-TEST-301_SAE12_FU6.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', '1104', '2023-10-18 10:21:51', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (207, '', '20231018 H2023001202826 SHR-TEST-301_SAE12_FU6.pdf', '<EMAIL>', '<EMAIL>', 'system', 'Y', '20230719-1', '2023-10-18 17:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (208, '', '20231018 H2023001202826 SHR-TEST-301_SAE12_FU6.pdf', '<EMAIL>', 'err01@hrcom', 'system', 'N', '1114', '2023-10-18 17:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (209, '', '20231018 H2023001202826 SHR-TEST-301_SAE12_FU6.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1114', '2023-10-18 17:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (210, '', '20231218 H2023001202927 SHR-TEST-301_FU1FU2_INV.pdf', '<EMAIL>', 'gzh324@qqcom,gzh324.com', '<EMAIL>', 'N', 'HRS001', '2024-01-08 14:36:29', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (211, '', '20231218 H2023001202927 SHR-TEST-301_FU1FU2_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'HRS001', '2024-01-08 14:36:30', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (212, '', '20231218 H2023001202125 SHR-TEST-301_FU1FU2_INV.pdf', '<EMAIL>', 'gzh324@qqcom,gzh324.com', '<EMAIL>', 'N', 'HRS001', '2024-01-08 15:02:49', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (213, '', '20231218 H2023001202125 SHR-TEST-301_FU1FU2_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'HRS001', '2024-01-08 15:02:49', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (214, '', '20231218 H2023001202125 SHR-TEST-301_FU1FU2_INV.pdf/20231218 H2023001201609 SHR-TEST-302_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'HRS002', '2024-01-08 17:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (215, '', 'H2023001201118 SHR-TEST-218_SAE03_FU4_INV(降级).pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'SHR6390', '2024-01-09 17:28:48', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (216, '', '20231208 H2023001205705 MA-SCLC-I-007_INI_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1104', '2024-01-16 17:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (217, '', 'H2023001201490 SHR-XXX-III-XX_SAEXX_INIFU1_INV_007.pdf/H2023001201490 SHR-XXX-III-XX_SAEXX_INIFU1_INV_009.pdf/', '<EMAIL>', 'gzh324@qqcom,gzh324.com', 'system', 'N', 'HRS001', '2024-01-16 17:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (218, '', 'H2023001201490 SHR-XXX-III-XX_SAEXX_INIFU1_INV_007.pdf/H2023001201490 SHR-XXX-III-XX_SAEXX_INIFU1_INV_009.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'HRS001', '2024-01-16 17:00:02', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (222, '', 'H2023001201118 SHR-TEST-216_SAE03_FU11_TEST8.pdf/H2023001201118 SHR-TEST-216_SAE03_FU11_TEST5.pdf/H2023001201118 SHR-TEST-216_SAE03_FU11_TEST3.pdf/H2023001201118 SHR-TEST-216_SAE03_FU11_TEST2.pdf/H2023001201118 SHR-TEST-216_SAE03_FU11_TEST4.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'TEST-1', '2024-02-21 09:11:55', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (223, '', 'H2023001201118 SHR-TEST-216_SAE03_FU11_TEST9.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'TEST-1', '2024-02-21 10:00:02', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (224, '', 'H2023001201118 SHR-TEST-216_SAE03_FU11_TEST13.pdf/H2023001201118 SHR-TEST-216_SAE03_FU11_TEST12.pdf/H2023001201118 SHR-TEST-216_SAE03_FU11_TEST11.pdf/H2023001201118 SHR-TEST-216_SAE03_FU11_TEST10.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'TEST-1', '2024-02-21 17:00:05', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (225, '', '20240313 H202409981 SHR-TEST-101.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', '1104', '2024-03-13 17:37:07', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (226, '', '20240319 H202409981 SHR-TEST-102.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'N', '1104', '2024-03-19 11:09:54', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (227, '', '20240319 H202409981 SHR-TEST-102.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'N', '1104', '2024-03-19 11:10:05', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (228, '', '20240319 H202409981 SHR-TEST-102.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'N', '1104', '2024-03-19 11:20:32', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (229, '', '20240319 H202409981 SHR-TEST-102.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'N', '1104', '2024-03-19 11:26:17', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (230, '', '20240319 H202409981 SHR-TEST-102.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'N', '1104', '2024-03-19 11:33:31', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (231, '', '20240319 H202409981 SHR-TEST-102.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'N', '1104', '2024-03-19 12:35:42', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (232, '', '20240319 H202409981 SHR-TEST-102.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', '1104', '2024-03-19 13:20:18', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (233, '', '20231218 H2023001202125 SHR-TEST-301_FU1FU2_INV.pdf/20231218 H2023001201609 SHR-TEST-302_INV.pdf/', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', 'EDCTEST', '2024-03-21 17:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (234, '', '20240319 H202409981 SHR-TEST-103.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', '1104', '2024-04-02 14:56:46', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (235, '', '20240319 H202409981 SHR-TEST-105.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', 'system', 'Y', '1104', '2024-04-02 15:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (236, '', 'H jimiTest001.pdf', '<EMAIL>', '<EMAIL>', '<EMAIL>', 'Y', 'TEST-4', '2024-04-02 15:05:56', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (237, '', 'H jimiTest002.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>', 'system', 'Y', 'EDC-20220302', '2024-04-03 10:00:01', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (238, '', '20230922 H2023001204222 RSJ10135_INI_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', '1104', '2024-04-03 14:40:43', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (239, '', '20240402 H2024001201388 SHR-A1904-I-101_INI_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', '1104', '2024-04-03 14:54:30', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (240, '', '20240105 H2023001205797 SHR6390-III-303_FU1FU2_INV.pdf', '<EMAIL>', 'gzh324@qqcom,gzh324.com', '<EMAIL>', 'N', 'APTN(YN968D1)', '2024-04-03 14:54:40', 'N');
INSERT INTO `mail_send_record`(`id`, `mail_content`, `file_name`, `sender`, `receiver`, `operate_user`, `status`, `compound_folder`, `send_time`, `is_deleted`) VALUES (241, '', '20240105 H2023001205797 SHR6390-III-303_FU1FU2_INV.pdf', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '<EMAIL>', 'Y', 'APTN(YN968D1)', '2024-04-03 14:54:40', 'N');
