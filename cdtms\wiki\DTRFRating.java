package net.bioknow.cdtms.wiki;

import com.mchange.v1.db.sql.ConnectionUtils;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTRecordFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class DTRFRating extends DTRecordFuncAction {



	public boolean canUse(int auth, String tableid, Long recordid) {

		try {

			if (!StringUtils.equals(tableid,"xxyjl1")) {
				return false;
			}
			Map userMap = SessUtil.getSessInfo().getUser();
			String projectid = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng = new DAODataMng(projectid);
			List<Map> learnerList = daoDataMng.listRecord("knowledge_base_learner", "obj.account_id=" + userMap.get("id") + " and obj.knowledge_base_id=" + recordid, null, 1);


			if (CollectionUtils.isEmpty(learnerList)) {
				HashMap<String, Object> learnerMapToSave = new HashMap<>();
				learnerMapToSave.put("learner",userMap.get("username"));
				learnerMapToSave.put("knowledge_base_id",recordid);
				learnerMapToSave.put("username",userMap.get("loginid"));
				learnerMapToSave.put("join_date",new Date());
				learnerMapToSave.put("account_id",Long.valueOf((String) userMap.get("id")));
				daoDataMng.saveRecord("knowledge_base_learner",learnerMapToSave);
				return true;

			}


			Map learnerMap = learnerList.get(0);
			if (StringUtils.equals((String) learnerMap.get("status"),"00")) {
				return true;

			}


		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}

	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBean fpb) {
		try {

			this.forwardByUri(request,response,"/wiki.ratingButton.do");

		} catch (Exception e) {
			Log.error("",e);
		}
	}

	public FuncInfoBean getFIB(String tableid) {
		FuncInfoBean fib = new FuncInfoBean();
		fib.setName("确认已完成培训");
		fib.setType(FuncInfoBean.FUNCTYPE_AJAXMENU);


		return fib;
	}

}
