package net.bioknow.cdtms.extdatabind;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import net.bioknow.mvc.tools.Language;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.face.ListPageInitFace;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ListPageInitHandlerBindDataLink implements ListPageInitFace {
	private Map lernProg = null;
	@Override
	public boolean canUse(int auth, String tableid, String projectId) {
        try {



			if (StringUtils.equals(tableid,"ext_data_bind")) {
				return true;
			}
        }
        catch (Exception e){
            Log.error("",e);
        }
        return false;
    }
	@Override
	public String getColName() {
		Language lang = Language.getInstance(this.getClass());
		return lang.get("设盲Summary");
	}
	@Override
    public String getJsInc() {

		return "";
    }

	@Override
	public Map getValueMap() {
		return this.lernProg;
	}
	@Override
	public void init(List listV,String tableid,String projectId) {
		try {

			DAODataMng daoDataMng = new DAODataMng(projectId);
			this.lernProg=new HashMap<>();
			for (Object dataObj : listV) {
				Map dataMap = (Map) dataObj;

				Long Id = (Long) dataMap.get("id");
				Map extDataBindMap = daoDataMng.getRecord("ext_data_bind", Id);
				String file_id = (String) extDataBindMap.get("file_id");

				String previewUrlJson = Actionextdatabind.httpGet("https://externalBlind.hengrui.com/external_data/getCompareFileUrl?fileId=" + file_id);


				Gson gson = new Gson();
				Map<String, Object> previewUrlMap = gson.fromJson(previewUrlJson, new TypeToken<Map<String, Object>>() {
				}.getType());


				String preview_url = (String) previewUrlMap.get("data");
				
				
				String Html = "<a target=\"_blank\" href=\""+preview_url+"\">查看</a>";
				this.lernProg.put("rid" + Id, Html);
			}
		} catch (Exception e) {
			Log.error("",e);
		}
	}

}
