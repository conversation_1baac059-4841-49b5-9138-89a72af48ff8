proc datasets library=work kill; quit;

option mprint symbolgen minoperator validvarname=v7;
%global system root version lib lib_lower memname_csn_list;


%let dir=%sysfunc(getoption(work));
x "cd &dir";
x "mkdir ./pgm";
x 'mc find minios3/ --name "*.sas" --exec "mc cp {} ./pgm/"';
options mautolocdisplay mautosource sasautos = ("&dir/pgm/");
%include "&dir/pgm/m_unlock_check.sas";
%include "&dir/pgm/m_post2s3.sas";
%include "&dir/pgm/m_gets3data.sas";
%include "&dir/pgm/m_exportxlsx_dmreview.sas";
%put &dir.;



%m_unlock_check(json_path = &jsonPath.);