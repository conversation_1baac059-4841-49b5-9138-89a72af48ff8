/*
 Navicat MySQL Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80032 (8.0.32)
 Source Host           : localhost:3306
 Source Schema         : susar_auto_mail

 Target Server Type    : MySQL
 Target Server Version : 80032 (8.0.32)
 File Encoding         : 65001

 Date: 01/03/2023 17:44:54
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for mail_send_record
-- ----------------------------
DROP TABLE IF EXISTS `mail_send_record`;
CREATE TABLE `mail_send_record`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `mail_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `receiver` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N',
  `file_id` int NOT NULL,
  `send_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_deleted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 148 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mail_send_record
-- ----------------------------
INSERT INTO `mail_send_record` VALUES (58, 'Hello， This is Test Email', '<EMAIL>', 'Y', 93, '2023-02-24 15:15:57', 'Y');
INSERT INTO `mail_send_record` VALUES (59, 'Hello， This is Test Email', '<EMAIL>', 'Y', 93, '2023-02-24 15:15:58', 'Y');
INSERT INTO `mail_send_record` VALUES (60, 'Hello， This is Test Email', '<EMAIL>', 'Y', 93, '2023-02-24 15:36:12', 'Y');
INSERT INTO `mail_send_record` VALUES (61, 'Hello， This is Test Email', '<EMAIL>', 'Y', 96, '2023-02-24 15:36:13', 'Y');
INSERT INTO `mail_send_record` VALUES (62, 'Hello， This is Test Email', '<EMAIL>', 'Y', 98, '2023-02-24 16:29:10', 'Y');
INSERT INTO `mail_send_record` VALUES (63, 'Hello， This is Test Email', '<EMAIL>', 'Y', 99, '2023-02-24 17:30:10', 'Y');
INSERT INTO `mail_send_record` VALUES (64, 'Hello， This is Test Email', '<EMAIL>', 'Y', 99, '2023-02-27 11:15:01', 'Y');
INSERT INTO `mail_send_record` VALUES (65, 'Hello， This is Test Email', '<EMAIL>', 'Y', 99, '2023-02-27 11:30:00', 'Y');
INSERT INTO `mail_send_record` VALUES (66, 'Hello， This is Test Email', '<EMAIL>', 'Y', 99, '2023-02-27 13:15:00', 'Y');
INSERT INTO `mail_send_record` VALUES (67, 'Hello， This is Test Email', '<EMAIL>', 'Y', 113, '2023-02-27 17:26:55', 'N');
INSERT INTO `mail_send_record` VALUES (68, 'Hello， This is Test Email', '<EMAIL>', 'Y', 114, '2023-02-27 17:45:00', 'N');
INSERT INTO `mail_send_record` VALUES (69, 'Hello， This is Test Email', '<EMAIL>', 'Y', 116, '2023-02-28 08:58:44', 'N');
INSERT INTO `mail_send_record` VALUES (70, 'Hello， This is Test Email', '<EMAIL>', 'Y', 127, '2023-02-28 09:59:13', 'N');
INSERT INTO `mail_send_record` VALUES (71, 'Hello， This is Test Email', '<EMAIL>', 'Y', 128, '2023-02-28 09:59:14', 'N');
INSERT INTO `mail_send_record` VALUES (72, 'Hello， This is Test Email', '<EMAIL>', 'Y', 129, '2023-02-28 10:19:15', 'N');
INSERT INTO `mail_send_record` VALUES (73, 'Hello， This is Test Email', '<EMAIL>', 'Y', 130, '2023-02-28 10:32:26', 'N');
INSERT INTO `mail_send_record` VALUES (74, 'Hello， This is Test Email', '<EMAIL>', 'Y', 132, '2023-02-28 13:02:47', 'N');
INSERT INTO `mail_send_record` VALUES (75, 'Hello， This is Test Email', '<EMAIL>', 'Y', 132, '2023-02-28 13:02:49', 'N');
INSERT INTO `mail_send_record` VALUES (76, 'Hello， This is Test Email', '<EMAIL>', 'Y', 134, '2023-02-28 13:02:50', 'N');
INSERT INTO `mail_send_record` VALUES (77, 'Hello， This is Test Email', '<EMAIL>', 'Y', 134, '2023-02-28 13:02:50', 'N');
INSERT INTO `mail_send_record` VALUES (78, 'Hello， This is Test Email', '<EMAIL>', 'Y', 137, '2023-02-28 13:11:00', 'N');
INSERT INTO `mail_send_record` VALUES (79, 'Hello， This is Test Email', '<EMAIL>', 'Y', 138, '2023-02-28 13:11:02', 'N');
INSERT INTO `mail_send_record` VALUES (80, 'Hello， This is Test Email', '<EMAIL>', 'Y', 139, '2023-02-28 13:11:03', 'N');
INSERT INTO `mail_send_record` VALUES (81, 'Hello， This is Test Email', '<EMAIL>', 'Y', 140, '2023-02-28 13:11:04', 'N');
INSERT INTO `mail_send_record` VALUES (82, 'Hello， This is Test Email', '<EMAIL>', 'Y', 141, '2023-02-28 13:11:55', 'N');
INSERT INTO `mail_send_record` VALUES (83, 'Hello， This is Test Email', '<EMAIL>', 'Y', 142, '2023-02-28 13:16:30', 'N');
INSERT INTO `mail_send_record` VALUES (84, 'Hello， This is Test Email', '<EMAIL>', 'Y', 144, '2023-02-28 13:30:00', 'N');
INSERT INTO `mail_send_record` VALUES (85, 'Hello， This is Test Email', '<EMAIL>', 'Y', 145, '2023-02-28 13:30:01', 'N');
INSERT INTO `mail_send_record` VALUES (86, 'Hello， This is Test Email', '<EMAIL>', 'Y', 146, '2023-02-28 13:30:10', 'N');
INSERT INTO `mail_send_record` VALUES (87, 'Hello， This is Test Email', '<EMAIL>', 'Y', 147, '2023-02-28 13:56:53', 'N');
INSERT INTO `mail_send_record` VALUES (88, 'Hello， This is Test Email', '<EMAIL>', 'Y', 148, '2023-02-28 13:56:58', 'N');
INSERT INTO `mail_send_record` VALUES (89, 'Hello， This is Test Email', '<EMAIL>', 'Y', 149, '2023-02-28 13:57:19', 'N');
INSERT INTO `mail_send_record` VALUES (90, 'Hello， This is Test Email', '<EMAIL>', 'Y', 150, '2023-02-28 13:57:59', 'N');
INSERT INTO `mail_send_record` VALUES (91, 'Hello， This is Test Email', '<EMAIL>', 'Y', 151, '2023-02-28 13:58:07', 'N');
INSERT INTO `mail_send_record` VALUES (92, 'Hello， This is Test Email', '<EMAIL>', 'Y', 152, '2023-02-28 13:58:10', 'N');
INSERT INTO `mail_send_record` VALUES (93, 'Hello， This is Test Email', '<EMAIL>', 'Y', 153, '2023-02-28 14:20:58', 'N');
INSERT INTO `mail_send_record` VALUES (94, 'Hello， This is Test Email', '<EMAIL>', 'Y', 154, '2023-02-28 14:21:12', 'N');
INSERT INTO `mail_send_record` VALUES (95, 'Hello， This is Test Email', '<EMAIL>', 'Y', 155, '2023-02-28 14:21:12', 'N');
INSERT INTO `mail_send_record` VALUES (96, 'Hello， This is Test Email', '<EMAIL>', 'Y', 156, '2023-02-28 15:15:00', 'N');
INSERT INTO `mail_send_record` VALUES (97, 'Hello， This is Test Email', '<EMAIL>', 'Y', 157, '2023-02-28 15:30:00', 'N');
INSERT INTO `mail_send_record` VALUES (98, 'Hello， This is Test Email', '<EMAIL>', 'Y', 158, '2023-02-28 15:30:01', 'N');
INSERT INTO `mail_send_record` VALUES (99, 'Hello， This is Test Email', '<EMAIL>', 'Y', 159, '2023-02-28 15:30:02', 'N');
INSERT INTO `mail_send_record` VALUES (100, 'Hello， This is Test Email', '<EMAIL>', 'Y', 160, '2023-03-01 10:36:32', 'N');
INSERT INTO `mail_send_record` VALUES (101, 'Hello， This is Test Email', '<EMAIL>', 'Y', 161, '2023-03-01 10:36:35', 'N');
INSERT INTO `mail_send_record` VALUES (102, 'Hello， This is Test Email', '<EMAIL>', 'Y', 162, '2023-03-01 10:36:35', 'N');
INSERT INTO `mail_send_record` VALUES (103, 'Hello， This is Test Email', '<EMAIL>', 'Y', 163, '2023-03-01 10:36:36', 'N');
INSERT INTO `mail_send_record` VALUES (104, 'Hello， This is Test Email', '<EMAIL>', 'Y', 164, '2023-03-01 10:36:36', 'N');
INSERT INTO `mail_send_record` VALUES (105, 'Hello， This is Test Email', '<EMAIL>', 'Y', 165, '2023-03-01 10:36:37', 'N');
INSERT INTO `mail_send_record` VALUES (106, 'Hello， This is Test Email', '<EMAIL>', 'Y', 166, '2023-03-01 11:02:17', 'N');
INSERT INTO `mail_send_record` VALUES (107, 'Hello， This is Test Email', '<EMAIL>', 'Y', 167, '2023-03-01 11:02:19', 'N');
INSERT INTO `mail_send_record` VALUES (108, 'Hello， This is Test Email', '<EMAIL>', 'Y', 168, '2023-03-01 11:02:20', 'N');
INSERT INTO `mail_send_record` VALUES (109, 'Hello， This is Test Email', '<EMAIL>', 'Y', 169, '2023-03-01 11:02:21', 'N');
INSERT INTO `mail_send_record` VALUES (110, 'Hello， This is Test Email', '<EMAIL>', 'Y', 170, '2023-03-01 11:02:21', 'N');
INSERT INTO `mail_send_record` VALUES (111, 'Hello， This is Test Email', '<EMAIL>', 'Y', 171, '2023-03-01 11:02:22', 'N');
INSERT INTO `mail_send_record` VALUES (112, 'Hello， This is Test Email', '<EMAIL>', 'Y', 172, '2023-03-01 11:05:12', 'N');
INSERT INTO `mail_send_record` VALUES (113, 'Hello， This is Test Email', '<EMAIL>', 'Y', 173, '2023-03-01 11:05:13', 'N');
INSERT INTO `mail_send_record` VALUES (114, 'Hello， This is Test Email', '<EMAIL>', 'Y', 174, '2023-03-01 11:05:14', 'N');
INSERT INTO `mail_send_record` VALUES (115, 'Hello， This is Test Email', '<EMAIL>', 'Y', 175, '2023-03-01 11:05:15', 'N');
INSERT INTO `mail_send_record` VALUES (116, 'Hello， This is Test Email', '<EMAIL>', 'Y', 176, '2023-03-01 11:05:15', 'N');
INSERT INTO `mail_send_record` VALUES (117, 'Hello， This is Test Email', '<EMAIL>', 'Y', 177, '2023-03-01 11:05:16', 'N');
INSERT INTO `mail_send_record` VALUES (118, 'Hello， This is Test Email', '<EMAIL>', 'Y', 178, '2023-03-01 11:37:19', 'N');
INSERT INTO `mail_send_record` VALUES (119, 'Hello， This is Test Email', '<EMAIL>', 'Y', 182, '2023-03-01 13:11:08', 'N');
INSERT INTO `mail_send_record` VALUES (120, 'Hello， This is Test Email', '<EMAIL>', 'Y', 183, '2023-03-01 13:28:40', 'N');
INSERT INTO `mail_send_record` VALUES (121, 'Hello， This is Test Email', '<EMAIL>', 'Y', 184, '2023-03-01 13:28:45', 'N');
INSERT INTO `mail_send_record` VALUES (122, 'Hello， This is Test Email', '<EMAIL>', 'Y', 185, '2023-03-01 13:28:46', 'N');
INSERT INTO `mail_send_record` VALUES (123, 'Hello， This is Test Email', '<EMAIL>', 'Y', 186, '2023-03-01 13:28:47', 'N');
INSERT INTO `mail_send_record` VALUES (124, 'Hello， This is Test Email', '<EMAIL>', 'Y', 187, '2023-03-01 13:28:47', 'N');
INSERT INTO `mail_send_record` VALUES (125, 'Hello， This is Test Email', '<EMAIL>', 'Y', 188, '2023-03-01 13:28:48', 'N');
INSERT INTO `mail_send_record` VALUES (126, 'Hello， This is Test Email', '<EMAIL>', 'Y', 189, '2023-03-01 14:16:02', 'N');
INSERT INTO `mail_send_record` VALUES (127, 'Hello， This is Test Email', '<EMAIL>', 'Y', 189, '2023-03-01 14:16:28', 'N');
INSERT INTO `mail_send_record` VALUES (128, 'Hello， This is Test Email', '<EMAIL>', 'Y', 189, '2023-03-01 14:17:40', 'N');
INSERT INTO `mail_send_record` VALUES (129, 'Hello， This is Test Email', '<EMAIL>', 'Y', 189, '2023-03-01 14:18:41', 'N');
INSERT INTO `mail_send_record` VALUES (130, 'Hello， This is Test Email', '<EMAIL>', 'Y', 189, '2023-03-01 14:19:44', 'N');
INSERT INTO `mail_send_record` VALUES (131, 'Hello， This is Test Email', '<EMAIL>', 'Y', 189, '2023-03-01 14:20:38', 'N');
INSERT INTO `mail_send_record` VALUES (132, 'Hello， This is Test Email', '<EMAIL>', 'Y', 189, '2023-03-01 14:21:45', 'N');
INSERT INTO `mail_send_record` VALUES (133, 'Hello， This is Test Email', '<EMAIL>', 'Y', 189, '2023-03-01 14:26:29', 'N');
INSERT INTO `mail_send_record` VALUES (134, 'Hello， This is Test Email', '<EMAIL>', 'Y', 190, '2023-03-01 14:26:30', 'N');
INSERT INTO `mail_send_record` VALUES (135, 'Hello， This is Test Email', '<EMAIL>', 'Y', 191, '2023-03-01 14:26:31', 'N');
INSERT INTO `mail_send_record` VALUES (136, 'Hello， This is Test Email', '<EMAIL>', 'Y', 192, '2023-03-01 14:26:32', 'N');
INSERT INTO `mail_send_record` VALUES (137, 'Hello， This is Test Email', '<EMAIL>', 'Y', 193, '2023-03-01 14:26:32', 'N');
INSERT INTO `mail_send_record` VALUES (138, 'Hello， This is Test Email', '<EMAIL>', 'Y', 194, '2023-03-01 14:26:33', 'N');
INSERT INTO `mail_send_record` VALUES (139, 'Hello， This is Test Email', '<EMAIL>', 'Y', 195, '2023-03-01 14:31:32', 'N');
INSERT INTO `mail_send_record` VALUES (140, 'Hello， This is Test Email', '<EMAIL>', 'Y', 196, '2023-03-01 14:31:39', 'N');
INSERT INTO `mail_send_record` VALUES (141, 'Hello， This is Test Email', '<EMAIL>', 'Y', 197, '2023-03-01 14:31:40', 'N');
INSERT INTO `mail_send_record` VALUES (142, 'Hello， This is Test Email', '<EMAIL>', 'Y', 198, '2023-03-01 14:31:40', 'N');
INSERT INTO `mail_send_record` VALUES (143, 'Hello， This is Test Email', '<EMAIL>', 'Y', 199, '2023-03-01 14:31:41', 'N');
INSERT INTO `mail_send_record` VALUES (144, 'Hello， This is Test Email', '<EMAIL>', 'Y', 200, '2023-03-01 14:31:41', 'N');
INSERT INTO `mail_send_record` VALUES (145, 'Hello， This is Test Email', '<EMAIL>', 'Y', 201, '2023-03-01 15:30:00', 'N');
INSERT INTO `mail_send_record` VALUES (146, 'Hello， This is Test Email', '<EMAIL>', 'Y', 202, '2023-03-01 15:30:01', 'N');
INSERT INTO `mail_send_record` VALUES (147, 'Hello， This is Test Email', '<EMAIL>', 'Y', 203, '2023-03-01 15:30:02', 'N');

SET FOREIGN_KEY_CHECKS = 1;
