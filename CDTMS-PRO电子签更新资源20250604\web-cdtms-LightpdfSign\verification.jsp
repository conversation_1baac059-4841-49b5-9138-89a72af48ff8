﻿<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=utf-8" %>
<%@ page language="java" pageEncoding="utf-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<fmt:setBundle  basename="net.bioknow.cdtms.lightpdfSign.i18n.${system_language}"/>


<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><fmt:message key="sign.title" /></title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .container {
            width: 500px;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .input-container {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .label {
            width: 100px;
            flex-shrink: 0;
            margin-right: 10px;
            text-align: right;
        }
        .verification-code-container {
            display: flex;
            align-items: center;
        }
        .verification-code-input {
            width: 40px;
            height: 40px;
            text-align: center;
            font-size: 18px;
            margin: 0 5px;
            border: none;
            outline: none;
            border-bottom: 1px solid #e6e6e6;
        }
        .input-field {
            flex-grow: 1;
            padding: 8px;
            font-size: 16px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        #signatureReason {
            width: 100%;
            padding: 8px;
            font-size: 16px;
        }
        .required {
            color: red;
        }
        button[type="submit"] {
            display: block;
            width: 100%;
            padding: 10px;
            margin: 0 auto;
            font-size: 18px;
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        @media screen and (max-width: 480px) {
            .input-container {
                flex-direction: column;
                align-items: flex-start;
            }
            .label {
                width: auto;
                margin-bottom: 5px;
            }
            .verification-code-container {
                flex-direction: row;
                align-items: center;
            }
            .input-field {
                width: 95%; /* Adjust this percentage as needed */
            }



        }

        .clearfix::after{display:block;clear:both;content:"";}

        .fr{float:right;}
        .cursor-pointer{cursor:pointer!important;}
        .mt-15{margin-top:15px!important;}
        .pl-8{padding-left:8px!important;}
        .text-green{color:#007f32!important;}
    </style>

    <style>
        /* Add your styles here */
        #overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        #iframeContainer {
            position: relative;
            width: 80%;
            max-width: 600px; /* Adjust the maximum width as needed */
            height: 80%;
            background-color: #fff; /* Adjust the background color as needed */
            border-radius: 8px;
            overflow: hidden;
        }

        #closeButton {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
        }


        /*! CSS Used from: https://dev.bioknow.net:2299/public/bootstrap/css/bootstrap.min.css */
        /**,::after,::before{box-sizing:border-box;}*/
        /*ul{margin-top:0;margin-bottom:1rem;}*/
        button{border-radius:0;}
        button:focus{outline:1px dotted;outline:5px auto -webkit-focus-ring-color;}
        button{margin:0;font-family:inherit;font-size:inherit;line-height:inherit;}
        button{overflow:visible;}
        button{text-transform:none;}
        button,html [type=button]{-webkit-appearance:button;}
        .btn{display:inline-block;font-weight:400;text-align:center;white-space:nowrap;vertical-align:middle;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;border:1px solid transparent;padding:.375rem .75rem;font-size:1rem;line-height:1.5;border-radius:.25rem;transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;}
        .btn:focus,.btn:hover{text-decoration:none;}
        .btn:focus{outline:0;box-shadow:0 0 0 .2rem rgba(0,123,255,.25);}
        .btn:disabled{opacity:.65;}
        .btn:not(:disabled):not(.disabled){cursor:pointer;}
        .btn:not(:disabled):not(.disabled):active{background-image:none;}

        .text-center{text-align:center!important;}

        /*! CSS Used from: https://dev.bioknow.net:2299/public/css/public.css?v=1693208763838 */
        button:focus{border-color:#ced4da!important;outline:0!important;box-shadow:none!important;}
        div:focus{outline:none;}
        ul,li{list-style:none;margin:0;padding:0;}
        .btn{font-size:12px!important;outline:none!important;box-shadow:none!important;}
        .bio-dropdown{position:relative;}
        .bio-dropdown-toggle::after{display:inline-block;width:0;height:0;margin-left:.255em;vertical-align:.255em;content:"";border-top:.3em solid;border-right:.3em solid transparent;border-bottom:0;border-left:.3em solid transparent;}
        /*! CSS Used from: https://dev.bioknow.net:2299/css/login.css?v=1693208763838 */
        .bio-dropdown-toggle{background-color:#fff;border:1px solid #ddd;color:#666;border-radius:4px;padding:3px 16px;}
        .bio-dropdown-toggle span{margin-left:8px;margin-right:20px;}
        /*! CSS Used from: https://dev.bioknow.net:2299/public/bootstrap/css/bootstrap.min.css */
        /**,::after,::before{box-sizing:border-box;}*/

        /*! CSS Used from: https://dev.bioknow.net:2299/public/css/public.css?v=1693208763838 */
        /**,*:before,*:after{margin:0;padding:0;}*/
        li{list-style:none;margin:0;padding:0;}
        /*! CSS Used from: https://dev.bioknow.net:2299/css/login.css?v=1693208763838 */





        /*! CSS Used from: https://dev.bioknow.net:2299/public/bootstrap/css/bootstrap.min.css */
        ul{margin-top:0;margin-bottom:1rem;}
        img{vertical-align:middle;border-style:none;}
        .dropdown-item{display:block;width:100%;clear:both;font-weight:400;color:#212529;text-align:inherit;white-space:nowrap;background-color:transparent;border:0;}
        .dropdown-item:focus,.dropdown-item:hover{color:#16181b;text-decoration:none;background-color:#f8f9fa;}
        .dropdown-item:active{color:#fff;text-decoration:none;background-color:#007bff;}
        .dropdown-item:disabled{color:#6c757d;background-color:transparent;}
        @media print{
            *,::after,::before{text-shadow:none!important;box-shadow:none!important;}
            img{page-break-inside:avoid;}
        }
        /*! CSS Used from: https://dev.bioknow.net:2299/public/css/public.css?v=1693208763838 */
        ul,li{list-style:none;margin:0;padding:0;}
        .bio-dropdown-menu{position:absolute;top:100%;left:0;z-index:1000;display:none;float:left;font-size:1rem;text-align:left;list-style:none;background-color:#fff;background-clip:padding-box;border:1px solid #EBEEF5;border-radius:.25rem;}
        .bio-dropdown:hover .bio-dropdown-menu{display:block;}
        /*! CSS Used from: https://dev.bioknow.net:2299/css/login.css?v=1693208763838 */
        .bio-dropdown:hover .bio-dropdown-menu{display:block;}
        .bio-dropdown-menu{padding:0;border-color:#ddd;width:100%;margin:0;}
        .bio-dropdown-menu > li:hover{background-color:#e3e3e3;cursor:pointer;}
        .login-header-main .bio-dropdown-menu > li > span{margin-left:8px;}


    </style>

</head>
<body>
<div class="container">

    <div style="justify-content: space-between;display: flex;align-items: start;">

        <h2 style="text-align: left; margin-top: 5px;"><fmt:message key="LoginSign" /><span style="color:red">(<fmt:message key="register.titleFirst2" />)</span></h2>
    </div>
    <h4 style="text-align: left; margin-top: 5px;"><fmt:message key="initiator" />:${currUserInfoMap.initiator}   <fmt:message key="InitiatDate" />:${currUserInfoMap.InitiatDate}</h4>


    <c:if test="${not empty currUserInfoMap.msg and empty currUserInfoMap.redirectUrl}">
    <h4 style="text-align: left; margin-top: 5px;">${currUserInfoMap.msg}</h4>
</div>
</c:if>

<c:if test="${not empty currUserInfoMap.redirectUrl}">
    <h4 style="text-align: left; margin-top: 5px;">    <button type="submit" onclick="login()">Login</button>
    </h4>
    </div>
</c:if>

<c:if test="${empty currUserInfoMap.msg}">

    <form id="verificationForm">

        <div class="input-container">
            <label class="label" for="fileName"><fmt:message key="FileName" /></label>
            <span>${currUserInfoMap.title}</span>
                <%--      <input class="input-field" type="email" id="email" name="email" required  value="${currUserInfoMap.email}">--%>
        </div>

        <div class="input-container">
            <label class="label" for="name"><fmt:message key="Name" /></label>
            <span>${currUserInfoMap.name}</span>
                <%--      <input class="input-field" type="email" id="email" name="email" required  value="${currUserInfoMap.email}">--%>
        </div>

        <div class="input-container">
            <label class="label" for="email"><fmt:message key="Email" /></label>
            <span>${currUserInfoMap.email}</span>
                <%--      <input class="input-field" type="email" id="email" name="email" required  value="${currUserInfoMap.email}">--%>
        </div>
        <div class="input-container">
            <label class="label" for="verificationCode"><fmt:message key="Password" /></label>
            <div class="verification-code-container" id="verificationCodeContainer">
                <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
                <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
                <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
                <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
                <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
                <input class="verification-code-input" type="password" maxlength="1" required oninvalid="setCustomValidity('Please enter a value')" oninput="setCustomValidity('')">
            </div>
        </div>
        <p id="errorMessage" style="color: red;"></p>
        <p id="successMessage" style="color: green;"></p>
        <button type="submit" id="verifyButton"><fmt:message key="submit" /></button>

        <div class="login-forget-psw text-green clearfix">

            <div onclick="showForgetPsw()" class="fr cursor-pointer mt-15 pl-8">
                <fmt:message key="sign.forgot_password" />
            </div>

        </div>
        <a href="https://cdtms.hengrui.com/webutil/js/pdfjs/web/viewer.show.do?file=/%E7%BA%BF%E4%B8%8A%E7%AD%BE%E5%90%8D%E5%8A%9F%E8%83%BD%E6%93%8D%E4%BD%9C%E6%8C%87%E5%8D%97_20231101.pdf&unloadstr=" target="_blank" style="font-weight: bold; font-size:18px;float: right;    color: red;  ">            <fmt:message key="sign.guide" />
        </a>
    </form>
    </div>

    <div id="overlay">
        <div id="iframeContainer">
            <div id="closeButton" onclick="closeForgetPsw()">X</div>
            <iframe id="forgetPswIframe" src="" width="100%" height="100%" frameborder="0"></iframe>
        </div>
    </div>
    <script>

        var currentWindow = window;

        const verificationCodeInputs = document.querySelectorAll(".verification-code-input");



        verificationCodeInputs.forEach(input => {
            input.addEventListener("input", () => {
                const val = ((input.value || '').match(/[0-9]/) || [])[0];

                if (val) {
                    input.value = val;

                    if (input.nextElementSibling) input.nextElementSibling.focus();
                } else {
                    input.value = '';
                }
            });

            input.addEventListener("keydown", event => {
                if (event.key === "Backspace" && !input.value && input.previousElementSibling) {
                    input.previousElementSibling.focus();
                }
            });




            input.addEventListener("paste", event => {
                event.preventDefault();
                const pasteText = event.clipboardData.getData('text');
                const numbers = pasteText.match(/[0-9]/g) || [];
                const length = numbers.length;
                for (let i = 0; i < 6; i++) {
                    const itemText = i < length ? numbers[i] : '';
                    verificationCodeInputs[i].value = itemText;
                }
                if (verificationCodeInputs[length]) verificationCodeInputs[length].focus();
            })
        });



        const errorMessage = document.getElementById("errorMessage");


        verificationForm.addEventListener("submit", async (e) => {
            e.preventDefault();
            // const signatureReason = document.getElementById("signatureReason").value;
            const verificationCodeContainer = document.getElementById("verificationCodeContainer");
            const verificationCodeInputs = verificationCodeContainer.querySelectorAll(".verification-code-input");
            let verificationCode = "";
            verificationCodeInputs.forEach(input => {
                verificationCode += input.value;
            });


            const response = await fetch("lightpdfSign.idVerify.do", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    email: "${currUserInfoMap.email}",
                    verificationCode: verificationCode,
                    // signatureReason:signatureReason,
                    id:"${currUserInfoMap.id}",
                    token:"${currUserInfoMap.token}",
                    url:"${currUserInfoMap.url}",
                    taskId:"${currUserInfoMap.taskId}",
                    system:"${system}",
                    language:"${currUserInfoMap.language}"

                })
            });

            const data = await response.json();
            if (data.status==200) {
                successMessage.textContent = "Verification success！";
                // window.location.href(data.data.redirectUrl);
                // window.location.close();
                var url = data.data.redirectUrl; // 替换为你想要打开的URL
                // var win = window.open(url, '_self');
                //closePage();


                var newWindow = window.open(url, "_blank");

                window.close();
            } else {
                errorMessage.textContent = "Verification error!";
                successMessage.textContent = "";
            }
        });

    </script>
</c:if>



<script>



    function choosech() {
        setCookieByDay('signlang', 'cn', 30);
        window.location.reload();
        return false;
    }
    function chooseen() {
        setCookieByDay('signlang', 'en', 30);
        window.location.reload();
        return false;
    }
    function setCookieByDay(name, value, days) {
        if(days==undefined || days==null || days<=0){
            setCookie(name,value);
        }else{
            var today = new Date();
            var expires_date = new Date( today.getTime() + days * 1000 * 60 * 60 * 24 );
            setCookie(name,value,expires_date);
        }
    }

    function setCookie(name, value, expires, path, domain, secure) {
        var curCookie = name + "=" + escape(value) +
            ((expires) ? "; expires=" + expires.toGMTString() : "") +
            ((path) ? "; path=" + path : "") +
            ((domain) ? "; domain=" + domain : "") +
            ((secure) ? "; secure" : "");
        document.cookie = curCookie;
    }

    function showForgetPsw() {
        var overlay = document.getElementById("overlay");
        var iframe = document.getElementById("forgetPswIframe");

// Set the iframe src to your forget password page URL
        iframe.src = "lightpdfSign.changepassword.do?email=${currUserInfoMap.email}&name=${currUserInfoMap.name}&language=${system_language}&system=${system}"; // Replace with the actual URL of your forget password page

// Display the overlay
        overlay.style.display = "flex";
    }

    function closeForgetPsw() {
        var overlay = document.getElementById("overlay");
        var iframe = document.getElementById("forgetPswIframe");

// Hide the overlay
        overlay.style.display = "none";

// Reset the iframe src
        iframe.src = "";
    }
    var myObject = {

        openURL: function (url) {
            let aLabel = document.createElement('a');
            //设置链接
            aLabel.setAttribute('href', url);
            //新窗口打开链接
            aLabel.setAttribute('target', '_blank');
            //设置标签ID
            aLabel.setAttribute('id', 'reportpoint');
            // 防止反复添加
            if (document.getElementById('reportpoint')) {
                document.body.removeChild(document.getElementById('reportpoint'));
            }
            document.body.appendChild(aLabel);
            aLabel.click();
            console.log("打开链接:",url);
            //window.open(url);
        }
    };

    <c:if test="${not empty currUserInfoMap.redirectUrl}">

    function login(){

        var newWindow =  window.open('${currUserInfoMap.redirectUrl}', '_blank');
//myObject.openURL('${currUserInfoMap.redirectUrl}');
        window.close();

//closePage();
    }
    </c:if>


    //关闭页面
    function closePage(){
        if (navigator.userAgent.indexOf('Firefox') != -1 || navigator.userAgent.indexOf('Chrome') != -1) {
            currentWindow.close();
        } else {
            currentWindow.opener = null
            currentWindow.open('', '_self')
            currentWindow.close()
        }
    }

</script>


</body>
</html>