# 📊 Excel Formula Examples for TABLE_NAME_CH Column

## 🎯 Purpose
These formulas will help you add the TABLE_NAME_CH column to Sheet2 by looking up Chinese names from Sheet1.

## 📝 Basic VLOOKUP Formula
```excel
=IFERROR(VLOOKUP(A2,Sheet1!A:B,2,FALSE),"NA")
```

**Explanation:**
- `A2` = Cell containing table name in Sheet2
- `Sheet1!A:B` = Range in Sheet1 with table names (A) and Chinese names (B)
- `2` = Return value from column 2 (Chinese names)
- `FALSE` = Exact match
- `"NA"` = Show "NA" if no match found

## 🔧 Formula Variations

### If Chinese names are in column C of Sheet1:
```excel
=IFERROR(VLOOKUP(A2,Sheet1!A:C,3,FALSE),"NA")
```

### If Chinese names are in column D of Sheet1:
```excel
=IFERROR(VLOOKUP(A2,Sheet1!A:D,4,FALSE),"NA")
```

### If table names are in column B of Sheet2:
```excel
=IFERROR(VLOOKUP(B2,Sheet1!A:B,2,FALSE),"NA")
```

### If table names are in column C of Sheet2:
```excel
=IFERROR(VLOOKUP(C2,Sheet1!A:B,2,FALSE),"NA")
```

## 🔍 Advanced Formulas

### Case-Insensitive Matching:
```excel
=IFERROR(VLOOKUP(UPPER(A2),UPPER(Sheet1!A:B),2,FALSE),"NA")
```

### Remove Extra Spaces:
```excel
=IFERROR(VLOOKUP(TRIM(A2),Sheet1!A:B,2,FALSE),"NA")
```

### Both Case-Insensitive and Trim:
```excel
=IFERROR(VLOOKUP(UPPER(TRIM(A2)),UPPER(Sheet1!A:B),2,FALSE),"NA")
```

### Using INDEX/MATCH (Alternative to VLOOKUP):
```excel
=IFERROR(INDEX(Sheet1!B:B,MATCH(A2,Sheet1!A:A,0)),"NA")
```

## 📋 Step-by-Step Application

### Step 1: Identify Your Columns
First, check your actual Excel file to identify:
- Which column in Sheet2 contains table names
- Which column in Sheet1 contains table names  
- Which column in Sheet1 contains Chinese names

### Step 2: Choose the Right Formula
Based on your column layout, pick the appropriate formula from above.

### Step 3: Common Column Scenarios

**Scenario A:** Sheet1 has table names in A, Chinese names in B; Sheet2 has table names in A
```excel
=IFERROR(VLOOKUP(A2,Sheet1!A:B,2,FALSE),"NA")
```

**Scenario B:** Sheet1 has table names in A, Chinese names in C; Sheet2 has table names in A  
```excel
=IFERROR(VLOOKUP(A2,Sheet1!A:C,3,FALSE),"NA")
```

**Scenario C:** Sheet1 has table names in B, Chinese names in C; Sheet2 has table names in A
```excel
=IFERROR(VLOOKUP(A2,Sheet1!B:C,2,FALSE),"NA")
```

### Step 4: Apply the Formula
1. Click on the first cell under your new TABLE_NAME_CH header
2. Type the formula (adjust column references as needed)
3. Press Enter
4. Copy the formula down to all rows with data

## 🎯 Expected Results

After applying the formula, you should see:
- **Matched tables:** Chinese names appear
- **Unmatched tables:** "NA" appears
- **Empty cells:** "NA" appears

## 🔧 Troubleshooting

### Common Issues and Solutions:

**#N/A Error:**
- Table name doesn't exist in Sheet1
- Check spelling and case sensitivity
- Use case-insensitive formula if needed

**#REF! Error:**
- Wrong column reference
- Sheet1 doesn't exist or was renamed
- Check your range references

**Wrong Results:**
- Column numbers might be incorrect
- Verify which column contains Chinese names
- Count columns carefully (A=1, B=2, C=3, etc.)

**Some Matches Missing:**
- Extra spaces in table names
- Use TRIM function in formula
- Check for hidden characters

## ✅ Final Check

After applying formulas:
1. Verify a few matches manually
2. Check that most table names have Chinese translations
3. Confirm "NA" appears for unmatched items
4. No error messages (#N/A, #REF!, etc.)

## 💡 Pro Tips

1. **Test First:** Try the formula on just one cell before copying to all rows
2. **Backup:** Save a copy of your original file before making changes
3. **Convert to Values:** After formulas work, copy and paste as values to remove formula dependencies
4. **Double-Check:** Manually verify a few results to ensure accuracy

Choose the formula that matches your specific column layout and apply it to get Chinese names in your TABLE_NAME_CH column!
