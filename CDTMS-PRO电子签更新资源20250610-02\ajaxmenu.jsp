<%@ page contentType="text/html;charset=utf-8"%>
<%@ page language="java" pageEncoding="utf-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@ taglib uri="/webutil/tlib/bioknow-tlib.tld" prefix="bt" %>

<%response.setHeader("Pragma","no-cache");%>

<c:choose>
	<c:when test="${fn:length(listr) eq 1}">

		<script>


			<c:forEach var="item" items="${listr}" varStatus="st">

			var url = '/formMail.getemail.do?id=${item.id}&recordid=' + g_recordid+"&receiver=${item.receiver}&isSign=${item.isSign}&ccer=${item.ccer}";
			</c:forEach>

			window.top.webmask_show_modalbyurl('<bt:lang name="发送邮件"/>',url,'80%','80vh','');
		</script>
	</c:when>
	<c:otherwise>
		<div style="width:220pt;">
			<c:forEach var="item" items="${listr}" varStatus="st">
				<div class="popupdiv_content_new">
						${st.count}.<a href="javascript:void(0);" onclick="$('#tablefuncmenudiv').hide();var url = 'formMail.getemail.do?id=${item.id}&recordid=' + g_recordid +'&anticache=${uuid}';window.top.webmask_show_modalbyurl('<bt:lang name="发送邮件"/>',url,'80%','80vh','');return false;">${item.name}</a>
				</div>
			</c:forEach>
		</div>
	</c:otherwise>
</c:choose>