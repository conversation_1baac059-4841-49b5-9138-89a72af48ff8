package net.bioknow.cdtms.schedule;

import com.cronutils.model.Cron;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.time.ExecutionTime;
import com.cronutils.parser.CronParser;
import net.bioknow.passport.datamng.PassportCacheUtil;
import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbcore.dbmng.CNT_DbMng;
import net.bioknow.uap.dbcore.schema.Schema;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.entitymng.EntityUtil;
import net.bioknow.uap.schemaplug.dtref.DtrefDAO;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


public class TaskGeneratorUtil {
    public static List<String> getExecutionTimeByNum(String cronStr, Integer num) {
        CronParser parser = new CronParser(CronDefinitionBuilder.instanceDefinitionFor(CronType.SPRING));
        Cron cron = parser.parse(cronStr);
        ExecutionTime time = ExecutionTime.forCron(cron);
        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime next = getNext(time, now);
        List<ZonedDateTime> timeList = new ArrayList<>(num);
        timeList.add(next);
        for (int i = 1; i < num; i++) {
            next = getNext(time, next);
            timeList.add(next);
        }
        DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<String> resultList = new ArrayList<>(num);
        for (ZonedDateTime item : timeList) {
            String result = item.format(format);
            resultList.add(result);
        }
        return resultList;
    }
    public static List<Date> getExecutionTimeByDateRange(String cronStr, Date startDate, Date endDate) {
        CronParser parser = new CronParser(CronDefinitionBuilder.instanceDefinitionFor(CronType.SPRING));
        Cron cron = parser.parse(cronStr);
        ExecutionTime time = ExecutionTime.forCron(cron);

        ZonedDateTime start = startDate.toInstant().atZone(ZoneId.systemDefault());
        ZonedDateTime end = endDate.toInstant().atZone(ZoneId.systemDefault());

        List<Date> resultList = new ArrayList<>();
        ZonedDateTime next = getNext(time, start);

        while (!next.isAfter(end)) {
            resultList.add(Date.from(next.toInstant()));
            next = getNext(time, next);
        }

        return resultList;
    }





    private static ZonedDateTime getNext(ExecutionTime time, ZonedDateTime current) {
        return time.nextExecution(current).get();
    }


        public static void main(String[] args) throws ParseException {
            // 定义 cron 表达式
            String cronExpression = "0 0 * * * ?"; // 示例: 每小时执行一次

            // 定义起始日期和结束日期
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date startDate = dateFormat.parse("2023-09-13 00:00:00");
            Date endDate = dateFormat.parse("2023-09-13 05:00:00");

            // 调用方法获取在日期范围内的执行时间列表
            List<Date> executionTimes = TaskGeneratorUtil.getExecutionTimeByDateRange(cronExpression, startDate, endDate);

            // 打印执行时间列表
            for (Date time : executionTimes) {
                System.out.println("Execution Time: " + time);
            }
        }


    protected static void setScheduleFinishTime(String projectId, String tableid, Map valueMap) {
        try {
            DAODbApi apidao = new DAODbApi(projectId);
            DAODataMng dmdao = new DAODataMng(projectId);
            DtrefDAO refdao = new DtrefDAO(projectId);

            String reffid = refdao.getRefField("xsht", tableid);
            Long studyid = (Long)valueMap.get(reffid);

            String whereTemp = "obj.tableid = '" + tableid + "'";
            Map mapFzq = apidao.getMapField(tableid, "zq");
            String zq = "";
            if(mapFzq != null){
                Object obj = valueMap.get("zq");
                if(obj != null){
                    zq = String.valueOf(obj);
                    whereTemp = DAODataMng.joinWhere(whereTemp, "obj.menuid is null or obj.menuid = '' or obj.menuid like '"+zq+"%'");
                }
            }

            List<EntityScheduleTemplate> listentity = EntityUtil.listEntity(projectId, whereTemp, "", EntityScheduleTemplate.class);
            if(listentity.size() == 0) return;
            EntityScheduleTemplate schedule = listentity.get(0);
            String extractid = schedule.extractid;//提取日期变量id
            String nodefieldid = schedule.nodefieldid;//分段变量id
            String finishfieldid = schedule.finishfieldid;//任务完成识别变量id
            String finishstatusvalue = schedule.finishstatusvalue;//完成变量值
            String markid = schedule.markid;//标记变量id
            String marktext = "";
            if(StringUtils.isNotEmpty(schedule.markid)){
                if(schedule.markid.contains(",")){
                    String[]  str = schedule.markid.split(",");
                    markid = schedule.markid.split(",")[0];
                    if(str.length >1){
                        marktext = schedule.markid.split(",")[1];
                    }
                }
            }
            String subtaskid = schedule.subtaskid;//子任务变量id
            String endtimefield = schedule.endtimefield;//任务完成日期变量
            String menuid = schedule.menuid;

            String userid = SessUtil.getSessInfo().getUserid();
            String unitId = PassportCacheUtil.getUnitIdByUserid(projectId, userid);

            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sf2 = new SimpleDateFormat("yyyy-MM-dd");

            List list = dmdao.listRecord("schedule", whereTemp + " and obj.study_id='" + studyid + "'", "", 1);
            if (list == null || list.size() == 0) return;
            Map map = (Map) list.get(0);

            Map mapF = apidao.getMapField(tableid, nodefieldid);
            Map mapFex = apidao.getMapField(tableid, extractid);
            Map mapFendtime = apidao.getMapField(tableid, endtimefield);

//            if (StringUtils.isNotEmpty(subtaskid)) {
//                Schema schema = apidao.getFieldType(tableid, subtaskid);
//                if(schema == null){
//                    Log.error("cdtms 甘特图同步"+tableid + ":" +subtaskid + "is null");
//                    return;
//                }
//                List listSub = dmdao.listRecord(tableid, "obj."+reffid+"='"+studyid+"'", "", 100, 1);
//                List listScheduleSave = new ArrayList();
//                Map mapCache = new HashMap();
//                for(int i=0;i<listSub.size();i++){
//                    Map mapSub = (Map)listSub.get(i);
//                    //Schema schema = apidao.getFieldType(tableid, subtaskid);
//                    Map mapF2 = apidao.getMapField(tableid, subtaskid);
//                    String value = schema.formatToUI(tableid, mapF2, mapSub);
//                    Date date = (Date)mapSub.get(extractid);
//                    if(mapCache.containsKey(value)){
//                        mapCache.put(value, mapCache.get(value) +";1,"+(date==null?"":sf.format(date)));
//                    }else{
//                        mapCache.put(value, "1,"+(date==null?"":sf.format(date)));
//                    }
//                }
//
//                Iterator ite = mapCache.keySet().iterator();
//                while((ite.hasNext())){
//                    String key = (String)ite.next();
//                    String value = (String)mapCache.get(key);
//                    Map mapSchedule = new HashMap();
//                    mapSchedule.put("study_id", map.get("study_id"));
//                    mapSchedule.put("name", key);
//                    mapSchedule.put("parent_schedule_id", map.get(CNT_DbMng.id));
//                    mapSchedule.put("type", map.get("type"));
//                    String[] str = value.split(";");
//                    Date min = null;
//                    Date max = null;
//                    for(String s : str){
//                        String[] str2 = s.split(",");
//                        if(str2.length !=2) continue;
//                        String date = str2[1];
//                        if(StringUtils.isEmpty(date)) continue;
//                        Date d = sf.parse(date);
//                        if(min ==null) {
//                            min=max =d;
//                        }else{
//                            if(d.getTime() < min.getTime()) min = d;
//                            if(d.getTime()>max.getTime()) max = d;
//                        }
//
//                    }
//
//                    //mapSchedule.put("planned_start_date", map.get("planned_start_date"));
//                    //mapSchedule.put("planned_end_date", map.get("planned_end_date"));
//                    mapSchedule.put("actual_start_date", min);
//                    mapSchedule.put("actual_end_date", max);
//                    mapSchedule.put("tableid", map.get("tableid"));
//                    mapSchedule.put("markpoint", value);
//                    listScheduleSave.add(mapSchedule);
//                }
//                String rids = "";
//                for(int i=0;i<listScheduleSave.size();i++){
//                    Map mapTem = (Map)listScheduleSave.get(i);
//                    Long rid = (Long)mapTem.get(CNT_DbMng.id);
//                    if(rids!="") rids +=",";
//                    rids += rid;
//                    String name = (String)mapTem.get("name");
//
//                }
//                String where = "obj.tableid='" + tableid + "'";
//                if(rids!="") where += " and obj.parent_schedule_id in ("+rids+")";
//                dmdao.delBatch("schedule", where);
//
//                dmdao.delBatch("schedule", "obj.tableid='" + tableid + "' and obj.parent_schedule_id='" + map.get(CNT_DbMng.id) + "'");
//                dmdao.saveBatch("schedule", listScheduleSave, Long.parseLong(userid), unitId==null?null:Long.parseLong(unitId));
//
//            }else{
//                if(StringUtils.isNotEmpty(nodefieldid) && mapF != null)//有分段变量
//                {    List listSub = dmdao.listRecord(tableid, "obj."+reffid+"='"+studyid+"'", "obj."+CNT_DbMng.createtime+" asc", 100);
//                    Map mapstCache = new HashMap();//提取日期变量
//                    Map mapetCache = new HashMap();//任务完成变量
//                    Map mapmarkCache = new HashMap();//标记变量
//
//                    Date minstart = null;
//                    Date minend = null;
//                    for(int i =0;i<listSub.size();i++){
//                        Map mapSub = (Map)listSub.get(i);
//                        Date d = (Date)mapSub.get(extractid);
//                        if(minstart == null || d.getTime() < minstart.getTime()) minstart = d;
//                        if(minend == null || d.getTime() > minend.getTime()) minend = d;
//                        String nodev = (String)mapSub.get(nodefieldid);
//                        if(mapmarkCache.containsKey(nodev)){
//                            String value = (String)mapmarkCache.get(nodev);
//                            value += ";"+mapSub.get(markid)+","+mapSub.get(extractid)+","+ mapSub.get(marktext);
//                            mapmarkCache.put(nodev, value);
//                        }else{
//                            mapmarkCache.put(nodev, mapSub.get(markid)+","+mapSub.get(extractid)+","+ mapSub.get(marktext));
//                        }
//                        if(mapstCache.containsKey(nodev)){
//                            Date date = (Date)mapstCache.get(nodev);
//                            if(date.getTime() > ((Date)mapSub.get(extractid)).getTime()){
//                                mapstCache.put(nodev, mapSub.get(extractid));
//                            }
//                        }else{
//                            mapstCache.put(nodev, mapSub.get(extractid));
//                        }
//                        if(StringUtils.isNotEmpty(finishfieldid) && StringUtils.isNotEmpty(finishstatusvalue)){
//                            String value = (String)mapSub.get(finishfieldid);
//                            if(finishstatusvalue.equals(value)){
//                                if(mapetCache.containsKey(nodev)){
//                                    Date date = (Date)mapetCache.get(nodev);
//                                    if(date.getTime() < ((Date)mapSub.get(extractid)).getTime()){
//                                        mapetCache.put(nodev, mapSub.get(extractid));
//                                    }
//                                }else{
//                                    mapetCache.put(nodev, mapSub.get(extractid));
//                                }
//                            }
//                        }
//                    }
//                    Iterator ite = mapstCache.keySet().iterator();
//                    List listtosave = new ArrayList();
//                    while(ite.hasNext()){
//                        String name = (String)ite.next();
//                        Date date = (Date)mapstCache.get(name);
//                        Map mapsub = new HashMap();
//                        mapsub.put("study_id", studyid);
//                        mapsub.put("name", name);
//                        mapsub.put("parent_schedule_id", map.get(CNT_DbMng.id));
//                        mapsub.put("actual_start_date", date);
//                        mapsub.put("actual_end_date", mapetCache.get(name));
//                        mapsub.put("tableid", map.get("tableid"));
//                        mapsub.put("markfield", map.get("markfield"));
//                        mapsub.put("nodefield", map.get("nodefield"));
//                        mapsub.put("markpoint", mapmarkCache.get(name));
//                        mapsub.put("type", map.get("type"));
//                        mapsub.put("menuid", menuid);
//                        listtosave.add(mapsub);
//                    }
//                    dmdao.delBatch("schedule", "obj.tableid='" + tableid + "' and obj.parent_schedule_id='" + map.get(CNT_DbMng.id) + "'");
//                    dmdao.saveBatch("schedule", listtosave, Long.parseLong(userid), unitId == null ? null : Long.parseLong(unitId));
//                    map.put("actual_start_date", minstart);
//                    map.put("actual_end_date", minend);
//                    dmdao.save("schedule", map);
//                }
//                else if(mapFendtime != null  && mapFex != null)//无分段变量，但是有提取时间变量及完成日期变量（也要分段显示）//提取两个日期， 分段不打点
//                {List listSub = dmdao.listRecord(tableid, "obj."+reffid+"='"+studyid+"'", "obj."+endtimefield+" asc, obj."+CNT_DbMng.createtime+" asc", 100);
//                    List listtosave = new ArrayList();
//                    listSub.forEach(x -> {
//                        Map maptemp = (Map)x;
//                        Map mapsub = new HashMap();
//                        mapsub.put("study_id", studyid);
//                        mapsub.put("name", "");
//                        mapsub.put("parent_schedule_id", map.get(CNT_DbMng.id));
//                        mapsub.put("actual_start_date", maptemp.get(extractid));
//                        mapsub.put("actual_end_date", maptemp.get(endtimefield));
//                        mapsub.put("tableid", map.get("tableid"));
//                        mapsub.put("type", map.get("type"));
//                        mapsub.put("menuid", menuid);
//                        listtosave.add(mapsub);
//                    });
//                    dmdao.delBatch("schedule", "obj.tableid='" + tableid + "' and obj.parent_schedule_id='" + map.get(CNT_DbMng.id) + "'");
//                    dmdao.saveBatch("schedule", listtosave, Long.parseLong(userid), unitId == null ? null : Long.parseLong(unitId));
//                    map.put("markfield", tableid);
//                    dmdao.save("schedule", map);
//                    if(mapFex != null && StringUtils.isNotEmpty(markid)){
//                        List listSub2 = dmdao.listRecord(tableid, "obj."+reffid+"='"+studyid+"'", "obj."+extractid+" asc, obj."+CNT_DbMng.createtime+" asc", 100);
//                        if(listSub2 != null && listSub2.size()>0){
//                            Map mapSub = (Map)listSub2.get(0);
//                            Date date = (Date)mapSub.get(extractid);
//                            map.put("actual_start_date", date);
//                            Map mapMax = (Map)listSub.get(listSub2.size() - 1);
//                            Date datemax = (Date)mapMax.get(extractid);
//                            map.put("actual_end_date", datemax);
//                            map.put("progress", 0.0D);
//                            String markpoint = "";
//                            for(Object obj : listSub2){
//                                Map m = (Map)obj;
//                                Date d = (Date)m.get(extractid);
//                                if(d == null) continue;
//                                String markv = (String)m.get(markid);
//                                if(!"".equals(markpoint)) markpoint += ";";
//                                markpoint += markv+","+new SimpleDateFormat("yyyy-MM-dd").format(d)+","+m.get(marktext);
//                            }
//                            map.put("markpoint", markpoint);
//                        }else{
//                            map.put("actual_start_date", null);
//                            map.put("actual_end_date", null);
//                            map.put("markpoint", "");
//                        }
//                        dmdao.save("schedule", map);
//                    }
//                }
//            }

            //
            String wheresub = "obj."+reffid+"='"+studyid+"'";
            if(StringUtils.isNotEmpty(zq)){
                wheresub = DAODataMng.joinWhere(wheresub, "obj.zq = '"+zq+"'");
            }
            List listSub = dmdao.listRecord(tableid, wheresub, "obj."+CNT_DbMng.createtime+" asc", 100);

            //仅设置了提取日期，多个打点显示
            if(StringUtils.isEmpty(subtaskid) && StringUtils.isEmpty(nodefieldid) && StringUtils.isEmpty(endtimefield)){
                String markpoint = "";
                for(Object obj : listSub){
                    Map m = (Map)obj;
                    Date d = (Date)m.get(extractid);
                    if(d == null) continue;
                    if(!"".equals(markpoint)) markpoint += ";";
                    markpoint += "1,"+sf2.format(d);
                }
                map.put("markpoint", markpoint);
                if(schedule.scheduletype.equals("milestone")){
                    if(listSub!=null&&listSub.size()>0){
                        Map maps = (Map)listSub.get(0);
                        Date d = (Date)maps.get(extractid);
                        if(d!=null){
                            map.put("actual_start_date", d);
                            map.put("actual_end_date", d);
                        }else{
                            map.put("actual_start_date", null);
                            map.put("actual_end_date", null);
                        }
                    }else{
                        map.put("actual_start_date", null);
                        map.put("actual_end_date", null);
                    }
                }
                dmdao.save("schedule", map);
            }
            //设置子任务
            if(StringUtils.isNotEmpty(subtaskid) && StringUtils.isNotEmpty(extractid)){
                List listScheduleSave = new ArrayList();
                Map mapCache = new HashMap();
                Map mapCacheid = new HashMap();
                for(int i=0;i<listSub.size();i++){
                    Map mapSub = (Map)listSub.get(i);
                    Schema schema = apidao.getFieldType(tableid, subtaskid);
                    Map mapF2 = apidao.getMapField(tableid, subtaskid);
                    String value = schema.formatToUI(tableid, mapF2, mapSub);
                    Date date = (Date)mapSub.get(extractid);
                    if(mapCache.containsKey(value)){
                        mapCache.put(value, mapCache.get(value) +";1,"+(date==null?"":sf2.format(date)));
                    }else{
                        mapCache.put(value, "1,"+(date==null?"":sf2.format(date)));
                    }
                    mapCacheid.put(mapSub.get(subtaskid), value);
                }

                Iterator ite = mapCache.keySet().iterator();
                while((ite.hasNext())){
                    String key = (String)ite.next();
                    String value = (String)mapCache.get(key);
                    Map mapSchedule = new HashMap();
                    mapSchedule.put("study_id", map.get("study_id"));
                    mapSchedule.put("name", key);
                    mapSchedule.put("parent_schedule_id", map.get(CNT_DbMng.id));
                    mapSchedule.put("type", "task");
                    String[] str = value.split(";");
                    Date min = null;
                    Date max = null;
                    for(String s : str){
                        String[] str2 = s.split(",");
                        if(str2.length !=2) continue;
                        String date = str2[1];
                        if(StringUtils.isEmpty(date)) continue;
                        Date d = sf2.parse(date);
                        if(min ==null) {
                            min=max =d;
                        }else{
                            if(d.getTime() < min.getTime()) min = d;
                            if(d.getTime()>max.getTime()) max = d;
                        }

                    }

                    mapSchedule.put("planned_start_date", map.get("planned_start_date"));
                    mapSchedule.put("planned_end_date", map.get("planned_end_date"));
                    //mapSchedule.put("actual_start_date", min);
                    //mapSchedule.put("actual_end_date", max);
                    mapSchedule.put("tableid", map.get("tableid"));
                    if(StringUtils.isNotEmpty(endtimefield)){
                        mapSchedule.put("render", "split");
                    }
                    mapSchedule.put("markpoint", value);
                    listScheduleSave.add(mapSchedule);
                }

                dmdao.delBatch("schedule", "obj.tableid='" + tableid + "' and obj.parent_schedule_id='" + map.get(CNT_DbMng.id) + "'");
                dmdao.saveBatch("schedule", listScheduleSave, Long.parseLong(userid), unitId==null?null:Long.parseLong(unitId));
                map.put("type", "project");
                dmdao.save("schedule", map);

                String rids = "";
                Map mapscheduleid = new HashMap();
                for(int i=0;i<listScheduleSave.size();i++){
                    Map mapTem = (Map)listScheduleSave.get(i);
                    Long rid = (Long)mapTem.get(CNT_DbMng.id);
                    if(rids!="") rids +=",";
                    rids += rid;
                    String name = (String)mapTem.get("name");
                    mapscheduleid.put(name, rid);
                }
                if(StringUtils.isNotEmpty(endtimefield) && StringUtils.isNotBlank(rids)){//2提取日期与完成日期（子任务分段）
                    dmdao.delBatch("schedule", "obj.tableid='" + tableid + "' and obj.parent_schedule_id in (" + rids + ")");
                }


                if(StringUtils.isNotEmpty(endtimefield)){
                    //2提取日期与完成日期（子任务分段）
                    Iterator ite2 = mapCacheid.keySet().iterator();
                    while(ite2.hasNext()){
                        String key = (String)ite2.next();
                        String value = (String)mapCacheid.get(key);
                        List listtemp = (List) listSub.stream().filter(x -> key.equals(((Map)x).get(subtaskid))).collect(Collectors.toList());
                        List listSubToSave = new ArrayList();
                        listtemp.forEach(x -> {
                            Map maptemp = (Map)x;
                            Map mapsub = new HashMap();
                            mapsub.put("study_id", studyid);
                            mapsub.put("name", "");
                            mapsub.put("parent_schedule_id", mapscheduleid.get(value));
                            mapsub.put("actual_start_date", maptemp.get(extractid));
                            mapsub.put("actual_end_date", maptemp.get(endtimefield));
                            mapsub.put("tableid", map.get("tableid"));
                            mapsub.put("type", "task");
                            mapsub.put("menuid", menuid);
                            listSubToSave.add(mapsub);
                        });
                        dmdao.saveBatch("schedule", listSubToSave, Long.parseLong(userid), unitId == null ? null : Long.parseLong(unitId));
                    }
                }
            }

            //设置分段变量（标记+提取日期+任务完成+任务完成编码值）
            if(StringUtils.isNotEmpty(nodefieldid)){
                Map mapstCache = new HashMap();//提取日期变量
                Map mapetCache = new HashMap();//任务完成变量
                Map mapmarkCache = new HashMap();//标记变量

                Date minstart = null;
                Date minend = null;
                for(int i =0;i<listSub.size();i++){
                    Map mapSub = (Map)listSub.get(i);
                    Date d = (Date)mapSub.get(extractid);
                    if(minstart == null || d.getTime() < minstart.getTime()) minstart = d;
                    if(minend == null || d.getTime() > minend.getTime()) minend = d;
                    String nodev = mapSub.get(nodefieldid) == null ? "" : (String)mapSub.get(nodefieldid);
                    Map mapFmark = apidao.getMapField(tableid, markid);
                    Schema schema = apidao.getFieldType(tableid, markid);;
                    Object obj = schema.formatToOutput(tableid,mapFmark, mapSub);
                    if(mapmarkCache.containsKey(nodev)){
                        String value = (String)mapmarkCache.get(nodev);
                        value += ";"+mapSub.get(markid)+","+sf2.format(d)+","+nodev+obj+ StringUtils.defaultIfEmpty((String)mapSub.get(marktext),"");
                        mapmarkCache.put(nodev, value);
                    }else{
                        mapmarkCache.put(nodev, mapSub.get(markid)+","+sf2.format(d)+","+nodev+obj+ StringUtils.defaultIfEmpty((String)mapSub.get(marktext),""));
                    }
                    if(mapstCache.containsKey(nodev)){
                        Date date = (Date)mapstCache.get(nodev);
                        if(date.getTime() > ((Date)mapSub.get(extractid)).getTime()){
                            mapstCache.put(nodev, mapSub.get(extractid));
                        }
                    }else{
                        mapstCache.put(nodev, mapSub.get(extractid));
                    }
                    if(StringUtils.isNotEmpty(finishfieldid) && StringUtils.isNotEmpty(finishstatusvalue)){
                        String value = (String)mapSub.get(finishfieldid);
                        if(finishstatusvalue.equals(value)){
                            if(mapetCache.containsKey(nodev)){
                                Date date = (Date)mapetCache.get(nodev);
                                if(date.getTime() < ((Date)mapSub.get(extractid)).getTime()){
                                    mapetCache.put(nodev, mapSub.get(extractid));
                                }
                            }else{
                                mapetCache.put(nodev, mapSub.get(extractid));
                            }
                        }
                    }
                }
                Iterator ite = mapstCache.keySet().iterator();
                List listtosave = new ArrayList();
                while(ite.hasNext()){
                    String name = (String)ite.next();
                    Date date = (Date)mapstCache.get(name);
                    Map mapsub = new HashMap();
                    mapsub.put("study_id", studyid);
                    mapsub.put("name", name);
                    mapsub.put("parent_schedule_id", map.get(CNT_DbMng.id));
                    mapsub.put("actual_start_date", date);
                    mapsub.put("actual_end_date", mapetCache.get(name));
                    mapsub.put("tableid", map.get("tableid"));
                    mapsub.put("markfield", map.get("markfield"));
                    mapsub.put("nodefield", map.get("nodefield"));
                    mapsub.put("markpoint", mapmarkCache.get(name));
                    mapsub.put("type", map.get("type"));
                    mapsub.put("menuid", menuid);
                    listtosave.add(mapsub);
                }
                dmdao.delBatch("schedule", "obj.tableid='" + tableid + "' and obj.parent_schedule_id='" + map.get(CNT_DbMng.id) + "'");
                dmdao.saveBatch("schedule", listtosave, Long.parseLong(userid), unitId == null ? null : Long.parseLong(unitId));
                map.put("actual_start_date", minstart);
                map.put("actual_end_date", minend);
                map.put("render", "split");
                dmdao.save("schedule", map);
            }

            //没有设置分段变量但是设置了提取日期及完成日期
            if(StringUtils.isEmpty(subtaskid)&& StringUtils.isEmpty(nodefieldid)&&StringUtils.isNotEmpty(extractid)&&StringUtils.isNotEmpty(endtimefield)){
                List listToSave = new ArrayList();
                listSub.forEach(x -> {
                    Map maptemp = (Map)x;
                    Map mapsub = new HashMap();
                    mapsub.put("study_id", studyid);
                    mapsub.put("name", "");
                    mapsub.put("parent_schedule_id", map.get(CNT_DbMng.id));
                    mapsub.put("actual_start_date", maptemp.get(extractid));
                    mapsub.put("actual_end_date", maptemp.get(endtimefield));
                    mapsub.put("tableid", map.get("tableid"));
                    mapsub.put("type", map.get("type"));
                    mapsub.put("menuid", menuid);
                    listToSave.add(mapsub);
                });
                dmdao.delBatch("schedule", "obj.tableid='" + tableid + "' and obj.parent_schedule_id='" + map.get(CNT_DbMng.id) + "'");
                dmdao.saveBatch("schedule", listToSave, Long.parseLong(userid), unitId == null ? null : Long.parseLong(unitId));
                map.put("render", "split");
                map.put("type", "project");
                dmdao.save("schedule", map);
            }

        } catch (Exception e) {
            Log.error(e.getMessage(), e);
        }
    }

    protected static void deleteSchedule(String projectId, String tableid, Map valueMap) {
        try {
            Long studyid = (Long)valueMap.get("studyid");
            String where = "obj.tableid='" + tableid + "' and obj.study_id='" + studyid + "'";
            DAODataMng dmdao = new DAODataMng(projectId);
            dmdao.delRecord(tableid, where);

        } catch (Exception e) {
            Log.error(e.getMessage(), e);
        }
    }

}
