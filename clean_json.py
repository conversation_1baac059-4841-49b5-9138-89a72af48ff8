#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON>ript to clean and fix JSON file with Chinese characters and control characters
"""

import json
import re
import codecs

def clean_json_content(content):
    """
    Clean JSON content by fixing common issues
    """
    # Remove or escape problematic control characters
    # Replace unescaped newlines in strings with escaped newlines
    content = re.sub(r'(?<!\\)\n(?=\s*[^}\],])', '\\n', content)
    
    # Replace unescaped tabs with escaped tabs
    content = re.sub(r'(?<!\\)\t', '\\t', content)
    
    # Replace unescaped carriage returns
    content = re.sub(r'(?<!\\)\r', '\\r', content)
    
    # Fix common control character issues
    content = content.replace('\x00', '')  # Remove null characters
    content = content.replace('\x08', '')  # Remove backspace
    content = content.replace('\x0c', '')  # Remove form feed
    
    return content

def fix_json_file(input_file, output_file=None):
    """
    Fix JSON file with Chinese characters and control character issues
    """
    if output_file is None:
        output_file = input_file.replace('.txt', '_cleaned.json')
    
    print(f"Cleaning JSON file: {input_file}")
    
    try:
        # Read the file with UTF-8 encoding, ignoring errors
        with codecs.open(input_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        print("Original file read successfully")
        
        # Clean the content
        cleaned_content = clean_json_content(content)
        
        # Try to parse the cleaned JSON
        try:
            data = json.loads(cleaned_content)
            print("JSON parsed successfully after cleaning")
            
            # Write the cleaned and properly formatted JSON
            with codecs.open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"Cleaned file saved as: {output_file}")
            return True
            
        except json.JSONDecodeError as e:
            print(f"JSON parsing still failed after cleaning: {e}")
            
            # Save the cleaned content anyway for manual inspection
            debug_file = output_file.replace('.json', '_debug.txt')
            with codecs.open(debug_file, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            print(f"Debug file saved as: {debug_file}")
            
            return False
            
    except Exception as e:
        print(f"Error processing file: {e}")
        return False

def create_sample_fixed_json(input_file, output_file=None, sample_size=5):
    """
    Create a sample of the first few records with proper Chinese characters
    """
    if output_file is None:
        output_file = input_file.replace('.txt', '_sample.json')
    
    # Sample data with corrected Chinese characters based on English names
    sample_data = [
        {
            "id": "nr",
            "name": "网站内容",  # Website Content
            "nameen": "Website Content",
            "note": "",
            "uuid": "F83BFBB9776551042444011F3BCAD39F"
        },
        {
            "id": "ljszb", 
            "name": "链接设置",  # Link Settings
            "nameen": "Link Settings",
            "note": "",
            "uuid": "F73258355BA17A935A82449590CF7BCA"
        },
        {
            "id": "tjszb",
            "name": "统计设置",  # Statistics Settings  
            "nameen": "Statistics Settings",
            "note": "",
            "uuid": "85D6C25D3DFE8C60A7D3421611324F47"
        },
        {
            "id": "gzlgzb",
            "name": "工作流定义",  # Workflow Definitions
            "nameen": "Workflow Definitions", 
            "note": "",
            "uuid": "DD0F380E968566AA6F0B897EDC906A9A"
        },
        {
            "id": "xmjs",
            "name": "角色",  # Role
            "nameen": "Role",
            "note": "",
            "uuid": "5D1371BA632D9438DC246019E82A4BBF"
        }
    ]
    
    try:
        with codecs.open(output_file, 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, ensure_ascii=False, indent=2)
        
        print(f"Sample file with correct Chinese characters saved as: {output_file}")
        return True
        
    except Exception as e:
        print(f"Error creating sample file: {e}")
        return False

if __name__ == "__main__":
    input_file = "json.txt"
    
    # Try to clean the JSON file
    if not fix_json_file(input_file):
        print("Automatic cleaning failed. Creating a sample file with correct Chinese characters...")
        create_sample_fixed_json(input_file)
