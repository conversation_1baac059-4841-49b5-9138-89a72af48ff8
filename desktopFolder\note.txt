1.根据特定数据集下载API获取下载该数据集所需的参数，以调用下载快照/锁定数据集API为例，以token查询载该数据集的查询条件数据格式 data_type、阶段标识 stage、文件名 fileName
2.根据step1中返回的data_type、stage、fileName，调用ctdms通用文件下载API usersyn/download，返回该文件流


根据该API获取当前节点的当前数据集信息，包括当前阶段标识，数据格式，文件名及其他能够唯一确定该数据集文件的参数，通过该参数的传递，调用/usersyn/download 通用下载接口能够返回该数据集的文件流，实现页面下载当前数据集文件
    
    
05.01 深圳北站 到 庆盛 00：20  1：00-6：00 分到 07：24
    

    
    
    
    <a title="下载" target="_blank" href="ftattach.download.do?tableid=db_unlock&amp;ufn=202302729D09165AD74A11B99002C8C9641535.xlsx&amp;fn=test.xlsx&amp;recordid=2389868544"><span class="bio-font bio-icon__leading-in"></span></a>
    
    
    
    
https://cdtms.hengrui.com/a/b?taskId=3294822401&server=https%3A%2F%2Fcdtms.hengrui.com%3A443&projectId=cdtmsen_val



3294822401
cdtmsen_val



<a target="_blank" href="ff_showpdf2swf.show.do?tableid=study_regular_review&amp;fieldid=files&amp;filename=202404C8D93FA53EC147A8AC81DCE22E3D7363.pdf&amp;ap=true">线上签名功能操作指南_20231101.pdf</a>



https://cdtms.hengrui.com/a/b?taskId=3294822403&server=https%3A%2F%2Fcdtms.hengrui.com%3A443&projectId=cdtmsen_val



%m_unlock_check(studyid=&studyid.,submit_date=&submitDate.,cutdate=&cutDate.,submit_psw=&sumitPsw.,new_psw=Hr123456);


********************************************************

********************************************************

4月工作内容：
1、susar v1.1.0 交接，生产、测试、验证环境更新部署，定位并解决线上问题，协助PV开发同事运维，熟悉运维及开发流程
2、ecrf报告解锁功能开发，完成页面搭建，sas调用，cdtms接口验证，完成开发流程设计文档

5月上旬：
1、完成ecrf报告解锁测试环境上线实测
2、继续参与cdtms其余各业务流程节点sas工具线上化开发

https://cdtms.hengrui.com/a/b?taskId=3303211014&server=https%3A%2F%2Fcdtms.hengrui.com%3A443&projectId=cdtmsen_val


https://cdtms.hengrui.com/a/b?taskId=3319562244&server=https%3A%2F%2Fcdtms.hengrui.com%3A443&projectId=cdtmsen_val

下载cutdate数据集

https://cdtms-tst.hengrui.com/


String bucketName, String fileName, Map<String, String> tags, Path downloadPath
06D39FCD-21F9444A-A6E15C50-635DF87F



***********:8087 访问 https://cdtms.hengrui.com  / https://cdtms.hengrui.com:443
***********:8087 访问 sas-hrsh-node1.hengrui.com : 8591
***********:8087 访问 https://minio.hengrui.com :9000 / http://**********:9001

************:8087 访问 https://cdtms-tst.hengrui.com  / https://cdtms-tst.hengrui.com:443
************:8087 访问 sas-hrsh-node1.hengrui.com : 8591
************:8087 访问 https://minio-tst.hengrui.com :9000 / http://***********:9000

************:8087 访问 https://meduap-tst.hengrui.com:8085/cdtmsen_val  /   http://***********:8085
************:8087 访问 sas-hrsh-node1.hengrui.com : 8591
************:8087 访问 https://minio-val.hengrui.com :9000 / http://***********:9000



https://cdtms.hengrui.com/a/b?taskId=3322544129&server=https%3A%2F%2Fcdtms.hengrui.com%3A443&projectId=cdtmsen_val


CDTMS手册优化测试项目.zip

2024/04/30/13/05

mc.exe cp CDTMS-ECRF-UNLOCK-TEST.zip minios3/submit --tags "key1=HRTAU&key2= CDTMS-ECRF-UNLOCK-TEST&key3=2024/04/30/13/05"

./mc.exe alias set minios3 http://**********:9000 minioadmin minioadmin

SHR-2004-102_PRO_SAS_20240514_112154
mc.exe cp CDTMSTestOptimise_UAT.zip minios3/uat --tags "key1=EDCServer&key2= CDTMSTestOptimise_UAT&key3=2024/05/14/10/05/20"


mc.exe cp pgmout_uat.sas minios3/uat/pgm --tags "key1=uat&key2=sas_code&key3=2024-06-04"



mc.exe cp SHR-2004-102_PRO_UAT.zip minios3/uat --tags "key1=EDCServer&key2=SHR-2004-102_PRO&key3=2024/05/14/10/05/20"



mc.exe cp SHR-2004-102_PRO_UAT_1715738643.json minios3/uat/json/SHR-2004-102_PRO_UAT_1715738643.json --tags "key1=json&key2=SHR-2004-102_PRO&key3=2024/05/15/10/06/20"


mc.exe ls minios3/uat


mc.exe mb minios3/uat/json


SHR-2004-102_PRO_UAT_1715738643.json

mc.exe ls minios3/



recipient_date

mc.exe alias set minios3 http://**********:9000 minioadmin minioadmin




https://cdtms.hengrui.com/a/b?taskId=3322544131&server=https%3A%2F%2Fcdtms.hengrui.com%3A443&projectId=cdtmsen_val





Hi 宾文
 申请开通***********/************/************ 三台服务器端口的访问权限，详细信息如下：
pro:
***********:8086/8087 单向访问 http://**********:9000
tst:
************:8086/8087 单向访问 sas-hrsh-node1.hengrui.com : 8591 (经验证，存在访问拒绝的情况)
val:
************:8086/8087 单向访问 sas-hrsh-node1.hengrui.com : 8591 (经验证，存在访问拒绝的情况)



84C7C646A63F4F9A8C47C81D5922F4D5


<a target="_blank" href="ff_showpdf2swf.show.do?tableid=study_regular_review&amp;fieldid=files&amp;filename=202403E84990F8AB2C488D868F0CBA3BFD8AF5.pdf&amp;ap=true">临床数据科学中心-方案偏离管理流程_20230413-培训PPT.pdf</a>

https://meduap-tst.hengrui.com:8085/usersyn/download?tableid=data_submission&token=84C7C646A63F4F9A8C47C81D5922F4D5&ufn=HRS-7535-102_PRO_Excel数据集(父子表合并)_20240424_110445.xlsx


https://meduap-tst.hengrui.com:8085/a/b?taskId=3314384900&server=https%3A%2F%2Fmeduap-tst.hengrui.com%3A8085&projectId=cdtmsen_val


<a target="blank" href="ftattach.download.do?tableid=ecrf_build1&amp;ufn=202405B7F92D174AB548B4B00065078BCFE6DD.csv&amp;fn=SDG.csv&amp;recordid=3314745344">SDG.csv</a>





view-source:https://cdtms.hengrui.com/tableapp.edit.do?tableid=uta&tn_alias=UAT&refinfo=studyid__1627127808&menuid=5_14&callmode=1&id=&defvalue=HEX:747970653A313B7a713A353B3B&readonly=false&readonlyfields=&anticacheid=1715678157497



2024.05.21
1.编写 uat java 后端程序 make sas sumbit function to be a common util
2.
3.





function getDvpAndecrf(){
    var ecrfVersion=$link{xsht,ecrf_version,obj.bbh=$form{bbh},modify_date desc};
        var dvpVersion=$link{xsht,dvp_version,obj.bbh=$form{bbh},modify_date desc};
return [ecrfVersion,dvpVersion];
}

getDvpAndecrf();





A5B3683453F140459F27F5989873DB84


2024.05.27
1.uat sas调用剩余 sas运行结果核对 cdtms上传API核对(taskId获取)
2.


2024年5月工作内容：
1.CDTMS 低代码节点按钮及页面逻辑配置js
2.对接UAT SAS程序线上化需求，确定API逻辑，开发sas程序提交接口
3.对接CDTMS开发同事，理解CDTMS相关接口调用逻辑，对接SAS程序同事约定调用宏程序参数格式，本地开发联调 


2024年6月工作计划：
上旬:外部数据管理sas 程序线上化需求
中下旬：完成UAT本地流程走通，并部署上线到服务器环境


https://clinical.hengruipharma.com:1818/usersyn/datalist/?token=D3B331A9524F4AFA9EAFBA048003CCEA&tableid=data_submission&where&username&pagesize&pagecount&type




usersyn/download?tableid=ecrf_build1&token=5BC5546FD0A04D86BFAAE38BC36B48D9&ufn=202405B7F92D174AB548B4B00065078BCFE6DD.csv


202405021B403C6A2F4817A97BB6760993C23B.xlsx


outboard_data_manager


https://cdtms-tst.hengrui.com:82



edm_uap_pro 生产：1.获取token     https://clinical.hengruipharma.com:1818/usersyn/gettoken?projectid=edm_uap&secret=5AF2E70E-1E6D-47B4-A9C5-3809448E11E8&tableid=outboard_data_manager
 2.查询附件文件名 https://clinical.hengruipharma.com:1818/usersyn/datalist/?token=xxxxxxx&tableid=outboard_data_manager&where&username&pagesize&pagecount&type=edit
3.根据查询到的附件名，调用下载接口
https://clinical.hengruipharma.com:1818/usersyn/download?tableid=outboard_data_manager&token=xxxxxxxxxxxxxxxx&ufn=xxxxxxxxxx.csv

edm_uap_test 测试：仅需把请求域名替换成https://cdtms-tst.hengrui.com:82   其他保持与edm_uap_pro 生产 一致



https://cdtms.hengrui.com/usersyn/gettoken?projectid=cdtmsen_val&secret=xxxxxxx&tableid=data_submission



[
    {
        "crf_zt": "10",
        "trans_freq": "5",
        "is_binding": "2",
        "lab_org": "上海方达生物技术有限公司",
        "userid": "971833344",
        "uuid": "88904FD74AE44BAEA2E99C442BD86AEB",
        "type_detail": "",
        "edm_email": "<EMAIL>",
        "ext_date": "2024-05-27 00:00:00",
        "ext_version": "V1.0",
        "lab_email": "<EMAIL>",
        "lab_name": "",
        "studyid": "2743140352",
        "id": "3378872325",
        "freqother": "",
        "site_lab_contact": "",
        "file_format": "7",
        "medium_type": "4",
        "site_lab_phone": "",
        "ext_data_type": "Immunogenicity",
        "createtime": "2024-05-27 16:39:56",
        "outboard_data_number": "DATA-002853",
        "modifyuserid": "971833344",
        "lab_contact": "",
        "site_lab_name": "",
        "blinging_admin": "",
        "edm_phone": "",
        "lab_address": "",
        "lrr": "侯凡",
        "dta_doc": "HR17031-302_Immunogenicity_DTA_V1.0_20240527_clean.pdf*202405318496B955914A8EA55343005F9394E1.pdf|",
        "dm_contact": "石林",
        "format_detail": "",
        "ext_data_text": "",
        "lab_phone": "",
        "zq": "5",
        "ds_type": "",
        "version_change_explain": "",
        "unitid": "323616770",
        "lastmodifytime": "2024-05-27 16:40:00",
        "set_binding": "1",
        "is_leadingin": "",
        "site_lab_mail": "",
        "is_site_lab": "0",
        "rq": "2024-05-27 00:00:00"
    },
    {
        "crf_zt": "00",
        "trans_freq": "5",
        "is_binding": "1",
        "lab_org": "上海鼎岳生物技术有限公司",
        "userid": "971833344",
        "uuid": "88512F7C72AC45A1B8A09453331D7B33",
        "type_detail": "",
        "edm_email": "<EMAIL>",
        "ext_date": "2024-05-27 00:00:00",
        "ext_version": "V0.1",
        "lab_email": "<EMAIL>",
        "lab_name": "",
        "studyid": "3008397312",
        "id": "3378872324",
        "freqother": "",
        "site_lab_contact": "",
        "file_format": "7",
        "medium_type": "4",
        "site_lab_phone": "",
        "ext_data_type": "Immunogenicity",
        "createtime": "2024-05-27 15:16:40",
        "outboard_data_number": "DATA-002852",
        "modifyuserid": "971833344",
        "lab_contact": "",
        "site_lab_name": "",
        "blinging_admin": "",
        "edm_phone": "",
        "lab_address": "",
        "lrr": "侯凡",
        "dta_doc": "",
        "dm_contact": "谷雅静",
        "format_detail": "",
        "ext_data_text": "",
        "lab_phone": "<EMAIL>",
        "zq": "5",
        "ds_type": "ADA_NAb",
        "version_change_explain": "",
        "unitid": "323616770",
        "lastmodifytime": "2024-05-27 15:16:40",
        "set_binding": "1",
        "is_leadingin": "",
        "site_lab_mail": "",
        "is_site_lab": "0",
        "rq": "2024-05-27 00:00:00"
    }
]

https://cdtms.hengrui.com/usersyn/datalist/?token=xxxxxxxx&tableid=ext_data&where&username&pagesize&pagecount&type=edit


https://cdtms.hengrui.com/usersyn/download?tableid=ext_data&token=xxxxxxx&ufn=xxxxxxxx




https://cdtms-tst.hengrui.com:82/usersyn/gettoken?projectid=cdtmsen_val&secret=5AF2E70E-1E6D-47B4-A9C5-3809448E11E8&tableid=ext_data


https://clinical.hengruipharma.com:1818/usersyn/gettoken?projectid=edm_uap&secret=5AF2E70E-1E6D-47B4-A9C5-3809448E11E8&tableid=outboard_data_manager
https://clinical.hengruipharma.com:1818/usersyn/gettoken?projectid=edm_uap&secret=xxxxxxx&tableid=outboard_data_manager
https://clinical.hengruipharma.com:1818/usersyn/gettoken?projectid=edm_uap&secret=xxxxxxx&tableid=data_submission


https://clinical.hengruipharma.com:1818/usersyn/datalist/?token=51EF059FBBDD4EAC928C243E98590E6C&tableid=outboard_data_manager&where&username&pagesize&pagecount&type=edit
https://clinical.hengruipharma.com:1818/usersyn/datalist/?token=xxxxxxxx&tableid=ext_data&where&username&pagesize&pagecount&type=edit

https://clinical.hengruipharma.com:1818/usersyn/datalist/?token=xxxxxxxx&tableid=ext_data&where&username&pagesize&pagecount&type=edit



https://clinical.hengruipharma.com:1818/usersyn/download?tableid=outboard_data_manager&token=51EF059FBBDD4EAC928C243E98590E6C&ufn=202405B7C9773B1EE74E57B23DE6886F3826C6.csv

airflow 服务器
root
Hr@airflow0509




key:uat value:{hostname:'xxxx',username:'',password:''}


"source /home/<USER>/airflow282/bin/activate && " +
                    "python /home/<USER>/edc_to_minio/edc_to_minio.py " +
                    "--data_type='data_set' --data_format='SAS' --env='UAT' --file_list='SHR-2106-101'";
                    
                    
                    
                    https://meduap-tst.hengrui.com:8085/uiframe_vue/firstpage?tableid=xsht&prjid=&viewname=&where=&nvwhere=obj.zt%3C40&refinfo=&readonly=&tn_alias=&addbtn=&hiddenBtns=&defvalue=&menuid=&entrytid=xsht&entryrid=&readonlyfields=&viewType=1&viewHandlerid=&refinfo_battachAdd=&whereinherate=&strgroup=&groupbyfield=&starmark=1&whereid=&showOperation=true&innerTable=&btnMerge=&ignoreDefValueWhere=false&listfields=&orderByField=&asc=&showPageSwitch=true&recordid=1627127808&pageuuid=D7F72FD23CB246F78B6FB836D866E665&menutype=singlePro
                   
                    
            /bin/sh -c  python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='data_set' --env='UAT' --file_list='SAS-Validation-duplicaterecord' --pro_test='test' --uuid='31efeaffdefc8bec'  
            
            
            
            
            mc.exe cp SHR-2004-102_PRO_UAT.zip minios3/uat/SHR-2004-102_PRO_UAT.zip "key1=EDCServer&key2=SHR-2004-102_PRO&key3=2024/05/14/10/05/20"
            
            
            
            
            
            
            
            
            
proc datasets lib=work nolist kill; quit;%let dir=%sysfunc(getoption(work));%global system studyid root version dir lang;x "cd &dir";%put &dir;x "mkdir ./pgm";x 'mc find minios3/pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';%include "&dir/pgm/*.sas";%let studyid=CDTMS手册优化测试项目;/*%m_post2s3(studyid=CDTMS手册优化测试项目);*//*%m_gets3data(studyid=CDTMS手册优化测试项目,data=@);*//*%let lang="CH";*/option mprint symbolgen validvarname=v7;/*%let jsonPath=minios3/uat/json/SHR-2004-102_PRO_UAT_1715738643.json;*/%M_std_uatpre(json_path=minios3/UAT/json/CDTMS手册优化测试项目_UAT_67dfffff77ebf312.json);            
            
 
 
 UAT报告  uat_plan_doc
 UAT测试数据集(SAS) test_data_files
 UAT测试数据集Excel  uatexcel     
        https://meduap-tst.hengrui.com:8085/remoteButtonTask/upload?taskId=**********&formId=3615C510937041DC8AF1F073F155CB32&fid=uat_plan_doc&fn=test.txt&projectId=cdtmsen_val    
 
https://meduap-tst.hengrui.com:8085/remoteButtonTask/upload?taskId=**********&formId=82C7F74BF3D74C06BE96BDD113E41B38&fid=test_data_files&fn=test.txt&projectId=cdtmsen_val

https://meduap-tst.hengrui.com:8085/remoteButtonTask/formId?taskId=**********&projectId=cdtmsen_val
  
  **********          
  cdtmsen_val          
  **********
  "formId": "26C82D9E2B104868B2E332F9FF511557"
	
	./mc.exe alias set minios3-t http://*********** minioadmin minioadmin
	
	 python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='edc_account_history' --env='PRO' --file_list='HR7777-I-106' --pro_test='test' --uuid='31efeaffdefc8bec'

getUserHistoryFile

clinicalinte

B4A9E679FEF34904B8BED822C53B7B7D

  
 
obj.studyid=(select obj2.id from Xsht as obj2 where obj2.studyid='SHR-1707-201')
        



response.setHeader("Access-Control-Allow-Origin","*");       
        
https://meduap-tst.hengrui.com:8082/usersyn/datalist?token=2AA392A85E8542A390A6B0EE0F6A5836&tableid=zxsyr1&where=obj.studyid=(select obj2.id from Studyinfo as obj2 where obj2.studyid='SHR-1707-201')
        
        https://meduap-tst.hengrui.com:8082/usersyn/datalist?token=2AA392A85E8542A390A6B0EE0F6A5836&tableid=zxsyr1&where=b2JqLnN0dWR5aWQ9KHNlbGVjdCBvYmoyLmlkIGZyb20gU3R1ZHlpbmZvIGFzIG9iajIgd2hlcmUgb2JqMi5zdHVkeWlkPSdTSFItMTcwNy0yMDEnKQ==
        
        ENVInfo.get("file_type")
        
        file_type
        permission_his
        
        
        
        taskId,projectId,fid,fileSuffix
        
        
        
        
python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='eCRF_online_approval'  --env='PRO' --file_list='HRS9531-203' --pro_test='test' --uuid='4A8D087A05E54B378493EE56F3C1AF74' --data_format='Excel'
        
        
http://************:8087/sas_online/getUserHistoryFile



1.uap 信息获取cro信息
2.ecrf 历史数据、交接、audit_trail 质疑汇总
3.数据库定义报告获取 接口下载       



http://************:8087/sas_online/getUserHistoryFile

FSFV:
		1	项目代码		studyid	动态表引用	  	
		2	中心编号		center	字符	  	
		3	受试者代码	subject_identification	字符	  	
		4	访视日期	 visit_date	日期时间	  	
		5	周期		zq	 整数	  	
		6	中心名称	 site_name	字符
		
		
LSLV:
		1	项目代码	 studyid 动态表引用	  	
		2	中心编号	 center	字符	  	
		3	责任人	name 字符	  	
		4	受试者代码 subject_identification 字符	  	
		5	访视日期	visit_date	日期时间	  	
		6	周期	 zq	整数	  	
		7	中心名称	dept_id	字符		
  	

		中心名称	subjectInfo.get("SITENAME")

		中心编号 subjectInfo.get("SITECODE")
		
		受试者代码 subjectInfo.get("SUBJID")
		
	    访视日期 subjectInfo.get("CTIME").toString().split(" ")[0]

		{ "taskId":"3294822401",
			"formId":"1168466760CE457CA3F02157C476FAE9",
			"projectId":"cdtmsen_val",
			"data":{"center":"123","site_name":"123","subject_identification":"123","visit_date":"2024-06-24" }
		
		}
		
		{"center":"123","site_name":"123","subject_identification":"123","visit_date":"2024-06-24" }



fsfv
E941A7395CCC4A89B352CDF13732041C


 http://************:8087/sas_online/getSubjectInfoLast

nohup java -jar ./cdtms_sas_online-0.0.1-SNAPSHOT.jar > "java.log" 2>&1

6月工作总结
sas程序线上化开发: 完成uat节点sas程序调用并返回结果数据集、ecrf填写指南文件获取、ecrf线上审批附件获取、配置比对报告附件获取、质疑汇总文件获取，edc权限历史记录/用户信息文件获取

7月工作计划：
上旬：优化FSFV、LSLV 节点信息获取程序，解决之前完成开发节点的测试问题
中旬：开发剩余cdtms节点优化Java部分的需求

        "taskId": "%s", "formId": "%s", "projectId": "%s",
		param.get("taskId"), param.get("formId"), param.get("projectId"), 
		
		+"&dataId="+dataId
		

python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='Subject_CRF' --env='PRO' --file_list='HRS-5635-101' --pro_test='val' --uuid='3dfcd7fbbf7895b3' --data_format='zip'











      Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", resultsA);




ENVInfo.put("data_type", "edc_account_history");





UAT测试数据集(SAS)
		
UAT测试数据集Excel	








 mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log /home/<USER>/blind_sas_log -o file_mode=0666,dir_mode=0770,username=zhouh36,password=HR9cf3cbd8,gid=root,uid=root
 mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log /home/<USER>/blind_sas_log -o file_mode=0666,dir_mode=0770,username=zhouh36,password=Zh123456,gid=root,uid=root
 mount.cifs //SHNVWSASECT01/EDMtst /home/<USER>/ -o file_mode=0666,dir_mode=0770,username='zhouh36',password='HR9cf3cbd8',gid=root,uid=root
 mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/ /home/<USER>'zhouh36',password='Zh123456',gid=root,uid=root
 mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log /home/<USER>/blind_sas_log -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root
 mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/csv_raw /home/<USER>/ex_original_csv -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root
 mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/json_blind /home/<USER>/ex_json_file -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root





1.dmrp 数据表 数据提取 回填表单
2.


ENVInfo.put("isLatest", "");

fileNameSuffix


1.
cdtmsmeduap环境流程优化:
开发ecrf解锁、数据管理uat、数据库修订报告节点SAS程序线上化API(Java部分)，
开发以下cdtms数据管理节点的附件获取API:edc用户权限历史、受试者ecrf交接、ecrf上线审批、edc生产环境使用、ecrf填写指南、数据库修订确认、eCRF上线与备份计划、EC编程与测试、数据库修订报告、数据审核计划(Java部分)
开发数据管理FSFV、LSLV节点受试者信息同步的API(Java部分)




cdtms meduap环境与百奥知同事对接，熟悉页面表单扩展功能配置如远程按钮配置，cdtms系统内部API调用，获取节点信息和数据修改、附件上传等功能实现，验证接口可用性和获取优化节点业务流程对应的信息



DM：
上线后质量控制 
dbqc  比对 
casebook 比对
sign 比对



ecrf 搭建

确定的流程 + 系统内部配置 

周二下午 过流程

百奥知 生产环境数据同步到测试环境 
	   tst环境
	   14段
	   




rtsm 一致性比对


python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='Logic_Check_Setting' --env='UAT' --pro_test='val' --data_format='Excel'




python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='DB_Definition' --env='PRO'  --pro_test='val' --uuid='45b9f97795eda42c' --data_format='Excel'



Create a single HTML. file for a real-time object detection web application
TensorFlow,js andthe COCO-SSD model. The application should:

Access the user's webcam and display the video feed.
Perform object detection on the video feed in real-time.
Draw bounding boxes around detected objects and label them with their
class and detection confidence.
Display a list of uniquely detected objects below the video feed, showing
the object class and the time it was first detected.
Ensure each object class is only listed once, regardless of how many
times it's detected.
Use a detection frequency of2 FPS to balance performance and
responsiveness.
include error handling for camera access and model loading
Style the application for a clean, modern look with a responsive design.
include all necessary HTML, CSS, and JavaScript in a single, self-
Claude 3.5 Sonnet



specified:
1.It accesses the user's webcam and displays the video feed.
2. It perfoms object detection on the video feed in real-time.
3. It draws bounding boxes around detected objects and labels them with their class and
detection confidence.
4. It displays a list of uniquely detected objects below the video feed, showing the object
class and the time it was first detected.
5. Each object class is only listed once, regardless of how many times it's detected
6. The detection frequency is set to 2 FPS (500ms interval) to balance performance and
responsiveness.
7.It includes error handling for camera access and model loading
8. The application is styled for a clean, moder look with a responsive design.
9. All necessary HTML, CSS, and JavaScript are included in a single, self-contained file
that can be run by opening it in a web browser.
10. It uses CDN links for TensorFlow,js and COCO-SSD model libraries.
To use this application:
1. Save the content of the artifact as an HTML file (e.g, "object_detection.html").
2. Open the file in a moder web browser (Chrome, Firefox, Edge, etc.).
3. Allow the application to access your camera when prompted.
4. Wait for the model to load and start detecting objects in real-time.
Note that the application requires an intemet connection to load the TensorFlowjs and
COCO-SSD libraries from the CDN,
Is there anything you'd like me to explain or modify in this application?



7月工作总结：
1.rtsm 随机一致性比对后端sas调用业务流程开发，需求对接整理，确定业务流程
2.优化sas-online后端接口逻辑，提升接口响应的稳定性;优化uat、ecrf交接文件传输节点，提升文件传输效率和降低页面响应时间，优化LSLV节点受试者信息更新逻辑
3.定位ecrf填写指南附件回填表单提交限制问题，dbs数据库meduap环境问题
4.完成方案流程图设计附件获取接口开发，实现文件跨节点调用供sas程序使用
5.完成dbs excel数据解析并自动同步到cdtms对应的节点下的自定义表
6.完成sas-online val服务器xxl-job 安装部署
7.熟悉在线审核工作流程配置

8月工作计划：
1.完成rtsm 随机一致性比对接口开发
2.完成cdtms meduap 环境 sas-online后端已开发接口的迁移，部署，并解决迁移过程中接口可能产生的问题


rtsm 一致性比对远程API 后端文件获取逻辑开发 需求澄清	
1.rtsm 一致性比对远程API开发需求对接 2.ecrf填写指南附件回填表单提交限制问题定位
sas-online 后端API程序优化	
sas程序线上化后端代码优化，dbs数据库比对报告测试环境问题定位	
sas程序线上化后端API优化 CDTMS在线审核流程教程熟悉	
方案流程图设计附件获取API开发，完成文件从研究方案获取并调用sas程序	
RTMS sas调用程序 API 开发	
定时任务xxl-job对接sas online程序，安装插件，新增任务处理器	 
1.ecrf 数据解析入库优化新增版本号 2.LSLV 获取最后一名受试者信息 API优化	
学习，数据写入，数据读取，数据填充，优化DBS数据读取部分代码逻辑	
完成dbs excel数据解析并自动同步到cdtms tbl_ecrf数据库表的API 开发与自测	
数据库定义报告获取，excel数据解析入库，接口开发	
1.uat测试节点优化 2.ecrf交接文件传输节点优化 3.对接数据库定义报告 数据解析流程




python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='Subject_CRF' --env='PRO'  --pro_test='val' --uuid='5bbb57ebabff69d2' --data_format='zip' --second='YES'


UpdateSchedule
workflow_review
https://meduap-tst.hengrui.com:8085/remoteButtonTask/dataSave


"sftp://<EMAIL>:***@clinical-ftp.hengrui.com:22357C:\Users\<USER>\Desktop\output.png"



"sftp://<EMAIL>:***@clinical-ftp.hengrui.com:22357/Projects/CDTMS手册优化测试项目/EDM_Unblind/PD/output.png"

Expecting / to follow the hostname in URI "sftp://<EMAIL>@clinical-ftp.hengrui.com:22357/Projects/CDTMS手册优化测试项目/EDM_Unblind/PD/

diffie-hellman-group14-sha1
diffie-hellman-group14-sha1		


com.jcraft.jsch.JSchUnknownHostKeyException: UnknownHostKey: [clinical-ftp.hengrui.com]:22357. RSA key fingerprint is SHA256:dLnjwVxI2PeMZsMNVaie8ezuAIv3LxOgKsUCm9zVP2Y



1.随机一致性比对 参数获取通用配置 
2.ECRF交接上传到SFTP，通知下游业务人员
3.





python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='eCRF_online_approval' --env='PRO' --pro_test='val'  --data_format='Excel'





数据集检查 显示隐藏
UAT结果 不通过 不显示 数据集检查
UAT 测试数据集EXCEL 回传
tdm 审uat报告。数据集检查在线看
DM UAT 流程：
1. DM提交---->TDM审批--->提交锁定
2. teamUAT-->PM MM TDM 审批 -->
3.
4.


TEAM UAT 不需要数据集检查
1.
2.
3.


https://meduap-tst.hengrui.com:8085/usersyn/datasave?token=CD3D7032459A41A08871D164C81DA5E9&tableid=crf_handover&formid=CF51D90A61394A02A6E824E0DA071A8B&username=<EMAIL>&format=list



	formInfoMap.put("studyId", studyId);
    formInfoMap.put("dataVersion", dataVersion);
    formInfoMap.put("param", param);
https://meduap-tst.hengrui.com:8085/usersyn/datalist?token=F5FD8109C86D432B954870064134C8EB&tableid=crf_handover&type=edit&where=obj.studyid%3D%272463039488%27and+obj.id%3D%27%7B%22note%22%3A%22save+success%22%2C%22id%22%3A%223562635264%22%2C%22status%22%3A%22200%22%7D%27

		