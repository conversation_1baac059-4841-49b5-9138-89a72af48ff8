/*
 Navicat MySQL Data Transfer

 Source Server         : 测试环境
 Source Server Type    : MySQL
 Source Server Version : 80028 (8.0.28)
 Source Host           : ***********:3306
 Source Schema         : susar_auto_mail

 Target Server Type    : MySQL
 Target Server Version : 80028 (8.0.28)
 File Encoding         : 65001

 Date: 20/04/2023 23:01:53
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_user_temp
-- ----------------------------
DROP TABLE IF EXISTS `t_user_temp`;
CREATE TABLE `t_user_temp`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `site_user_role` int NULL DEFAULT NULL COMMENT '角色代码，1 代表 Site_PI, 2 代表 Site_INV, 3 代表机构SUSAR人员',
  `site_user_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户邮箱',
  `site_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外键，关联site表',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_user_temp
-- ----------------------------
INSERT INTO `t_user_temp` VALUES ('0', 1, '<EMAIL>', 'UAP-20220302None\r');
INSERT INTO `t_user_temp` VALUES ('110', 2, '<EMAIL>', 'CDTMS_TEST_001CN001\r');
INSERT INTO `t_user_temp` VALUES ('117', 1, '<EMAIL>', 'CDTMS_TEST_00202\r');
INSERT INTO `t_user_temp` VALUES ('118', 1, '<EMAIL>', 'Test_Yan001\r');
INSERT INTO `t_user_temp` VALUES ('119', 1, '<EMAIL>', 'Test_YanNone\r');
INSERT INTO `t_user_temp` VALUES ('12', 1, '<EMAIL>', 'E417002\r');
INSERT INTO `t_user_temp` VALUES ('129', 1, '<EMAIL>', 'HR_A007_01CN001\r');
INSERT INTO `t_user_temp` VALUES ('130', 1, '<EMAIL>', 'EDC_TEST_005CN001\r');
INSERT INTO `t_user_temp` VALUES ('132', 2, '<EMAIL>', 'RTSM_006_ENCN001\r');
INSERT INTO `t_user_temp` VALUES ('133', 1, '<EMAIL>', 'RTSM_006_ENCN001\r');
INSERT INTO `t_user_temp` VALUES ('134', 2, '<EMAIL>', 'RTSM_007_ENCN001\r');
INSERT INTO `t_user_temp` VALUES ('135', 1, '<EMAIL>', 'RTSM_007_ENCN001\r');
INSERT INTO `t_user_temp` VALUES ('136', 1, '<EMAIL>', 'RTSM_007_ENCN001\r');
INSERT INTO `t_user_temp` VALUES ('137', 2, '<EMAIL>', 'RTSM_008_CHNCN001\r');
INSERT INTO `t_user_temp` VALUES ('138', 1, '<EMAIL>', 'RTSM_008_CHNCN001\r');
INSERT INTO `t_user_temp` VALUES ('139', 1, '<EMAIL>', 'RTSM_008_CHNCN001\r');
INSERT INTO `t_user_temp` VALUES ('14', 1, '<EMAIL>', 'HR_A008CN003\r');
INSERT INTO `t_user_temp` VALUES ('140', 2, '<EMAIL>', 'RTSM_009_CHNCN001\r');
INSERT INTO `t_user_temp` VALUES ('141', 1, '<EMAIL>', 'RTSM_009_CHNCN001\r');
INSERT INTO `t_user_temp` VALUES ('142', 1, '<EMAIL>', 'RTSM_009_CHNNone\r');
INSERT INTO `t_user_temp` VALUES ('143', 2, '<EMAIL>', 'RTSM_010_CHNCN001\r');
INSERT INTO `t_user_temp` VALUES ('144', 1, '<EMAIL>', 'RTSM_010_CHNCN001\r');
INSERT INTO `t_user_temp` VALUES ('149', 2, '<EMAIL>', 'RTSM_011_CHNNone\r');
INSERT INTO `t_user_temp` VALUES ('150', 1, '<EMAIL>', 'RTSM_011_CHNCN001\r');
INSERT INTO `t_user_temp` VALUES ('157', 1, '<EMAIL>', '\"SHR-1802-II-201CN001,CN006\"\r\n160,2,<EMAIL>,RTSM_011CN001\r\n161,1,<EMAIL>,RTSM_011CN001\r\n162,2,<EMAIL>,RTSM_011CN002\r\n163,1,<EMAIL>,RTSM_011CN002\r\n168,1,<EMAIL>,HR18034-201CN001\r\n169,1,<EMAIL>,HR18034-201CN002\r\n170,1,<EMAIL>,HR180');
INSERT INTO `t_user_temp` VALUES ('29', 1, '<EMAIL>', 'teststudyNone\r');
INSERT INTO `t_user_temp` VALUES ('36', 1, '<EMAIL>', 'HR_TEST_HOME1CN001\r');
INSERT INTO `t_user_temp` VALUES ('44', 1, '<EMAIL>', 'Account testCN002\r');
INSERT INTO `t_user_temp` VALUES ('45', 1, '<EMAIL>', 'Account testCN003\r');
INSERT INTO `t_user_temp` VALUES ('54', 2, '<EMAIL>', 'RTSM_A001_WJCN001\r');
INSERT INTO `t_user_temp` VALUES ('55', 1, '<EMAIL>', 'RTSM_A001_WJCN001\r');
INSERT INTO `t_user_temp` VALUES ('67', 1, '<EMAIL>', 'EDC-UAP TESTCN002\r');
INSERT INTO `t_user_temp` VALUES ('68', 1, '<EMAIL>', 'SHR1020-I-201CN001\r');
INSERT INTO `t_user_temp` VALUES ('69', 1, '<EMAIL>', 'SHR1020-I-201CN002\r');
INSERT INTO `t_user_temp` VALUES ('70', 1, '<EMAIL>', 'SHR1020-I-201CN001\r');
INSERT INTO `t_user_temp` VALUES ('76', 1, '<EMAIL>', 'EDC_001_CHNCN001\r');
INSERT INTO `t_user_temp` VALUES ('77', 1, '<EMAIL>', 'EDC_002_CHNCN001\r');
INSERT INTO `t_user_temp` VALUES ('79', 1, '<EMAIL>', 'EDC_004_ENCN001\r');
INSERT INTO `t_user_temp` VALUES ('80', 1, '<EMAIL>', 'EDC_005_ENCN001\r');
INSERT INTO `t_user_temp` VALUES ('83', 2, '<EMAIL>', 'RTSM_001_CHNCN001\r');
INSERT INTO `t_user_temp` VALUES ('84', 1, '<EMAIL>', 'RTSM_001_CHNCN001\r');
INSERT INTO `t_user_temp` VALUES ('85', 1, '<EMAIL>', 'RTSM_002_CHNCN001\r');
INSERT INTO `t_user_temp` VALUES ('86', 2, '<EMAIL>', 'RTSM_002_CHNCN001\r');
INSERT INTO `t_user_temp` VALUES ('87', 2, '<EMAIL>', 'RTSM_003_CHNCN001\r');
INSERT INTO `t_user_temp` VALUES ('88', 2, '<EMAIL>', 'RTSM_003_CHNCN001\r');
INSERT INTO `t_user_temp` VALUES ('89', 2, '<EMAIL>', 'RTSM_004_ENCN001\r');
INSERT INTO `t_user_temp` VALUES ('90', 1, '<EMAIL>', 'RTSM_004_ENCN001\r');
INSERT INTO `t_user_temp` VALUES ('91', 1, '<EMAIL>', 'RTSM_005_ENCN001\r');
INSERT INTO `t_user_temp` VALUES ('92', 2, '<EMAIL>', 'RTSM_005_ENCN001\r');
INSERT INTO `t_user_temp` VALUES ('93', 1, '<EMAIL>', 'UAP_TEST_001CN001\r');
INSERT INTO `t_user_temp` VALUES ('94', 1, '<EMAIL>', 'UAP_TEST_001CN002\r');

SET FOREIGN_KEY_CHECKS = 1;
