/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80032
 Source Host           : localhost:3306
 Source Schema         : external_data

 Target Server Type    : MySQL
 Target Server Version : 80032
 File Encoding         : 65001

 Date: 08/12/2023 15:43:03
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ex_blind_email_record
-- ----------------------------
DROP TABLE IF EXISTS `ex_blind_email_record`;
CREATE TABLE `ex_blind_email_record`  (
  `id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `user_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `project_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `file_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `file_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `receive_emails` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP,
  `original_file_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ex_blind_email_record
-- ----------------------------
INSERT INTO `ex_blind_email_record` VALUES ('29fe6fd1d4bb5dc1', '<EMAIL>', 'EDM_002_ZMM', 'shr-1707-102_pk_frontagelab_20231201_serum_prod_rev1.csv', '13cd77fa33fc31e6', '<EMAIL>', '2023-12-07 16:06:43', '29fc47dfdbf2e34a', '1');
INSERT INTO `ex_blind_email_record` VALUES ('31ccdbed7d1ed7c8', '<EMAIL>', 'EDM_002_ZMM', 'shr-1707-102_pk_frontagelab_20231201_serum_prod_rev1.csv', '439c477d6eed2b8c', '<EMAIL>', '2023-12-07 16:03:20', '27bd5b4ffd8e1d19', '0');
INSERT INTO `ex_blind_email_record` VALUES ('338c6b7e40bd5946', '<EMAIL>', 'EDM_002_ZMM', 'shr-1707-102_pk_frontagelab_20231201_serum_prod_rev1.csv', '13cd77fa33fc31e6', '<EMAIL>', '2023-12-07 16:06:22', '29fc47dfdbf2e34a', '0');
INSERT INTO `ex_blind_email_record` VALUES ('6bbf77c2f7fbe459', '<EMAIL>', 'HRS8807-I-101', 'hrs8807_i_101_hrs8807_plasma_20231201_dft_rev1.csv', 'db8dd3fffdb9af9a', '<EMAIL>', '2023-12-07 17:01:37', '2dac67fef15b2372', '1');
INSERT INTO `ex_blind_email_record` VALUES ('77edf3ef7f660a76', '<EMAIL>', 'EDM_002_ZMM', 'shr-1707-102_pk_frontagelab_20231201_serum_prod_rev1.csv', 'bed676ef9fe122a', '<EMAIL>', '2023-12-07 16:05:28', '1efe7eed9bfe71c', '1');
INSERT INTO `ex_blind_email_record` VALUES ('79bccbce5dc5ebc9', '<EMAIL>', 'HRS8807-I-101', 'hrs8807_i_101_hrs8807_plasma_20231201_dft_rev1.csv', 'db8dd3fffdb9af9a', '<EMAIL>', '2023-12-07 17:00:56', '2dac67fef15b2372', '0');
INSERT INTO `ex_blind_email_record` VALUES ('a1dcd7ce3d7f47ae', '<EMAIL>', 'EDM_002_ZMM', 'shr-1707-102_pk_frontagelab_20231201_serum_prod_rev1.csv', '439c477d6eed2b8c', '<EMAIL>', '2023-12-07 16:04:01', '27bd5b4ffd8e1d19', '1');
INSERT INTO `ex_blind_email_record` VALUES ('a3fedbff1ff73157', '<EMAIL>', 'EDM_002_ZMM', 'shr-1707-102_pk_frontagelab_20231201_serum_prod_rev1.csv', 'bed676ef9fe122a', '<EMAIL>', '2023-12-07 16:05:03', '1efe7eed9bfe71c', '0');
INSERT INTO `ex_blind_email_record` VALUES ('a79ec75d6fe98055', '<EMAIL>', 'EDM_002_ZMM', 'shr-1707-102_pk_frontagelab_20231201_serum_prod_rev1.csv', '3ddfcbdcb47f9eff', '<EMAIL>', '2023-12-07 16:01:54', '2dedc7dfaff6e9d5', '0');
INSERT INTO `ex_blind_email_record` VALUES ('adfd67ddbfb9846b', '<EMAIL>', 'EDM_002_ZMM', 'shr-1707-102_pk_frontagelab_20231201_serum_prod_rev1.csv', '3ddfcbdcb47f9eff', '<EMAIL>', '2023-12-07 16:02:31', '2dedc7dfaff6e9d5', '1');

SET FOREIGN_KEY_CHECKS = 1;
