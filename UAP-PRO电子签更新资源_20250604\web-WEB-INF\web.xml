<?xml version="1.0" encoding="ISO-8859-1"?>

<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd"
         id="BioknowUAP" version="3.1">

    <listener>
        <listener-class>net.bioknow.mvc.ContextHandler</listener-class>
    </listener>
    <listener>
        <listener-class>net.bioknow.mvc.SessHandler</listener-class>
    </listener>

    <filter>
        <filter-name>prjfilter</filter-name>
        <filter-class>net.bioknow.passport.projectmng.ProjectFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>prjfilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <servlet>
        <servlet-name>action</servlet-name>
        <servlet-class>
            net.bioknow.mvc.CtrlServlet
        </servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet>
        <servlet-name>DisplayChart</servlet-name>
        <servlet-class>
            org.jfree.chart.servlet.DisplayChart
        </servlet-class>
    </servlet>

    <servlet-mapping>
        <servlet-name>action</servlet-name>
        <url-pattern>*.do</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>DisplayChart</servlet-name>
        <url-pattern>/DisplayChart</url-pattern>
    </servlet-mapping>

    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>

    <mime-mapping>
        <extension>csv</extension>
        <mime-type>text/csv</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mht</extension>
        <mime-type>message/rfc822</mime-type>
    </mime-mapping>

    <welcome-file-list>
        <welcome-file>index.htm</welcome-file>
        <welcome-file>index.html</welcome-file>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>
    <error-page>
        <error-code>404</error-code>
        <location>/404.html</location>
    </error-page>
    <error-page>
        <error-code>400</error-code>
        <location>/404.html</location>
    </error-page>

    <listener>
        <listener-class>net.bioknow.cdtms.lightpdfSign.TimerUrgingSignersStarter</listener-class>
    </listener>

</web-app>
