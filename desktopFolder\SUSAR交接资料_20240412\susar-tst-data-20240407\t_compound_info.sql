INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (1, 'SHR-A2102', 'SHR-A2102', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (2, 'UAP-20220302', 'UAP-20220302', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (3, 'SHR-7367', 'SHR-7367测试', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (4, 'SHR7280', 'SHR7280', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (5, 'SHR6390', 'SHR6390', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (6, 'EDC-20220302', 'EDC-20220302', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (7, 'SHR1020', 'SHR1020', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (8, 'SHR4640', 'SHR4640', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (9, 'HR17031', 'HR17031', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (10, 'SHR-1316', 'SHR-1316', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (11, 'SHR-A1811', 'SHR-A1811', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (12, 'HRS-1358', 'HRS-1358', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (13, 'HRS9531', 'HRS9531', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (14, 'INS068', 'INS068', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (15, 'SHR1210', 'SHR1210', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (16, 'HR19034', 'HR19034 滴眼液', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (17, 'SHR-1701', 'SHR-1701', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (18, 'HR070803', '伊立替康脂质体（HR070803）', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (19, 'SHR8554', 'SHR8554', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (20, 'Compound A', 'Compound A', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (21, 'SHR-1802', 'SHR-1802', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (22, 'EDCTEST', 'EDCTEST', '2024-03-13 16:45:26', '2024-03-13 16:45:26', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (23, 'SHR0410', 'SHR0410', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (24, 'INS062', 'INS062注射液', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (25, 'HR20014', 'HR20014注射液', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (26, 'SHR-1316(SHR-8068)', 'SHR-1316/SHR-8068', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (27, '1104', 'SHR-1316/SHR-8068', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (28, '1114', '1114', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (29, '1209', '1209', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (30, '1213', '1213', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (31, 'Compound B', 'Compound B', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (32, 'Compound C', 'Compound C', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (33, 'Compound  C', 'Compound  C', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (34, 'SHR0302', 'SHR0302', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (35, 'APTN(YN968D1)', 'APTN/YN968D1', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (36, 'HRS001', 'HRS001', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'Y');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (37, 'HR18034', 'HR18034', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (38, 'HR7777', 'HR7777', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (39, 'SHR-7367-测试', 'SHR-7367-测试', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (40, 'RTSM330TEST', 'RTSM330TEST', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (41, 'SHR-1707', 'SHR-1707', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (42, '20230713', '20230713', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (43, '20230717', '20230717', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (44, '20230719-1', '20230719-1', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (45, '20230720', '20230720', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (46, 'TEST-1', 'TEST-1', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (47, 'TEST-2', 'TEST-2', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (48, 'TEST-3', 'TEST-3', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (49, '20230727', '20230727', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (50, '20230807', '20230807', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (51, 'SHR-9839', 'SHR-9839', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (52, 'HRS002', 'HRS002', '2024-03-13 16:45:27', '2024-03-13 16:45:27', 'N');
INSERT INTO `t_compound_info`(`id`, `compound_no`, `compound_name`, `update_time`, `create_time`, `is_deleted`) VALUES (53, 'EDCTEST-102', 'NA', '2024-03-25 14:28:25', '2024-03-25 14:28:25', 'Y');
