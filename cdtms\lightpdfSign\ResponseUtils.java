package net.bioknow.cdtms.lightpdfSign;


import org.apache.commons.lang3.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

public class ResponseUtils {


    public static void response(HttpServletResponse resp, HttpStatus status) throws IOException {
        response(resp,status,null);
    }

    public static void response(HttpServletResponse resp, HttpStatus status, Object data) throws IOException {
        resp.setContentType("application/json");
        resp.setCharacterEncoding("UTF-8");

        HashMap<String, Object> msgMap = new HashMap<>();


        msgMap.put("status", status.getCode());
        msgMap.put("msg", status);

        if (ObjectUtils.isNotEmpty(data)) {
            msgMap.put("data", data);
        }

        resp.setStatus(status.getCode());
        PrintWriter out = resp.getWriter();
        out.print(JsonUtils.toJson(msgMap));

//        System.out.println(JsonUtils.toJson(msgMap));
        out.flush();
        out.close();
    }





}
