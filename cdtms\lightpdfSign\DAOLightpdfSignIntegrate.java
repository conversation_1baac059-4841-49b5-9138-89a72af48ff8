package net.bioknow.cdtms.lightpdfSign;

import java.util.List;
import java.util.Map;

import net.bioknow.webplug.configdb.DAOConfigDB;

public class DAOLightpdfSignIntegrate {

    protected final static String xmlpath = "/cdtms/LightpdfSign/configform.xml";

    protected final static String debug = "debug";

    protected final static String sn = "sn";
    protected final static String esignSecret = "esignSecret";
    protected final static String esignProject = "esignProject";
    protected final static String studyTableid = "studyTableid";
    protected final static String studyTableStudyFiledid = "studyTableStudyFiledid";
    protected final static String studyUserTableid = "studyUserTableid";
    protected final static String esignUrl = "esignUrl";
    protected final static String CDTMSUrl = "CDTMSUrl";
    protected final static String businessTypeCode = "businessTypeCode";
    protected final static String rebackUrl = "rebackUrl";
    protected final static String organizationCode = "organizationCode";
    protected final static String clinicalinteaddr_dev = "clinicalinteaddr_dev";
    protected final static String clinicalinteaddr_uat = "clinicalinteaddr_uat";
    protected final static String clinicalinteaddr_pro = "clinicalinteaddr_pro";


    protected final static String checkAccountUrl = "checkAccountUrl";
    protected final static String checkProjectid = "checkProjectid";



    public static List<Map<String, String>> listRule(String projectId) throws Exception {
        DAOConfigDB dao = new DAOConfigDB(projectId, DAOLightpdfSignIntegrate.xmlpath);
        return dao.list();
    }

    protected static List<Map<String, String>> listRule(String projectId, String sn_no) throws Exception {
        DAOConfigDB dao = new DAOConfigDB(projectId, DAOLightpdfSignIntegrate.xmlpath);

        return dao.listByTable(sn_no, sn);
    }

}
