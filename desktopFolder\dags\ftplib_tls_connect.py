from ftplib import FTP_TLS
import ssl
import socket
import os
class tyFTP(FTP_TLS):
    def connect(self, host='', port=0, timeout=-999):
        '''Connect to host.  Arguments are:
        - host: hostname to connect to (string, default previous host)
        - port: port to connect to (integer, default previous port)
        '''
        if host != '':
            self.host = host
        if port > 0:
            self.port = port
        if timeout != -999:
            self.timeout = timeout
        try:
            self.sock = socket.create_connection((self.host, self.port), self.timeout)
            self.af = self.sock.family
            #add this line!!!
            self.sock = ssl.wrap_socket(self.sock, self.keyfile, self.certfile,ssl_version=ssl.PROTOCOL_TLSv1_2)
            #add end
            self.file = self.sock.makefile('rb')
            self.welcome = self.getresp()
        except Exception as e:
            print (e)
        return self.welcome
 
class FTPSClient(object):

    def __init__(self, username, password, hostname, port=990):
        self.username = username
        self.password = password
        self.hostname = hostname
        self.port = port

    def get_conn(self, ftp_class=tyFTP):
        conn = ftp_class()
        conn.connect(self.hostname, self.port)
        conn.login(self.username, self.password)
        conn.prot_p()
        return conn

    def upload(self, content, filename_server):
        ftps = self.get_conn()
        # filename_server = filename_server.ltrip("/")
        dirname = os.path.dirname(filename_server)
        try:
            ftps.mkd(dirname)
        except Exception as e:
            if str(e) == "550 Directory already exists":
                pass
            else:
                raise Exception(repr(e))

        ftps.cwd(dirname)

        bufsize = 1024  
        fp = open(content,'rb')  
        fp.seek(0)
        try:
            ftps.storbinary('STOR '+ os.path.basename(filename_server) ,fp,bufsize)
        except Exception as e:
            if str(e) == "[Errno 0] Error":
                pass
            else:
                raise Exception("Upload error")

        fp.close()
        ftps.set_debuglevel(2)  
        ftps.close()
        
        
if __name__ == '__main__':
    username = 'erkang.zhou.hengrui.com'
    password = 'Zero2One4?'
    hostname = 'ftp01.ftp.mdsol.com' 

    ftp_client = FTPSClient(username, password, hostname)
    ftp_client.get_conn()
    