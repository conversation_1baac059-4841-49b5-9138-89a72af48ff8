#register.title=Set Sign PIN
LoginSign=Log in to the signature page
register.title=Reset Signature PIN
register.titleFirst=Set Signature PIN 
register.titleFirst2=NOT THE CAPTCHA IN THE EMAIL
register.Informed=Using this electronic signature module implies that the signatory consents to the legal equivalence of the electronic signatures created herein to their handwritten signatures.
Email=Email
#register.Checkcode=Captcha
register.Checkcode=Verification Code
register.changeErrorInfo=PIN does not match
Password=PIN
FileName=File
Name=Name
confirmPassword=Confirm PIN
register.notice=When using the UAP electronic signature module for the first time, you need to set a personal identification number (PIN), which is only valid within this module. Subsequent signature verifications will require entering this PIN. Please keep it securely and refrain from sharing it with others.
#submit=Agree & Submit
submit=Confirm
cancel=Disagree
sign.submit=Login
#register.sendcheckcode=Receive PIN Set Captcha
register.sendcheckcode=Reset PIN
#checkcodemail.title=Sign Password Set Checkcode
checkcodemail.title=Reset Signature PIN
#checkcodemail.content1=Your verification code is:
checkcodemail.content1=Your signature PIN verification code is:
#checkcodemail.content2=,Please input in a timely manner within 10 minutes!
checkcodemail.content2=. Please enter the 6-digit code on the reset signature PIN page within 10 minutes.
sign.title=Sign verify
sign.forgot_password=Forget PIN
sign.guide=Signature operation guide
#initiator=Initiator
initiator=Organizer
#InitiatDate=Initiat Date
InitiatDate=Created Date
Signature=eSign
Signatures=Signatures
abolished=Abolished
