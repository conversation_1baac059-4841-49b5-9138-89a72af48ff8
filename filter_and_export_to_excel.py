#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Filter PERFECT_CHINESE_DATASET.json using condition.txt exclusion list
and export the excluded data to Excel format with all attributes as columns
"""

import json
import codecs
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows

def load_exclusion_list(condition_file):
    """
    Load the list of IDs to exclude from condition.txt
    """
    try:
        with codecs.open(condition_file, 'r', encoding='utf-8') as f:
            exclusion_ids = [line.strip() for line in f if line.strip()]
        
        # Remove duplicates while preserving order
        unique_exclusion_ids = []
        seen = set()
        for id_val in exclusion_ids:
            if id_val not in seen:
                unique_exclusion_ids.append(id_val)
                seen.add(id_val)
        
        print(f"📋 Loaded exclusion list from {condition_file}")
        print(f"   - Total exclusion entries: {len(exclusion_ids)}")
        print(f"   - Unique exclusion IDs: {len(unique_exclusion_ids)}")
        
        return unique_exclusion_ids
        
    except Exception as e:
        print(f"❌ Error loading exclusion list: {e}")
        return []

def load_json_data(json_file):
    """
    Load the JSON dataset
    """
    try:
        with codecs.open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📊 Loaded JSON data from {json_file}")
        print(f"   - Total records: {len(data)}")
        
        return data
        
    except Exception as e:
        print(f"❌ Error loading JSON data: {e}")
        return []

def filter_excluded_data(data, exclusion_ids):
    """
    Filter data to get records whose IDs are NOT in the exclusion list
    """
    excluded_data = []
    included_data = []
    
    exclusion_set = set(exclusion_ids)
    
    for record in data:
        record_id = record.get('id', '')
        if record_id in exclusion_set:
            included_data.append(record)  # These are in the exclusion list (to be excluded from output)
        else:
            excluded_data.append(record)  # These are NOT in exclusion list (to be included in output)
    
    print(f"🔍 Filtering results:")
    print(f"   - Records in exclusion list (excluded from output): {len(included_data)}")
    print(f"   - Records NOT in exclusion list (included in output): {len(excluded_data)}")
    
    return excluded_data

def get_all_attributes(data):
    """
    Get all unique attributes from the dataset
    """
    all_attributes = set()
    for record in data:
        all_attributes.update(record.keys())
    
    # Sort attributes with important ones first
    important_attrs = ['id', 'name', 'nameen', 'note', 'uuid']
    other_attrs = sorted([attr for attr in all_attributes if attr not in important_attrs])
    
    ordered_attributes = important_attrs + other_attrs
    
    print(f"📋 Found {len(all_attributes)} unique attributes")
    
    return ordered_attributes

def create_excel_file(data, output_file):
    """
    Create Excel file with filtered data
    """
    try:
        if not data:
            print("⚠️  No data to export")
            return False
        
        # Get all attributes
        all_attributes = get_all_attributes(data)
        
        # Create DataFrame with all attributes as columns
        rows = []
        for record in data:
            row = {}
            for attr in all_attributes:
                # Use the value if it exists, otherwise use "NA"
                row[attr] = record.get(attr, "NA")
            rows.append(row)
        
        df = pd.DataFrame(rows)
        
        # Create Excel workbook
        wb = Workbook()
        ws = wb.active
        ws.title = "Filtered Data"
        
        # Add header row with formatting
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        # Write headers
        for col_num, column_name in enumerate(df.columns, 1):
            cell = ws.cell(row=1, column=col_num, value=column_name)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
        
        # Write data rows
        for row_num, row_data in enumerate(dataframe_to_rows(df, index=False, header=False), 2):
            for col_num, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_num, column=col_num, value=value)
                # Center align for better readability
                cell.alignment = Alignment(horizontal="left", vertical="center")
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # Save the workbook
        wb.save(output_file)
        
        print(f"✅ Excel file created successfully!")
        print(f"   - Output file: {output_file}")
        print(f"   - Records exported: {len(data)}")
        print(f"   - Columns: {len(all_attributes)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating Excel file: {e}")
        return False

def create_summary_report(original_count, filtered_count, exclusion_count, output_file):
    """
    Create a summary report of the filtering process
    """
    report_file = output_file.replace('.xlsx', '_summary_report.txt')
    
    try:
        with codecs.open(report_file, 'w', encoding='utf-8') as f:
            f.write("📊 DATA FILTERING SUMMARY REPORT\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("🎯 FILTERING RESULTS:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Original dataset records: {original_count}\n")
            f.write(f"Exclusion list entries: {exclusion_count}\n")
            f.write(f"Records exported to Excel: {filtered_count}\n")
            f.write(f"Records excluded: {original_count - filtered_count}\n\n")
            
            f.write("📁 OUTPUT FILES:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Excel file: {output_file}\n")
            f.write(f"Summary report: {report_file}\n\n")
            
            f.write("📋 PROCESS:\n")
            f.write("-" * 30 + "\n")
            f.write("1. Loaded exclusion list from condition.txt\n")
            f.write("2. Loaded JSON dataset from PERFECT_CHINESE_DATASET.json\n")
            f.write("3. Filtered out records whose IDs are in exclusion list\n")
            f.write("4. Exported remaining records to Excel with all attributes\n")
            f.write("5. Used 'NA' for missing attribute values\n\n")
            
            success_rate = (filtered_count / original_count * 100) if original_count > 0 else 0
            f.write(f"✅ Export success rate: {success_rate:.1f}%\n")
        
        print(f"📋 Summary report saved as: {report_file}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating summary report: {e}")
        return False

def main():
    """
    Main function to execute the filtering and export process
    """
    print("🚀 FILTERING AND EXCEL EXPORT PROCESS")
    print("=" * 60)
    
    # File paths
    condition_file = "condition.txt"
    json_file = "PERFECT_CHINESE_DATASET.json"
    output_file = "filtered_excluded_data.xlsx"
    
    # Step 1: Load exclusion list
    exclusion_ids = load_exclusion_list(condition_file)
    if not exclusion_ids:
        print("❌ Failed to load exclusion list")
        return
    
    # Step 2: Load JSON data
    data = load_json_data(json_file)
    if not data:
        print("❌ Failed to load JSON data")
        return
    
    # Step 3: Filter data (get records NOT in exclusion list)
    filtered_data = filter_excluded_data(data, exclusion_ids)
    
    # Step 4: Create Excel file
    success = create_excel_file(filtered_data, output_file)
    
    # Step 5: Create summary report
    if success:
        create_summary_report(len(data), len(filtered_data), len(exclusion_ids), output_file)
        
        print(f"\n🎊 PROCESS COMPLETED SUCCESSFULLY!")
        print(f"=" * 60)
        print(f"📊 Results:")
        print(f"   - Original records: {len(data)}")
        print(f"   - Exclusion list entries: {len(exclusion_ids)}")
        print(f"   - Records exported: {len(filtered_data)}")
        print(f"   - Excel file: {output_file}")
        
        if len(filtered_data) > 0:
            print(f"\n✨ Your Excel file is ready with {len(filtered_data)} records!")
            print(f"Each record has all {len(get_all_attributes(filtered_data))} possible attributes as columns.")
            print(f"Missing values are filled with 'NA'.")
        else:
            print(f"\n⚠️  No records to export (all records were in exclusion list)")
    else:
        print(f"\n❌ Process failed!")

if __name__ == "__main__":
    main()
