package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTRecordFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


public class DTRFlightpdfSign extends DTRecordFuncAction {

	private String funcid = null;

	public boolean canUse(int auth, String tableid, Long recordid) {

		try {



			String projectId = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng=new DAODataMng(projectId);

			List<Map<String, String>> LightpdfSignIntegrateList = DAOLightpdfSignIntegrate.listRule(projectId);
			if (CollectionUtils.isEmpty(LightpdfSignIntegrateList)) {
				return  false;
			}


			List<Map> engineList = daoDataMng.listRecord("esign_engine","obj.tableid='"+tableid+"'",null,100);

			if (CollectionUtils.isEmpty(engineList)) {
				return  false;
			}

//			Map engineMap = (Map) engineList.get(0);

			for (Map engineMap : engineList) {
				String showWhere = (String) engineMap.get("where");
				String engineId = (String) engineMap.get("id");

				int count = daoDataMng.count(tableid, "obj.id=" + recordid + " and (" + showWhere + ")");

				List esignInstanceList = daoDataMng.listRecord("esign_instance", "obj.active = 1 and obj.tableid='" + tableid + "' and obj.recordid='" + recordid + "' and obj.sign_flow_id is not null and obj.esign_engine_id="+engineMap.get("id"), null, 1);

				if (CollectionUtils.isEmpty(esignInstanceList) && (StringUtils.isEmpty(showWhere) || count>0) ) {

					this.funcid=engineId;
					return true;
				}

			}



		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}

	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBean fpb) {
		try {

			String recordid = fpb.getRecordid();
			String tableid = fpb.getTableid();
			String projectId = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng=new DAODataMng(projectId);
//			List esignInstanceList = daoDataMng.listRecord("esign_instance", "obj.active = 1 and obj.tableid='" + tableid + "' and obj.recordid='" + recordid + "' and obj.sign_flow_id is not null", null, 1);
			this.redirectByUri(request, response,"/LightpdfSignIntergrate.eSign.do?recordid="+recordid+"&tableid="+tableid+"&funid="+this.funcid);


		} catch (Exception e) {
			Log.error("",e);
		}
	}

	public FuncInfoBean getFIB(String tableid) {
		FuncInfoBean fib = new FuncInfoBean();
		try{
			fib.setName(this.getLanguage().get("Signature"));
			fib.setType(FuncInfoBean.FUNCTYPE_TOPMASKDIV);
			fib.setWinHeight(800);
			fib.setWinWidth(900);

			fib.setSimpleViewShow(true);
		} catch (Exception e) {
			Log.error("",e);
		}
		return fib;
	}

}
