package net.bioknow.cdtms.extdatabind;

import net.bioknow.mvc.ioc.AppStartFace;
import net.bioknow.passport.validate.PageIgnore;
import net.bioknow.uap.dbcore.schema.UtilInputAssist;
import net.bioknow.uap.dbdatamng.function.FuncFactory;

import javax.servlet.ServletConfig;

public class EHAppStart implements AppStartFace{

    public void onStart(ServletConfig arg) throws Exception {
        FuncFactory.addRecordFunc(new DTRFexdatabind());
        FuncFactory.addRecordFunc(new DTRFexdatabindMailQC());
        FuncFactory.addRecordFunc(new DTRFexdatabindMailEDM());
        FuncFactory.addRecordFunc(new DTRFexdatabindMailTeam());
        FuncFactory.addRecordFunc(new DTRFexdatabindTeamQC());
        FuncFactory.addRecordFunc(new DTRFexdatabindQC());
        FuncFactory.addRecordFunc(new DTRFexdatabindPreview());
        PageIgnore.addIgnore("/extdatabind.checktoken.do");
        UtilInputAssist.addFaceInputAssist(new EHIASelectEDM());
    }

}
