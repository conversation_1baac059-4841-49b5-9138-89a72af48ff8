/*
 Navicat MySQL Data Transfer

 Source Server         : 测试环境_87
 Source Server Type    : MySQL
 Source Server Version : 80028 (8.0.28)
 Source Host           : ***********:3306
 Source Schema         : susar_auto_mail

 Target Server Type    : MySQL
 Target Server Version : 80028 (8.0.28)
 File Encoding         : 65001

 Date: 28/07/2023 14:24:36
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_site_clear_backup
-- ----------------------------
DROP TABLE IF EXISTS `t_site_clear_backup`;
CREATE TABLE `t_site_clear_backup`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `site_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'site表主键',
  `compound_name` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '化合物名称',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25029 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
