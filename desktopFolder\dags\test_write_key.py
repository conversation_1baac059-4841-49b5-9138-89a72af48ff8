import airflow
from airflow.models import DAG
from airflow.operators.python import PythonOperator
import json
from airflow.utils.log.logging_mixin import LoggingMixin
import os
import re
from datetime import datetime 

args = {
    'owner': 'zhouhui',
    'start_date': datetime(2023, 6, 29)
}

dag = DAG(dag_id='test_write_key', default_args=args, schedule_interval="0 17 * * *")
# 任务1:使用PyMySQL查询文件路径和文件名,并根据文件路径上传至minio,上传时以文件名命名
def wirtekey():
	now = datetime.now()
	year = now.strftime("%Y")
	month = now.strftime("%m")  
	day = now.strftime("%d")    
	hour = now.strftime("%H")   
	minute = now.strftime("%M") 
	second = now.strftime("%S")
	key_value = year + '_' + month + '_' + day + '_' + hour + '_' + minute + '_' + second 
	upload_cmd =f'mc cp /home/<USER>/raw/mysql-devel-8.0.28-1.el9.x86_64.rpm --tags "key1=test&key2={key_value}"' 
	os.system(upload_cmd)


task2 = PythonOperator(
    task_id='wirtekey',  
    python_callable=wirtekey,
    dag=dag,  
)

task2