package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTRecordFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class DTRFlightpdfReplenish extends DTRecordFuncAction {


	public boolean canUse(int auth, String tableid, Long recordid) {

		try {

			if (!StringUtils.equals(tableid,"esign_instance")) {
				return false;
			}
			String projectId = SessUtil.getSessInfo().getProjectid();

			DAODataMng daoDataMng=new DAODataMng(projectId);
			Map esignInstanceMap = daoDataMng.getRecord(tableid, recordid);
			if (!StringUtils.equals(SessUtil.getSessInfo().getUserid(),String.valueOf(esignInstanceMap.get("userid")))) {

				return false;
			}
			String esignInstanceStatus = (String) esignInstanceMap.get("status");
			if (StringUtils.equals(esignInstanceStatus,"3")) {
				if (daoDataMng.count("esign_signer","obj.esign_instance_id="+recordid+" and obj.status=2")>0) {
					return true;
				}
			}

		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}

	public static void main(String[] args) {
		// 创建一个ScriptEngineManager
		ScriptEngineManager manager = new ScriptEngineManager();
		ScriptEngine engine = manager.getEngineByName("js");

		// JavaScript代码
		String script = "function formatNumberWithCommas(number) {\n" +
				"  // 将数字转换为字符串，并按小数点拆分成整数部分和小数部分\n" +
				"  var parts = number.toString().split('.');\n" +
				"  \n" +
				"  // 处理整数部分\n" +
				"  var integerPart = parts[0];\n" +
				"  // 将整数部分转换为字符串数组，并反转顺序\n" +
				"  integerPart = integerPart.split('').reverse().join('');\n" +
				"  \n" +
				"  // 遍历整数部分，每三位添加逗号\n" +
				"  integerPart = integerPart.replace(/(\\d{3})/g, '$1,');\n" +
				"  \n" +
				"  // 如果最后一个字符是逗号，则去掉\n" +
				"  if (integerPart.endsWith(',')) {\n" +
				"    integerPart = integerPart.slice(0, -1);\n" +
				"  }\n" +
				"  \n" +
				"  // 重新反转整数部分的顺序\n" +
				"  integerPart = integerPart.split('').reverse().join('');\n" +
				"  \n" +
				"  // 拼接整数部分和小数部分\n" +
				"  var formattedNumber = integerPart;\n" +
				"  if (parts[1]) {\n" +
				"    formattedNumber += '.' + parts[1];\n" +
				"  }\n" +
				"  \n" +
				"  return formattedNumber;\n" +
				"}\n" +
				"\n" +
				"// 示例用法\n" +
				"var number = 1234567.89;\n" +
				"formatNumberWithCommas(number);\n" +
				"\n";

		try {
			// 执行JavaScript代码
			Object result = engine.eval(script);
			// 输出结果
			System.out.println(result.toString());
		} catch (ScriptException e) {
			// 处理可能出现的脚本执行异常
			e.printStackTrace();
		}
	}

	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBean fpb) {
		try {

			String projectid = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng = new DAODataMng(projectid);

			List esignFileList = daoDataMng.listRecord("esign_file", "obj.esign_instance_id=" + fpb.getRecordid(), null, 1);

			Map esignFileMap = (Map) esignFileList.get(0);
			String signedFile = (String) esignFileMap.get("signed_file");
			String signedFileName = signedFile.split("\\|")[0].split("\\*")[0];


			Map esignInstanceMap = daoDataMng.getRecord("esign_instance", Long.valueOf(fpb.getRecordid()));
			List<Map> esignUserList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id=" + fpb.getRecordid() +" and obj.status='0'", null, 1);

			ArrayList<String> options = new ArrayList<String>();

			if (!CollectionUtils.isEmpty(esignUserList)) {
				for (Map signerMap : esignUserList) {
					options.add(signerMap.get("name") + "<" + signerMap.get("user_code") + ">");
				}


				request.setAttribute("ReSignerOptions",StringUtils.join(options,"|"));

			}

			if (!ObjectUtils.isEmpty(esignInstanceMap.get("study_id"))) {
				Map StudyMap = daoDataMng.getRecord("xsht", (Long) esignInstanceMap.get("study_id"));
				request.setAttribute("ReStudyId",esignInstanceMap.get("study_id"));
				request.setAttribute("ReStudyName",StudyMap.get("studyid"));

			}
			request.setAttribute("ReLanguage",esignInstanceMap.get("language"));
			request.setAttribute("ReType",esignInstanceMap.get("type"));
			request.setAttribute("signedFile",signedFile);
			request.setAttribute("signedFileName",signedFileName);

			this.forwardByUri(request,response,"/LightpdfSignIntergrate.signCreate.do");
		} catch (Exception e) {
			Log.error("",e);
		}
	}

	public FuncInfoBean getFIB(String tableid) {
		FuncInfoBean fib = new FuncInfoBean();


		fib.setName("补签");
		fib.setType(FuncInfoBean.FUNCTYPE_TOPMASKDIV);
		fib.setWinHeight(800);
		fib.setWinWidth(1000);
		fib.setSimpleViewShow(true);




		return fib;
	}

}
