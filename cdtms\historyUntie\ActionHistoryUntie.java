//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package net.bioknow.cdtms.historyUntie;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import net.bioknow.mvc.RootAction;
import net.bioknow.mvc.tools.Language;
import net.bioknow.passport.datamng.PassportCacheUtil;
import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.history.DAODbHistory;
import net.bioknow.uap.dbdatamng.history.JDOHistory;
import net.bioknow.uap.schemaplug.dtref.DtrefDAO;
import net.bioknow.webutil.langutil.LangCacheUtil;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

public class ActionHistoryUntie extends RootAction {
    public ActionHistoryUntie() {
    }

    public void Untie(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {

            String projectId = SessUtil.getSessInfo().getProjectid();
             DAODataMng daoDataMng = new DAODataMng(projectId);
            daoDataMng.delBatch("data_modify_track", null);

            DAODbHistory daoDbHistory = new DAODbHistory(projectId);
            String tableidDesc = "roles,ryjl";
            List tableModiHisUntieList = new ArrayList();
            String[] tableidArr = tableidDesc.split(",");

            for (String tableid : tableidArr) {
                List tableModiHisList = daoDbHistory.listModiHisByTable(tableid);
                formatModiHisByVar(tableModiHisList, projectId);
            }

            response.getOutputStream().write(String.valueOf("成功").getBytes());
            response.getOutputStream().close();
        } catch (Exception var14) {
            Log.error("", var14);
            response.getOutputStream().write(String.valueOf("失败").getBytes());
            response.getOutputStream().close();
        }

    }

    public void formatModiHisByVar(List list, String projectId) throws Exception {
        String locString = Language.getLocString();
        DAODataMng daoDataMng = new DAODataMng(projectId);
        DAODbApi daoDbApi = new DAODbApi(projectId);
        DtrefDAO dtrefDAO = new DtrefDAO(projectId);

        for(int j = 0; j < list.size(); ++j) {
            Object obj = list.get(j);
            JDOHistory his = null;
            String appendV = "";
            if (obj instanceof JDOHistory) {
                his = (JDOHistory)obj;
            } else {
                Map map = (Map)obj;
                his = (JDOHistory)map.get("jdohistory");
                String var10000;
                if (map.get("strappend") == null) {
                    var10000 = "";
                } else {
                    var10000 = (String)map.get("strappend");
                }
            }

            String tableid = his.getTableid();
            Map mapTable = daoDbApi.getMapTable(tableid);
            Long recordid = his.getRecordid();
            String studyCode = "";
            String RefField = dtrefDAO.getRefField("xsht", tableid);
            Long userid;
            if (StringUtils.isNotEmpty(RefField)) {
                Map nDataMap = daoDataMng.getRecord(tableid, recordid);
                userid = (Long)nDataMap.get(RefField);
                List<Map> studyList = daoDataMng.listRecord("xsht", "obj.id=" + userid, (String)null, 1);
                if (CollectionUtils.isNotEmpty(studyList)) {
                    studyCode = (String)((Map)studyList.get(0)).get("studyid");
                }
            }

            String modifyreason = his.getModifyreason();
            userid = his.getUserid();
            Map mapAfter = his.getAftervalueMapHtmlReplace();
            Map mapBefore = his.getBeforevalueMapHtmlReplace();
            if (modifyreason == null) {
                modifyreason = "";
            }

            String currUsername = PassportCacheUtil.getUserNameById(projectId, String.valueOf(userid));
            String keys = his.getValuekey();
            String[] keyA = keys.split(";");

            for(int k = 0; k < keyA.length; ++k) {
                String[] keyAA = keyA[k].split(",");
                String fidModify = keyAA[0];
                if (fidModify.equals("limitnum") || fidModify.equals("zw")) {
                    String fname = keyAA[1];
                    String fkey;
                    if (locString != null && locString.equals("en")) {
                        if (keyAA.length == 4) {
                            fname = keyAA[3];
                        } else {
                            fkey = "field:" + tableid + "." + fidModify;
                            fname = LangCacheUtil.getTrans(projectId, Language.getLocString(), fkey, fname);
                        }
                    }

                    fkey = keyAA[1];
                    if (mapBefore.containsKey(keyAA[0])) {
                        fkey = keyAA[0];
                    }

                    String vBefore = String.valueOf(mapBefore.get(fkey));
                    String vAfter = String.valueOf(mapAfter.get(fkey));
                    if (!vBefore.equals(vAfter)) {
                        HashMap<Object, Object> modfiyInfoMap = new HashMap();
                        modfiyInfoMap.put("tableid", tableid);
                        modfiyInfoMap.put("study_code", studyCode);
                        modfiyInfoMap.put("filed_name", fname);
                        modfiyInfoMap.put("content", vBefore);
                        modfiyInfoMap.put("change_to", vAfter);
                        modfiyInfoMap.put("record_id", recordid);
                        modfiyInfoMap.put("date", his.getCreated());
                        modfiyInfoMap.put("user_name", currUsername);
                        modfiyInfoMap.put("reason", this.getLanguage().get(modifyreason));
                        daoDataMng.save("data_modify_track", modfiyInfoMap);

                    }
                }
            }
        }
    }
}
