package net.bioknow.cdtms.ReviewDetermine;

import com.google.gson.Gson;
import net.bioknow.appplug.onlyoffice.review.EntityOnline_review;
import net.bioknow.appplug.onlyoffice.review.EntityReview_detail;
import net.bioknow.appplug.onlyoffice.review.FaceReviewDetermine;
import net.bioknow.passport.datamng.PassportCacheUtil;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.schemaplug.dtref.DtrefDAO;
import net.bioknow.uapplug.dbview.DAODbview;
import net.bioknow.uapplug.dbview.DbviewUtil;
import net.bioknow.uapplug.usersyn.CNT_Usersyn;
import net.bioknow.uapplug.usersyn.DAOUsersyn;
import net.bioknow.uapplug.usersyn.UserSynUtil;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import net.bioknow.webutil.tools.PYUtil;
import net.bioknow.webutil.tools.UUIDUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

public class EHReviewDetermine implements FaceReviewDetermine {
    @Override
    public String getKey() {
        return "HENGRUI_CDTMS";
    }

    @Override
    public String getName() {
        return "恒瑞CDTMS";
    }

    /**
     * 只要有不是数据中心的任一一个人，这条记录就是外审
     * @param review 审核总记录
     * @param loginIds 当前的登录ID
     * @return
     */
    @Override
    public List<EntityReview_detail> changePersonDetermineByLoginId(EntityOnline_review review, List<String> loginIds) {
        try {
            boolean isOut = false;
            String projectId = SessUtil.getSessInfo().getProjectid();
            List<String> tempLoginIds = new ArrayList<>();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            DtrefDAO dtrefDAO = new DtrefDAO(projectId);
            String refField = dtrefDAO.getRefField("xsht", review.tid);
            Map record = daoDataMng.getRecordCopy(review.tid, Long.valueOf(review.rid));
            Object o = record.get(refField);
            if(o==null||StringUtils.isBlank(o.toString())) return null;
            String studyId = String.valueOf(o);
            if(!loginIds.isEmpty()){
                tempLoginIds.addAll(loginIds);
                List roles = daoDataMng.listRecord("roles", "obj.studyid='" + studyId + "' and obj.active='1'", "obj.id desc", Integer.MAX_VALUE);
                if(roles!=null&&!roles.isEmpty()) {
                    for (Object role : roles) {
                        Map<String,Object> map = (Map<String, Object>) role;
                        Object o1 = map.get("member");
                        if(o1==null||StringUtils.isBlank(o1.toString())) continue;
                        String loginid = UserSynUtil.getUserloginid(projectId, o1.toString());
                        if(StringUtils.isNotBlank(loginid)) tempLoginIds.remove(loginid);
                    }
                }

                List<String> list = new ArrayList<>();
                for (String string : "MDM,随机经理,DDM,技术运营负责人,MEDM,数据管理负责人".split(",")) {
                    list.add(string.toLowerCase());
                }
                DAODbview dbview = new DAODbview(projectId);
                int ruleIndexByName = dbview.getRuleIndexByName("项目参与人");
                String search = "studyid='" + studyId + "'";
                List dataList = DbviewUtil.getDataList(projectId, String.valueOf(ruleIndexByName), search, null, Integer.MAX_VALUE, 1);
                if(!dataList.isEmpty()){
                    List<String> lidList = new ArrayList<>();
                    for (Object data : dataList) {
                        if(data instanceof Map){
                            Map dataMap = (Map) data;
                            String role = (String) dataMap.get("role");
                            if(StringUtils.isBlank(role)) continue;
                            if(!list.contains(role.toLowerCase())) continue;
                            Object o1 = dataMap.get("email");
                            if(o1==null||StringUtils.isBlank(o1.toString())) continue;
                            lidList.add(String.valueOf(o1).trim().toLowerCase());
                        }
                    }
                    tempLoginIds.removeIf(lidList::contains);
                    tempLoginIds.removeIf(StringUtils::isBlank);
                }
                isOut = !tempLoginIds.isEmpty();
            }

            // 如果是外审,要排除当前登录人
            if(isOut){
                String userLoginId = SessUtil.getSessInfo().getUserloginid();
                loginIds.remove(userLoginId);
            }


            DAOUsersyn usersyn = new DAOUsersyn(projectId);
            Map<String, String> stringStringMap = usersyn.listRule().get(0);
            String emailFid = stringStringMap.get(CNT_Usersyn.field_email);
            List<EntityReview_detail> details = new ArrayList<>();
            for (String loginId : loginIds) {
                EntityReview_detail detail = new EntityReview_detail();
                detail.pid = review.id;
                detail.loginid = loginId;
                detail.status = "0";
                detail.type = isOut?"2":"1";
                detail.mailid = UUIDUtil.get();
                Map userMap = UserSynUtil.getUserMap(projectId, loginId);
                detail.email = userMap==null?"":(String) userMap.get(emailFid);
                details.add(detail);
            }
            return details;
        }catch (Exception e){
            Log.error("",e);
        }
        return null;
    }

    @Override
    public List<EntityReview_detail> changePersonDetermineByEmail(EntityOnline_review review, List<String> emails) {
        //
        List<String> userIdList = new ArrayList<>();
        for (int i = 0; i < emails.size(); i++){

        }
        return Collections.emptyList();
    }

    @Override
    public List<Map<String, Object>> getOtherUsers() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (String value : "MDM,随机经理,DDM,技术运营负责人,MEDM,数据管理负责人".split(",")) {
            Map<String, Object> map = new HashMap<>();
            map.put("label", value);
            map.put("value", PYUtil.c2PyHead(value));
            list.add(map);
        }
        return list;
    }

    @Override
    public List<String> getOtherRole(String projectId,String studyId) {
        Log.error("getOtherRole:"+studyId);
        List<String> list = new ArrayList<>();
        try {
            String userloginid = SessUtil.getSessInfo().getUserloginid();
            DAODbview dbview = new DAODbview(projectId);
            int ruleIndexByName = dbview.getRuleIndexByName("项目参与人");
            String search = "studyid='" + studyId + "' and email='" + userloginid + "'";
            List dataList = DbviewUtil.getDataList(projectId, String.valueOf(ruleIndexByName), search, null, Integer.MAX_VALUE, 1);
            Log.error("getOtherRole1:"+dataList.size());
            if(!dataList.isEmpty()){
                String[] split = "MDM,随机经理,DDM,技术运营负责人,MEDM,数据管理负责人".split(",");
                Log.error("getOtherRole2:"+new Gson().toJson(dataList));
                List<String> idList = new ArrayList<>(Arrays.asList(split));
                for (Object data : dataList) {
                    if(data instanceof Map){
                        Map dataMap = (Map) data;
                        Log.error("getOtherRole3:"+new Gson().toJson(dataMap));
                        String role = (String) dataMap.get("role");
                        if(StringUtils.isBlank(role)) continue;
                        if(!idList.contains(role)) continue;
                        list.add(PYUtil.c2PyHead(role));
                    }
                }
            }
            Log.error("getOtherRole4:"+new Gson().toJson(list));
        }catch (Exception e){

        }
        return list;
    }
}
