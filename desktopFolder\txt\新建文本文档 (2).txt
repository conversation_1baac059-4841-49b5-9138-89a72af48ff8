WITH minio_tbl AS         (         SELECT         *,         toString(sipHash64(*)) AS lab_data_id         FROM s3(?, 'minioadmin', 'minioadmin', 'CSVWithNames')         ),         temp1 AS(         SELECT *         FROM minio_tbl         ),         result AS (         SELECT         mysql_lab_review.*,         temp1.*         FROM         (         SELECT         lab_data_id,         operate_user,         operate_type,         toString ( create_time, 'Asia/Shanghai' ) AS create_time         FROM         mysql ( '***********:3306', 'dm_platform', 'dm_operation_records', 'susaruser', 'Hr@db0316' )         WHERE         equals(file_name, ?)         AND ( equals(operate_type, 0)   OR  equals(operate_type, 1)  )         ) AS mysql_lab_review         LEFT JOIN         temp1         on temp1.lab_data_id = mysql_lab_review.lab_data_id         )         select * FROM result