package net.bioknow.cdtms.schedule;

import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbdatamng.face.AfterSaveFace;
import net.bioknow.uap.entitymng.EntityUtil;

import java.util.List;
import java.util.Map;

public class EHDTAfterSave implements AfterSaveFace {

    @Override
    public int getIndex() {
        return 0;
    }

    @Override
    public boolean isTableTrigEvent(String tableid, String projectId) {
        DAODbApi apidao = new DAODbApi(projectId);
        Map mapT = apidao.getMapTable(EntityUtil.getTableId(EntityScheduleTemplate.class));
        if (mapT == null) return false;
        List<EntityScheduleTemplate> listentity = EntityUtil.listEntity(projectId, "obj.tableid = '" + tableid + "'", "", EntityScheduleTemplate.class);
        return listentity.size() > 0;
    }

    @Override
    public void onAdd(String tableid, Map valueMap, String projectId) {
        TaskGeneratorUtil.setScheduleFinishTime(projectId, tableid, valueMap);
    }

    @Override
    public void onUpdate(String tableid, Map valueMap, Map valueMapOrg, String projectId) {
        TaskGeneratorUtil.setScheduleFinishTime(projectId, tableid, valueMap);
    }
}
