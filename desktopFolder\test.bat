"C:\Program Files (x86)\WinSCP\WinSCP.exe" /command "open ftp://erkang.zhou.hengrui.com:Zero2One4?@142.4.66.122:990 -implicit" "cd /hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand" "lcd C:\Projects\GRP_CDSC_PENGR\SHR-A1811-III-301\data\raw" "get -neweronly *SHR_A1811_III_301_*"  "exit" /log=C:\Projects\GRP_CDSC_PENGR\SHR-A1811-III-301\data\raw\log_file_%ymd%.txt
   

set MC_PATH=C:\Users\<USER>\Downloads\mc.exe

forfiles /p "C:\Projects\GRP_CDSC_PENGR\SHR-A1811-III-301\data\raw" /s /m *.* /c "cmd /c set SOURCE_FILE=@path" /d -1
echo %SOURCE_FILE%
set DEST_PATH=minios3/raw/SHR-A1811-III-301_sas.zip
set TAGS="key1=RAVE&key2=SHR-A1811-III-301"

echo Upload started at %time% on %date%...  

rem Execute mc to upload file with tags
"%MC_PATH%" cp %SOURCE_FILE% %DEST_PATH% --tags %TAGS%

if %errorlevel% neq 0 goto error
goto success

:error
echo Upload failed, retrying...
"%MC_PATH%" cp %SOURCE_FILE% %DEST_PATH% --tags %TAGS%  
if %errorlevel% neq 0 goto error 

:success
echo Upload completed at %time% on %date%.  

:end