public void getTaskList(HttpServletRequest request, HttpServletResponse response) throws IOException {
    try {
        // ... existing code ...

        // 定义特殊任务列表
        final List<String> SPECIAL_TASKS = Arrays.asList(
            "数据库解锁"
            // 可以在这里添加其他特殊任务
        );

        for (Map scheduleMap : scheduleList) {
            Map<String, Object> scheduleToJsonMap = new HashMap<>();
            String tableid = (String) scheduleMap.get("tableid");
            scheduleToJsonMap.put("studyCode", scheduleMap.get("study_id"));
            scheduleToJsonMap.put("id", scheduleMap.get("id"));
            
            // 获取并处理任务名称
            String name = (String)scheduleMap.get("name");
            if(StringUtils.isNotEmpty(name)){
                name = LangCacheUtil.getTrans(projectId, Language.getLocString(), "schedule:name:"+name, name);
            }
            scheduleToJsonMap.put("text", name);
            
            // 判断是否为特殊任务
            boolean isSpecialTask = SPECIAL_TASKS.contains(name);
            
            scheduleToJsonMap.put("owner", scheduleMap.get("owner"));
            scheduleToJsonMap.put("desc", scheduleMap.get("desc"));
            String type = (String) scheduleMap.get("type");
            scheduleToJsonMap.put("type", "cycle".equals(type) ? "task" : type);
            scheduleToJsonMap.put("cycle", scheduleMap.get("cycle"));
            scheduleToJsonMap.put("render", scheduleMap.get("render"));

            Date planstart = (Date) scheduleMap.get("planned_start_date");
            Date planend = (Date) scheduleMap.get("planned_end_date");
            Date acstart = (Date) scheduleMap.get("actual_start_date");
            Date acend = (Date) scheduleMap.get("actual_end_date");

            // 根据任务类型处理时间字段
            if (isSpecialTask) {
                // 特殊任务的时间字段设置为空字符串
                scheduleToJsonMap.put("planned_start_date", "");
                scheduleToJsonMap.put("planned_end_date", "");
                scheduleToJsonMap.put("actual_start_date", "");
                scheduleToJsonMap.put("actual_end_date", "");
                scheduleToJsonMap.put("start_date", "");
                scheduleToJsonMap.put("end_date", "");
                scheduleToJsonMap.put("progress", "0");
                scheduleToJsonMap.put("planned_duration", 0);
                scheduleToJsonMap.put("actual_duration", 0);
                scheduleToJsonMap.put("baseline_duration", 0);
            } else {
                // 普通任务的正常时间处理
                scheduleToJsonMap.put("actual_duration", scheduleMap.get("actual_duration"));
                scheduleToJsonMap.put("baseline_duration", scheduleMap.get("baseline_duration"));
                scheduleToJsonMap.put("planned_start_date", planstart != null ? SDFymdhms.format(planstart) : null);
                scheduleToJsonMap.put("planned_end_date", planend != null ? SDFymdhms.format(planend) : null);
                scheduleToJsonMap.put("planned_duration", scheduleMap.get("planned_duration") != null ? (Long) scheduleMap.get("planned_duration") / 60 : 0);
                scheduleToJsonMap.put("constraint_types", scheduleMap.get("constraint_types"));
                scheduleToJsonMap.put("actual_start_date", acstart != null ? SDFymdhms.format(acstart) : null);
                scheduleToJsonMap.put("actual_end_date", acend != null ? SDFymdhms.format(acend) : null);
                
                double progress = scheduleMap.get("progress") == null ? 0d : (double) scheduleMap.get("progress");
                scheduleToJsonMap.put("progress", String.format("%.8f", progress).replaceAll("0*$", "").replaceAll("\\.$", ""));
                
                scheduleToJsonMap.put("start_date", acstart != null ? SDFymdhms.format(acstart) : planstart == null ? null : SDFymdhms.format(planstart));
                scheduleToJsonMap.put("end_date", acend != null ? SDFymdhms.format(acend) : planend != null ? SDFymdhms.format(planend) : SDFymdhms.format(new Date()));
            }

            // 状态处理逻辑也需要考虑特殊任务
            if (isSpecialTask) {
                scheduleToJsonMap.put("status", "0"); // 特殊任务默认状态
            } else {
                // 原有的状态判断逻辑
                if (acstart == null) {
                    scheduleToJsonMap.put("status", "0");
                } else if (acstart != null && acend == null && (planend == null || (new Date()).getTime() < planend.getTime())) {
                    scheduleToJsonMap.put("status", "1");
                } else if (acstart != null && acend != null) {
                    scheduleToJsonMap.put("status", "2");
                } else if (acstart != null & acend == null && planend != null && new Date().getTime() > planend.getTime()) {
                    scheduleToJsonMap.put("status", "3");
                }
            }

            // ... rest of the existing code ...

            scheduleToJsonList.add(scheduleToJsonMap);
        }

        // ... rest of the existing code ...

    } catch (Exception e) {
        Log.error("", e);
    }
}