@echo off
REM =====================================================
REM Oracle Database Comparison Batch Script
REM For Windows environments without Python/Internet
REM =====================================================

echo =====================================================
echo Oracle Database Comparison Tool
echo =====================================================
echo.

REM Get database connection details from user
set /p TARGET_USER="Enter target database username: "
set /p TARGET_PASS="Enter target database password: "
set /p TARGET_HOST="Enter target database host (or leave blank for local): "
set /p TARGET_SERVICE="Enter target database service name: "

set /p SOURCE_USER="Enter source database username: "
set /p SOURCE_PASS="Enter source database password: "
set /p SOURCE_HOST="Enter source database host: "
set /p SOURCE_SERVICE="Enter source database service name: "

echo.
echo =====================================================
echo Creating database comparison script...
echo =====================================================

REM Create temporary SQL script with user inputs
echo -- Auto-generated database comparison script > temp_comparison.sql
echo -- Generated on %date% %time% >> temp_comparison.sql
echo. >> temp_comparison.sql

echo -- Create database link to source >> temp_comparison.sql
echo CREATE DATABASE LINK source_db_link >> temp_comparison.sql
echo CONNECT TO %SOURCE_USER% IDENTIFIED BY %SOURCE_PASS% >> temp_comparison.sql

if "%SOURCE_HOST%"=="" (
    echo USING '%SOURCE_SERVICE%'; >> temp_comparison.sql
) else (
    echo USING '(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=%SOURCE_HOST%)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=%SOURCE_SERVICE%)))'; >> temp_comparison.sql
)

echo. >> temp_comparison.sql
echo -- Test database link >> temp_comparison.sql
echo SELECT 'Database link created successfully' as status FROM dual@source_db_link; >> temp_comparison.sql
echo. >> temp_comparison.sql

REM Append the main comparison script
type quick_comparison.sql >> temp_comparison.sql

echo. >> temp_comparison.sql
echo -- Cleanup >> temp_comparison.sql
echo DROP DATABASE LINK source_db_link; >> temp_comparison.sql

echo =====================================================
echo Running database comparison...
echo =====================================================

REM Determine connection string for target database
if "%TARGET_HOST%"=="" (
    set TARGET_CONN=%TARGET_USER%/%TARGET_PASS%@%TARGET_SERVICE%
) else (
    set TARGET_CONN=%TARGET_USER%/%TARGET_PASS%@%TARGET_HOST%:1521/%TARGET_SERVICE%
)

REM Run the comparison and save results
echo Connecting to target database and running comparison...
sqlplus -S %TARGET_CONN% @temp_comparison.sql > comparison_results_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%.txt 2>&1

if %ERRORLEVEL% EQU 0 (
    echo.
    echo =====================================================
    echo Comparison completed successfully!
    echo =====================================================
    echo Results saved to: comparison_results_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%.txt
    echo.
    echo Opening results file...
    notepad comparison_results_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%.txt
) else (
    echo.
    echo =====================================================
    echo Error occurred during comparison!
    echo =====================================================
    echo Check the results file for error details.
    echo Common issues:
    echo - Incorrect database credentials
    echo - Network connectivity problems
    echo - Insufficient database privileges
    echo.
    pause
)

REM Cleanup temporary files
del temp_comparison.sql

echo.
echo =====================================================
echo Script completed. Press any key to exit.
echo =====================================================
pause
