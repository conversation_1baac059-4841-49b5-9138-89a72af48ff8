import ftplib
import ssl
import socket

class tyFTP(ftplib.FTP_TLS):
	def __init__(self, host='', user='', passwd='', acct='', keyfile=None,
		certfile=None, timeout=60):
		ftplib.FTP_TLS.__init__(self, host, user, passwd, acct, keyfile, certfile, timeout)
	def connect(self, host='', port=0, timeout=60):
		'''Connect to host.  Arguments are:
		- host: hostname to connect to (string, default previous host)
		- port: port to connect to (integer, default previous port)
		'''
		if host != '':
			self.host = host
		if port > 0:
			self.port = port
		if timeout != -999:
			self.timeout = timeout
		try:
			self.sock = socket.create_connection((self.host, self.port), self.timeout)
			self.af = self.sock.family
			#add this line!!!
			self.sock = ssl.wrap_socket(self.sock, self.keyfile, self.certfile,ssl_version=ssl.PROTOCOL_TLSv1_2)
			#add end
			self.file = self.sock.makefile('rb')
			self.welcome = self.getresp()
		except Exception as e:
			print (e)
		

ctx = ssl._create_stdlib_context(ssl.PROTOCOL_TLSv1_2)
ftps = tyFTP()
ftps.set_debuglevel(2)
ftps.set_pasv(True)
ftps.connect(host='ftp01.ftp.mdsol.com',port=990, timeout=60)
#ftps.login( user='erkang.zhou.hengrui.com', passwd='Zero2One4')
dir_path = '/hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand/'
#ftps.cwd(str(dir_path))
print(ftps.size("/hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand/clary_sage@1_SHR_1701_II_207_15624_20230524_081100.zip"))		
		
ftps.close()