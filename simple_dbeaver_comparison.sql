-- =====================================================
-- SIMPLE TABLE COMPARISON FOR DBEAVER
-- No SQL*Plus commands, no PL/SQL blocks
-- Just pure SQL SELECT statements
-- =====================================================

-- Query 1: Compare row counts for key tables
-- Shows only tables with different row counts
SELECT 
    table_name,
    source_count,
    target_count,
    ABS(source_count - target_count) as difference,
    CASE 
        WHEN source_count = target_count THEN 'SAME'
        ELSE 'DIFFERENT'
    END as status
FROM (
    SELECT 'tbl_attachment' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_attachment) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_attachment) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_log_event' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_log_event) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_log_event) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_systemcode' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_systemcode) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_systemcode) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_visit_set' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_study_visit_set) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_visit_set) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_eclinichistory' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_eclinichistory) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_eclinichistory) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_crf_visit' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_study_crf_visit) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_crf_visit) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_xmgt' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_xmgt) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_xmgt) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_partner' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_study_partner) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_partner) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_proj_plan' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_proj_plan) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_proj_plan) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_risk_management' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_risk_management) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_risk_management) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_qc' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_study_qc) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_qc) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_schedule' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_schedule) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_schedule) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_crf_design' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_crf_design) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_crf_design) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_manual_rev_plan' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_manual_rev_plan) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_manual_rev_plan) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_edm_account' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_edm_account) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_edm_account) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_dm_report' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_dm_report) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_dm_report) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_employe_growth' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_employe_growth) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_employe_growth) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_training' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_study_training) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_training) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_db_qc' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_db_qc) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_db_qc) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_final_report' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_final_report) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_final_report) as target_count
    FROM dual
)
WHERE source_count != target_count  -- Only show tables with differences
ORDER BY ABS(source_count - target_count) DESC;
