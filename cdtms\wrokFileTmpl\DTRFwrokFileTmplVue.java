package net.bioknow.cdtms.wrokFileTmpl;

import net.bioknow.cdtms.lightpdfSign.DAOLightpdfSignIntegrate;
import net.bioknow.services.core.ApiResult;
import net.bioknow.services.uap.dbdatamng.function.DTRecordFuncActionNew;
import net.bioknow.services.uap.dbdatamng.function.FuncInfoBeanNew;
import net.bioknow.services.uap.dbdatamng.function.FuncParamBeanNew;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.schemaplug.dtref.DtrefDAO;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class DTRFwrokFileTmplVue extends DTRecordFuncActionNew {



	public boolean canUse(int auth, String tableid, Long recordid) {

		try {
			String projectid = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng = new DAODataMng(projectid);
			int TmplCount = daoDataMng.count("dm_template_file", "obj.table_id='" + tableid + "'");

			if(TmplCount>0){

				return  true;
			}

		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return false;
	}



	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBeanNew fpb) {
		try {

			String recordid = fpb.getRecordid();
			String tableid = fpb.getTableid();
			String projectId = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng=new DAODataMng(projectId);
			DtrefDAO dtrefDAO = new DtrefDAO(projectId);

			HashMap map = new HashMap();
			String RefField = dtrefDAO.getRefField("xsht", tableid);
				Map DataMap = daoDataMng.getRecord(tableid, Long.valueOf(recordid));
				Map studyMap = daoDataMng.getRecord("xsht", (Long)DataMap.get(RefField));
					Date startDate = (Date) studyMap.get("start_date");
					// 创建一个SimpleDateFormat对象，指定日期格式
					SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

					// 使用SimpleDateFormat将Date对象转换为字符串
					String startDatestr = dateFormat.format(startDate);






			map.put("url", "/uapvue/index.html#/list?tableid=dm_template_file&where=obj.table_id%3D%27"+tableid+"%27%20and%20NVL(obj.overdate%2C%20sysdate)%3E%3Dto_date(%27"+startDatestr+"%27%2C%27yyyy-mm-dd%27)");

			response.getOutputStream().write(ApiResult.ok("ok", map).getBytes(StandardCharsets.UTF_8));
			response.getOutputStream().close();


		} catch (Exception e) {
			Log.error("",e);
		}
	}

	public FuncInfoBeanNew getFIB(String tableid) {
		FuncInfoBeanNew fib = new FuncInfoBeanNew("cdtms_DTRFlightpdfSignVue");
		try{
			fib.setName("工作模板");
			fib.setType(FuncInfoBeanNew.FUNCTYPE_VUE_MODAL);
			fib.setWinHeight(800);
			fib.setWinWidth(900);
			fib.setSimpleViewShow(true);fib.setAppendParams(false);
		} catch (Exception e) {
			Log.error("",e);
		}
		return fib;
	}

}
