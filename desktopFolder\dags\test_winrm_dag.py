from airflow import DAG
from datetime import datetime
from airflow.providers.microsoft.winrm.hooks.winrm import WinR<PERSON>Hook
from airflow.providers.microsoft.winrm.operators.winrm import WinRMOperator

default_args = {
    'owner': 'zhouhui',
    'start_date': datetime(2023, 6, 12)
}

dag = DAG('test_winrm_dag', default_args=default_args)

winrm_hook = WinRMHook(
    ssh_conn_id='winrm_conn'
)
run_script = WinRMOperator(
    task_id='run_script',
    winrm_hook=winrm_hook, 
    command='powershell.exe python C:\\Users\\<USER>\\Desktop\\dags\\test.py',
    dag=dag  
)
run_script