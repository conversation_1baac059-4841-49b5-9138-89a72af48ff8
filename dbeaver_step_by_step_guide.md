# 🎯 DBeaver Step-by-Step Comparison Guide

## ❌ **Problem Solved**
The error `ORA-00922: 选项缺失或无效` occurs because DBeaver doesn't support SQL*Plus commands like:
- `SET PAGESIZE`
- `SET LINESIZE` 
- `SET SERVEROUTPUT`
- `PROMPT`

## ✅ **Solution: Use Pure SQL Queries**

### **Step 1: Verify Schema Access**
Copy and paste this query in DBeaver:

```sql
SELECT 'Schema Access Check' as check_type, 
       owner as schema_name, 
       COUNT(*) as table_count 
FROM all_tables 
WHERE owner IN ('CDTMS_PILOT', 'CDTMS_TEMP') 
GROUP BY owner
ORDER BY owner;
```

**Expected Result:**
```
SCHEMA_NAME    TABLE_COUNT
CDTMS_PILOT    XXX
CDTMS_TEMP     XXX
```

### **Step 2: Quick Test - Compare Key Tables**
Copy and paste this query:

```sql
SELECT 
    table_name,
    source_count,
    target_count,
    ABS(source_count - target_count) as difference,
    CASE 
        WHEN source_count = target_count THEN 'SAME'
        ELSE 'DIFFERENT'
    END as status
FROM (
    SELECT 'tbl_attachment' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_attachment) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_attachment) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_log_event' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_log_event) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_log_event) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_systemcode' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_systemcode) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_systemcode) as target_count
    FROM dual
);
```

### **Step 3: Check Individual Tables**
For each table you want to check, use this pattern:

```sql
-- Replace 'tbl_attachment' with your table name
SELECT 'tbl_attachment' as table_name, 'PILOT' as schema, COUNT(*) as row_count 
FROM CDTMS_PILOT.tbl_attachment
UNION ALL
SELECT 'tbl_attachment' as table_name, 'TEMP' as schema, COUNT(*) as row_count 
FROM CDTMS_TEMP.tbl_attachment;
```

### **Step 4: Find Different Records (If Counts Differ)**
If a table has different row counts, find the specific differences:

```sql
-- Records in PILOT but not in TEMP
SELECT COUNT(*) as records_only_in_pilot
FROM (
    SELECT * FROM CDTMS_PILOT.tbl_attachment 
    MINUS 
    SELECT * FROM CDTMS_TEMP.tbl_attachment
);

-- Records in TEMP but not in PILOT
SELECT COUNT(*) as records_only_in_temp
FROM (
    SELECT * FROM CDTMS_TEMP.tbl_attachment 
    MINUS 
    SELECT * FROM CDTMS_PILOT.tbl_attachment
);
```

## 🚀 **Quick Start for DBeaver**

### **Method 1: Use the Simple Script**
1. Open DBeaver
2. Connect to your database
3. Open `simple_dbeaver_comparison.sql`
4. Execute the entire script (Ctrl+Enter)

### **Method 2: Use Individual Queries**
1. Open `dbeaver_individual_queries.sql`
2. Copy each "Query X" section separately
3. Paste and run them one by one
4. Start with Query 1 to verify access

### **Method 3: Manual One-by-One Checks**
For each table from 比对范围.txt, run:

```sql
-- Check tbl_attachment
SELECT COUNT(*) FROM CDTMS_PILOT.tbl_attachment;   -- Note the count
SELECT COUNT(*) FROM CDTMS_TEMP.tbl_attachment;    -- Compare with above

-- Check tbl_log_event  
SELECT COUNT(*) FROM CDTMS_PILOT.tbl_log_event;
SELECT COUNT(*) FROM CDTMS_TEMP.tbl_log_event;

-- Continue for other tables...
```

## 📊 **Expected Results**

### **If Tables Are Identical:**
```
TABLE_NAME         SOURCE_COUNT    TARGET_COUNT    DIFFERENCE    STATUS
tbl_attachment     1250           1250            0             SAME
tbl_log_event      15420          15420           0             SAME
```

### **If Tables Have Differences:**
```
TABLE_NAME         SOURCE_COUNT    TARGET_COUNT    DIFFERENCE    STATUS
tbl_attachment     1250           1248            2             DIFFERENT
tbl_log_event      15420          15380           40            DIFFERENT
```

## 🔧 **Troubleshooting Common DBeaver Issues**

### **Error: "Table or view does not exist"**
- **Cause**: Table doesn't exist in one or both schemas
- **Solution**: Check table existence first:
```sql
SELECT table_name FROM all_tables 
WHERE owner = 'CDTMS_PILOT' AND table_name = 'TBL_ATTACHMENT';
```

### **Error: "Insufficient privileges"**
- **Cause**: No access to schemas
- **Solution**: Ask DBA for SELECT privileges on both schemas

### **Error: "Invalid identifier"**
- **Cause**: Wrong schema or table name
- **Solution**: Verify exact names:
```sql
SELECT owner, table_name FROM all_tables 
WHERE table_name LIKE '%ATTACHMENT%';
```

## 📋 **DBeaver-Specific Tips**

### **1. Execute Queries Properly**
- **Select the query text** before pressing Ctrl+Enter
- **Or place cursor** in the query and press Ctrl+Enter
- **Use semicolons** to separate multiple queries

### **2. View Results Clearly**
- **Expand result columns** by dragging column borders
- **Sort results** by clicking column headers
- **Export results** using right-click → Export

### **3. Handle Large Results**
- **Limit rows** if tables are very large:
```sql
SELECT COUNT(*) FROM CDTMS_PILOT.large_table WHERE ROWNUM <= 1000;
```

### **4. Save Queries**
- **Save frequently used queries** as SQL files
- **Create bookmarks** for important comparisons
- **Use query history** to re-run previous checks

## 🎯 **Quick Comparison Template**

Copy this template and modify table names as needed:

```sql
-- Quick comparison template
SELECT 
    'TABLE_NAME_HERE' as table_name,
    (SELECT COUNT(*) FROM CDTMS_PILOT.TABLE_NAME_HERE) as pilot_count,
    (SELECT COUNT(*) FROM CDTMS_TEMP.TABLE_NAME_HERE) as temp_count,
    (SELECT COUNT(*) FROM CDTMS_PILOT.TABLE_NAME_HERE) - 
    (SELECT COUNT(*) FROM CDTMS_TEMP.TABLE_NAME_HERE) as difference
FROM dual;
```

## ✅ **Success Indicators**

### **All Tables Identical:**
- All difference values = 0
- All status = 'SAME'
- No error messages

### **Tables Need Attention:**
- Difference values > 0
- Status = 'DIFFERENT'
- Investigate using MINUS queries

This approach works perfectly with DBeaver and avoids all SQL*Plus compatibility issues! 🚀
