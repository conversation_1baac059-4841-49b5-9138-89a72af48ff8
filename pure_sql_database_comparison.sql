-- =====================================================
-- PURE SQL DATABASE COMPARISON SOLUTION
-- No Python, No Internet Required - Oracle SQL Only
-- =====================================================

-- =====================================================
-- STEP 1: SETUP DATABASE LINK
-- =====================================================
-- Replace with your actual source database connection details
/*
CREATE DATABASE LINK source_db_link
CONNECT TO your_source_username IDENTIFIED BY your_source_password
USING '(DESCRIPTION=
    (ADDRESS=(PROTOCOL=TCP)(HOST=your_source_host)(PORT=1521))
    (CONNECT_DATA=(SERVICE_NAME=your_source_service_name))
)';
*/

-- Test the database link
SELECT 'Database link test successful' as status FROM dual@source_db_link;

-- =====================================================
-- STEP 2: CREATE RESULT TABLES (Run once)
-- =====================================================

-- Drop tables if they exist (optional)
-- DROP TABLE comparison_summary;
-- DROP TABLE comparison_details;

-- Create summary table
CREATE TABLE comparison_summary (
    comparison_date DATE DEFAULT SYSDATE,
    comparison_type VARCHAR2(50),
    total_items NUMBER,
    matches NUMBER,
    mismatches NUMBER,
    match_percentage NUMBER(5,2),
    status VARCHAR2(20)
);

-- Create details table
CREATE TABLE comparison_details (
    detail_id NUMBER GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    comparison_date DATE DEFAULT SYSDATE,
    table_name VARCHAR2(128),
    comparison_type VARCHAR2(50),
    source_value VARCHAR2(4000),
    target_value VARCHAR2(4000),
    match_status VARCHAR2(10),
    difference_info CLOB
);

-- =====================================================
-- STEP 3: TABLE EXISTENCE COMPARISON
-- =====================================================

-- Clear previous results
DELETE FROM comparison_details WHERE comparison_type = 'TABLE_EXISTENCE';

-- Insert table existence comparison
INSERT INTO comparison_details (table_name, comparison_type, source_value, target_value, match_status, difference_info)
WITH source_tables AS (
    SELECT table_name FROM user_tables@source_db_link
    WHERE table_name NOT LIKE 'BIN$%'
),
target_tables AS (
    SELECT table_name FROM user_tables
    WHERE table_name NOT LIKE 'BIN$%'
),
table_comparison AS (
    SELECT 
        COALESCE(s.table_name, t.table_name) as table_name,
        CASE WHEN s.table_name IS NOT NULL THEN 'EXISTS' ELSE 'MISSING' END as source_status,
        CASE WHEN t.table_name IS NOT NULL THEN 'EXISTS' ELSE 'MISSING' END as target_status
    FROM source_tables s
    FULL OUTER JOIN target_tables t ON s.table_name = t.table_name
)
SELECT 
    table_name,
    'TABLE_EXISTENCE',
    source_status,
    target_status,
    CASE WHEN source_status = target_status THEN 'MATCH' ELSE 'MISMATCH' END,
    CASE 
        WHEN source_status = 'MISSING' THEN 'Table exists only in target database'
        WHEN target_status = 'MISSING' THEN 'Table exists only in source database'
        ELSE 'Table exists in both databases'
    END
FROM table_comparison;

COMMIT;

-- =====================================================
-- STEP 4: ROW COUNT COMPARISON
-- =====================================================

-- Clear previous results
DELETE FROM comparison_details WHERE comparison_type = 'ROW_COUNT';

-- Get list of common tables and compare row counts
DECLARE
    v_source_count NUMBER;
    v_target_count NUMBER;
    v_sql VARCHAR2(1000);
    
    CURSOR common_tables IS
        SELECT table_name FROM user_tables
        WHERE table_name IN (SELECT table_name FROM user_tables@source_db_link)
        AND table_name NOT LIKE 'BIN$%'
        ORDER BY table_name;
BEGIN
    FOR rec IN common_tables LOOP
        BEGIN
            -- Get source count
            v_sql := 'SELECT COUNT(*) FROM ' || rec.table_name || '@source_db_link';
            EXECUTE IMMEDIATE v_sql INTO v_source_count;
            
            -- Get target count
            v_sql := 'SELECT COUNT(*) FROM ' || rec.table_name;
            EXECUTE IMMEDIATE v_sql INTO v_target_count;
            
            -- Insert comparison result
            INSERT INTO comparison_details (
                table_name, comparison_type, source_value, target_value, match_status, difference_info
            ) VALUES (
                rec.table_name,
                'ROW_COUNT',
                TO_CHAR(v_source_count),
                TO_CHAR(v_target_count),
                CASE WHEN v_source_count = v_target_count THEN 'MATCH' ELSE 'MISMATCH' END,
                'Source: ' || v_source_count || ' rows, Target: ' || v_target_count || ' rows, Difference: ' || ABS(v_source_count - v_target_count)
            );
            
        EXCEPTION
            WHEN OTHERS THEN
                INSERT INTO comparison_details (
                    table_name, comparison_type, source_value, target_value, match_status, difference_info
                ) VALUES (
                    rec.table_name,
                    'ROW_COUNT',
                    'ERROR',
                    'ERROR',
                    'ERROR',
                    'Error: ' || SQLERRM
                );
        END;
    END LOOP;
    
    COMMIT;
END;
/

-- =====================================================
-- STEP 5: COLUMN STRUCTURE COMPARISON
-- =====================================================

-- Clear previous results
DELETE FROM comparison_details WHERE comparison_type = 'COLUMN_STRUCTURE';

-- Compare column structures
INSERT INTO comparison_details (table_name, comparison_type, source_value, target_value, match_status, difference_info)
WITH source_columns AS (
    SELECT 
        table_name,
        column_name,
        data_type || '(' || NVL(TO_CHAR(data_length), 'NULL') || ')' || 
        CASE WHEN nullable = 'Y' THEN ' NULL' ELSE ' NOT NULL' END as column_def
    FROM user_tab_columns@source_db_link
    WHERE table_name NOT LIKE 'BIN$%'
),
target_columns AS (
    SELECT 
        table_name,
        column_name,
        data_type || '(' || NVL(TO_CHAR(data_length), 'NULL') || ')' || 
        CASE WHEN nullable = 'Y' THEN ' NULL' ELSE ' NOT NULL' END as column_def
    FROM user_tab_columns
    WHERE table_name NOT LIKE 'BIN$%'
),
column_comparison AS (
    SELECT 
        COALESCE(s.table_name, t.table_name) as table_name,
        COALESCE(s.column_name, t.column_name) as column_name,
        NVL(s.column_def, 'MISSING') as source_def,
        NVL(t.column_def, 'MISSING') as target_def,
        CASE 
            WHEN s.column_def = t.column_def THEN 'MATCH'
            WHEN s.column_def IS NULL THEN 'TARGET_ONLY'
            WHEN t.column_def IS NULL THEN 'SOURCE_ONLY'
            ELSE 'DIFFERENT'
        END as comparison_result
    FROM source_columns s
    FULL OUTER JOIN target_columns t 
        ON s.table_name = t.table_name AND s.column_name = t.column_name
)
SELECT 
    table_name,
    'COLUMN_STRUCTURE',
    source_def,
    target_def,
    CASE WHEN comparison_result = 'MATCH' THEN 'MATCH' ELSE 'MISMATCH' END,
    'Column: ' || column_name || ', Status: ' || comparison_result
FROM column_comparison
WHERE comparison_result != 'MATCH';

COMMIT;

-- =====================================================
-- STEP 6: SIMPLE DATA CHECKSUM COMPARISON
-- =====================================================

-- Clear previous results
DELETE FROM comparison_details WHERE comparison_type = 'DATA_CHECKSUM';

-- Compare data checksums for tables with ROWID (simple approach)
DECLARE
    v_source_checksum NUMBER;
    v_target_checksum NUMBER;
    v_source_count NUMBER;
    v_target_count NUMBER;
    v_sql VARCHAR2(1000);
    
    CURSOR checksum_tables IS
        SELECT table_name FROM user_tables
        WHERE table_name IN (SELECT table_name FROM user_tables@source_db_link)
        AND table_name NOT LIKE 'BIN$%'
        AND ROWNUM <= 10  -- Limit to first 10 tables for performance
        ORDER BY table_name;
BEGIN
    FOR rec IN checksum_tables LOOP
        BEGIN
            -- Get source checksum and count
            v_sql := 'SELECT SUM(ORA_HASH(ROWID)), COUNT(*) FROM ' || rec.table_name || '@source_db_link';
            EXECUTE IMMEDIATE v_sql INTO v_source_checksum, v_source_count;
            
            -- Get target checksum and count
            v_sql := 'SELECT SUM(ORA_HASH(ROWID)), COUNT(*) FROM ' || rec.table_name;
            EXECUTE IMMEDIATE v_sql INTO v_target_checksum, v_target_count;
            
            -- Insert comparison result
            INSERT INTO comparison_details (
                table_name, comparison_type, source_value, target_value, match_status, difference_info
            ) VALUES (
                rec.table_name,
                'DATA_CHECKSUM',
                v_source_checksum || ':' || v_source_count,
                v_target_checksum || ':' || v_target_count,
                CASE WHEN v_source_checksum = v_target_checksum AND v_source_count = v_target_count 
                     THEN 'MATCH' ELSE 'MISMATCH' END,
                'Checksum comparison based on ROWID hash'
            );
            
        EXCEPTION
            WHEN OTHERS THEN
                INSERT INTO comparison_details (
                    table_name, comparison_type, source_value, target_value, match_status, difference_info
                ) VALUES (
                    rec.table_name,
                    'DATA_CHECKSUM',
                    'ERROR',
                    'ERROR',
                    'ERROR',
                    'Error: ' || SQLERRM
                );
        END;
    END LOOP;
    
    COMMIT;
END;
/

-- =====================================================
-- STEP 7: GENERATE SUMMARY STATISTICS
-- =====================================================

-- Clear previous summary
DELETE FROM comparison_summary;

-- Generate summary for each comparison type
INSERT INTO comparison_summary (comparison_type, total_items, matches, mismatches, match_percentage, status)
SELECT 
    comparison_type,
    COUNT(*) as total_items,
    SUM(CASE WHEN match_status = 'MATCH' THEN 1 ELSE 0 END) as matches,
    SUM(CASE WHEN match_status != 'MATCH' THEN 1 ELSE 0 END) as mismatches,
    ROUND(SUM(CASE WHEN match_status = 'MATCH' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as match_percentage,
    CASE 
        WHEN SUM(CASE WHEN match_status != 'MATCH' THEN 1 ELSE 0 END) = 0 THEN 'PERFECT'
        WHEN SUM(CASE WHEN match_status = 'MATCH' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) >= 95 THEN 'GOOD'
        WHEN SUM(CASE WHEN match_status = 'MATCH' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) >= 80 THEN 'FAIR'
        ELSE 'POOR'
    END as status
FROM comparison_details
WHERE match_status != 'ERROR'
GROUP BY comparison_type;

COMMIT;

-- =====================================================
-- STEP 8: VIEW RESULTS
-- =====================================================

-- Summary Report
PROMPT =====================================================
PROMPT DATABASE COMPARISON SUMMARY REPORT
PROMPT =====================================================

SELECT 
    'Comparison Date: ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') as report_info
FROM dual;

SELECT 
    comparison_type as "Comparison Type",
    total_items as "Total Items",
    matches as "Matches",
    mismatches as "Mismatches", 
    match_percentage || '%' as "Match %",
    status as "Status"
FROM comparison_summary
ORDER BY comparison_type;

-- Detailed Mismatches
PROMPT 
PROMPT =====================================================
PROMPT DETAILED MISMATCHES
PROMPT =====================================================

SELECT 
    table_name as "Table Name",
    comparison_type as "Type",
    source_value as "Source Value",
    target_value as "Target Value",
    difference_info as "Details"
FROM comparison_details
WHERE match_status = 'MISMATCH'
ORDER BY table_name, comparison_type;

-- Tables with Most Issues
PROMPT 
PROMPT =====================================================
PROMPT TABLES WITH MOST ISSUES
PROMPT =====================================================

SELECT 
    table_name as "Table Name",
    COUNT(*) as "Issue Count",
    LISTAGG(comparison_type, ', ') WITHIN GROUP (ORDER BY comparison_type) as "Issue Types"
FROM comparison_details
WHERE match_status = 'MISMATCH'
GROUP BY table_name
ORDER BY COUNT(*) DESC;

-- Error Summary
PROMPT 
PROMPT =====================================================
PROMPT ERRORS ENCOUNTERED
PROMPT =====================================================

SELECT 
    table_name as "Table Name",
    comparison_type as "Type",
    difference_info as "Error Details"
FROM comparison_details
WHERE match_status = 'ERROR'
ORDER BY table_name;

-- =====================================================
-- STEP 9: EXPORT RESULTS TO FILE (Optional)
-- =====================================================

-- To export results to a file, use SQL*Plus spool command:
/*
SPOOL comparison_results.txt
-- Run the SELECT statements above
SPOOL OFF
*/

-- =====================================================
-- STEP 10: CLEANUP (Optional)
-- =====================================================

-- To clean up after comparison:
/*
DROP TABLE comparison_summary;
DROP TABLE comparison_details;
DROP DATABASE LINK source_db_link;
*/

PROMPT 
PROMPT =====================================================
PROMPT COMPARISON COMPLETED SUCCESSFULLY!
PROMPT =====================================================
PROMPT Check the results above for any mismatches or errors.
PROMPT Results are also stored in comparison_summary and comparison_details tables.
PROMPT =====================================================
