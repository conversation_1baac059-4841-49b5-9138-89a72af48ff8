filename = kwargs['filename']
	sas = saspy.SASsession()
	sas.submit('''%global system studyid root version dir lang;''')
	sas.submit('''proc datasets lib=work nolist kill; run;quit;%let dir=%sysfunc(getoption(work));x "cd &dir";x "mkdir ./pgm";x 'mc find minios3/pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';%include "&dir/pgm/*.sas";''')
	sas.submit('''%let studyid=SHR-1501-I-103;''')
	sas.submit('''%m_post2s3(studyid=&studyid.);''')
	sas.submit('''%m_gets3data(studyid=&studyid.,data=@);''')
	sas.submit('''%let lang="CH";''')
	sas.submit('''option mprint symbolgen validvarname=v7;''')
	sas.submit('%m_medical_joinaecm;')
	sas.submit('%clin_consistency;')
	sas.submit('''%m_medical_aedrugeq;''')
	sas.submit('''%include "./pgm/m_out2s3.sas);''')
	sas.submit('''%m_out2s3(studyid=&studyid.);''')
	sas.submit('''%m_med_formfie;''')
	sas.submit('''%m_med_stadm;''')
	sas.submit('''%m_med_starand;''')
	sas.submit('''%m_med_stasub;''')
	sas.submit('''%m_med_staae;''')
	sas.submit('''%m_med_stalb;''')
	sas.submit('''%m_create_hrtaubuild_lab;''')
	sas.submit('''%m_derive_hrtaubuild(folder=csvdata_lab);''')
	sas.submit('''%m_create_hrtau_lab(is_als=N);''')
	sas.submit('''%M_std_dvs_labreview;''')
	sas.submit('''%m_out2s3T(studyid=&studyid.,suffix=labctc);''')
	sas.submit('''%m_mean_meanlb;''')
	sas.submit('''%m_out2s3T(studyid=&studyid.,suffix=lbvis);''')
	sas.submit('''%m_med_stavs;''')
	sas.submit('''%M_std_dvs_vsview;''')
	sas.submit('''%m_out2s3T(studyid=&studyid.,suffix=vsview);''')
	sas.submit('''%m_med_staeg;''')
	sas.submit('''%M_std_dvs_egview;''')
	sas.submit('''%m_out2s3T(studyid=&studyid.,suffix=egview);''')
	
	sas.submitLOG('''%let studyid = SHR-1501-I-103;''')
	sas.submit('''%M_gen_pro_coding(formoid=ae,folder=,output=Y);''')
	sas.submit('''%readin_coding;''')
	sas.submit('''%m_med_dseos_pre_process(dseos=dseos,dsstdat =dsstdat);''')
	sas.submit('''%m_med_dsrand_pre_process(dsrand=DSENROLL,regime = DSPHASE);''')

	sas.submit('''data derived.ae; set derived.ae; rename sitenumber = siteid; run;''')
	sas.submit('''data derived.exi; set derived.exi; rename sitenumber = siteid; run;''')
	sas.submit('''data derived.dseot; set derived.dseot; rename sitenumber = siteid; run;''')
	sas.submit('''data derived.dseot_bkp; set derived.dseot; run;''')
	sas.submit('''data derived.dseot; set derived.dseot; if svnum = 'SHR-1501治疗结束页'; run;''')
	sas.submit('''%m_med_hrtau_dseot_pre_process(dseot=dseot, dsstdat = ,dsdecod = dsdecod,dsterm = dsterm);''')
	sas.submit('''%m_med_hrtau_ae_pre_process(aerel = aerel, ctcae = aetoxgr);''')
	sas.submit('''%m_med_hrtau_ex_pre_process(data = exi,expdost=expdost,exdosadj=EXDOSADJ);''')
	sas.submit('''%ae_ex_process;''')
	sas.submit('''%label_ae_ex;''')
	sas.submit('''data ae_ex_med1; set out.ae_ex; run;''')
	sas.submit('''data ex_med1; set out.ex; run;''')

	sas.submit('''data derived.exi_bcg; set derived.exi_bcg; rename sitenumber = siteid; run;''')
	sas.submit('''data derived.dseot; set derived.dseot_bkp; if svnum = '卡介苗治疗结束页'; run;''')
	sas.submit('''%m_med_hrtau_dseot_pre_process(dseot=dseot, dsstdat = dsstdat,dsdecod = dsdecod,dsterm = dsterm);''')
	sas.submit('''%m_med_hrtau_ae_pre_process(aerel = aerel1, ctcae = aetoxgr);''')
	sas.submit('''%m_med_hrtau_ex_pre_process(data = exi_bcg,expdost=expdost,exdosadj=);''')
	sas.submit('''%ae_ex_process;''')
	sas.submit('''%label_ae_ex;''')
	sas.submit('''data ae_ex_med2; set out.ae_ex; run;''')
	sas.submit('''data ex_med2; set out.ex; run;''')

	sas.submit('''data out.ae_ex(label='AE_EX'); set ae_ex_med2 ae_ex_med1; run;''')
	sas.submit('''data out.ex(label='EX'); set ex_med2 ex_med1; run;''')

	sas.submit('''%m_out2s3T(studyid=SHR-1501-I-103,suffix=AEEX);''')
	sas.endsas() 