%global system studyid root version dir ;
proc datasets lib=work nolist kill; quit;
%let dir=%sysfunc(getoption(work));
x "cd &dir";%put &dir;x "mkdir ./pgm";x 'mc find minios3-t/pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';%include "&dir/pgm/*.sas";


/*比对程序*/
%let studyid=&studyid.;
%let jsonPath=&jsonPath.;
option mprint symbolgen validvarname=v7;
%m_compare_newdata_post2s3(studyid=&studyid.);
%m_dsrand_compare_gets3data(studyid=&studyid.,data=@);
%M_dsrand_compare(studyid=&studyid.,jsonPath=&jsonPath.);

