<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=utf-8" %>
<%@ page language="java" pageEncoding="utf-8" %>
<%@ taglib uri="/webutil/tlib/bioknow-tlib.tld" prefix="bt" %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Composer</title>
    <link href="/cdtms/LightpdfSign/js/bootstrap.min.css" rel="stylesheet">
    <script type="text/javascript" src="/cdtms/LightpdfSign/js/jquery.min.js"></script>

    <link href="/cdtms/LightpdfSign/js/select2.min.css" rel="stylesheet"/>
    <script src="/cdtms/LightpdfSign/js/select2.min.js"></script>


    <link href="/cdtms/LightpdfSign/js/style.css" rel="stylesheet">

    <script src="/cdtms/LightpdfSign/js/dropzone.min.js"></script>
    <link
            rel="stylesheet"
            href="/cdtms/LightpdfSign/js/dropzone.min.css"
            type="text/css"
    />

    <script src="/cdtms/LightpdfSign/js/flatpickr.js"></script>
    <link rel="stylesheet" href="/cdtms/LightpdfSign/js/flatpickr.min.css">

    <link rel=stylesheet type="text/css" href="/public/css/public.css"></link>
    <style>
        body {
            /*background-color: #f8f9fa;*/
        }

        .container {
            margin-top: 50px;
        }

        label {
            font-weight: bold;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .editor-wrapper {
            border: 1px solid #ccc;
        }

        #toolbar-container {
            border-bottom: 1px solid #ccc;
        }

        #editor-container {
            height: 200px;
        }

        button[type="submit"] {
            display: block;
            width: 100%;
            padding: 10px;
            font-size: 18px;
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        /* Custom styles for Select2 and Bootstrap integration */
        .select2-container {
            width: 100% !important;
        }
        .select2-search__field{
            /* height: 28px; */
            border:none !important;
            padding-right:0px !important;
            /* background-color: #ffffff; */
            /* border-radius: 4px; */
            /* outline: none; */
            /* padding-left: 5px; */
            /* padding-right: 5px; */
            /* font-size: 12px; */
            /* color: #606266; */
            /* transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1); */
        }



        #overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        #iframeContainer {
            position: relative;
            width: 80%;
            max-width: 500px; /* Adjust the maximum width as needed */
            height: 200px;
            background-color: #fff; /* Adjust the background color as needed */
            border-radius: 8px;
            overflow: hidden;
            padding: 20px
        }

        #closeButton {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
        }
        .requiredFields {
            color: red;
        }
        .btn-primary:disabled {
            background-color: #00000075 !important;
        }
        #bodyLoading {
            position: absolute;
            left: 48.4%;
            top: 38%;
            border: 5px solid #cbcbca;
            border-top: 5px solid #0a44b3;
            border-radius: 50%;
            width: 41px;
            height: 41px;
            animation: spin 2s linear infinite;
        }
        #spinWrap {
            width: 100%;
            height: 100%;
            position: absolute;
            right: 0%;
            top: 0%;
            background: rgba(255, 255, 255, 0.9);
            z-index: 999;
            display: none;
        }
        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        #loadingMessage {
            box-sizing: border-box;
            width: 100%;
            text-align: center;
            z-index: 100;
            outline: none;
            top: 44%;
            position: absolute;
            font-size: 14px;
            font-weight: 600;
            color: rgba(48, 49, 51, .5);
        }
    </style>
</head>
<body style="height: auto !important;">
<div id="spinWrap">
    <div id="bodyLoading"></div>
    <div id="loadingMessage"></div>
</div>
<div class="container">
    <form id="signForm">

        <div class="form-group row">
            <div class="col-sm-6 d-flex align-items-center">
                <label for="type" class="col-sm-4 col-form-label"><span class="requiredFields">*</span>类型:</label>
                <div class="col-sm-8" id="type">
                    <label style="display: inline-block;">
                        <input type="radio" name="signatureType" value="general" checked> 通用文件
                    </label>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <label style="display: inline-block;">
                        <input type="radio" name="signatureType" value="project"> 项目文件
                    </label>
                </div>
            </div>
            <div class="col-sm-6 d-flex align-items-center">
                <label for="type" class="col-sm-4 col-form-label"><span class="requiredFields">*</span>审签:</label>
                <div class="col-sm-8" id="signType">
                    <label style="display: inline-block;">
                        <input type="radio" name="isOrderSign" value="1" > 是
                    </label>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <label style="display: inline-block;">
                        <input type="radio" name="isOrderSign" value="0" checked> 否
                    </label>
                </div>
            </div>
        </div>



        <div class="form-group row" id="div_studyid">
            <label for="studyid" class="col-sm-2 col-form-label"><span class="requiredFields">*</span>研究代码:</label>

            <div class="col-sm-10" >
                <select id="studyid" class="form-control" ></select>
            </div>
        </div>


        <div class="form-group row" id="div_author" style="display: none;">
            <label for="author" class="col-sm-2 col-form-label"><span class="requiredFields">*</span>作者:</label>
            <div class="col-sm-10">
                <select id="author" class="form-control" multiple="multiple" required></select>
            </div>
        </div>
        
        <div class="form-group row">
            <label for="recipients" class="col-sm-2 col-form-label"><span class="requiredFields">*</span>签署人:</label>
            <div class="col-sm-10">
                <select id="recipients" class="form-control" multiple="multiple" required></select>
            </div>
        </div>
        <%--        <div class="form-group row">
                    <label for="subject" class="col-sm-2 col-form-label">主题:</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="subject" required>
                    </div>
                </div>--%>

        <div class="form-group row">
            <label for="signFlowExpireTime" class="col-sm-2 col-form-label"><span class="requiredFields">*</span>过期天数(自然日):</label>
            <div class="col-sm-4">
                <%--                <input type="text" class="form-control" autocomplete="off" id="signFlowExpireTime"--%>
                <%--                       name="signFlowExpireTime" placeholder="签署截止日期" required>--%>

                <select id="signFlowExpireTime" class="form-control">
                    <option value="1">1天</option>
                    <option value="3" selected>3天</option>
                    <option value="5">5天</option>

                </select>

                    <!-- Add a hidden field to store the actual date if needed -->
                    <input type="hidden" id="hiddenExpirationDate" name="hiddenExpirationDate">

            </div>

            <label for="language" class="col-sm-2 col-form-label"><span class="requiredFields">*</span>待签文件语种:</label>
            <div class="col-sm-4">
                <select id="language" class="form-control">
                    <option value="cn">中文</option>
                    <option value="en">English</option>
                </select>
            </div>
        </div>

        <div class="form-group">
            <div class="editor-wrapper">
                <div id="toolbar-container"></div>
                <div id="editor-container"></div>
            </div>
        </div>
        <div class="form-group">
            <div class="row">
                <div class="col-sm-6">
                    <label class="col-sm-12 col-form-label pl-0"><span class="requiredFields">*</span>待签文件:</label>
                    <div class="col-sm-12 pl-0">
                        <form id="frm" name=form_input action="" method="post" target="_self">
                            <c:if test="${not empty signedFile}">
                                <table class="table table-xs w-100">
                                    <tbody>
                                    <tr height="1">
                                        <td>
                                            <lable class="signedFileNameLabel">${signedFileName}</lable>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </c:if>

                            <c:if test="${signedFile==null || signedFile==''}">
                                <jsp:include page="/webutil/fileup/fileinput.jsp">
                                    <jsp:param name="name" value="signpage"/>
                                    <jsp:param name="formname" value="form_input"/>
                                    <jsp:param name="maxfile" value="1"/>
                                    <jsp:param name="filetype" value="pdf"/>
                                </jsp:include>
                            </c:if>
                        </form>
                    </div>
                </div>
                
                <div class="col-sm-6">
                    <label class="col-sm-12 col-form-label pl-0">附件:</label>
                    <div class="col-sm-12 pl-0">
                        <form id="attachmentForm" name="attachment_form" action="" method="post" target="_self">
                            <jsp:include page="/webutil/fileup/fileinput.jsp">
                                <jsp:param name="name" value="attachments"/>
                                <jsp:param name="formname" value="attachment_form"/>
                                <jsp:param name="maxfile" value="50"/>
                                <jsp:param name="filetype" value=""/>
                            </jsp:include>
                            <input type="hidden" id="attachments" name="attachments" value="">
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <p id="errorMessage" style="color: red;"></p>
        <p id="successMessage" style="color: green;"></p>
        <button id="submitbtn" class="btn btn-primary" style="width: 100%;padding: 10px;" onclick="submitbtn()">提交</button>

    </form>
</div>


<div id="overlay">
    <div id="iframeContainer">
        <div id="closeButton" onclick="closeForgetPsw()">X</div>
        <form id="signer">

            <div class="form-group row">
                <label for="type" class="col-sm-2 col-form-label">姓名：</label>

                <div class="col-sm-10">
                    <input class="input-field" type="text" style="width: 100%" required="" id="signer_name">
                </div>

            </div>
            <div class="form-group row">
                <label for="type" class="col-sm-2 col-form-label">邮箱：</label>

                <div class="col-sm-10">
                    <input class="input-field" type="text"  style="width: 100%" required="" id="signer_mail">
                </div>

            </div>

            <button type="submit" id="signerbtn" class="btn btn-primary">确认</button>
        </form>
    </div>
</div>

<script lang="javascript">
    var addFlag="0";
    // 删除待签文件
    function deleteFilesignpage(){
        $("#filelistdivsignpage").html("");
        $("#signpage").val("");
        $("#fileup_frmsignpage").show();
    }
    
    // 完全重写附件删除函数
    function deleteFileattachments(index){
        index=index-1;
        console.log("原始deleteFileattachments被调用，索引:", index);
        
        // 拿到所有文件内容的字符串
        const filesString = $("#attachments").val();
        console.log("文件列表字符串:", filesString);
        
        // 如果没有文件，直接返回
        if (!filesString || filesString.trim() === "") {
            console.log("没有附件可删除");
            return;
        }
        
        // 解析文件字符串为数组
        let filesArray = filesString.split("|").filter(f => f.trim() !== "");
        console.log("文件数组(删除前):", filesArray);
        
        // 检查索引是否有效
        if (index < 0 || index >= filesArray.length) {
            console.error("无效的索引:", index, "数组长度:", filesArray.length);
            return;
        }
        
        // 删除数组中的文件（注意数组索引从0开始）
        const removedFile = filesArray[index];
        console.log("要删除的文件:", removedFile, "索引:", index);
        
        // 从数组中移除文件
        filesArray.splice(index, 1);
        console.log("文件数组(删除后):", filesArray);
        
        // 更新文件列表字符串
        const newFilesString = filesArray.join("|");
        $("#attachments").val(newFilesString);
        console.log("更新后的文件列表字符串:", newFilesString);
        
        // 如果没有文件了，清空显示并显示上传控件
        if (filesArray.length === 0) {
            console.log("所有文件已删除，显示上传控件");
            $("#filelistdivattachments").empty();
            $("#fileup_frmattachments").show();
            
            // 确保上传表单重置
            try {
                $("#attachment_form")[0].reset();
            } catch(e) {
                console.error("重置表单失败:", e);
            }
            return;
        }
        
        // 重新构建文件列表HTML显示
        let newHtml = "";
        for (let i = 0; i < filesArray.length; i++) {
            const file = filesArray[i];
            const fileName = file.split("*")[0];
            
            newHtml += (i+1) + ". " + fileName;
            newHtml += `<img class="cursor-pointer ml-2" title="Delete" src="/webutil/fileup/close.gif" 
                           onclick="manualDeleteAttachment(${i})" style="margin-left: 5px;">`;
            
            if (i < filesArray.length - 1) {
                newHtml += "<br>";
            }
        }
        
        // 更新显示
        $("#filelistdivattachments").html(newHtml);
        console.log("已更新文件列表显示");
    }
    
    // 添加一个手动删除函数，用于自定义删除逻辑
    function manualDeleteAttachment(index) {
        console.log("手动删除附件，索引:", index);
        
        // 调用删除函数并阻止事件冒泡
        deleteFileattachments(index);
        
        // 阻止事件冒泡
        if (event) {
            event.stopPropagation();
            event.preventDefault();
        }
        return false;
    }
    
    /***
     * 开启loading
     */
    function startLoading(msg) {
        let spinWrap = document.getElementById("spinWrap");
        let loadingMessage = document.getElementById("loadingMessage");
        spinWrap.style.display = "inline-block";
        //获取返回值并赋值到loadingMsg
        loadingMessage.innerText = msg;
    }

    /***
     * 关闭loading
     */
    function closeLoading() {
        let spinWrap = document.getElementById("spinWrap");
        spinWrap.style.display = "none";
    }
</script>

<script type="text/javascript">
/*    var threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);

    const signFlowExpireTime = flatpickr("#signFlowExpireTime", {
        enableTime: true,
        dateFormat: "Y-m-d H:i:S",
        minDate: "today",
        locale: "zh",
        defaultDate: threeDaysFromNow


    });*/

// Remove the flatpickr initialization completely
// Instead, just use the select element directly

// If you need to set a default value (e.g., 3 days)
$(document).ready(function() {
    // The select already has "3" selected by default in the HTML
    // No need to do anything else

    // If you need to calculate expiration date based on selection
    $("#signFlowExpireTime").change(function() {
        const days = parseInt($(this).val());
        const expirationDate = new Date();
        expirationDate.setDate(expirationDate.getDate() + days);
        console.log("Expiration date set to:", expirationDate.toLocaleString());

        // If you need to store the actual date somewhere
        $("#hiddenExpirationDate").val(formatDate(expirationDate));
    });

    // Trigger change to set initial expiration date
    $("#signFlowExpireTime").trigger("change");
});

// Helper function to format date as YYYY-MM-DD HH:MM:SS
function formatDate(date) {
    const pad = (num) => String(num).padStart(2, '0');

    return date.getFullYear() + '-' +
        pad(date.getMonth() + 1) + '-' +
        pad(date.getDate()) + ' ' +
        pad(date.getHours()) + ':' +
        pad(date.getMinutes()) + ':' +
        pad(date.getSeconds());
}

// Function to get expiration days for form submission
function getSignFlowExpireValue() {
    return $("#signFlowExpireTime").val();
}


</script>
<script>

    $('#div_studyid').hide();

    // Add change event handler to radio buttons
    $('input[name="signatureType"]').change(function () {
        if ($(this).val() === 'project') {
            // If '项目签字' is selected, hide type and show div_studyid
            $('#div_studyid').show();
        } else {
            // If '通用签字' is selected, show type and hide div_studyid
            $('#div_studyid').hide();
        }
        $("#studyid").empty();
    });
</script>

<script>
    $(document).ready(function() {
        // 初始化作者选择框
        $author=  $('#author').select2({
            ajax: {
                url: "/lightpdfSign.signerSearch.do",
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        param: $("#studyid").val(),
                        q: params.term,
                        page: params.page
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;
                    return {
                        results: data.data.results,
                        pagination: {
                            more: (params.page * 30) < data.total_count
                        }
                    }
                },
                cache: true
            },
            tags: true,
            createTag: function (params) {
                var term = $.trim(params.term);

                if (term === '') {
                    return null;
                }

                return {
                    id: term,
                    text: term,
                    newTag: true
                };
            },
            templateResult: function (data) {
                // Customize the display of results
                if (data.newTag) {
                    return $('<a style="color: red; ">没有想要的结果，去添加: ' + data.text + '</a>');
                }
                return data.text;
            },
            maximumSelectionLength: 1, // 限制只能选择一个
            allowClear: true, // 允许清除选择
            placeholder: '请选择作者' // 添加占位符文本
        }).on('select2:select', function(e) {
            // Prevent selecting the "New" tag
            var isNewTag = e.params.data.newTag;
            if (isNewTag) {
                addFlag="1";
                // e.preventDefault();
                var selectedOptions = $(this).val() || [];
                selectedOptions.pop(); // 移除最后一个元素，即"New"标签
                $(this).val(selectedOptions).trigger('change');
                // Unselect the "New" tag
                // $(this).val(null).trigger('change');

                // alert(JSON.stringify(e.params.data.id));

                // Handle the click event, e.g., show a modal
                // openModal(e.params.data.id);
                $('#signer_name').val(e.params.data.id);
                $('#signer_mail').val(e.params.data.id);
                showForgetPsw();


            }
            // 选中新选项后，清除之前的所有选项，只保留当前选中的选项
            // const selectedData = e.params.data;
            // $(this).val(null).trigger('change');
            // const option = new Option(selectedData.text, selectedData.id, true, true);
            // $(this).append(option).trigger('change');
        });

        // 监听会签单选框变化
        $('input[name="isOrderSign"]').change(function() {
            if ($(this).val() === '1') {
                $('#div_author').show();
            } else {
                $('#div_author').hide();
                $('#author').val(null).trigger('change');
            }
        });
    });

    // 修改验证函数，添加作者验证
    function verifyForm(){
        let msgArray = new Array();
        // ... 原有验证代码 ...

        // 添加作者验证
        if ($('input[name="isOrderSign"]:checked').val() === '1') {
            let author = $('#author').val();
            if (!author) {
                msgArray.push("会签时作者必填");
            }
        }

        return msgArray.join("，");
    }








    $recipients= $('#recipients').select2({
        multiple: true,
        // allowClear: option.allowClear,
        // minimumInputLength: 1,
        closeOnSelect:false,//Controls whether the dropdown is closed after a selection is made.
        ajax: {
            url: "/lightpdfSign.signerSearch.do",
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    param:$("#studyid").val(),
                    q: params.term, // search term
                    page: params.page
                };
            },
            processResults: function (data, params) {
                // parse the results into the format expected by Select2
                // since we are using custom formatting functions we do not need to
                // alter the remote JSON data, except to indicate that infinite
                // scrolling can be used
                params.page = params.page || 1;

                return {
                    results: data.data.results,
                    pagination: {
                        more: (params.page * 30) < data.total_count
                    }
                }
            },
            cache: true

        },
        tags: true,
        createTag: function (params) {
            var term = $.trim(params.term);

            if (term === '') {
                return null;
            }

            return {
                id: term,
                text: term,
                newTag: true
            };
        },
        templateResult: function (data) {
            // Customize the display of results
            if (data.newTag) {
                return $('<a style="color: red; ">没有想要的结果，去添加: ' + data.text + '</a>');
            }
            return data.text;
        },

    }).on('select2:select', function (e) {
        // Prevent selecting the "New" tag
        var isNewTag = e.params.data.newTag;
        if (isNewTag) {
            addFlag="2";
            // e.preventDefault();
            var selectedOptions = $(this).val() || [];
            selectedOptions.pop(); // 移除最后一个元素，即"New"标签
            $(this).val(selectedOptions).trigger('change');
            // Unselect the "New" tag
            // $(this).val(null).trigger('change');

            // alert(JSON.stringify(e.params.data.id));

            // Handle the click event, e.g., show a modal
            // openModal(e.params.data.id);
            $('#signer_name').val(e.params.data.id);
            $('#signer_mail').val(e.params.data.id);
            showForgetPsw();


        }
    });




</script>


<script>
    $studyid=$("#studyid").select2({
        ajax: {
            url: "/lightpdfSign.studySearch.do",
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    q: params.term, // search term
                    page: params.page
                };
            },
            processResults: function (data, params) {
                // parse the results into the format expected by Select2
                // since we are using custom formatting functions we do not need to
                // alter the remote JSON data, except to indicate that infinite
                // scrolling can be used
                params.page = params.page || 1;

                return {
                    results: data.data.results,
                    pagination: {
                        more: (params.page * 30) < data.total_count
                    }
                };
            },
            cache: true

        },

        // placeholder: 'Search for a repository',
        minimumInputLength: 1,
        // templateResult: formatRepo,
        // templateSelection: formatRepoSelection
    });

</script>
<script src="/cdtms/LightpdfSign/js/index.js"></script>

<script>
    const {createEditor, createToolbar} = window.wangEditor

    const editorConfig = {
        placeholder: '已预设邮件签字部分，其他部分按需添加',
        onChange(editor) {
            const html = editor.getHtml()
            //console.log('editor content', html)
            // 也可以同步到 <textarea>
        }
    }

    const editor = createEditor({
        selector: '#editor-container',
        html: '<p><br></p>',
        config: editorConfig,
        mode: 'simple' // or 'simple'
    })

    const toolbarConfig = {}

    const toolbar = createToolbar({
        editor,
        selector: '#toolbar-container',
        config: toolbarConfig,
        mode: 'simple' // or 'simple'
    })
</script>


<script>
  // 转换附件格式的函数（使用逗号分隔）
  function convertAttachmentsFormat(oldFormat) {
    console.log("原始附件值:", oldFormat);
    if (!oldFormat) return "";
    
    // 检测分隔符 - 使用竖线或逗号
    let filesArray = [];
    if (oldFormat.includes("|")) {
        // 使用竖线分隔
        filesArray = oldFormat.split("|").filter(f => f.trim() !== "");
        console.log("使用竖线分隔，得到", filesArray.length, "个文件");
    } else if (oldFormat.includes(",")) {
        // 使用逗号分隔
        filesArray = oldFormat.split(",").filter(f => f.trim() !== "");
        console.log("使用逗号分隔，得到", filesArray.length, "个文件");
    } else {
        // 可能只有一个文件，不包含分隔符
        filesArray = [oldFormat];
        console.log("没有分隔符，假设只有1个文件");
    }
    
    const newFormatArray = [];
    
    for (const file of filesArray) {
        console.log("处理文件:", file);
        
        // 原格式: 路径*服务器文件名
        const parts = file.split("*");
        if (parts.length !== 2) {
            console.log("文件格式不正确，跳过:", file);
            continue;
        }
        
        let clientPath = parts[0];
        let serverFileName = parts[1];
        
        console.log("解析得到 - 客户端路径:", clientPath, "服务器文件名:", serverFileName);
        
        // 新格式: 服务器文件名||客户端路径||||attachment
        let newFormat = serverFileName+'||'+clientPath+'||||attachment';
        newFormatArray.push(newFormat);
        console.log("转换为新格式:", newFormat);
    }
    
    // 使用逗号连接转换后的文件
    let newFormat = newFormatArray.join(",");
    
    console.log("转换后的附件值:", newFormat);
    return newFormat;
}

    const errorMessage = document.getElementById("errorMessage");
    const successMessage = document.getElementById("successMessage");
    function submitbtn(){
        try {
            debugger
            startLoading("正在提交,请稍后...");
            // $('#submitbtn').attr('disabled',true)
            let verifyMsg = verifyForm();
            if (verifyMsg.length !== 0){
                errorMessage.textContent = verifyMsg;
                closeLoading();
                // $('#submitbtn').attr('disabled',false)
                return;
            }
       // 获取签署人列表
       let recipientsList = $('#recipients').select2('val');
            
            // 如果是会签且有作者，将作者添加到签署人列表首位
            if ($('input[name="isOrderSign"]:checked').val() === '1') {
                const author = $('#author').val();
                if (author) {
                    // 移除作者（如果已存在于签署人列表中）
                    recipientsList = recipientsList.filter(item => item !== author);
                    // 将作者添加到列表首位
                    recipientsList.unshift(author);
                }
            }

            let attachments =convertAttachmentsFormat($('#attachments').val());

           let data = JSON.stringify({
                recipients: recipientsList.join(";"),
                studyid: $('#studyid').val(),
                subject: $('#subject').val(),
                signpage: $('#signpage').val(),
                attachments:attachments,
                language: $('#language').val(),
                signPageFile: '${signedFile}',
                signFlowExpireTime: $('#signFlowExpireTime').val(),
                type:  $('input[name="signatureType"]:checked').val(),
               signType: $('input[name="isOrderSign"]:checked').val(),
                body: editor.getHtml()
            });
            // $.post("lightpdfSign.createCheck.do", data, function (data){
            //     if (data.status == 200) {
            //         successMessage.textContent = "验证成功！";
            //         window.open(data.data.setUrl);
            //         window.location = data.data.redirectUrl;
            //     } else {
            //         successMessage.textContent = "";
            //     }
            //     closeLoading();
            // },"json");
            $.ajax("lightpdfSign.createCheck.do", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                data: data,
                success: function (data) {
                    if (data.status == 200) {
                        successMessage.textContent = "验证成功！";
                        window.open(data.data.setUrl);
                        window.location = data.data.redirectUrl;
                    } else {
                        successMessage.textContent = "";
                    }
                    closeLoading()
                    // $('#submitbtn').attr('disabled',false)
                }
            });
        }catch (e) {
            closeLoading();
        }
    }

    signForm.addEventListener("submit", async (e) => {

        $('#submitbtn').attr('disabled',true)

        e.preventDefault();
        console.log($('#recipients').select2('data'));
        let verifyMsg = verifyForm();
        if (verifyMsg.length !== 0){
            errorMessage.textContent = verifyMsg;
            return;
        }
        const response = await fetch("lightpdfSign.createCheck.do", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                recipients: $('#recipients').select2('val').join(";"),
                studyid: $('#studyid').val(),
                subject: $('#subject').val(),
                signpage: $('#signpage').val(),
                attachments: $('#attachments').val(),
                language: $('#language').val(),
                signPageFile: '${signedFile}',
                signFlowExpireTime: $('#signFlowExpireTime').val(),
                type:  $('input[name="signatureType"]:checked').val(),
                body: editor.getHtml()

            })
        });
        const data = await response.json();
        if (data.status == 200) {
            successMessage.textContent = "验证成功！";
            window.open(data.data.setUrl);
            window.location = data.data.redirectUrl;
        } else {
            // errorMessage.textContent = "验证码或邮箱错误";
            successMessage.textContent = "";
        }
        $('#submitbtn').attr('disabled',false)
    });



    signer.addEventListener("submit", async (e) => {


        e.preventDefault();


        if (!isValidName($('#signer_name').val())) {
            alert('姓名仅允许包含大小写字母、空格或只包含中文字符');
            return;
        }
        if (!isEmailValid($('#signer_mail').val())) {
            alert('邮箱格式错误！');
            return;
        }
        var selectedValues = $('#recipients').select2('val');
        // alert(JSON.stringify(selectedValues));

// 要检查的目标值
        var targetValue = '<'+$('#signer_mail').val()+'>';
        // alert(JSON.stringify(targetValue));

// 判断目标值是否部分匹配已选中的值中的任何一个
        if(selectedValues){
            var isValueSelected = selectedValues.some(function(value) {
                // 使用indexOf进行部分匹配（不区分大小写）
                return value.toLowerCase().includes(targetValue.toLowerCase());
            });
        }


        if(isValueSelected){
            alert('邮箱已存在！');
            return;
        }

        var item=$('#signer_name').val()+'<'+$('#signer_mail').val()+'>';
        var option = new Option(item, item, true, true);
        if(addFlag==="1"){
            $author.append(option);
            $author.trigger('change');
        }else if(addFlag==="2"){
            $recipients.append(option);
            $recipients.trigger('change');
        }


        closeForgetPsw();
    });

     function verifyForm(){
        let recipients = $('#recipients').select2('val')
        let studyid = $('#studyid').val()
        let language = $('#language').val()
        let signFlowExpireTime = $('#signFlowExpireTime').val()
        let signatureType = $('input[name="signatureType"]:checked').val()
        let msgArray = new Array();
        if (signatureType === null){
            msgArray.push("类型必填");
        }
        if (signatureType === "project"&& studyid.length === null){
            msgArray.push("研究代码必填！");
        }
        if (recipients === null){
            msgArray.push("签署人必填");
        }
        if (signFlowExpireTime === null){
            msgArray.push("过期天数(自然日)必填");
        }
        if (language === null){
            msgArray.push("待签文件语种必填！");
        }

        if($("#filelistdivsignpage").length > 0){
         let fileItem = $("#filelistdivsignpage").text()

         if (fileItem === ""){
            msgArray.push("待签文件必填！")
        }
        }

         // 添加作者验证
         if ($('input[name="isOrderSign"]:checked').val() === '1') {
             let author = $('#author').val();
             console.log($('#author').val());
             if (!author) {
                 msgArray.push("会签时作者必填");
             }
         }
        return msgArray.join("，");
    }
    function isEmailValid(email) {
        // 使用正则表达式匹配邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function isValidName(name) {
        // 正则表达式：只包含大小写字母、空格或只包含中文字符
        const regex = /^([\u4e00-\u9fa5]+|[A-Za-z\s]+)$/;
        return regex.test(name);
    }
</script>
<script>

function showForgetPsw() {
// var overlay = document.getElementById("overlay");
// var iframe = document.getElementById("forgetPswIframe");
//
// Set the iframe src to your forget password page URL
<%--iframe.src = "lightpdfSign.changepassword.do?email=${currUserInfoMap.email}"; // Replace with the actual URL of your forget password page--%>

// Display the overlay
overlay.style.display = "flex";
}

function closeForgetPsw() {
// var overlay = document.getElementById("overlay");
// var iframe = document.getElementById("forgetPswIframe");

// Hide the overlay
overlay.style.display = "none";

// Reset the iframe src
// iframe.src = "";
}


<c:if test="${not empty signedFile}">

var ReType='${ReType}';
var ReStudyId='${ReStudyId}';
var ReLanguage='${ReLanguage}';
var ReStudyName='${ReStudyName}';
var ReSignerOptions='${ReSignerOptions}';
$("#language").val(ReLanguage);

if(ReStudyId){

    $studyid.append(new Option( ReStudyName,ReStudyId, true, true));
}


if(ReType=='project'){

    document.getElementsByName('signatureType')[1].checked = true;
    $('#div_studyid').show();

}


if(ReSignerOptions){

    ReSignerOptions=ReSignerOptions.split("|");
    ReSignerOptions.forEach(ReSignerOption => {
    $recipients.append(new Option(ReSignerOption, ReSignerOption, true, true));

    });
}


</c:if>

    </script>

    </body>
    </html>
