#!/bin/bash

# 进入工作目录
cd /home/<USER>/8087/ || {
    echo "无法进入工作目录，请检查路径是否存在！"
    exit 1
}

# 移动新jar包并覆盖旧文件
mv -f /tmp/cdtms_sas_online-0.0.1-SNAPSHOT.jar ./

# 查找并杀死旧进程
PID=$(ps -aux | grep cdtms_sas_online-0.0.1-SNAPSHOT.jar | grep -v grep | awk '{print $2}')
if [ -n "$PID" ]; then
    echo "正在终止旧进程 PID: $PID"
    kill -9 $PID
    sleep 2  # 等待进程终止
else
    echo "未找到运行中的旧进程"
fi

# 启动新应用
echo "正在启动应用程序..."
nohup java -jar ./cdtms_sas_online-0.0.1-SNAPSHOT.jar > java.log 2>&1 &

# 显示启动结果
echo "应用启动完成！查看日志：tail -f java.log"
echo "当前进程 PID: $(pgrep -f cdtms_sas_online-0.0.1-SNAPSHOT.jar)"