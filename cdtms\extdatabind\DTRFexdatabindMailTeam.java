package net.bioknow.cdtms.extdatabind;

import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTRecordFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


public class DTRFexdatabindMailTeam extends DTRecordFuncAction {

    private String receiver = null;
    private Long zq = null;

    public boolean canUse(int auth, String tableid, Long recordid) {

        try {

            if (!StringUtils.equals(tableid, "ext_data_bind")) {
                return false;
            }
            if (Long.valueOf(SessUtil.getSessInfo().getUserid()) <= 2) return true;


            String projectid = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectid);

            Map exdataBindMap = daoDataMng.getRecord(tableid, recordid);


            String blind_status = (String) exdataBindMap.get("blind_status");
            Map odmMap = daoDataMng.getRecord("outboard_data_manager", (Long) exdataBindMap.get("ext_data_id"));
            Long zq1 = (Long) odmMap.get("zq");
            this.zq = zq1;

            if (!StringUtils.equals(blind_status, "2")) {
                return false;

            }

            Long studyid = (Long) exdataBindMap.get("study_id");
            Map userMap = SessUtil.getSessInfo().getUser();

            List StudyUserList = daoDataMng.listRecord("roles", "obj.studyid=" + studyid + " and obj.active='1' and obj.member=" + (String) userMap.get("id"), null, 1);
            if (CollectionUtils.isEmpty(StudyUserList)) {

                return false;

            }


            Map StudyUserMap = (Map) StudyUserList.get(0);

            String role = (String) StudyUserMap.get("limitnum");


            if ((StringUtils.equals(role, "EDM"))) {


                if(zq1==5) {
                    String operateName = (String) exdataBindMap.get("operate_name");
                    Long studyId = (Long) exdataBindMap.get("study_id");

                    String roleName = operateName.split("-")[0];

                    String[] roleNameArry = roleName.split(",");


                    String roleNameInClause = Arrays.stream(roleNameArry)
                            .map(value -> "'" + value + "'")
                            .collect(Collectors.joining(","));

                    List<Map> teamUserList = daoDataMng.listRecord("cra", "obj.studyid=" + studyId + " and obj.syzt='1' and exists(select obj2.id from Codelist as obj2  where obj2.codelist_cname='其他项目组人员角色' and obj2.active='1' and obj2.code_value=obj.limitnum and (obj2.code_cdescription in (" + roleNameInClause + ")))", null, 100);


                    if (CollectionUtils.isNotEmpty(teamUserList)) {

                        this.receiver = teamUserList.stream()
                                .map(teamUserMap -> (String) teamUserMap.get("cra_email"))  // 替换 "key1" 为你想要的key
                                .filter(email -> StringUtils.isNotEmpty(email))  // 如果value可能为null，则添加此过滤器
                                .collect(Collectors.joining(","));
                    }
                }else {

                    Long dtaBindMethodId = (Long) exdataBindMap.get("dta_bind_method_id");
                    Long exdataBindId = (Long) exdataBindMap.get("id");

                    List<Map> extDataBindHisList = daoDataMng.listRecord("ext_data_bind", "obj.dta_bind_method_id=" + dtaBindMethodId+" and obj.id !="+exdataBindId , "obj.id desc", 1);

                    Map extDataBindHisMap = extDataBindHisList.get(0);

                     this.receiver=(String) extDataBindHisMap.get("file_receive_emails");


                }


                return true;

            }


        } catch (Exception e) {
            Log.error("", e);
        }
        return false;
    }

    public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBean fpb) {
        try {


            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            List<Map> engineList = null;
            if (this.zq == 5) {
                engineList = daoDataMng.listRecord("email_template", "obj.tableid='" + fpb.getTableid() + "' and obj.name='发送TEAMQC'", null, 100);
            } else {
                engineList = daoDataMng.listRecord("email_template", "obj.tableid='" + fpb.getTableid() + "' and obj.name='发送TEAM'", null, 100);
            }

            ArrayList<Map> currEngineList = new ArrayList<>();

            engineList.get(0).put("receiver", this.receiver);

            engineList.get(0).put("isSign", "2");

            currEngineList.add(engineList.get(0));
            String userloginid = SessUtil.getSessInfo().getUserloginid();
            engineList.get(0).put("ccer", userloginid);

            request.setAttribute("listr", currEngineList);
//			request.setAttribute("receiver", this.receiver);
            this.forwardByUri(request, response, "/formMail.ajaxmenu.do");


        } catch (Exception e) {
            Log.error("", e);
        }
    }

    public FuncInfoBean getFIB(String tableid) {
        FuncInfoBean fib = new FuncInfoBean();

        fib.setName("发送给项目组");
        fib.setType(FuncInfoBean.FUNCTYPE_AJAXMENU);
        fib.setWinHeight(800);
        fib.setWinWidth(800);
        fib.setSimpleViewShow(true);


        return fib;
    }

}
