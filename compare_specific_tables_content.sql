-- =====================================================
-- COMPARE SPECIFIC TABLES CONTENT - ROW BY ROW
-- Source Schema: CDTMS_PILOT
-- Target Schema: CDTMS_TEMP
-- Tables: From 比对范围.txt
-- Purpose: Find tables with different content
-- =====================================================

SET PAGESIZE 1000
SET LINESIZE 200
SET SERVEROUTPUT ON SIZE 1000000
SET FEEDBACK OFF
SET ECHO OFF

-- Define schemas
DEFINE SOURCE_SCHEMA = 'CDTMS_PILOT'
DEFINE TARGET_SCHEMA = 'CDTMS_TEMP'

PROMPT =====================================================
PROMPT CONTENT COMPARISON FOR SPECIFIC TABLES
PROMPT Source Schema: &SOURCE_SCHEMA
PROMPT Target Schema: &TARGET_SCHEMA
PROMPT =====================================================

-- Create result table to store differences
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE table_content_differences';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

CREATE TABLE table_content_differences (
    table_name VARCHAR2(128),
    comparison_result VARCHAR2(50),
    source_count NUMBER,
    target_count NUMBER,
    difference_count NUMBER,
    status VARCHAR2(20),
    check_time DATE DEFAULT SYSDATE
);

-- Main comparison procedure
DECLARE
    v_source_count NUMBER;
    v_target_count NUMBER;
    v_table_exists_source NUMBER;
    v_table_exists_target NUMBER;
    v_sql VARCHAR2(4000);
    v_difference_count NUMBER;
    v_status VARCHAR2(20);
    v_tables_processed NUMBER := 0;
    v_tables_with_differences NUMBER := 0;
    
    -- Table list from 比对范围.txt (first 50 tables for demonstration)
    TYPE table_array IS TABLE OF VARCHAR2(128);
    v_tables table_array := table_array(
        'tbl_attachment',
        'tbl_mdchapter_menuid',
        'tbl_mdchapter',
        'tbl_eclinichistory',
        'tbl_esign_account',
        'tbl_study_visit_set',
        'tbl_study_visit_table',
        'tbl_study_crf_visit',
        'tbl_study_crf_visit_table',
        'tbl_study_crf_table_pctpt',
        'tbl_study_coding_ver_update1',
        'tbl_xmgt',
        'tbl_study_partner',
        'tbl_proj_plan',
        'tbl_yqsq',
        'tbl_risk_management',
        'tbl_study_qc',
        'tbl_schedule',
        'tbl_crf_design',
        'tbl_manual_rev_plan',
        'tbl_im',
        'tbl_edm_account',
        'tbl_study_ext_test_data',
        'tbl_prot_workflow',
        'tbl_edcblind',
        'tbl_dm_report',
        'tbl_fahsjkxd',
        'tbl_dm_disc_log',
        'tbl_xtsj',
        'tbl_interim_analyplan',
        'tbl_non_outboard_data_manager',
        'tbl_external_data_dta_check',
        'tbl_sas_ver_pro',
        'tbl_sascheck',
        'tbl_blindedtransfer',
        'tbl_sas_check_run',
        'tbl_project_oreport',
        'tbl_blinding_export',
        'tbl_final_manage_report',
        'tbl_db_archive',
        'tbl_db_unlock_application',
        'tbl_verification_pr_data',
        'tbl_verification_report_data',
        'tbl_db_lock_data_qc',
        'tbl_ext_data_final',
        'tbl_zzjg',
        'tbl_ryjl',
        'tbl_employe_probation_report',
        'tbl_resume',
        'tbl_employe_work_experience'
    );
    
BEGIN
    DBMS_OUTPUT.PUT_LINE('Starting content comparison for ' || v_tables.COUNT || ' tables...');
    DBMS_OUTPUT.PUT_LINE('');
    
    FOR i IN 1..v_tables.COUNT LOOP
        BEGIN
            v_tables_processed := v_tables_processed + 1;
            
            -- Check if table exists in both schemas
            SELECT COUNT(*) INTO v_table_exists_source
            FROM all_tables 
            WHERE owner = '&SOURCE_SCHEMA' AND table_name = UPPER(v_tables(i));
            
            SELECT COUNT(*) INTO v_table_exists_target
            FROM all_tables 
            WHERE owner = '&TARGET_SCHEMA' AND table_name = UPPER(v_tables(i));
            
            IF v_table_exists_source = 0 AND v_table_exists_target = 0 THEN
                -- Table doesn't exist in either schema
                INSERT INTO table_content_differences 
                VALUES (v_tables(i), 'BOTH_MISSING', 0, 0, 0, 'MISSING', SYSDATE);
                
                DBMS_OUTPUT.PUT_LINE(RPAD(v_tables(i), 35) || ' - BOTH_MISSING');
                
            ELSIF v_table_exists_source = 0 THEN
                -- Table only exists in target
                v_sql := 'SELECT COUNT(*) FROM &TARGET_SCHEMA.' || v_tables(i);
                EXECUTE IMMEDIATE v_sql INTO v_target_count;
                
                INSERT INTO table_content_differences 
                VALUES (v_tables(i), 'SOURCE_MISSING', 0, v_target_count, v_target_count, 'DIFFERENT', SYSDATE);
                
                v_tables_with_differences := v_tables_with_differences + 1;
                DBMS_OUTPUT.PUT_LINE(RPAD(v_tables(i), 35) || ' - SOURCE_MISSING (Target: ' || v_target_count || ' rows)');
                
            ELSIF v_table_exists_target = 0 THEN
                -- Table only exists in source
                v_sql := 'SELECT COUNT(*) FROM &SOURCE_SCHEMA.' || v_tables(i);
                EXECUTE IMMEDIATE v_sql INTO v_source_count;
                
                INSERT INTO table_content_differences 
                VALUES (v_tables(i), 'TARGET_MISSING', v_source_count, 0, v_source_count, 'DIFFERENT', SYSDATE);
                
                v_tables_with_differences := v_tables_with_differences + 1;
                DBMS_OUTPUT.PUT_LINE(RPAD(v_tables(i), 35) || ' - TARGET_MISSING (Source: ' || v_source_count || ' rows)');
                
            ELSE
                -- Table exists in both schemas - compare content
                
                -- Get row counts
                v_sql := 'SELECT COUNT(*) FROM &SOURCE_SCHEMA.' || v_tables(i);
                EXECUTE IMMEDIATE v_sql INTO v_source_count;
                
                v_sql := 'SELECT COUNT(*) FROM &TARGET_SCHEMA.' || v_tables(i);
                EXECUTE IMMEDIATE v_sql INTO v_target_count;
                
                -- Calculate difference
                v_difference_count := ABS(v_source_count - v_target_count);
                
                IF v_source_count = v_target_count THEN
                    -- Same row count - check if content is identical using checksum
                    BEGIN
                        -- Try to compare using a simple checksum approach
                        -- This is a basic comparison - for more detailed comparison, 
                        -- you would need to compare actual data
                        
                        IF v_source_count = 0 THEN
                            -- Both tables are empty
                            v_status := 'IDENTICAL';
                            INSERT INTO table_content_differences 
                            VALUES (v_tables(i), 'IDENTICAL_EMPTY', v_source_count, v_target_count, 0, 'IDENTICAL', SYSDATE);
                            
                            DBMS_OUTPUT.PUT_LINE(RPAD(v_tables(i), 35) || ' - IDENTICAL (Both empty)');
                        ELSE
                            -- Tables have data - assume different for now (detailed comparison would require more complex logic)
                            v_status := 'SAME_COUNT';
                            INSERT INTO table_content_differences 
                            VALUES (v_tables(i), 'SAME_ROW_COUNT', v_source_count, v_target_count, 0, 'SAME_COUNT', SYSDATE);
                            
                            DBMS_OUTPUT.PUT_LINE(RPAD(v_tables(i), 35) || ' - SAME_COUNT (' || v_source_count || ' rows) - Content check needed');
                        END IF;
                        
                    EXCEPTION
                        WHEN OTHERS THEN
                            v_status := 'CHECK_ERROR';
                            INSERT INTO table_content_differences 
                            VALUES (v_tables(i), 'CHECK_ERROR', v_source_count, v_target_count, 0, 'ERROR', SYSDATE);
                            
                            DBMS_OUTPUT.PUT_LINE(RPAD(v_tables(i), 35) || ' - CHECK_ERROR: ' || SQLERRM);
                    END;
                ELSE
                    -- Different row counts
                    v_status := 'DIFFERENT';
                    INSERT INTO table_content_differences 
                    VALUES (v_tables(i), 'DIFFERENT_COUNT', v_source_count, v_target_count, v_difference_count, 'DIFFERENT', SYSDATE);
                    
                    v_tables_with_differences := v_tables_with_differences + 1;
                    DBMS_OUTPUT.PUT_LINE(RPAD(v_tables(i), 35) || ' - DIFFERENT (Source: ' || v_source_count || ', Target: ' || v_target_count || ', Diff: ' || v_difference_count || ')');
                END IF;
            END IF;
            
            -- Progress indicator
            IF MOD(v_tables_processed, 10) = 0 THEN
                DBMS_OUTPUT.PUT_LINE('--- Processed ' || v_tables_processed || ' tables ---');
            END IF;
            
        EXCEPTION
            WHEN OTHERS THEN
                INSERT INTO table_content_differences 
                VALUES (v_tables(i), 'ERROR', 0, 0, 0, 'ERROR', SYSDATE);
                
                DBMS_OUTPUT.PUT_LINE(RPAD(v_tables(i), 35) || ' - ERROR: ' || SQLERRM);
        END;
    END LOOP;
    
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=====================================================');
    DBMS_OUTPUT.PUT_LINE('COMPARISON SUMMARY');
    DBMS_OUTPUT.PUT_LINE('=====================================================');
    DBMS_OUTPUT.PUT_LINE('Total tables processed: ' || v_tables_processed);
    DBMS_OUTPUT.PUT_LINE('Tables with differences: ' || v_tables_with_differences);
    DBMS_OUTPUT.PUT_LINE('Tables identical/same count: ' || (v_tables_processed - v_tables_with_differences));
    
    COMMIT;
END;
/

-- Display summary results
PROMPT 
PROMPT =====================================================
PROMPT DETAILED RESULTS SUMMARY
PROMPT =====================================================

SELECT 
    status,
    COUNT(*) as table_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM table_content_differences
GROUP BY status
ORDER BY COUNT(*) DESC;

PROMPT 
PROMPT =====================================================
PROMPT TABLES WITH DIFFERENCES (NEED ATTENTION)
PROMPT =====================================================

SELECT 
    table_name as "Table Name",
    comparison_result as "Issue Type",
    source_count as "Source Rows",
    target_count as "Target Rows",
    difference_count as "Difference"
FROM table_content_differences
WHERE status IN ('DIFFERENT', 'ERROR')
ORDER BY difference_count DESC, table_name;

PROMPT 
PROMPT =====================================================
PROMPT TABLES REQUIRING CONTENT VERIFICATION
PROMPT =====================================================

SELECT 
    table_name as "Table Name",
    source_count as "Row Count",
    'Manual content check needed' as "Action Required"
FROM table_content_differences
WHERE status = 'SAME_COUNT'
AND source_count > 0
ORDER BY source_count DESC;

PROMPT 
PROMPT =====================================================
PROMPT QUICK MANUAL VERIFICATION COMMANDS
PROMPT =====================================================

PROMPT To manually verify content differences for specific tables, use:
PROMPT 
PROMPT 1. For tables with different row counts:
SELECT 'SELECT COUNT(*) FROM &SOURCE_SCHEMA.' || table_name || '; -- Source' as "SQL Commands"
FROM table_content_differences 
WHERE status = 'DIFFERENT' AND ROWNUM <= 3
UNION ALL
SELECT 'SELECT COUNT(*) FROM &TARGET_SCHEMA.' || table_name || '; -- Target' as "SQL Commands"
FROM table_content_differences 
WHERE status = 'DIFFERENT' AND ROWNUM <= 3;

PROMPT 
PROMPT 2. For detailed content comparison (example):
PROMPT SELECT * FROM CDTMS_PILOT.table_name MINUS SELECT * FROM CDTMS_TEMP.table_name;
PROMPT SELECT * FROM CDTMS_TEMP.table_name MINUS SELECT * FROM CDTMS_PILOT.table_name;

PROMPT 
PROMPT =====================================================
PROMPT TABLES WITH CONTENT DIFFERENCES - FINAL LIST
PROMPT =====================================================

SELECT table_name as "Tables Requiring Sync Review"
FROM table_content_differences
WHERE status IN ('DIFFERENT', 'ERROR')
ORDER BY table_name;

PROMPT 
PROMPT =====================================================
PROMPT COMPARISON COMPLETED!
PROMPT Results stored in: table_content_differences
PROMPT =====================================================
