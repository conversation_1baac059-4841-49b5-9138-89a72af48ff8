package net.bioknow.cdtms.lightpdfSign;


import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import net.bioknow.dbplug.wordreport.CNT_WORDREPORT;
import net.bioknow.dbplug.wordreport.DAOWordreport;
import net.bioknow.dbplug.wordreport.DAOWordreportCFG;
import net.bioknow.mvc.tools.WebPath;
import net.bioknow.passport.projectmng.ProjectUtil;
import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbcore.schema.CNT_Schema;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uapplug.dbview.DAODbview;
import net.bioknow.uapplug.dbview.DbviewUtil;
import net.bioknow.webutil.fileup.DAOFileup;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.*;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URL;
import java.nio.channels.Channels;
import java.nio.channels.FileChannel;
import java.nio.channels.ReadableByteChannel;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cn.jpush.api.push.model.PushModel.gson;

public class LightpdfSignIntegrateUtil {
    //    private static String esignSecret = "Jhof8N4y15S1bGM3";
//    private static String esignProject = "1000003";
//    public static String esignUrl = "https://esignapi-tst.hengrui.com:8199";
//    private static String businessTypeCode = "ecae90dabba0b5f0b244a15e21535ac3";
////    private static String rebackUrl = "https://cdtms-tst.hengrui.com/eSignIntergrate.Receive.do";
//    private static String rebackUrl = "http://dev.bioknow.net:7780/eSignIntergrate.Receive.do?projectid=defaultdb";
//    private static String organizationCode = "4e60fff8e64a43a4aa88d6fb6fd2bb8e";
//
//
    public static String getTemplatePdf(String projectId, String template, String tableid, Map dataMap, HttpServletRequest request) throws Exception {


        DAOWordreportCFG daoWordreportCFG = new DAOWordreportCFG(projectId);
        DAOWordreport daoWordreport = new DAOWordreport(projectId);
        List WordreportRulelist = daoWordreportCFG.getRuleByTableid(tableid);
        Map templateRuleMap = null;


        for (int j = 0; j < WordreportRulelist.size(); j++) {
            Map WordreportRulelistMap = (Map) WordreportRulelist.get(j);
            String fn = (String) WordreportRulelistMap.get(CNT_WORDREPORT.filename);
            if (fn.equals(template))
                templateRuleMap = WordreportRulelistMap;
        }

        String urlroot = "http://" + request.getServerName() + ":" + request.getServerPort()
                + request.getContextPath();
        String docUri = daoWordreport.getDocFile(templateRuleMap, dataMap, urlroot);

        String filePath = WebPath.getRootPath() + docUri;
        daoWordreport.convertDocToPdf(filePath, "2", "2");
//                filePath = filePath.substring(0, filePath.lastIndexOf(".")) + ".pdf";
//
        File filePdf = new File(WebPath.getRootPath() + docUri + ".pdf");// .doc.pdf
        int p = docUri.lastIndexOf(".");
        docUri = docUri.substring(0, p) + ".pdf";
        File fileLocal = new File(filePath + "/" + docUri);

        return filePath + "/" + docUri;

    }

    public static String eSignRegister(String projectId, String eSignfileKey, Map esignInstanceMap, List<Map> signerInfos, Map DAOLightpdfSignIntegrateMap) {
        String CNSMsg = "";
        try {

            String esignUrl = (String) DAOLightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.esignUrl);
            String rebackUrl = (String) DAOLightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.rebackUrl);

            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();

            //获取文件公共信息
            HashMap CASParamMap = new HashMap<>();
            CASParamMap.put("subject", esignInstanceMap.get("subject"));
            CASParamMap.put("remark", esignInstanceMap.get("remark"));
//            CASParamMap.put("file_author", esignInstanceMap.get("file_author"));
            Map currentUserMap = SessUtil.getSessInfo().getUser();

            CASParamMap.put("file_author", currentUserMap.get("username"));

            String systemName = projectId;

//            systemName = projectId;
            //替换 systemName 字符
            List<Map<String, String>> daoLightpdfSignIntegrate = DAOLightpdfSignIntegrate.listRule(projectId);
            Log.info("项目id： " + projectId + "；；；一体化地址start：" + daoLightpdfSignIntegrate);
            if (daoLightpdfSignIntegrate != null) {
                Map<String, String> daoClinicaLinteRule = daoLightpdfSignIntegrate.get(0);
                int serverType = ProjectUtil.getServerType(projectId);
                if (serverType == 1) {
                    systemName = daoClinicaLinteRule.getOrDefault(DAOLightpdfSignIntegrate.clinicalinteaddr_dev, "");
                } else if (serverType == 2) {
                    systemName = daoClinicaLinteRule.getOrDefault(DAOLightpdfSignIntegrate.clinicalinteaddr_uat, "");
                } else {
                    systemName = daoClinicaLinteRule.getOrDefault(DAOLightpdfSignIntegrate.clinicalinteaddr_pro, "");
                }
            }
            CASParamMap.put("created_by", systemName);

            Date signFlowExpireTime = (Date) esignInstanceMap.get("signFlowExpireTime");
//            SimpleDateFormat DateFormatYMDHMS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Long signFlowExpireDate = signFlowExpireTime.getTime() / 1000;

            CASParamMap.put("expiration_date", signFlowExpireDate);
            CASParamMap.put("callback_url", rebackUrl + "?projectid=" + projectId);
            CASParamMap.put("file_id", eSignfileKey);
            CASParamMap.put("file_status", "00");
            CASParamMap.put("file_code", esignInstanceMap.get("file_code"));

            String CNSParamJson = gson.toJson(CASParamMap);
            CNSMsg = sendPost(esignUrl + "/api/tasks", CNSParamJson, projectId);
            Map CNSMsgMap = gson.fromJson(CNSMsg, new TypeToken<Map<String, Object>>() {
            }.getType());

            if (!StringUtils.equals(String.valueOf(CNSMsgMap.get("status")), "200")) {
                return CNSMsg;

            }

            Map GUUDataMap = (Map) CNSMsgMap.get("data");
            String signFlowId = (String) GUUDataMap.get("task_id");

            for (Map signerInfoMap : signerInfos) {

                String name = (String) signerInfoMap.get("name");

                if (StringUtils.isEmpty(name)) {

                    String email_2 = (String) signerInfoMap.get("email_2");

                    signerInfoMap.put("name", email_2.split("@")[0]);

                }


                signerInfoMap.put("task_id", signFlowId);

                String SCSMsg = sendPost(esignUrl + "/api/tasks/" + signFlowId + "/users", gson.toJson(signerInfoMap), projectId);

                Map SCSMsgMap = gson.fromJson(SCSMsg, new TypeToken<Map<String, Object>>() {
                }.getType());

                if (!StringUtils.equals(String.valueOf(SCSMsgMap.get("status")), "200")) {
                    continue;

                }

                Map SCSMsgDataMap = (Map) SCSMsgMap.get("data");

                signerInfoMap.put("signer_id", SCSMsgDataMap.get("id"));


            }

//            String SCSMsg = httpPut(esignUrl + "/api/tasks/" + signFlowId, "{\"type\":1}");


            //获取签署人信息
//            CASParamMap.put("sign_users", signerInfos);

            return CNSMsg;

        } catch (Exception e) {
            Log.error("", e);
        }
        return CNSMsg;
    }

    public static void downloadByNIO(String url, String saveDir, String fileName) {


        ReadableByteChannel rbc = null;
        FileOutputStream fos = null;
        FileChannel foutc = null;
        try {
            String urieUrl = URLEncodeUtil.urie(url);
            rbc = Channels.newChannel(new URL(urieUrl).openStream());
            File file = new File(saveDir, fileName);
            file.getParentFile().mkdirs();
            fos = new FileOutputStream(file);
            foutc = fos.getChannel();
            foutc.transferFrom(rbc, 0, Long.MAX_VALUE);

        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            Log.error("", e);
        } finally {
            if (rbc != null) {
                try {
                    rbc.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (foutc != null) {
                try {
                    foutc.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }
//    public static String uploadFile(String filePath, String fileName)  {
//        try {
//
//            List<Map<String, String>> listR = DAOeSignIntegrate.listRule(projectId,"1");
//
//            String esignUrl = listR.get(0).get(DAOeSignIntegrate.esignUrl);
//        Gson gson = new Gson();
//        HashMap GUUParamMap = new HashMap<>();
//        GUUParamMap.put("requestID", FormUtil.getFormId());
//        GUUParamMap.put("type", 0);
//        String GUUParamJson = gson.toJson(GUUParamMap);
//        String GUUMsg = sendPost(esignUrl + "/file/v1/generateUploadUrl", GUUParamJson,projectId);
//        Map GUUMsgMap = gson.fromJson(GUUMsg, Map.class);
//
//        if ((Double) GUUMsgMap.get("code") == 200) {
//            Map GUUDataMap = (Map) GUUMsgMap.get("data");
//            String uploadFileMsg = null;
//
//            uploadFileMsg = uploadFile(String.valueOf(GUUDataMap.get("url")), filePath, fileName.replace(" ", ""));
//
//            Map uploadFileMsgMap = gson.fromJson(uploadFileMsg, Map.class);
//
//            if ((Double) uploadFileMsgMap.get("code") == 200) {
//                Map uploadFileDataMap = (Map) uploadFileMsgMap.get("data");
//                return (String) uploadFileDataMap.get("fileKey");
//            }else {
//
//                return "400||"+(String) uploadFileMsgMap.get("message");
//            }
//
//
//         }
//        return "400";
//        } catch (Exception e) {
//            Log.error("", e);
//        }
//
//        return "400";
//    }

    public static String sendPost(String url, String data, String projectId) {

        String response = null;

        try {

            CloseableHttpClient httpclient = null;
            CloseableHttpResponse httpresponse = null;

            try {

                httpclient = HttpClients.createDefault();
                HttpPost httppost = new HttpPost(url);

                httppost.setHeader("Content-Type", "application/json");
                StringEntity stringentity = new StringEntity(data, ContentType.create("text/json", "UTF-8"));
                httppost.setEntity(stringentity);
                httpresponse = httpclient.execute(httppost);
                response = EntityUtils.toString(httpresponse.getEntity());
            } finally {

                if (httpclient != null) {
                    httpclient.close();
                }

                if (httpresponse != null) {
                    httpresponse.close();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return response;


    }


    public static void main(String[] args) {

       // String s = transferPdf("https://cdtms.hengrui.com/usersyn/gettoken?projectid=cdtmsen_val&secret=5AF2E70E-1E6D-47B4-A9C5-3809448E11E8");
        String result=transferPdf();

        System.out.println(result);
    }

    public static String transferPdf(){
        String fileName="随机一致性比对参数.xlsx";
        String signFilePathFullURI="C:\\Users\\<USER>\\Documents\\WXWork\\1688857437402592\\Cache\\File\\2025-01\\随机一致性比对参数.xlsx";
        String uploadResult = LightpdfSignIntegrateUtil.uploadFile("https://sas-online-tst.hengrui.com/sas_online/transferXlsxToPdf", signFilePathFullURI, fileName);
        //判断接口是否成功
        Map uploadFileMsgMap = gson.fromJson(uploadResult, new TypeToken<Map<String, Object>>() {
        }.getType());
        if (StringUtils.equals(String.valueOf(uploadFileMsgMap.get("code")), "200.0")) {
            //下载文件流，保存到本地
            String folderPath ="C:\\Users\\<USER>\\Documents\\WXWork\\1688857437402592\\Cache\\File\\2025-01";
            String pdfName ="随机一致性比对参数" + ".pdf";
            String downloadUrl="https://sas-online-tst.hengrui.com/sas_online/files/"+pdfName;
            LightpdfSignIntegrateUtil.downloadByNIO(downloadUrl,folderPath,pdfName);
           System.out.println("success");
           return "success";
        }
        return "fail";
    }
    public static String httpGet(String url) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse httpResponse = null;
        String finalString = null;
        HttpGet httpGet = new HttpGet(url);
        httpGet.setHeader("Accept", "*/*");
        httpGet.setHeader("Accept-Encoding", "gzip, deflate");
        httpGet.setHeader("Cache-Control", "no-cache");
        httpGet.setHeader("Connection", "keep-alive");
        httpGet.setHeader("Content-Type", "application/json;charset=UTF-8");

        try {
            httpResponse = httpClient.execute(httpGet);
            HttpEntity entity = httpResponse.getEntity();
            finalString = EntityUtils.toString(entity, "UTF-8");
            try {
                httpResponse.close();
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return finalString;
    }


    public static String httpDelete(String url) {
        // 获取连接客户端工具
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse httpResponse = null;
        String finalString = null;
        HttpDelete httpDelete = new HttpDelete(url);
        /**公共参数添加至httpGet*/
        /**header中通用属性*/
//        httpDelete.setHeader("Accept", "*/*");
//        httpDelete.setHeader("Accept-Encoding", "gzip, deflate");
//        httpDelete.setHeader("Cache-Control", "no-cache");
//        httpDelete.setHeader("Connection", "keep-alive");
//        httpDelete.setHeader("Content-Type", "application/json;charset=UTF-8");
        /**业务参数*/

        try {
            httpResponse = httpClient.execute(httpDelete);
            HttpEntity entity = httpResponse.getEntity();
            finalString = EntityUtils.toString(entity, "UTF-8");
            try {
                httpResponse.close();
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return finalString;
    }


    public static String httpPost(String url, String data) {
        // 返回body
        String body = null;
        // 获取连接客户端工具
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse httpResponse = null;
        // 2、创建一个HttpPost请求
        HttpPost post = new HttpPost(url);
        // 5、设置header信息
        /**header中通用属性*/
        post.setHeader("Accept", "*/*");
        post.setHeader("Accept-Encoding", "gzip, deflate");
        post.setHeader("Cache-Control", "no-cache");
        post.setHeader("Connection", "keep-alive");
        post.setHeader("Content-Type", "application/json;charset=UTF-8");
        /**业务参数*/

        // 设置参数
        if (data != null) {

            //System.out.println(JSON.toJSONString(map));
            try {
                StringEntity entity1 = new StringEntity(data, "UTF-8");
                entity1.setContentEncoding("UTF-8");
                entity1.setContentType("application/json");
                post.setEntity(entity1);

                // 7、执行post请求操作，并拿到结果
                httpResponse = httpClient.execute(post);
                // 获取结果实体
                HttpEntity entity = httpResponse.getEntity();
                if (entity != null) {
                    // 按指定编码转换结果实体为String类型
                    body = EntityUtils.toString(entity, "UTF-8");
                }
                try {
                    httpResponse.close();
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return body;
    }

    public static String httpPut(String url, String data) {

        String encode = "utf-8";

        //HttpClients.createDefault()等价于 HttpClientBuilder.create().build();
        CloseableHttpClient closeableHttpClient = HttpClients.createDefault();
        HttpPut httpput = new HttpPut(url);
        // 5、设置header信息
        /**header中通用属性*/
        httpput.setHeader("Accept", "*/*");
        httpput.setHeader("Accept-Encoding", "gzip, deflate");
        httpput.setHeader("Cache-Control", "no-cache");
        httpput.setHeader("Connection", "keep-alive");
        httpput.setHeader("Content-Type", "application/json;charset=UTF-8");
        /**业务参数*/

        //组织请求参数
        StringEntity stringEntity = new StringEntity(data, encode);
        httpput.setEntity(stringEntity);
        String content = null;
        CloseableHttpResponse httpResponse = null;
        try {
            //响应信息
            httpResponse = closeableHttpClient.execute(httpput);
            HttpEntity entity = httpResponse.getEntity();
            content = EntityUtils.toString(entity, encode);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                httpResponse.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        try {
            closeableHttpClient.close();  //关闭连接、释放资源
        } catch (IOException e) {
            e.printStackTrace();
        }
        return content;
    }


    private static String HMACSHA256(String data, String key) {
        try {


            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");

            SecretKeySpec secret_key = new SecretKeySpec(key.getBytes("UTF-8"), "HmacSHA256");

            sha256_HMAC.init(secret_key);

            byte[] array = sha256_HMAC.doFinal(data.getBytes("UTF-8"));

            StringBuilder sb = new StringBuilder();

            for (byte item : array) {

                sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));

            }

            return sb.toString();
        } catch (Exception e) {
            Log.error("", e);

        }
        return "";
    }


    public static String uploadFile(String url, String filePath, String filename) {
        String result = null;

        try {
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("text/plain");
            RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                    .addFormDataPart("files", filename,
                            RequestBody.create(MediaType.parse("application/octet-stream"),
                                    new File(filePath)))
                    .build();
            Request request = new Request.Builder()
                    .url(url)
                    .method("POST", body)
                    .build();
            Response response = null;

            response = client.newCall(request).execute();

            if (response.code() == 200) {
                result = response.body().string();

            }
        } catch (IOException e) {
            Log.error("", e);
            return "408";

        }

        return result;
    }


    public static String getUserCode(String email, Long studyId, String projectId) {
        try {

            List<Map<String, String>> listR = DAOLightpdfSignIntegrate.listRule(projectId, "1");

            String esignUrl = listR.get(0).get(DAOLightpdfSignIntegrate.esignUrl);


//            String Projectid = SessUtil.getSessInfo().getProjectid();

            DAODataMng daoDataMng = new DAODataMng(projectId);
//        ArrayUtils

            String mobile = null;
            Long accountId = null;

            List accountList = (List) daoDataMng.listRecord("ryjbzl", "obj.loginid='" + email + "'", null, 1);


            if (!CollectionUtils.isEmpty(accountList)) {

                Map accountMap = (Map) accountList.get(0);
                if (!StringUtils.isEmpty((String) accountMap.get("domain_accounts"))) {
                    return (String) accountMap.get("domain_accounts");
                }

                accountId = (Long) accountMap.get("id");

                List currentUserInfoList = (List) daoDataMng.listRecord("ryjl", "obj.email='" + email + "'", null, 1);
                if (!CollectionUtils.isEmpty(currentUserInfoList)) {
                    Map UserInfoMap = (Map) currentUserInfoList.get(0);
                    mobile = (String) UserInfoMap.get("sjh");
                }

            } else if (studyId != null) {

                List partnerUserList = daoDataMng.listRecord("cra", "obj.studyid=" + studyId + " and obj.cra_email='" + email + "'", null, 100);
                if (!CollectionUtils.isEmpty(partnerUserList)) {

                    Map partnerUserMap = (Map) partnerUserList.get(0);
                    mobile = (String) partnerUserMap.get("cra_phone");
                }

            }

            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();


            HashMap GUCParamMap = new HashMap<>();
            GUCParamMap.put("mobile", mobile);
            String GUCParamJson = gson.toJson(GUCParamMap);


            String GUCMsg = sendPost(esignUrl + "/manage/v1/innerUsers/detail", GUCParamJson, projectId);
            Map GUCMsgMap = gson.fromJson(GUCMsg, new TypeToken<Map<String, Object>>() {
            }.getType());

            if (StringUtils.equals(String.valueOf(GUCMsgMap.get("code")), "200")) {

                List GUUDataList = (List) GUCMsgMap.get("data");
                Map GUUDataMap = (Map) GUUDataList.get(0);
                String userCode = (String) GUUDataMap.get("userCode");

                if (!CollectionUtils.isEmpty(accountList)) {

                    Map accountToSaveMap = new HashMap();
                    accountToSaveMap.put("id", accountId);
                    accountToSaveMap.put("domain_accounts", userCode);

                    daoDataMng.save("ryjbzl", accountToSaveMap);
                }
                return userCode;


            }
            return GUCMsg;

        } catch (Exception e) {
            Log.error("", e);
        }
        return "";
    }


    public static Map analysisSigners(String singers, String projectId) {
        try {
//            String projectId = SessUtil.getSessInfo().getProjectid();

            Map<String, Object> analysisSignersResult = new HashMap<>();
            Map emailUsercodeMap = new HashMap<>();

            DAODataMng daoDataMng = new DAODataMng(projectId);


            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();


            List signersList = gson.fromJson(singers, new TypeToken<Map<String, Object>>() {
            }.getType());

            List<Map<String, Object>> signersFileJoinList = new ArrayList();


            ArrayList<Object> signerInfosList = new ArrayList<>();

            for (Object signersItem : signersList) {
                Map signersMap = (Map) signersItem;

                Long fileId = Long.valueOf((String) signersMap.get("id"));


                String[] signers = String.valueOf(signersMap.get("signers")).split(",");
                for (String signer : signers) {

                    HashMap signerMap = new HashMap();
                    signerMap.put("email", signer);
                    signerMap.put("esign_file_id", fileId);

                    signersFileJoinList.add(signerMap);

                }

            }


            Map<String, List<Map<String, Object>>> signerGroupEmailMap = signersFileJoinList.stream().collect(Collectors.groupingBy((Map map1) -> (String) map1.get("email")));

            Iterator signerGroupEmailIterator = signerGroupEmailMap.keySet().iterator();


            while (signerGroupEmailIterator.hasNext()) {

                String email = (String) signerGroupEmailIterator.next();
                List signerGroupEmailFilesList = (List) signerGroupEmailMap.get(email);

                String userCode = getUserCode(email, null, projectId);


                Map signerInfosMap = new HashMap<>();
                signerInfosMap.put("userType", 1);
                signerInfosMap.put("userCode", userCode);
                signerInfosMap.put("signMode", "1");
                signerInfosMap.put("signNode", "1");
                ArrayList<Object> sealInfosList = new ArrayList<>();

                emailUsercodeMap.put(email, userCode);


                for (Object signerGroupEmailFilesItem : signerGroupEmailFilesList) {

                    Map signerGroupEmailFileMap = (Map) signerGroupEmailFilesItem;
                    Map sealInfosMap = new HashMap<>();
                    Map esignFileRecord = null;

                    esignFileRecord = daoDataMng.getRecord("esign_file", (Long) signerGroupEmailFileMap.get("esign_file_id"));

                    sealInfosMap.put("fileKey", esignFileRecord.get("esign_file_key"));


                    sealInfosList.add(sealInfosMap);
                }
                signerInfosMap.put("sealInfos", sealInfosList);
                signerInfosList.add(signerInfosMap);

            }
            analysisSignersResult.put("signerInfosList", signerInfosList);
            analysisSignersResult.put("signersFileJoinList", signersFileJoinList);
            analysisSignersResult.put("emailUsercodeMap", emailUsercodeMap);

            return analysisSignersResult;
        } catch (Exception e) {
            Log.error("", e);
        }
//        daoDataMng.saveBatch("esign_file_signer",signersListToSave,null,null);


        return null;
    }


    public static String getStudyUser(Long studyId, String sginRole, String projectId) {
        String eSignerSelector = "";
        try {
//            String  projectId = SessUtil.getSessInfo().getProjectid();


            DAODbview daoDbview = new DAODbview(projectId);
            int stuydUserMapIndex = daoDbview.getRuleIndexByName("项目参与人");
            List stuydUserMap = DbviewUtil.getDataList(projectId, stuydUserMapIndex + "", "studyid='" + studyId + "' or studyid is null", "", 999, 1);

            for (Object item2 : stuydUserMap) {
                Map selectorMap = (Map) item2;
                String selectorName = (String) selectorMap.get("name");
                String selectorEmail = (String) selectorMap.get("email");
                String selectorRole = (String) selectorMap.get("role");
                eSignerSelector = eSignerSelector + "{name:'" + selectorName + "',value:'" + selectorEmail + "'";
                if (("," + sginRole + ",").contains("," + selectorRole + ","))
                    eSignerSelector = eSignerSelector + ",selected: true";
                eSignerSelector = eSignerSelector + "},";


            }

        } catch (Exception e) {
            Log.error("", e);
        }
        return eSignerSelector;
    }


    public static Map getRecordUI(String tableid, Map mapV, String projectId) throws Exception {
//        String  projectId = SessUtil.getSessInfo().getProjectid();
        DAODbApi dadao = new DAODbApi(projectId);
        List listF = dadao.getListFieldByTableId(tableid);
        Map mapNew = new HashMap();
        for (int i = 0; i < listF.size(); i++) {
            Map mapF = (Map) listF.get(i);
            String fid = (String) mapF.get(CNT_Schema.id);
            String v = dadao.getFieldType(tableid, fid).formatToUI(tableid, mapF, mapV);
            mapNew.put(fid, v);
        }

        mapNew.put("id", mapV.get("id"));
        return mapNew;
    }

    public static boolean getSignCreateAuth(String projectId) throws Exception {
        String tableId = "sign_online_auth";
        DAODataMng daoDataMng = new DAODataMng(projectId);
        String userLoginId = SessUtil.getSessInfo().getUserloginid();
        if(daoDataMng.count(tableId,"1=1")<=0) {
        return true;
        }
        List<Map<String, Object>> signCreateAuth = daoDataMng.listRecord(tableId, "obj.login_id='" + userLoginId + "'", null, 10000);
        return !signCreateAuth.isEmpty();
    }

    public static Date dateTimeFormat (String dateTimeStr) throws Exception {
        String str = dateTimeStr.replaceAll("GMT[+-]\\d+", "");
       return new SimpleDateFormat("yyyy/MM/ddHH:mm:ss").parse(str);
    }

}
