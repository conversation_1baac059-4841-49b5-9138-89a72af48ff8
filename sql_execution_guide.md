# 🚀 SQL-Only Database Comparison Execution Guide

## 📋 Prerequisites
- Oracle SQL*Plus or SQL Developer installed
- Access to both source and target Oracle databases
- Sufficient privileges to create database links and tables

## 🔧 Method 1: Using SQL*Plus (Command Line)

### Step 1: Connect to Target Database
```bash
# Connect to your target database
sqlplus username/password@target_database

# Or if connecting locally
sqlplus username/password
```

### Step 2: Create Database Link
```sql
-- Replace with your actual source database details
CREATE DATABASE LINK source_db_link
CONNECT TO source_username IDENTIFIED BY source_password
USING '(DESCRIPTION=
    (ADDRESS=(PROTOCOL=TCP)(HOST=source_host)(PORT=1521))
    (CONNECT_DATA=(SERVICE_NAME=source_service_name))
)';

-- Test the connection
SELECT 'Connection successful' FROM dual@source_db_link;
```

### Step 3: Run the Comparison Script
```bash
# Execute the complete comparison script
@pure_sql_database_comparison.sql

# Or copy and paste the SQL commands one by one
```

### Step 4: Export Results to File
```sql
-- Set up output formatting
SET PAGESIZE 1000
SET LINESIZE 200
SET TRIMSPOOL ON

-- Start spooling to file
SPOOL database_comparison_results.txt

-- Run result queries
SELECT * FROM comparison_summary;
SELECT * FROM comparison_details WHERE match_status = 'MISMATCH';

-- Stop spooling
SPOOL OFF
```

## 🖥️ Method 2: Using Oracle SQL Developer (GUI)

### Step 1: Create Connection
1. Open SQL Developer
2. Create new connection to target database
3. Test connection

### Step 2: Execute Script
1. Open new SQL worksheet
2. Copy and paste the `pure_sql_database_comparison.sql` content
3. Modify database link details (lines 10-16)
4. Execute the entire script (F5) or run sections individually

### Step 3: View Results
1. Results will appear in the Script Output panel
2. You can also query the result tables directly:
   ```sql
   SELECT * FROM comparison_summary;
   SELECT * FROM comparison_details;
   ```

### Step 4: Export Results
1. Right-click on result set
2. Choose "Export Data"
3. Select format (CSV, Excel, etc.)

## 📊 Method 3: Manual Step-by-Step Execution

If you prefer to run each comparison type separately:

### 1. Table Existence Check
```sql
-- Check which tables exist in both databases
SELECT 
    s.table_name as source_table,
    t.table_name as target_table,
    CASE 
        WHEN s.table_name IS NULL THEN 'TARGET_ONLY'
        WHEN t.table_name IS NULL THEN 'SOURCE_ONLY'
        ELSE 'BOTH'
    END as existence_status
FROM (SELECT table_name FROM user_tables@source_db_link) s
FULL OUTER JOIN (SELECT table_name FROM user_tables) t 
    ON s.table_name = t.table_name
ORDER BY COALESCE(s.table_name, t.table_name);
```

### 2. Row Count Comparison for Specific Table
```sql
-- Replace 'YOUR_TABLE_NAME' with actual table name
SELECT 
    'YOUR_TABLE_NAME' as table_name,
    (SELECT COUNT(*) FROM YOUR_TABLE_NAME@source_db_link) as source_count,
    (SELECT COUNT(*) FROM YOUR_TABLE_NAME) as target_count,
    (SELECT COUNT(*) FROM YOUR_TABLE_NAME@source_db_link) - 
    (SELECT COUNT(*) FROM YOUR_TABLE_NAME) as difference
FROM dual;
```

### 3. Column Structure Comparison for Specific Table
```sql
-- Replace 'YOUR_TABLE_NAME' with actual table name
SELECT 
    COALESCE(s.column_name, t.column_name) as column_name,
    s.data_type as source_type,
    t.data_type as target_type,
    s.data_length as source_length,
    t.data_length as target_length,
    CASE 
        WHEN s.column_name IS NULL THEN 'TARGET_ONLY'
        WHEN t.column_name IS NULL THEN 'SOURCE_ONLY'
        WHEN s.data_type != t.data_type OR s.data_length != t.data_length THEN 'DIFFERENT'
        ELSE 'MATCH'
    END as comparison_result
FROM (
    SELECT column_name, data_type, data_length 
    FROM user_tab_columns@source_db_link 
    WHERE table_name = 'YOUR_TABLE_NAME'
) s
FULL OUTER JOIN (
    SELECT column_name, data_type, data_length 
    FROM user_tab_columns 
    WHERE table_name = 'YOUR_TABLE_NAME'
) t ON s.column_name = t.column_name
ORDER BY COALESCE(s.column_name, t.column_name);
```

## 🎯 Quick Comparison for Critical Tables

If you only need to check specific critical tables:

```sql
-- Quick row count check for multiple tables
SELECT 'EMPLOYEES' as table_name, 
       (SELECT COUNT(*) FROM EMPLOYEES@source_db_link) as source_count,
       (SELECT COUNT(*) FROM EMPLOYEES) as target_count FROM dual
UNION ALL
SELECT 'CUSTOMERS' as table_name,
       (SELECT COUNT(*) FROM CUSTOMERS@source_db_link) as source_count,
       (SELECT COUNT(*) FROM CUSTOMERS) as target_count FROM dual
UNION ALL
SELECT 'ORDERS' as table_name,
       (SELECT COUNT(*) FROM ORDERS@source_db_link) as source_count,
       (SELECT COUNT(*) FROM ORDERS) as target_count FROM dual;
```

## 📈 Performance Tips for Large Databases

### 1. Use Parallel Hints
```sql
SELECT /*+ PARALLEL(4) */ COUNT(*) FROM large_table@source_db_link;
```

### 2. Sample Large Tables
```sql
-- Compare sample of large table (1% sample)
SELECT COUNT(*) FROM (
    SELECT * FROM large_table@source_db_link SAMPLE(1)
);
```

### 3. Compare by Date Ranges
```sql
-- Compare recent data only
SELECT COUNT(*) FROM your_table@source_db_link 
WHERE created_date >= SYSDATE - 30;  -- Last 30 days
```

## 🔍 Troubleshooting Common Issues

### Database Link Issues
```sql
-- Test database link
SELECT * FROM user_db_links;

-- Check database link status
SELECT db_link, host, created FROM user_db_links;

-- Test simple query
SELECT SYSDATE FROM dual@source_db_link;
```

### Permission Issues
```sql
-- Check current user privileges
SELECT * FROM user_sys_privs WHERE privilege LIKE '%DATABASE LINK%';

-- Check table access
SELECT table_name FROM user_tables@source_db_link WHERE ROWNUM <= 5;
```

### Memory/Performance Issues
```sql
-- Check session memory usage
SELECT name, value FROM v$mystat s, v$statname n 
WHERE s.statistic# = n.statistic# 
AND name LIKE '%memory%';
```

## 📋 Results Interpretation

### Summary Table Results
- **PERFECT**: 100% match - No issues found
- **GOOD**: 95%+ match - Minor differences acceptable
- **FAIR**: 80-95% match - Some investigation needed
- **POOR**: <80% match - Significant issues require attention

### Common Mismatch Types
- **TABLE_EXISTENCE**: Tables missing in one database
- **ROW_COUNT**: Different number of records
- **COLUMN_STRUCTURE**: Schema differences
- **DATA_CHECKSUM**: Data content differences

## 🎯 Quick Commands Summary

```bash
# Connect and run complete comparison
sqlplus username/password@database @pure_sql_database_comparison.sql

# Export results to file
sqlplus username/password@database << EOF
SPOOL results.txt
SELECT * FROM comparison_summary;
SELECT * FROM comparison_details WHERE match_status = 'MISMATCH';
SPOOL OFF
EXIT
EOF

# Clean up after comparison
sqlplus username/password@database << EOF
DROP TABLE comparison_summary;
DROP TABLE comparison_details;
DROP DATABASE LINK source_db_link;
EXIT
EOF
```

This approach gives you complete database comparison capabilities using only Oracle SQL tools! 🚀
