package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.services.core.ApiResult;
import net.bioknow.services.uap.dbdatamng.function.DTRecordFuncActionNew;
import net.bioknow.services.uap.dbdatamng.function.FuncInfoBeanNew;
import net.bioknow.services.uap.dbdatamng.function.FuncParamBeanNew;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class DTRFlightpdfSignVue extends DTRecordFuncActionNew {
	private Long funid = null;


	public boolean canUse(int auth, String tableid, Long recordid) {

		try {



			String projectId = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng=new DAODataMng(projectId);

			List<Map<String, String>> LightpdfSignIntegrateList = DAOLightpdfSignIntegrate.listRule(projectId);
			if (CollectionUtils.isEmpty(LightpdfSignIntegrateList)) {
				return  false;
			}


			List<Map> engineList = daoDataMng.listRecord("esign_engine","obj.tableid='"+tableid+"'",null,100);

			if (CollectionUtils.isEmpty(engineList)) {
				return  false;
			}

			for (Map engineMap : engineList) {
				String showWhere = (String) engineMap.get("where");
				Long engineId = (Long) engineMap.get("id");

				int count = daoDataMng.count(tableid, "obj.id=" + recordid + " and (" + showWhere + ")");

				List esignInstanceList = daoDataMng.listRecord("esign_instance", "obj.active = 1 and obj.tableid='" + tableid + "' and obj.recordid='" + recordid + "' and obj.sign_flow_id is not null and obj.esign_engine_id="+engineMap.get("id"), null, 1);

				if (CollectionUtils.isEmpty(esignInstanceList) && (StringUtils.isEmpty(showWhere) || count>0) ) {

					this.funid=engineId;
					return true;
				}

			}


		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}



	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBeanNew fpb) {
		try {

			String recordid = fpb.getRecordid();
			String tableid = fpb.getTableid();
			String projectId = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng=new DAODataMng(projectId);
			HashMap map = new HashMap();
			map.put("url", "/LightpdfSignIntergrate.eSign.do?recordid="+recordid+"&tableid="+tableid+"&funid="+this.funid);
			response.getOutputStream().write(ApiResult.ok("ok", map).getBytes(StandardCharsets.UTF_8));
			response.getOutputStream().close();





		} catch (Exception e) {
			Log.error("",e);
		}
	}

	public FuncInfoBeanNew getFIB(String tableid) {
		FuncInfoBeanNew fib = new FuncInfoBeanNew("cdtms_DTRFlightpdfSignVue");
		try{
			fib.setName(this.getLanguage().get("Signature"));
			fib.setType(FuncInfoBeanNew.FUNCTYPE_VUE_MODAL);
			fib.setWinHeight(800);
			fib.setAppendParams(false);
			fib.setWinWidth(900);
			fib.setSimpleViewShow(true);
		} catch (Exception e) {
			Log.error("",e);
		}
		return fib;
	}

}
