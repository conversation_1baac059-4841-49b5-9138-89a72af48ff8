package net.bioknow.cdtms.extdatabind;

import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.face.BeforeSaveFace;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EHBeforeexdatabinld implements BeforeSaveFace {
	@Override
	public boolean isTableTrigEvent(String tableid, String projectId) {
		try {

			if (StringUtils.equals(tableid,"dta_bind_method")) {


				return true;
			}

		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}

	@Override
	public String onAdd(String tableid, Map valueMap, String projectId) {

		Map clonedValueMap = new HashMap<>(valueMap);

		String maskvar_s = (String) clonedValueMap.get("maskvar_s");
		String clearvar_s = (String) clonedValueMap.get("clearvar_s");
		String randvalues = (String) clonedValueMap.get("randvalues");
		clonedValueMap.put("maskvar_s", StringUtils.isNotEmpty(maskvar_s) ? maskvar_s.split(",") : null);
		clonedValueMap.put("clearvar_s", StringUtils.isNotEmpty(clearvar_s) ? clearvar_s.split(",") : null);
		clonedValueMap.put("randvalues", StringUtils.isNotEmpty(randvalues) ? randvalues.split(",") : null);

		String blindMethodInfo = Actionextdatabind.getBlindMethodInfo(clonedValueMap);


		valueMap.put("blind_info",blindMethodInfo);

		return null;
	}

	@Override
	public String onUpdate(String tableid, Map valueMap, Map valueMapOrg, String projectId) {
		try {



			Map clonedValueMap = new HashMap<>(valueMap);

			String maskvar_s = (String) clonedValueMap.get("maskvar_s");
			String clearvar_s = (String) clonedValueMap.get("clearvar_s");
			String randvalues = (String) clonedValueMap.get("randvalues");
			clonedValueMap.put("maskvar_s", StringUtils.isNotEmpty(maskvar_s) ? maskvar_s.split(",") : null);
			clonedValueMap.put("clearvar_s", StringUtils.isNotEmpty(clearvar_s) ? clearvar_s.split(",") : null);
			clonedValueMap.put("randvalues", StringUtils.isNotEmpty(randvalues) ? randvalues.split(",") : null);
			String blindMethodInfo = Actionextdatabind.getBlindMethodInfo(clonedValueMap);

			valueMap.put("blind_info",blindMethodInfo);

		} catch (Exception e) {
			Log.error("",e);
		}
		return null;
	}

	
}
