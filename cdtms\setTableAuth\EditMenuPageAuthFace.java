package net.bioknow.cdtms.setTableAuth;



import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.face.EditPageAuthFace;
import net.bioknow.uap.dbdatamng.projectauth.DAOProjectAuth;
import net.bioknow.uap.schemaplug.dtref.DtrefDAO;
import net.bioknow.uapplug.reportform.DAOReportform;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang3.StringUtils;

/**
 * 表单权限接口类
 * <AUTHOR>
 *
 */
public class EditMenuPageAuthFace implements EditPageAuthFace {

	@Override
	public boolean canUse(String tableid, String recordid, String refinfo, String menuid, String projectId) {


		try {


			if (StringUtils.isEmpty(recordid)) {
				return false;
			}


			if (StringUtils.isNotEmpty(refinfo) && StringUtils.isNotEmpty(menuid)) {
				return false;
			}



			DtrefDAO dtrefDAO = new DtrefDAO(projectId);
			String RefField = dtrefDAO.getRefField("xsht", tableid);
			if (StringUtils.isEmpty(RefField)) {
				return false;
			}
			DAOReportform daoReportform = new DAOReportform(projectId);
			String Menuid = daoReportform.getMenuid("xsht", tableid);
			if (StringUtils.isEmpty(Menuid)) {
				return false;
			}

			return false;

		} catch (Exception e) {
			Log.error("",e);
		}
		return false;


	}

	@Override
	public int getAuth(String tableid, String recordid, String refinfo, String menuid, String projectId) {
		try {
			DAOProjectAuth daoProjectAuth = new DAOProjectAuth(projectId);
			DtrefDAO dtrefDAO = new DtrefDAO(projectId);
			String RefField = dtrefDAO.getRefField("xsht", tableid);
			DAODataMng daoDataMng = new DAODataMng(projectId);
			String studyId =String.valueOf(daoDataMng.getRecord(tableid, Long.valueOf(recordid)).get(RefField));
			DAOReportform daoReportform = new DAOReportform(projectId);
			String Menuid = daoReportform.getMenuid("xsht", tableid);
			String auth = String.valueOf(daoProjectAuth.getRecordAuth(tableid, recordid, "xsht", studyId, Menuid.split(",")[0]));
			return daoProjectAuth.getRecordAuth(tableid,recordid,"xsht",studyId,Menuid.split(",")[0]);
		} catch (Exception e) {
			Log.error("",e);
		}
		return 0;
	}

}