import time
 
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.bash import Bash<PERSON>perator
from airflow.providers.http.operators.http import SimpleHttpOperator
from datetime import timedelta, datetime
import json
 
 
DAG_DEFAULT_ARGS = {
    'owner': '<PERSON><PERSON><PERSON>',
    'depends_on_past': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=1)
}
 
def get_ip():
    return '***********'
   
def sleep_python(duration: int):
    print(f"sleep for {duration} seconds")
    time.sleep(duration)
 
 
with DAG('hello_dag',
         start_date=datetime(2023, 5, 16),
         schedule_interval="0/5 * * * *",
         description="My first DAG",
         default_args=DAG_DEFAULT_ARGS, catchup=False) as dag:
 
    start_task = BashOperator(task_id="Start", bash_command="echo 'start task at {{ds}}'")
 
    sleep_task = PythonOperator(task_id="sleep", python_callable=sleep_python, op_args=(2,))
 
    get_ip_task = PythonOperator(task_id="get_ip", python_callable=get_ip)
 
    end_task = BashOperator(task_id="End",
                            bash_command="echo ip: {{ task_instance.xcom_pull(task_ids='get_ip') }}")
 
    start_task >> [sleep_task, get_ip_task] >> end_task 