import os
import ftplib  
from datetime import datetime
from airflow import DAG 
from airflow.operators.python_operator import PythonOperator

DAG_ID = 'upload_rave_data'  

default_args = {
    'owner': 'zhouhui',
    'depends_on_past': False,
    'start_date': datetime(2023, 5, 18)
}

dag = DAG(DAG_ID, default_args=default_args, schedule_interval='@daily')

download_data_dir = '/tmp/hsperfdata_root/rave_download_data'  
minio_endpoint = 'minio:9000'  
minio_bucket = 'minios3'
minio_raw_path = 'raw'       
 
def download_rave_data():
    """下载RAVE数据保存到本地目录"""
    HOST = 'ftp01.ftp.mdsol.com'
    USER = 'erkang.zhou.hengrui.com'
    PASSWORD = 'Zero2One4?'
    log_dir = '/tmp/hsperfdata_root'
    log_name = datetime.now().strftime('%Y%m%d%H%M%S') + '.log'
    log_file = os.path.join(log_dir, log_name)
    with open(log_file, 'w') as f:
         f.write(f'Start time: {datetime.now()}\n')
    ftp = ftplib.FTP_TLS(HOST,USER, PASSWORD)
    ftp.login('TLS', '')
    ftp.cwd('/')
    local_dir = 'Z:/Projects/GRP CDSC PENGR/SHR-1210-III-315/data/raw'
    os.chdir(local_dir)
    ftp.retrlines('LIST', callback=lambda s: s.lower().strip())
    filenames = []
    ftp.retrlines('NLST', callback=filenames.append)
    new_files = []
    for filename in filenames:
        if filename.startswith('SHR 1219 III 315 ????'):
              new_files.append(filename)
    for file in new_files:
        local_path = os.path.join(local_dir, file)
        ftp.retrbinary('RETR ' + file, open(local_path, 'wb').write)
    with open(log_file, 'a') as f:
        f.write(f'End time: {datetime.now()}\n')
    ftp.quit() 

def upload_rave_data_to_minio():
    """使用MinIO CLI工具mc将本地RAVE数据上传到MinIO对象存储服务器""" 
    files = os.listdir(download_data_dir)
    for fname in files:
        local_file = os.path.join(download_data_dir, fname)
        filename=fname
        #local_file = '{local_file}'.format(local_file=local_file, filename=filename)
                             
        # 获取minios3/raw目录下的文件列表
        list_cmd = 'mc ls minios3/raw' 
        result = os.popen(list_cmd).read()
     
        # 检查是否有同名文件
        if filename in result:
            # 有同名文件,获取本地文件和MinIO上的文件md5
            local_md5_cmd = f'md5sum {local_file}'   
            local_md5 = os.popen(local_md5_cmd).read().split(' ')[0]

            remote_md5_cmd = f'mc cat minios3/raw/{filename} | md5sum'
            remote_md5 = os.popen(remote_md5_cmd).read().split(' ')[0]

            # 比较md5,如果不同则上传并覆盖
            if local_md5 != remote_md5:
                upload_cmd = f'mc cp {local_file} minios3/raw --tags "key1=RAVE&key2={filename}"'  
                os.system(upload_cmd)   
                
        else:
            # 无同名文件,直接上传
            upload_cmd = f'mc cp {local_file} minios3/raw --tags "key1=RAVE&key2={filename}"'  
            os.system(upload_cmd)
        
download_rave_data_task = PythonOperator(
    task_id='download_rave_data',
    python_callable=download_rave_data,
    dag=dag
)

upload_rave_data_task = PythonOperator(
    task_id='upload_rave_data', 
    python_callable=upload_rave_data_to_minio,
    dag=dag
)   

download_rave_data_task >> upload_rave_data_task