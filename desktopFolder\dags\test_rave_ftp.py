from airflow import DAG
from airflow.providers.ftp.sensors.ftp import FTPSensor
from airflow.providers.ftp.operators.ftp import FTPDownloadOperator
from datetime import datetime

default_args = {
    'owner': 'zhouhui',
    'start_date': datetime(2023, 5, 31),
}

dag = DAG('rave_ftp_download_dag', default_args=default_args, schedule_interval='0 0 * * *')

# Define the FTP connection details
ftp_conn_id = 'my_ftp_connection'
ftp_host = 'ftp01.ftp.mdsol.com'
ftp_port = 990  # Assuming the FTP server uses port 990 for SSL/TLS
ftp_username = 'erkang.zhou.hengrui.com'
ftp_password = 'Zero2One4?'
ftp_ssl = True  # Use SSL/TLS implicit connection policy

# Define the local file path to download the remote file to
local_filepath = '/tmp/hsperfdata_root/rave_download_data/'

# Create the FTPSensor to check for file availability on the remote FTP server
ftp_sensor_task = FTPSensor(
    task_id='check_file_availability',
    ftp_conn_id=ftp_conn_id,
    filepath='/hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand/clary_sage@1_SHR_1701_II_207_15624_20230524_081100.zip',  # Specify the remote file path to check
    poke_interval=60,  # Interval between checks (in seconds)
    dag=dag,
)

# Create the FTPDownloadOperator to download the file from the remote FTP server
ftp_download_task = FTPDownloadOperator(
    task_id='download_file',
    ftp_conn_id=ftp_conn_id,
    remote_filepath='/hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand/clary_sage@1_SHR_1701_II_207_15624_20230524_081100.zip',  # Specify the remote file path to download
    local_filepath=local_filepath,
    dag=dag,
)

# Set task dependencies
ftp_sensor_task >> ftp_download_task
