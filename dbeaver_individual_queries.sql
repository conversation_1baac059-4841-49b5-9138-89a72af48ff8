-- =====================================================
-- INDIVIDUAL QUERIES FOR DBEAVER
-- Run each query separately in DBeaver
-- Copy and paste each section individually
-- =====================================================

-- Query 1: Check if you have access to both schemas
SELECT 'Schema Access Check' as check_type, 
       owner as schema_name, 
       COUNT(*) as table_count 
FROM all_tables 
WHERE owner IN ('CDTMS_PILOT', 'CDTMS_TEMP') 
GROUP BY owner
ORDER BY owner;

-- Query 2: Quick test - Compare a few key tables
SELECT 
    table_name,
    source_count,
    target_count,
    ABS(source_count - target_count) as difference
FROM (
    SELECT 'tbl_attachment' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_attachment) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_attachment) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_log_event' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_log_event) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_log_event) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_systemcode' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_systemcode) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_systemcode) as target_count
    FROM dual
);

-- Query 3: Check specific table existence
SELECT 
    table_name,
    CASE WHEN pilot_exists = 1 THEN 'YES' ELSE 'NO' END as in_pilot,
    CASE WHEN temp_exists = 1 THEN 'YES' ELSE 'NO' END as in_temp
FROM (
    SELECT 
        'tbl_attachment' as table_name,
        (SELECT COUNT(*) FROM all_tables WHERE owner = 'CDTMS_PILOT' AND table_name = 'TBL_ATTACHMENT') as pilot_exists,
        (SELECT COUNT(*) FROM all_tables WHERE owner = 'CDTMS_TEMP' AND table_name = 'TBL_ATTACHMENT') as temp_exists
    FROM dual
    UNION ALL
    SELECT 
        'tbl_log_event' as table_name,
        (SELECT COUNT(*) FROM all_tables WHERE owner = 'CDTMS_PILOT' AND table_name = 'TBL_LOG_EVENT') as pilot_exists,
        (SELECT COUNT(*) FROM all_tables WHERE owner = 'CDTMS_TEMP' AND table_name = 'TBL_LOG_EVENT') as temp_exists
    FROM dual
    UNION ALL
    SELECT 
        'tbl_systemcode' as table_name,
        (SELECT COUNT(*) FROM all_tables WHERE owner = 'CDTMS_PILOT' AND table_name = 'TBL_SYSTEMCODE') as pilot_exists,
        (SELECT COUNT(*) FROM all_tables WHERE owner = 'CDTMS_TEMP' AND table_name = 'TBL_SYSTEMCODE') as temp_exists
    FROM dual
);

-- Query 4: Manual row count checks (run these individually)
-- Copy and paste each line separately:

-- Check tbl_attachment
SELECT 'tbl_attachment' as table_name, 'PILOT' as schema, COUNT(*) as row_count FROM CDTMS_PILOT.tbl_attachment
UNION ALL
SELECT 'tbl_attachment' as table_name, 'TEMP' as schema, COUNT(*) as row_count FROM CDTMS_TEMP.tbl_attachment;

-- Check tbl_log_event  
SELECT 'tbl_log_event' as table_name, 'PILOT' as schema, COUNT(*) as row_count FROM CDTMS_PILOT.tbl_log_event
UNION ALL
SELECT 'tbl_log_event' as table_name, 'TEMP' as schema, COUNT(*) as row_count FROM CDTMS_TEMP.tbl_log_event;

-- Check tbl_systemcode
SELECT 'tbl_systemcode' as table_name, 'PILOT' as schema, COUNT(*) as row_count FROM CDTMS_PILOT.tbl_systemcode
UNION ALL
SELECT 'tbl_systemcode' as table_name, 'TEMP' as schema, COUNT(*) as row_count FROM CDTMS_TEMP.tbl_systemcode;

-- Check tbl_study_visit_set
SELECT 'tbl_study_visit_set' as table_name, 'PILOT' as schema, COUNT(*) as row_count FROM CDTMS_PILOT.tbl_study_visit_set
UNION ALL
SELECT 'tbl_study_visit_set' as table_name, 'TEMP' as schema, COUNT(*) as row_count FROM CDTMS_TEMP.tbl_study_visit_set;

-- Check tbl_eclinichistory
SELECT 'tbl_eclinichistory' as table_name, 'PILOT' as schema, COUNT(*) as row_count FROM CDTMS_PILOT.tbl_eclinichistory
UNION ALL
SELECT 'tbl_eclinichistory' as table_name, 'TEMP' as schema, COUNT(*) as row_count FROM CDTMS_TEMP.tbl_eclinichistory;

-- Query 5: Find different records (if tables have different counts)
-- Example for tbl_attachment - modify table name as needed:

-- Records in PILOT but not in TEMP
SELECT 'Records in PILOT but not in TEMP' as difference_type, COUNT(*) as count_diff
FROM (
    SELECT * FROM CDTMS_PILOT.tbl_attachment 
    MINUS 
    SELECT * FROM CDTMS_TEMP.tbl_attachment
);

-- Records in TEMP but not in PILOT  
SELECT 'Records in TEMP but not in PILOT' as difference_type, COUNT(*) as count_diff
FROM (
    SELECT * FROM CDTMS_TEMP.tbl_attachment 
    MINUS 
    SELECT * FROM CDTMS_PILOT.tbl_attachment
);

-- Query 6: Extended comparison for more tables
-- Run this to check additional tables:
SELECT 
    table_name,
    source_count,
    target_count,
    CASE 
        WHEN source_count = target_count THEN 'SAME'
        ELSE 'DIFFERENT (' || ABS(source_count - target_count) || ')'
    END as status
FROM (
    SELECT 'tbl_study_crf_visit' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_study_crf_visit) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_crf_visit) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_xmgt' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_xmgt) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_xmgt) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_partner' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_study_partner) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_partner) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_proj_plan' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_proj_plan) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_proj_plan) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_risk_management' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_risk_management) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_risk_management) as target_count
    FROM dual
);

-- Query 7: Summary of all checks
-- Run this last to get an overview:
SELECT 
    'Total schemas accessible' as metric,
    TO_CHAR(COUNT(DISTINCT owner)) as value
FROM all_tables 
WHERE owner IN ('CDTMS_PILOT', 'CDTMS_TEMP')
UNION ALL
SELECT 
    'Tables in CDTMS_PILOT' as metric,
    TO_CHAR(COUNT(*)) as value
FROM all_tables 
WHERE owner = 'CDTMS_PILOT'
UNION ALL
SELECT 
    'Tables in CDTMS_TEMP' as metric,
    TO_CHAR(COUNT(*)) as value
FROM all_tables 
WHERE owner = 'CDTMS_TEMP';

-- =====================================================
-- INSTRUCTIONS FOR DBEAVER:
-- =====================================================
-- 1. Copy and paste each "Query X" section separately
-- 2. Run them one by one in DBeaver
-- 3. Start with Query 1 to verify schema access
-- 4. Use Query 4 for individual table checks
-- 5. Use Query 5 to find specific different records
-- =====================================================
