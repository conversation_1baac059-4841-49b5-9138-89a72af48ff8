/*
 Navicat MySQL Data Transfer

 Source Server         : 测试环境
 Source Server Type    : MySQL
 Source Server Version : 80028 (8.0.28)
 Source Host           : ***********:3306
 Source Schema         : susar_auto_mail

 Target Server Type    : MySQL
 Target Server Version : 80028 (8.0.28)
 File Encoding         : 65001

 Date: 18/04/2023 23:01:38
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for file_upload_record
-- ----------------------------
DROP TABLE IF EXISTS `file_upload_record`;
CREATE TABLE `file_upload_record`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `compound_folder` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '文件下载路径',
  `folder_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_send` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N',
  `is_deleted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of file_upload_record
-- ----------------------------
INSERT INTO `file_upload_record` VALUES (1, '20230418 H2023001201347 SHR-A1811-I-102_SAE46_FU2_INV.pdf', '<EMAIL>', 'SHR-A1811', 'smb://zhouh36:HR9cf3cbd8@10.10.5.56/susar/SHR-A1811/2023-04/20230418 H2023001201347 SHR-A1811-I-102_SAE46_FU2_INV.pdf', 'smb://zhouh36:HR9cf3cbd8@10.10.5.56/susar/SHR-A1811/2023-04/', 'http://***********:8085/susarAuto/file/SHR-A1811/2023-04/20230418 H2023001201347 SHR-A1811-I-102_SAE46_FU2_INV.pdf', '2023-04-18 11:37:08', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (2, 'H2023001201117 SHR-1316-III-302_SAE246_FU2_INV.pdf', '<EMAIL>', 'SHR-1316', 'smb://zhouh36:HR9cf3cbd8@10.10.5.56/susar/SHR-1316/H2023001201117 SHR-1316-III-302_SAE246_FU2_INV.pdf', 'smb://zhouh36:HR9cf3cbd8@10.10.5.56/susar/SHR-1316/', 'http://***********:8085/susarAuto/file/susar/SHR-1316/H2023001201117 SHR-1316-III-302_SAE246_FU2_INV.pdf', '2023-04-18 11:38:56', 'N', 'Y');
INSERT INTO `file_upload_record` VALUES (3, '20230417 H2023001201449 SHR-1316-III-302_SAE263_FU2_INV.pdf', '<EMAIL>', 'SHR-1316', 'smb://zhouh36:HR9cf3cbd8@10.10.5.56/susar/SHR-1316/2023-04/20230417 H2023001201449 SHR-1316-III-302_SAE263_FU2_INV.pdf', 'smb://zhouh36:HR9cf3cbd8@10.10.5.56/susar/SHR-1316/2023-04/', 'http://***********:8085/susarAuto/file/SHR-1316/2023-04/20230417 H2023001201449 SHR-1316-III-302_SAE263_FU2_INV.pdf', '2023-04-18 11:40:23', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (4, '20230413 H2022001203970 SHR6390-III-302_SAE65_FU6_INV.pdf', '<EMAIL>', 'SHR6390', 'smb://zhouh36:HR9cf3cbd8@10.10.5.56/susar/SHR6390/2023-04/20230413 H2022001203970 SHR6390-III-302_SAE65_FU6_INV.pdf', 'smb://zhouh36:HR9cf3cbd8@10.10.5.56/susar/SHR6390/2023-04/', 'http://***********:8085/susarAuto/file/SHR6390/2023-04/20230413 H2022001203970 SHR6390-III-302_SAE65_FU6_INV.pdf', '2023-04-18 11:40:56', 'Y', 'N');
INSERT INTO `file_upload_record` VALUES (5, 'H2023001201117 SHR-1316-III-302_SAE246_FU2_INV.pdf', '<EMAIL>', 'SHR-1316', 'smb://zhouh36:HR9cf3cbd8@10.10.5.56/susar/SHR-1316/2023-04/H2023001201117 SHR-1316-III-302_SAE246_FU2_INV.pdf', 'smb://zhouh36:HR9cf3cbd8@10.10.5.56/susar/SHR-1316/2023-04/', 'http://***********:8085/susarAuto/file/SHR-1316/2023-04/H2023001201117 SHR-1316-III-302_SAE246_FU2_INV.pdf', '2023-04-18 17:31:02', 'Y', 'N');

SET FOREIGN_KEY_CHECKS = 1;
