-- Step 1: Test basic connectivity
SELECT 'Connection test successful' as test_result FROM dual;

-- Step 2: Test schema access
SELECT COUNT(*) as table_count FROM all_tables WHERE owner = 'CDTMSEN_VAL';

-- Step 3: Test specific table access
SELECT COUNT(*) as attachment_count FROM CDTMSEN_VAL.tbl_attachment;

-- Step 4: Test simple comparison
SELECT 
    (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_attachment) as source_count,
    (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_attachment) as target_count
FROM dual;

-- Step 5: Test UNION ALL
SELECT 'tbl_attachment' as table_name, COUNT(*) as row_count FROM CDTMSEN_VAL.tbl_attachment
UNION ALL
SELECT 'tbl_attachment' as table_name, COUNT(*) as row_count FROM CDTMS_TEMP.tbl_attachment;
