开发、测试、生产各一套

1.用作全平台任务调度的服务器airflow 端口：8090
访问权限：
外部：https://ftp01.ftp.mdsol.com/WebInterface/login.html
外部: https://hengruimedicine-ravex.mdsol.com
以下为互通权限

存储对象服务器
sFTP生产环境 ***********:22357
sFTP测试环境 ***********:22357

医学审核平台正式环境 ***********:8000
医学审核平台测试环境 ***********:8000
医学审核平台开发环境 **********:8000

SAS 服务器 **********:22


***********:8085 susar邮件发送平台正式环境
***********:8085 susar邮件发送平台测试环境
***********:8085 susar邮件发送平台开发环境



操作系统：Linux Centos7 ；
配置需求：jdk1.8以上，tomcat8.5.85以上，mysql8.0版本，nginx1.22.0；
网络要求：满足应用程序协议，（http，SMB，ftp，smtp，TCP/IP一系列协议）；
内存要求：8GB
硬盘要求：500G


2.存储对象服务器 端口8021
以下为互通权限：
airflow服务器
sFTP生产环境 ***********:22357
sFTP测试环境 ***********:22357

医学审核平台正式环境 ***********:8000
医学审核平台测试环境 ***********:8000
医学审核平台开发环境 **********:8000

SAS 服务器 **********:22


***********:8085 susar邮件发送平台正式环境
***********:8085 susar邮件发送平台测试环境
***********:8085 susar邮件发送平台开发环境

操作系统：Linux Centos7 ；
配置需求：jdk1.8以上，tomcat8.5.85以上，mysql8.0版本，nginx1.22.0；
网络要求：满足应用程序协议，（http，SMB，ftp，smtp，TCP/IP一系列协议）；
内存要求：8GB
硬盘要求：开发、测试：250G  生产：1TB



