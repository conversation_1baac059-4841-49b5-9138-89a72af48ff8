#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;
    add_header X-Frame-Options "ALLOW-FROM https://meduap-tst.hengrui.com:8085 https://cdtms-val.hengrui.com https://cdtms.hengrui.com";
	add_header Content-Security-Policy "frame-ancestors 'self' https://meduap-tst.hengrui.com:8085 https://cdtms.hengrui.com https://cdtms-val.hengrui.com";
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Headers X-Requested-With;
    add_header Access-Control-Allow-Methods GET,POST,OPTIONS;
  #  add_header Content-Security-Policy upgrade-insecure-requests;
    server {
        listen       9528;
        server_name  localhost;
 	 # 上述端口指向的根目录
         root /home/<USER>/8087/front/dist;
    	# 项目根目录中指向项目首页
    	 index index.html;
    	 add_header X-Frame-Options "ALLOW-FROM https://meduap-tst.hengrui.com:8085 https://cdtms-val.hengrui.com https://cdtms.hengrui.com";
		 add_header Content-Security-Policy "frame-ancestors 'self' https://meduap-tst.hengrui.com:8085 https://cdtms.hengrui.com https://cdtms-val.hengrui.com";
        #charset koi8-r;
        #access_log  logs/host.access.log  main;
        location = / {
           try_files $uri $uri/ @router;
           index index.html index.htm;
        }
        location ~ /sas_online/  {
		  #host修改为真实的域名和端口
				   add_header X-Frame-Options "ALLOW-FROM https://meduap-tst.hengrui.com:8085 https://cdtms.hengrui.com https://cdtms-val.hengrui.com";
				   add_header Content-Security-Policy "frame-ancestors 'self' https://meduap-tst.hengrui.com:8085 https://cdtms.hengrui.com https://cdtms-val.hengrui.com";
				   proxy_set_header   Host             $host;
                   #客户真实ip
                   proxy_set_header   X-Real-IP        $remote_addr;
                   proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
                  
                   #客户端真实协议
                   # proxy_set_header   X-Forwarded-Proto  $scheme;
                   proxy_pass  http://localhost:8087;   #设置代理服务器的协义和地址
				   client_max_body_size 50m;
                   # proxy_ssl_verify off;
                   # proxy_ssl_server_name on;
        }
	# 由于路由的资源不一定是真实的路径，无法找到具体文件
    	# 所以需要将请求重写到 index.html 中，然后交给真正的 Vue 路由处理请求资源
    	location @router {
      	    rewrite ^.*$ /index.html last;
    	}
        #error_page  404              /404.html;
        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
    #    location = /50x.html {
    #        root   html;
    #    }
    	location ~ /#/ {   
			  add_header X-Frame-Options "ALLOW-FROM https://meduap-tst.hengrui.com:8085 https://cdtms.hengrui.com https://cdtms-val.hengrui.com";
			  add_header Content-Security-Policy "frame-ancestors 'self' https://meduap-tst.hengrui.com:8085 https://cdtms.hengrui.com https://cdtms-val.hengrui.com";
			  #host修改为真实的域名和端口
			  proxy_set_header   Host             $http_host;
			  #客户真实ip
			  proxy_set_header   X-Real-IP        $remote_addr;
			  proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
			  #客户端真实协议
			  proxy_set_header   X-Forwarded-Proto  $scheme;
			  proxy_pass  http://localhost:8087;   #设置代理服务器的协义和地址
	}
        # proxy the PHP scripts to Apache listening on 127.0.0.1:80
        #
        #location ~ \.php$ {
        #    proxy_pass   http://127.0.0.1;
        #}

        # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
        #
        #location ~ \.php$ {
        #    root           html;
        #    fastcgi_pass   127.0.0.1:9000;
        #    fastcgi_index  index.php;
        #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
        #    include        fastcgi_params;
        #}

        # deny access to .htaccess files, if Apache's document root
        # concurs with nginx's one
        #
        #location ~ /\.ht {
        #    deny  all;
        #}
    }


    # another virtual host using mix of IP-, name-, and port-based configuration
    #
    #server {
    #    listen       8000;
    #    listen       somename:8080;
    #    server_name  somename  alias  another.alias;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}

    # HTTPS server
    #
    #server {
    #    listen       443 ssl;
    #    server_name  localhost;

    #    ssl_certificate      cert.pem;
    #    ssl_certificate_key  cert.key;

    #    ssl_session_cache    shared:SSL:1m;
    #    ssl_session_timeout  5m;

    #    ssl_ciphers  HIGH:!aNULL:!MD5;
    #    ssl_prefer_server_ciphers  on;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}
   server {
        listen       443 ssl;
        server_name  localhost;
        ssl_certificate     /home/<USER>
        ssl_certificate_key /home/<USER>

        ssl_session_cache    shared:SSL:1m;
		ssl_session_timeout  5m;
        ssl_ciphers  HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;
		add_header X-Frame-Options "ALLOW-FROM https://meduap-tst.hengrui.com:8085 https://cdtms.hengrui.com https://cdtms-val.hengrui.com";
		add_header Content-Security-Policy "frame-ancestors 'self' https://meduap-tst.hengrui.com:8085 https://cdtms.hengrui.com https://cdtms-val.hengrui.com";

        location = / {
              root   html;
              index  index.html index.htm;
           }

        location / {
			proxy_pass  http://localhost:8087;
            add_header X-Frame-Options "ALLOW-FROM https://meduap-tst.hengrui.com:8085 https://cdtms.hengrui.com https://cdtms-val.hengrui.com";
			add_header Content-Security-Policy "frame-ancestors 'self' https://meduap-tst.hengrui.com:8085 https://cdtms.hengrui.com https://cdtms-val.hengrui.com";
        }
     }
}
