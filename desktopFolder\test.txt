﻿%global system studyid root version dir lang m_minio;
proc datasets lib=work nolist kill; quit;
%let dir=%sysfunc(getoption(work));
x "cd &dir";
%put &dir;

/*初始化minio别名*/

x "mkdir ./pgm";x 'mc find minios3-t/pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';%include "&dir/pgm/*.sas";

/*指定minio宏参数*/

%let m_minio=minios3-t;


x "mkdir ./pgm";x 'mc find &m_minio./pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';%include "&dir/pgm/*.sas";

%let studyid=&studyid.;

/*%let studyid=SHR-1703-201;*/
%let env=UAT;
%let type=edcserver/sas;

/*语言能否获得*/
/*%let lang="CH";*/

/*%m_post2s3(studyid=&studyid.);*/
/*%m_gets3data(studyid=&studyid.,data=@);*/


option mprint symbolgen validvarname=v7;

/*%let jsonPath=minios3-t/uat/json/CDTMS手册优化测试项目_UAT_dbafeeabf7dfb071.json;*/

%M_std_uatpre;

/*%M_std_uatpre(json_path=&jsonPath.);*/


/*sas.submit('''x "nohup mc alias set minios3-t http://10.10.14.28:9000 bio_liaw Bioknow##2023";''')*/
