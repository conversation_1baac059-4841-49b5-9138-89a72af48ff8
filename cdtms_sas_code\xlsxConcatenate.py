import pandas as pd
from openpyxl import load_workbook
import argparse  # Import argparse for command-line argument parsing

def concatenate_excel(source_file, sample_file,output_file):
    # 读取源Excel文件
    source_file = f'/home/<USER>/8087/DVS/{source_file}.xlsx'
    sheet_name = '逻辑核查设置'  # 替换为源Excel文件中的sheet名称
    columns_to_copy = ['生效状态','DVP版本','DVPID','来源','表','变量','核查类型','逻辑核查描述','质疑提示信息','PD类别']  # 替换为要复制的列名

    # 使用pandas读取sheet并选择特定的列
    df = pd.read_excel(source_file, sheet_name=sheet_name, usecols=columns_to_copy)
    df = df[df['生效状态'] == '是']
    df = df.drop(columns=['生效状态'])
    old_column_names = ['DVP版本','DVPID','质疑提示信息']  # 替换为要修改的列名列表
    new_column_names = [ 'DVS版本号','DVS ID','界面提示信息']  # 替换为新的列名列表
    df.rename(columns={old_name: new_name for old_name, new_name in zip(old_column_names, new_column_names)}, inplace=True)
    
    # 增加行号
    new_column_name0 = '序号'
    df[new_column_name0] = range(1, len(df) + 1)
    # 增加固定列
    new_column_name1 = '修改类型(新增/修改/删除)'
    new_column_name2 = 'DM意见'
    new_column_name3 = 'TDM意见'
    new_column_name4 = '项目组意见'
    new_column_values1 =''
    new_column_values2 =''
    new_column_values3 =''
    new_column_values4 =''

    df[new_column_name1] = new_column_values1
    df[new_column_name2] = new_column_values2
    df[new_column_name3] = new_column_values3
    df[new_column_name4] = new_column_values4

    new_column_order = ['序号', 'DVS版本号','修改类型(新增/修改/删除)','DVS ID','来源','表','变量','核查类型','逻辑核查描述','界面提示信息','PD类别','DM意见','TDM意见','项目组意见']
    df = df.reindex(columns=new_column_order)
    source_sheet = df
    print(source_sheet)
    
    # 加载或创建目标Excel文件
    try:
        target_wb = load_workbook(f'/home/<USER>/8087/DVS/{sample_file}.xlsx')
    except FileNotFoundError:
        from openpyxl import Workbook
        target_wb = Workbook()
        target_wb.save(f'/home/<USER>/8087/DVS/{sample_file}.xlsx')  # 创建一个空的工作簿
        target_wb = load_workbook(f'/home/<USER>/8087/DVS/{sample_file}.xlsx')  # 重新加载以获取Worksheet对象

    target_sheet = target_wb.active if target_wb.sheetnames else target_wb.create_sheet()

    ws = target_wb['逻辑核查']
    for row in ws.iter_rows(min_row=2):
        for cell in row:
            cell.value = None
    for index, row in source_sheet.iterrows():
        ws.append(row.tolist())
    target_wb.save(f'/home/<USER>/8087/DVS/{output_file}.xlsx')

if __name__ == '__main__':
    parser = argparse.ArgumentParser()  # Create an ArgumentParser object
    parser.add_argument('--source_file', required=True, help='Source Excel file name without extension')
    parser.add_argument('--output_file', required=True, help='Output Excel file name without extension')
    args = parser.parse_args()  # Parse the command-line arguments

    try:
        concatenate_excel(args.source_file, args.output_file)  # Use parsed arguments
        print('所有文件处理完成')
    except Exception as e:
        print(f'处理过程中出错: {str(e)}')
