package net.bioknow.cdtms.studyClose;

import net.bioknow.services.uap.dbdatamng.function.DTTableFuncActionNew;
import net.bioknow.services.uap.dbdatamng.function.FuncInfoBeanNew;
import net.bioknow.services.uap.dbdatamng.function.FuncParamBeanNew;
import net.bioknow.uap.dbdatamng.function.DTTableFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;


public class DTRFstudyOpenVue extends DTTableFuncActionNew {


	@Override
	public boolean canUse(int auth, String tableid, String refinfo, boolean readonly) {
		try {
			String projectId = SessUtil.getSessInfo().getProjectid();
			String userid = SessUtil.getSessInfo().getUserid();
			List<Map<String, String>> studyCloseIntegrateList = DAOstudyCloseIntegrate.listRule(projectId);
			String studyCloseIntegrateTableid = studyCloseIntegrateList.get(0).get(DAOstudyCloseIntegrate.tableid);
			String studyCloseAuthorizedRole = studyCloseIntegrateList.get(0).get(DAOstudyCloseIntegrate.AuthorizedRole);

			Map currentUserMap = SessUtil.getSessInfo().getUser();

			String[] AuthorizedRole = studyCloseAuthorizedRole.split(",");
			String[] currentRole = String.valueOf(currentUserMap.get("ryjs_org")).split(",");
			if(StringUtils.equals(tableid,studyCloseIntegrateTableid) && (!Collections.disjoint(Arrays.asList(AuthorizedRole), Arrays.asList(currentRole)) || Long.parseLong(userid)<10)){
				return true;
			}
		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}



	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBeanNew fpb) {
		try {


			this.redirectByUri(request, response, "/studyClose.showOpen.do?tableid="+fpb.getTableid());
			return;
		} catch (Exception e) {
			Log.error("", e);
		}
	}


	public FuncInfoBeanNew getFIB(String tableid) {
		FuncInfoBeanNew fib = new FuncInfoBeanNew("cdtmsen_val");
		try{
			fib.setName("恢复项目");
			fib.setType(FuncInfoBeanNew.FUNCTYPE_INNERWINDOW);
			fib.setWinHeight(600);
			fib.setWinWidth(800);
			fib.setSimpleViewShow(true);

		} catch (Exception e) {
			Log.error("",e);
		}
		return fib;
	}

}
