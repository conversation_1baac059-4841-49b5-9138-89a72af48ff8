#!/bin/bash

Logdir=/usr/local/mysql/data
Time=$(date +%F)
mkdir -p /usr/local/mysql/logs/"log-"$Time
if [ $? -ne 0 ]; then
echo "mkdir failed" >> /usr/local/mysql/logs/db_backup.log
exit 1
fi

find $Logdir -type f -mmin 1 -exec bash -c 'cp "$0" /usr/local/mysql/logs/' {} \;

if [ -f /usr/local/mysql/logs/mysql-bin.* ];then
echo "mysql backup success on $(date +%F)" >> /usr/local/mysql/logs/"log-"$Time/db_backup.log
else
echo "mysql backup failed on $(date +%F)" >> /usr/local/mysql/logs/"log-"$Time/db_backup.log
fi

export MYSQL_PWD=Hr@mysql1024;
/usr/bin/mysqladmin -uroot flush-logs >/dev/null