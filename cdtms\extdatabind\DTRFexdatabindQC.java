package net.bioknow.cdtms.extdatabind;

import net.bioknow.cdtms.lightpdfSign.LightpdfSignIntegrateUtil;
import net.bioknow.mvc.tools.WebPath;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTRecordFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.fileup.DAOFileup;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


public class DTRFexdatabindQC extends DTRecordFuncAction {


	public boolean canUse(int auth, String tableid, Long recordid) {

		try {

			if (!StringUtils.equals(tableid,"ext_data_bind")) {
				return false;
			}

			if(Long.valueOf(SessUtil.getSessInfo().getUserid())<=2) return  true;

			String projectid = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng = new DAODataMng(projectid);

			Map exdataBindMap = daoDataMng.getRecord(tableid, recordid);

			String is_approve = (String) exdataBindMap.get("is_approve");
			String blind_status = (String) exdataBindMap.get("blind_status");
//			String blind_status = (String) exdataBindMap.get("blind_status");
			if (StringUtils.isNotEmpty(is_approve) ) {
				return false;

			}



			Long studyid = (Long) exdataBindMap.get("study_id");
			Map userMap = SessUtil.getSessInfo().getUser();

			List StudyUserList = daoDataMng.listRecord("roles", "obj.studyid=" + studyid + " and obj.active='1' and obj.member=" + (String) userMap.get("id"), null, 1);
			if (CollectionUtils.isEmpty(StudyUserList)) {

				return false;

			}



			Map StudyUserMap = (Map) StudyUserList.get(0);

			String role = (String) StudyUserMap.get("limitnum");


			if ((StringUtils.equals(role,"EDM(QC)"))) {

				return true;

			}






		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}

	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBean fpb) {
		try {




			this.forwardByUri(request,response,"/extdatabind.qcajaxmenu.do?id="+fpb.getRecordid()+"&tableid="+fpb.getTableid()+"&refinfo="+fpb.getRefinfo());





		} catch (Exception e) {
			Log.error("",e);
		}
	}

	public FuncInfoBean getFIB(String tableid) {
		FuncInfoBean fib = new FuncInfoBean();

		fib.setName("QC结果");
		fib.setType(FuncInfoBean.FUNCTYPE_AJAXMENU);
		fib.setWinHeight(800);
		fib.setWinWidth(800);
		fib.setSimpleViewShow(true);


		return fib;
	}

}
