# MINIO命令行

### 安装客户端

#### ./mc alias set minios3 http://10.10.5.75:9000 minioadmin minioadmin

### 列出minio中的存储对象

#### mc ls minios3/bucketname 

### 列出tag

#### mc tag list minios3/raw/success.log

### 上传文件

#### mc cp success.log minios3/raw --tags "key1=xxxx&key2=xxxx"

### 删除bucket里的文件（多个）

#### mc rm minios3/raw/file1.txt minios3/raw/file2.txt minios3/raw/file3.txt

### 删除bucket：

####  mc rb --force minios3/airflow-logs

### 创建bucket:

#### mc mb minios3/airflow-dags

# Airflow 命令行

### 安装

### pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/   apache-airflow;

#### pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/   pymysql；

### 数据库赋权：

### 创建airflow安装库

#### CREATE DATABASE IF NOT EXISTS airflow DEFAULT CHARACTER SET utf8 DEFAULT COLLATE utf8_general_ci;

### 赋权

#### grant all privileges on airflow.* to 'root'@'%' identified by 'Hr_mysql1024';

#### flush privileges;



### 修改airflow.cfg配置文件中启动库和执行器配置：

#### sql_alchemy_conn =  mysql+pymysql://root:Hr_mysql1024@localhost:3306/airflow?charset=utf8

#### executor = LocalExecutor

### 用户创建：

#### airflow users create --username xxxx --firstname xxx --lastname xxxx--role Admin --email <EMAIL>



### 用户删除

#### airflow users delete --username admin



##  web重启：

### 停止

#### ps -ef|egrep 'scheduler|airflow-webserver'|grep -v grep|awk '{print $2}'|xargs kill -9

### 启动

#### nohup airflow webserver -p 8090 >>$AIRFLOW_HOME/airflow-webserver.log 2>&1 &

### scheduler重启：

### 停止

####  rm -rf /xxxx/airflow/airflow-scheduler.pid 

### 启动

#### airflow scheduler -D 守护进程方式

### Dag脚本验证：

#### 列出当前dag下的所有任务：

#### 格式---airflow tasks list dag名称，样例：

#### airflow tasks list process_sas_data

#### 验证任务是否执行成功：

####  格式---airflow  tasks test dag名称 任务名称 yyyymmdd，样例：

#### airflow  tasks test upload_rave_data  upload_rave_data 20230518
