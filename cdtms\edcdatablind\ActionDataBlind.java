package net.bioknow.cdtms.edcdatablind;

import net.bioknow.mvc.RootAction;
import net.bioknow.passport.datamng.PassportCacheUtil;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.schemaplug.attach.AttachDAO;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.FileUtil;
import net.bioknow.webutil.tools.Log;
import net.bioknow.webutil.tools.URLUtil;
import net.bioknow.webutil.tools.UUIDUtil;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class ActionDataBlind extends RootAction {

    public void addEdcBlind(HttpServletRequest request, HttpServletResponse response) throws IOException {

        try {
            String rid = request.getParameter("rid");
            String studycode = request.getParameter("studycode");
            String data = request.getParameter("data");
            String[] datas = data.split(";;", 7);
            if(datas.length != 7){
                this.gotoMsgWin(request, response, "参数错误");
            }
            String exportTime = datas[0];
            String reportFileName = datas[1];
            String reportFilePath = datas[2];
            String exclusionDataSetFileName = datas[3];
            String logFileName = datas[4];
            String exclusionDataSetFilePath = datas[5];
            String logFilePath = datas[6];

            String projectId = SessUtil.getSessInfo().getProjectid();
            String loginid = SessUtil.getSessInfo().getUserloginid();
            String userid = PassportCacheUtil.getUserIdByLoginid(projectId, loginid);
            String unitid = PassportCacheUtil.getUnitIdByUserid(projectId, userid);
            DAODataMng dmdao = new DAODataMng(projectId);
            String tableid = "study_data_blind";
            Map mapdata = dmdao.getRecord("study_data_blind", Long.parseLong(rid));
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            mapdata.put("export_date", sf.parse(exportTime));

            String reportFileuuid = getFileUuid(projectId, studycode, tableid, reportFileName, reportFilePath);
            mapdata.put("source_file", reportFileName+"*"+reportFileuuid+"|");

            String exclusionDataSetFileuuid = getFileUuid(projectId, studycode, tableid, exclusionDataSetFileName, exclusionDataSetFilePath);
            mapdata.put("blinded_file", exclusionDataSetFileName+"*"+exclusionDataSetFileuuid+"|");

            dmdao.saveRecord(tableid, mapdata);

            String logFileuuid = getFileUuid(projectId, studycode, tableid, logFileName, logFilePath);
            File filelog = new AttachDAO(projectId).getFile(logFileuuid, tableid);
            String logstr = FileUtil.readFile(filelog);
            String[] logs = logstr.split("\r\n");
            String fnames = "";
            for(String str : logs){
                if(StringUtils.isNotEmpty(fnames)) break;
                if(str.startsWith("变量名")) fnames = str;
            }
            String[] re = fnames.split(":");
            fnames = re[1];
            String[] ft = fnames.split(",");
            List listtosave = new ArrayList();
            Long sn = 1L;
            for(String s : ft){
                int m = s.indexOf("(");
                int n = s.indexOf(";");
                String fname = s.substring(0, m);
                String tname = s.substring(n+1, s.length()-1);
                Map map = new HashMap();
                listtosave.add(map);
                map.put("study_data_blind_id", Long.parseLong(rid));
                map.put("table_code", tname);
                map.put("filed_code", fname);
                map.put("sn", sn);
                sn ++;
            }
            dmdao.delRecord("study_data_blind_param", "obj.study_data_blind_id='"+rid+"'");
            dmdao.saveBatch("study_data_blind_param", listtosave, Long.parseLong(userid), StringUtils.isEmpty(unitid)?null:Long.parseLong(unitid));

            //设盲记录
            Map mapsub = new HashMap();
            mapsub.put("study_data_blind_id", Long.parseLong(rid));
            mapsub.put("bind_date", sf.parse(exportTime));
            mapsub.put("user_name", mapdata.get("owner_name"));
            mapsub.put("user_name", mapdata.get("owner_name"));
            mapsub.put("bind_file", mapdata.get("owner_name"));
            dmdao.delRecord("study_data_blind_review", "obj.study_data_blind_id='"+rid+"'");
            dmdao.saveRecord("study_data_blind_review", mapsub);
            response.getOutputStream().write("OK".getBytes(StandardCharsets.UTF_8));
            response.getOutputStream().close();
        } catch (Exception e) {
            Log.error(e.getMessage(), e);
        }
    }

    private String getFileUuid(String projectId, String studycode, String tableid, String reportFileName, String reportFilePath) {
        try{

            File tempfolder = AttachDAO.getTempFolder();
            AttachDAO attachDAO = new AttachDAO(projectId);
            String uuid = UUIDUtil.get()+"."+attachDAO.getFileExt(reportFileName);
            File file = new File(tempfolder, uuid);
            file.createNewFile();
            String downloadurl = PathUtil.getStudyPath(projectId, studycode) + "/report/report/download?path="+ URLEncoder.encode(reportFilePath, "UTF8")+"&fileName="+URLEncoder.encode(reportFileName, "UTF8");
            URLUtil.download(downloadurl, file);
            return attachDAO.saveFile(file, tableid);
        }catch (Exception e){
            Log.error(e.getMessage(), e);
        }
        return "";
    }

}
