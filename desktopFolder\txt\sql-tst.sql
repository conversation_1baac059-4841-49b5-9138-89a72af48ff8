/*
 Navicat Premium Data Transfer

 Source Server         : 虚拟机
 Source Server Type    : MySQL
 Source Server Version : 80020
 Source Host           : *************:3307
 Source Schema         : book

 Target Server Type    : MySQL
 Target Server Version : 80020
 File Encoding         : 65001

 Date: 16/02/2023 01:40:09
*/
CREATE DATABASE
IF
	NOT EXISTS `susar_tst_mail` DEFAULT CHARACTER
	SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `susar_tst_mail`;

SET NAMES utf8mb4;

SET FOREIGN_KEY_CHECKS = 0;
-- ----------------------------
-- Table structure for file_upload_record
-- ----------------------------
DROP TABLE
    IF
    EXISTS `file_upload_record`;
CREATE TABLE `file_upload_record` (
                                      `id` INT NOT NULL AUTO_INCREMENT,
                                      `file_name` VARCHAR ( 255 ) CHARACTER
                                          SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                                      `user_name` VARCHAR ( 32 ) CHARACTER
                                          SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                                      `compound_folder` VARCHAR ( 200 ) CHARACTER
                                          SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
                                      `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                      PRIMARY KEY ( `id` ) USING BTREE
) ENGINE = INNODB AUTO_INCREMENT = 1 CHARACTER
SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

ALTER TABLE file_upload_record ADD COLUMN file_path VARCHAR ( 255 ) DEFAULT NULL COMMENT '文件下载路径' AFTER compound_folder;-- ----------------------------
-- Table structure for compound_folder
-- ----------------------------
DROP TABLE
    IF
    EXISTS `compound_folder`;
CREATE TABLE `compound_folder` (
                                   `id` INT NOT NULL AUTO_INCREMENT,
                                   `folder_name` VARCHAR ( 255 ) CHARACTER
                                       SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                                   `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                   PRIMARY KEY ( `id` ) USING BTREE,
                                   UNIQUE INDEX `id` ( `id` ASC ) USING BTREE
) ENGINE = INNODB AUTO_INCREMENT = 8 CHARACTER
SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;-- ----------------------------


SET FOREIGN_KEY_CHECKS = 1;-- ----------------------------
-- Table structure for mail_send__record
-- ----------------------------
DROP TABLE
    IF
    EXISTS `mail_send_record`;
CREATE TABLE `mail_send_record` (
                                    `id` INT NOT NULL AUTO_INCREMENT,
                                    `mail_content` VARCHAR ( 255 ) CHARACTER
                                        SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                                    `receiver` VARCHAR ( 32 ) CHARACTER
                                        SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                                    `status` CHAR ( 1 ) NOT NULL DEFAULT 'N',
                                    `file_id` INT NOT NULL,
                                    `send_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                    PRIMARY KEY ( `id` ) USING BTREE
) ENGINE = INNODB AUTO_INCREMENT = 1 CHARACTER
SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
ALTER TABLE file_upload_record ADD COLUMN is_deleted CHAR NOT NULL DEFAULT 'N' AFTER create_time;
ALTER TABLE mail_send_record ADD COLUMN is_deleted CHAR NOT NULL DEFAULT 'N' AFTER send_time;
ALTER TABLE mail_send_record MODIFY COLUMN receiver TEXT;
ALTER TABLE mail_send_record ADD COLUMN file_name TEXT AFTER mail_content;
ALTER TABLE mail_send_record ADD COLUMN sender VARCHAR(255) AFTER file_name;
ALTER TABLE file_upload_record ADD COLUMN folder_path VARCHAR ( 255 ) NULL AFTER file_path;
ALTER TABLE file_upload_record ADD COLUMN is_send CHAR NOT NULL DEFAULT 'N' AFTER create_time;
ALTER TABLE compound_folder ADD COLUMN is_deleted CHAR NOT NULL DEFAULT 'N' AFTER create_time;
ALTER TABLE file_upload_record ADD COLUMN url VARCHAR ( 255 ) NULL AFTER folder_path;
ALTER TABLE mail_send_record CHANGE file_id compound_folder VARCHAR ( 255 ) DEFAULT NULL COMMENT '化合物文件夹名称';
ALTER TABLE mail_send_record ADD COLUMN operate_user VARCHAR ( 255 ) DEFAULT 'system' COMMENT '发送人' AFTER receiver;
DROP TABLE
    IF
    EXISTS `file_store_folder`;
CREATE TABLE `file_store_folder` (
                                     `id` INT NOT NULL AUTO_INCREMENT,
                                     `folder_name` VARCHAR ( 255 ) CHARACTER
                                         SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                                     `folder_path` VARCHAR ( 255 ) CHARACTER
                                         SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                                     `compound_folder` VARCHAR ( 200 ) CHARACTER
                                         SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
                                     `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                     `is_deleted` CHAR NOT NULL DEFAULT 'N',
                                     PRIMARY KEY ( `id` ) USING BTREE
) ENGINE = INNODB AUTO_INCREMENT = 1 CHARACTER
SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

SET NAMES utf8mb4;

SET FOREIGN_KEY_CHECKS = 0;-- ----------------------------
-- Table structure for t_site
-- ----------------------------
DROP TABLE
    IF
    EXISTS `t_site`;
CREATE TABLE `t_site` (
                          `ID` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
                          `compound_no` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
                          `compound_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
                          `study_no` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
                          `study_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
                          `site_no` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
                          `site_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL
) ENGINE = INNODB CHARACTER
SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

SET NAMES utf8mb4;

SET FOREIGN_KEY_CHECKS = 0;-- ----------------------------
-- Table structure for t_user
-- ----------------------------
DROP TABLE
    IF
    EXISTS `t_user`;
CREATE TABLE `t_user`  (
                           `ID` bigint NULL DEFAULT NULL,
                           `site_user_role` bigint NULL DEFAULT NULL,
                           `site_user_email` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
                           `site_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for user_info
-- ----------------------------
DROP TABLE IF EXISTS `user_info`;
CREATE TABLE `user_info`  (
                              `id` int NOT NULL AUTO_INCREMENT,
                              `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                              `user_password` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                              `role` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
                              `user_mail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                              `site_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外键，关联site表',
                              `validate_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                              `is_deleted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N',
                              `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                              `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                              `code_generate_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of user_info
-- ----------------------------
INSERT INTO `user_info` VALUES (1,'<EMAIL>', '$2a$10$AwXaSJlZte78Eq0VR2uZv.uLB45p.4h7NL82lSVz2QLNHaKrDXkZ2', 'Admin', '<EMAIL>', NULL, NULL, 'N', '2023-06-20 15:22:39', '2023-06-20 15:22:39', '2023-06-20 15:22:39');

SET FOREIGN_KEY_CHECKS = 1;



SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for user_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `user_operation_log`;
CREATE TABLE `user_operation_log`  (
                                       `id` int NOT NULL AUTO_INCREMENT,
                                       `user_id` int NOT NULL,
                                       `object_id` int NULL DEFAULT NULL COMMENT '文件id',
                                       `operation_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '未知' COMMENT '操作类型',
                                       `object_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '对象类型',
                                       `object_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '对象名称',
                                       `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Y',
                                       `operate_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 112 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of user_operation_log
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_site_clear
-- ----------------------------
DROP TABLE IF EXISTS `t_site_clear`;
CREATE TABLE `t_site_clear`  (
                                 `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 `site_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT 'site表主键',
                                 `compound_name` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '化合物名称',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- Table structure for t_user
-- ----------------------------
DROP TABLE IF EXISTS `t_cc_user`;
CREATE TABLE `t_cc_user`  (
                              `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
                              `site_user_role` int NULL DEFAULT NULL COMMENT '角色代码，1 代表 Site_PI, 2 代表 Site_INV, 3 代表机构SUSAR人员',
                              `site_user_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户邮箱',
                              `site_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外键，关联site表',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;


-- ----------------------------
-- Function for process pdf start package name
-- ----------------------------
DROP FUNCTION if EXISTS get_substr;
DELIMITER ;;
CREATE DEFINER=`root`@`%` FUNCTION `get_substr`(input_str VARCHAR(255)) RETURNS varchar(255) CHARSET utf8mb4
    DETERMINISTIC
BEGIN
    DECLARE start_pos INT;
    DECLARE end_pos INT DEFAULT -1;
    DECLARE i INT;

SELECT INSTR(input_str, 'H') INTO start_pos;

SET i = start_pos;
    WHILE i <= LENGTH(input_str) DO
        IF SUBSTR(input_str, i, 1) = ' ' THEN
            IF SUBSTR(input_str, i+1, 1) REGEXP '^[A-Za-z]' THEN
                SET end_pos = i - 1;
END IF;
END IF;
        SET i = i + 1;
END WHILE;

    IF end_pos = -1 THEN
        RETURN SUBSTR(input_str, start_pos, LENGTH(input_str));
ELSE
        RETURN SUBSTR(input_str, start_pos, end_pos - start_pos + 1);
END IF;
END ;;
DELIMITER ;


-- ----------------------------
-- Table structure for mail_config
-- ----------------------------

DROP TABLE
    IF
    EXISTS `mail_config`;
CREATE TABLE `mail_config` (
                               `id` INT NOT NULL AUTO_INCREMENT,
                               `mail_address` VARCHAR ( 255 ) CHARACTER
                                   SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '发送邮箱地址',
                               `account` VARCHAR ( 255 ) CHARACTER
                                   SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '发送邮箱账号',
                               `password` VARCHAR ( 255 ) CHARACTER
                                   SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '发送邮箱密码',
                               `title` VARCHAR ( 255 ) CHARACTER
                                   SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '发送邮箱主题',
                               `content` TEXT CHARACTER
                                   SET utf8mb4 COLLATE utf8mb4_general_ci  COMMENT '发送邮箱内容',
                               `operate_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
                               PRIMARY KEY ( `id` ) USING BTREE
) ENGINE = INNODB AUTO_INCREMENT = 112 CHARACTER
SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_site_clear_backup
-- ----------------------------
DROP TABLE IF EXISTS `t_site_clear_backup`;
CREATE TABLE `t_site_clear_backup`  (
                                        `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        `site_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'site表主键',
                                        `compound_name` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '化合物名称',
                                        `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25029 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- Data of mail_config
-- ----------------------------
INSERT INTO `mail_config` ( `mail_address`, `account`, `password`, `title`, `content`, `operate_time` )
VALUES
    (
        '<EMAIL>',
        '<EMAIL>',
        'HR230505@',
        '恒瑞医药【test】的临床试验中SUSAR安全性信息通知信',
        '尊敬的研究者、机构/伦理老师，\r\n您好！\r\n感谢您对江苏恒瑞医药股份有限公司申办的临床试验的支持。\r\n根据《药物临床试验质量管理规范》的公告（2020年第57号）第四十八条规定：申办者应将可疑且非预期严重不良反应快速报告给所有参加临床试验的研究者及临床试验机构、伦理委员会。\r\n现向您提供在中国及境外开展的包含test的临床试验中SUSAR报告。\r\n请您及时阅读，并按需存档。\r\n此邮件为系统邮件，请勿回复。如有问题，请联系项目组。\r\n\r\n此致\r\n敬礼！\r\n\r\n\r\n江苏恒瑞医药股份有限公司\r\n',
        NOW());


-- ----------------------------
-- Table structure for email_bounce_info
-- ----------------------------
DROP TABLE
    IF
    EXISTS `email_bounce_info`;
CREATE TABLE `email_bounce_info` (
                                     `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键',
                                     `subject` VARCHAR ( 255 ) CHARACTER
                                         SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退信主题',
                                     `receive_time` VARCHAR ( 255 ) CHARACTER
                                         SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退信时间',
                                     `receiver` VARCHAR ( 255 ) CHARACTER
                                         SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退信邮箱地址',
                                     `error_code` VARCHAR ( 255 ) CHARACTER
                                         SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退信状态码',
                                     `is_resend` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否补发',
                                     `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     PRIMARY KEY ( `id` ) USING BTREE
) ENGINE = INNODB AUTO_INCREMENT = 25029 CHARACTER
SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;


-- ----------------------------
-- Table structure for email_dict
-- ----------------------------
DROP TABLE
    IF
    EXISTS email_dict;
CREATE TABLE email_dict (
                            `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键',
                            `code` VARCHAR ( 255 ) CHARACTER
                                SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编码',
                            `type_code` VARCHAR ( 255 ) CHARACTER
                                SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型编码',
                            `name` VARCHAR ( 255 ) CHARACTER
                                SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '字典名',
                            `value` VARCHAR ( 255 ) CHARACTER
                                SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '字典值',
                            `remark` VARCHAR(255) CHARACTER
                                SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '备注',
                            PRIMARY KEY ( `id` ) USING BTREE
) ENGINE = INNODB AUTO_INCREMENT = 0 CHARACTER
SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- Table data for email_dict
-- ----------------------------
INSERT INTO email_dict(code,type_code,name,value,remark)
VALUES('421','1','该服务无法使用，请稍后重试','该服务无法使用，请稍后重试','');
INSERT INTO email_dict(code,type_code,name,value,remark)
VALUES('450','1','由于用户的邮箱不可用，请求的操作未执行','由于用户的邮箱不可用，请求的操作未执行','');
INSERT INTO email_dict(code,type_code,name,value,remark)
VALUES('451','1','由于服务器错误，邮件未发送','由于服务器错误，邮件未发送','');
INSERT INTO email_dict(code,type_code,name,value,remark)
VALUES('452','1','由于服务器存储空间不足，命令已停止','由于服务器存储空间不足，命令已停止','');
INSERT INTO email_dict(code,type_code,name,value,remark)
VALUES('455','1','	服务器目前无法处理该命令','	服务器目前无法处理该命令','');
INSERT INTO email_dict(code,type_code,name,value,remark)
VALUES('500','1','由于语法错误，服务器无法识别该命令','由于语法错误，服务器无法识别该命令','');
INSERT INTO email_dict(code,type_code,name,value,remark)
VALUES('501','1','	在命令参数或参数中发现了语法错误','	在命令参数或参数中发现了语法错误','');
INSERT INTO email_dict(code,type_code,name,value,remark)
VALUES('502','1','	命令未执行','	命令未执行','');
INSERT INTO email_dict(code,type_code,name,value,remark)
VALUES('503','1','服务器遇到了错误的命令序列','服务器遇到了错误的命令序列','');
INSERT INTO email_dict(code,type_code,name,value,remark)
VALUES('541','1','邮件被收件人地址拒绝','邮件被收件人地址拒绝','');
INSERT INTO email_dict(code,type_code,name,value,remark)
VALUES('550','1','	由于用户的邮箱不可用，或者接收服务器因为邮件可能是垃圾邮件而拒绝了邮件，请求的命令失败','	由于用户的邮箱不可用，或者接收服务器因为邮件可能是垃圾邮件而拒绝了邮件，请求的命令失败','');
INSERT INTO email_dict(code,type_code,name,value,remark)
VALUES('551','1','目标收件人邮箱在接收服务器中不可用','目标收件人邮箱在接收服务器中不可用','');
INSERT INTO email_dict(code,type_code,name,value,remark)
VALUES('552','1','由于收件人的邮箱存储空间不足，因此邮件未发送','由于收件人的邮箱存储空间不足，因此邮件未发送','');
INSERT INTO email_dict(code,type_code,name,value,remark)
VALUES('553','1','由于邮箱名称不存在，命令已停止','由于邮箱名称不存在，命令已停止','');
INSERT INTO email_dict(code,type_code,name,value,remark)
VALUES('554','1','事务失败，且没有更多详细说明','事务失败，且没有更多详细说明','');