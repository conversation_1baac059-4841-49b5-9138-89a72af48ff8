%global system studyid root version dir lang m_minio;
proc datasets lib=work nolist kill; quit;
%let root=%sysfunc(getoption(work));
x "cd &root.";
%put &root.;
%let m_minio=minios3-t;
x "mkdir ./pgm";
x 'mc find minios3-t/pgm/ --name "m_handle_protocol_tree.sas" --exec "mc cp {} ./pgm/"';
x 'mc find minios3-t/pgm/ --name "m_protocol_trans.sas" --exec "mc cp {} ./pgm/"';
x 'mc find minios3-t/pgm/ --name "m_create_hrtaubuild.sas" --exec "mc cp {} ./pgm/"';
x 'mc find minios3-t/pgm/ --name "m_derive_hrtaubuild.sas" --exec "mc cp {} ./pgm/"';
x 'mc find minios3-t/pgm/ --name "m_gets3data_folder.sas" --exec "mc cp {} ./pgm/"';
%include "&root./pgm/*.sas";
option mprint symbolgen validvarname=v7;
%let studyid=&studyid.;
%let jsonMinioPath=&jsonPath.;
%m_gets3data_folder(studyid=&studyid.,data=@);
%let lang="CH";
%m_handle_protocol_tree;
%m_protocol_trans;