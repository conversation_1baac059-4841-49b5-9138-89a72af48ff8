/*
 Navicat MySQL Data Transfer

 Source Server         : 测试环境
 Source Server Type    : MySQL
 Source Server Version : 80028 (8.0.28)
 Source Host           : ***********:3306
 Source Schema         : susar_auto_mail

 Target Server Type    : MySQL
 Target Server Version : 80028 (8.0.28)
 File Encoding         : 65001

 Date: 18/04/2023 23:02:55
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for user_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `user_operation_log`;
CREATE TABLE `user_operation_log`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `object_id` int NULL DEFAULT NULL COMMENT '文件id',
  `operation_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '未知' COMMENT '操作类型',
  `object_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '对象类型',
  `object_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '对象名称',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Y',
  `operate_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 123 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of user_operation_log
-- ----------------------------
INSERT INTO `user_operation_log` VALUES (112, 38, 1, '上传', '文件', '20230418 H2023001201347 SHR-A1811-I-102_SAE46_FU2_INV.pdf', 'Y', '2023-04-18 11:37:08');
INSERT INTO `user_operation_log` VALUES (113, 38, 2, '上传', '文件', 'H2023001201117 SHR-1316-III-302_SAE246_FU2_INV.pdf', 'Y', '2023-04-18 11:38:56');
INSERT INTO `user_operation_log` VALUES (114, 38, 2, '删除', '文件', 'H2023001201117 SHR-1316-III-302_SAE246_FU2_INV.pdf', 'Y', '2023-04-18 11:39:44');
INSERT INTO `user_operation_log` VALUES (115, 38, 3, '上传', '文件', '20230417 H2023001201449 SHR-1316-III-302_SAE263_FU2_INV.pdf', 'Y', '2023-04-18 11:40:23');
INSERT INTO `user_operation_log` VALUES (116, 38, 4, '上传', '文件', '20230413 H2022001203970 SHR6390-III-302_SAE65_FU6_INV.pdf', 'Y', '2023-04-18 11:40:56');
INSERT INTO `user_operation_log` VALUES (117, 38, 1, '下载', '文件', '20230418 H2023001201347 SHR-A1811-I-102_SAE46_FU2_INV.pdf', 'Y', '2023-04-18 11:48:07');
INSERT INTO `user_operation_log` VALUES (118, 38, 1, '下载', '文件', '20230418 H2023001201347 SHR-A1811-I-102_SAE46_FU2_INV.pdf', 'Y', '2023-04-18 11:49:27');
INSERT INTO `user_operation_log` VALUES (119, 38, 3, '下载', '文件', '20230417 H2023001201449 SHR-1316-III-302_SAE263_FU2_INV.pdf', 'Y', '2023-04-18 11:50:11');
INSERT INTO `user_operation_log` VALUES (120, 38, 4, '下载', '文件', '20230413 H2022001203970 SHR6390-III-302_SAE65_FU6_INV.pdf', 'Y', '2023-04-18 11:51:26');
INSERT INTO `user_operation_log` VALUES (121, 38, 4, '下载', '文件', '20230413 H2022001203970 SHR6390-III-302_SAE65_FU6_INV.pdf', 'Y', '2023-04-18 11:53:48');
INSERT INTO `user_operation_log` VALUES (122, 40, 5, '上传', '文件', 'H2023001201117 SHR-1316-III-302_SAE246_FU2_INV.pdf', 'Y', '2023-04-18 17:31:02');

SET FOREIGN_KEY_CHECKS = 1;
