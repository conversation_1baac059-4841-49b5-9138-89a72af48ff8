import argparse
from openpyxl import load_workbook
from openpyxl import Workbook

def write_df_to_excel(source_file, target_file, sheet_name):
    # 加载源Excel文件
    source_wb = load_workbook(rf'/home/<USER>/8087/DVS/{source_file}.xlsx')
    source_sheet = source_wb[sheet_name]

    # 加载或创建目标Excel文件
    try:
        target_wb = load_workbook(rf'/home/<USER>/8087/DVS/{target_file}.xlsx')
    except FileNotFoundError:
        target_wb = Workbook()
        target_wb.save(rf'/home/<USER>/8087/DVS/{target_file}.xlsx')  # 创建一个空的工作簿
        target_wb = load_workbook(rf'/home/<USER>/8087/DVS/{target_file}.xlsx')  # 重新加载以获取Worksheet对象

    if sheet_name in target_wb.sheetnames:
        target_sheet = target_wb[sheet_name]
    else:
        target_sheet = target_wb.create_sheet(sheet_name)

    # 删除所有合并单元格
    for merged_range in list(target_sheet.merged_cells.ranges):
        target_sheet.unmerge_cells(str(merged_range))

    # 清空目标工作表的单元格值
    for row in target_sheet.iter_rows():
        for cell in row:
            cell.value = None

    # 复制数据
    for row in source_sheet.iter_rows(min_row=1, max_row=source_sheet.max_row, min_col=1, max_col=source_sheet.max_column):
        for cell in row:
            is_merged = False
            for merged_range in source_sheet.merged_cells.ranges:
                if cell.coordinate in merged_range:
                    # 找到合并区域的起点单元格
                    top_left = source_sheet.cell(row=merged_range.min_row, column=merged_range.min_col)
                    # 在目标工作表的对应起点单元格赋值
                    target_top_left = target_sheet.cell(row=top_left.row, column=top_left.column)
                    target_top_left.value = top_left.value
                    is_merged = True
                    break
            if not is_merged:
                # 直接赋值
                target_cell = target_sheet[cell.coordinate]
                target_cell.value = cell.value

    # 复制合并区域的格式
    for merged_range in source_sheet.merged_cells.ranges:
        target_sheet.merge_cells(str(merged_range))
        
    # 从A3单元格开始，设置字体颜色为黑色，非斜体
    black_font = Font(color='000000', italic=False)  # 定义黑色、非斜体字体
    for row in target_sheet.iter_rows(min_row=3, max_row=target_sheet.max_row, 
                                     min_col=1, max_col=target_sheet.max_column):
        for cell in row:
            cell.font = black_font
    # 保存目标Excel文件
    target_wb.save(rf'/home/<USER>/8087/DVS/{target_file}.xlsx')

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Copy data from one Excel file to another.')
    parser.add_argument('--source_file', required=True, help='The name of the source Excel file (without extension).')
    parser.add_argument('--target_file', required=True, help='The name of the target Excel file (without extension).')
    parser.add_argument('--sheet_name', required=True, help='The name of the sheet to copy data from.')

    args = parser.parse_args()

    try:
        write_df_to_excel(args.source_file, args.target_file, args.sheet_name)
        print('所有文件处理完成')
    except Exception as e:
        print(f'处理过程中出错: {str(e)}')