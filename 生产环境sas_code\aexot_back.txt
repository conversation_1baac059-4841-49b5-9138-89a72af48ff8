%global system studyid root version dir lang m_minio repdat;
proc datasets lib=work nolist kill; quit;%let dir=%sysfunc(getoption(work));

x "cd &dir";x "mkdir ./pgm";x 'mc find minios3/pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';
%include "&dir/pgm/*.sas";
option mprint symbolgen validvarname=v7;

%let m_minio=minios3;
%let repdat=&sysdate.;

%let studyid=&studyid.;

%let lang="&lang.";
/*%put &lang.;*/




%m_post2s3(studyid=&studyid.,env=uat);
%m_gets3data(studyid=&studyid.,data=@,env=uat);

option mprint symbolgen validvarname=v7;



/*%let repdat=&sysdate.;*/
/*%let m_minio=minios3;*/
x "mc find &m_minio./sdv/json --name ""&studyid._AE-EX-EOT.json"" | xargs -I{} mc cp {} ./doc/";
filename y "&root./doc/&studyid._AE-EX-EOT.json";
libname jsonrec json fileref=y;
proc copy in=jsonrec out=work;
run;
data alldata2;
	set alldata;
	if  P1='parm' then  do;
		value=tranwrd(tranwrd(tranwrd(value,"，",","),"<",""),">","");
		prnum=count(value,",")+1;
		do i=1 to prnum;
		value_=scan(value,i,",");output;
		end;
	end;
	else output;
run;

data _null_;
	set alldata2;
	if find(lowcase(value_),"exi=") then 	call symputx("exi_",scan(value_,2,"="));
	if find(lowcase(value_),"exo=") then 	call symputx("exo_",scan(value_,2,"="));
	if find(lowcase(value_),"drug_allday=") then 	call symputx("drug_allday_",scan(value_,2,"="));
	else call symputx("drug_allday_","");
	if  P1='sascode' then  do;
		if value^='' then call symputx("sascode",value);
		else  call symputx("sascode","");
	end;
run;

%put exo="&exo_."  exi="&exi_."   sascode="&sascode."  drug_allday="&drug_allday_.";


option mprint symbolgen validvarname=v7;
%m_custom_function;   
&sascode.
%M_std_dvs_ae_ex_all(isminio=Y,exo=&exo_.,exi=&exi_.,drug_allday=&drug_allday_.);
%m_exportxlsx_dmreview(check=AE-EX-EOT,outputfile=one,outputlocal=&m_minio./sdv/output/,zipyn=N,tagyn=Y) 
