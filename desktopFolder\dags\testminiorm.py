import os
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.models.connection import Connection


default_args = {
    'owner': '<PERSON><PERSON><PERSON>',
    'start_date': datetime(2023, 9, 18)
}

dag = DAG('miniorm_dag', default_args=default_args)

conn = Connection(
    conn_id="my_s3_conn",
    conn_type="s3",
    login="minioadmin",  # Reference to AWS Access Key ID
    password="minioadmin",  # Reference to AWS Secret Access Key
    extra={
        # Specify extra parameters here
         "endpoint_url": "http://10.10.14.28:9000"
    },
)
def print_connn_info():
    env_key = f"AIRFLOW_CONN_{conn.conn_id.upper()}"
    conn_uri = conn.get_uri()
    print(f"{env_key}={conn_uri}")
    os.environ[env_key] = conn_uri
    print(conn.  # Validate connection credentials.
task1 = PythonOperator(
        task_id ='task1',
        python_callable = print_connn_info,
        dag=dag 
    )
task1