import ftplib
import ssl
from io import BytesIO



def download_file(ftps: ftplib.FTP_TLS, ftps_filename: str, local_filename: str):
    """
    下载文件, FTP -> LOCAL
    :param ftp: FTP实例
    :param ftp_filename: FTP服务器的文件路径
    :param local_filename: 本地保存的文件路径
    """
    with open(local_filename, "wb") as fp:
        cmd = "RETR " + ftps_filename    # 使用 RETR 命令下载文件
        ftps.retrbinary(cmd=cmd, callback=fp.write)

class ImplicitFTP_TLS(ftplib.FTP_TLS):
    """FTP_TLS subclass that automatically wraps sockets in SSL to support implicit FTPS."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._sock = None

    @property
    def sock(self):
        """Return the socket."""
        return self._sock

    @sock.setter
    def sock(self, value):
        """When modifying the socket, ensure that it is ssl wrapped."""
        if value is not None and not isinstance(value, ssl.SSLSocket):
            value = self.context.wrap_socket(value)
        self._sock = value
      
        
ftps = ImplicitFTP_TLS()
ftps.set_debuglevel(2) 
ftps.connect(host='ftp01.ftp.mdsol.com', port=990, timeout=50)
ftps.login(user='erkang.zhou.hengrui.com', passwd='Zero2One4?')
print(ftps.getwelcome())
# 获取指定目录路径
dir_path = '/hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand/'
ftps.cwd(dir_path)
print(ftps.size("/hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand/clary_sage@1_SHR_1701_II_207_15624_20230524_081100.zip"))
download_file(ftps,ftps_filename="/hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand/clary_sage@1_SHR_1701_II_207_15624_20230524_081100.zip", local_filename="/tmp/hsperfdata_root/rave_download_data/clary_sage@1_SHR_1701_II_207_15624_20230524_081100.zip")
#ftps.list()
# 设置传输模式为二进制
#ftps.voidcmd('TYPE I')  

# 下载整个目录
#data = BytesIO()
#ftps.retrbinary('LIST', data.write)  

#files = []
#for line in data.getvalue().splitlines():
#    if '<DIR>' in line:
#        continue
#    files.append(line.split()[-1].decode())

#print(files)  
ftps.close()