package net.bioknow.cdtms.formMail;

import net.bioknow.mvc.tools.Language;
import net.bioknow.services.uap.dbdatamng.face.FaceRecordViewTab;
import net.bioknow.uap.dbcore.face.TableViewFace;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.webutil.session.SessUtil;
import org.jfree.util.Log;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EHFRVMailLog implements FaceRecordViewTab {

    @Override
    public boolean canUse(String projectId, String tableId, Map mapV) {

        try {
            String projectid = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectid);
            int mailTmplCount = daoDataMng.count("email_template", "obj.tableid='" + tableId + "'");

            if(mailTmplCount>0){

                return  true;
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return false;
    }

    @Override
    public List initPageParam(String projectId, String tableId, Map mapV, String refinfo) {

        try {
            String projectid = SessUtil.getSessInfo().getProjectid();

            DAODataMng daoDataMng = new DAODataMng(projectid);

            int mailLogCount = daoDataMng.count("email_send_log", "obj.tableid='" + tableId + "' and obj.recordid="+mapV.get("id"));
            List listRst = new ArrayList();

            Map mapRecordView = new HashMap();
            listRst.add(mapRecordView);
            Language lang = Language.getInstance(this.getClass());

            Log.info(Language.getLocString());
            mapRecordView.put("name", lang.get("mailLog"));
//            mapRecordView.put("groupName", mailLogCount);
            mapRecordView.put("count", mailLogCount);
            mapRecordView.put("type", "list");
            mapRecordView.put("url", "/uapvue/index.html#/list?prjid=" + projectId + "&tableid=email_send_log&nvwhere=obj.tableid='"+tableId + "' and obj.recordid="+mapV.get("id"));
            return listRst;


        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}
