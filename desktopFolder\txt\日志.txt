2023.02.03
1.账号权限开通,oa提交笔记本管理员权限申请
2.sas软压熟悉
3.javaGuide学习
4.需掌握的技能:vue 
  试用期转正：6个月需上线一套可用的系统（以功能为主）
  优先级：可用系统的开发，需求调研，澄清,选取合适功能
  
  
  2023.02.06
  plan：
  1.安装本地开发环境，node.js、vscode、navicat
  2.熟悉vue-admin项目
  
 do:
 1.vscode、node.js安装、git安装、tomcat安装环境配置
 2.熟悉vue-admin 前端框架
 3.xxljob 定时任务调度熟悉
 2.劳动合同签署
 
 2023.02.07
 plan:
 1.vue-admin项目启动问题解决，熟悉前端框架功能
 2.熟悉xxl-job 定时任务的实现
 3.熟悉susar邮件自动发送功能
 4.sas相关基础知识总览
 done:
 1.vue-admin项目启动问题解决，熟悉前端框架功能 10%
 2.熟悉培训计划课程
 3.sas相关基础知识:软件介绍、资源管理器、日志、编辑器、审阅DataSet
 4.navicat\postman安装
 5.susar报告原型确认 50min  archive
 
2023.02.08
plan:
1.vue_admin 前端项目熟悉文档看到快速导航 20%，改变框架表单标题和导航
2.临床研发技术创新部介绍会议 0.5h:
	业务需求：系统孤立，数据隔离未打通，信息化意识提升
	部门架构：数据科学中心的发展而来的数据创新部门，去年下半年成立，利用现有人力，
蓝图：提供职业发展的空间，对数据管理的工作的辅助，进行信息化提升，数据辅助，一体化系统的构建，临床研究的这块的数据管理，业务痛点多，功能需要完善,管理层需要数据流动，需要把控数据的流转效率，一体化、规范化，全过程管理，提升临床试验的效率和质量
tips:百奥知是第三方系统集成商
数据创新部是润滑剂，对业务了解，技术创新的人才体系培养
电子化系统的全生命周期，分析与提供与业务痛点相关的解决方案（重在解决）
便利、高效、合规、可用
个人：了解业务痛点，提出解决方案，绩效考评较模糊,要靠自己调研，分析，思考，改进、提升 individual
问题：	
进度：
susar邮件发送前端这块工作，我已本地搭建了VUE的开发环境，熟悉了一部分VUE语法和admin框架的整体功能，需要一周时间学习该框架的辅助教程，然后动手实践项目，大家看对这块工作有什么建议
done:
页面基本搭建，文件上传，

2023.02.09
paln:
1.代码版本控制，git上传
2.完成文件上传前后端集成:
	文件上传后存放的位置？
	自动发送是先给定文件的位置，去读取文件，后自动发送至相应的邮箱，然后再将文件移动到相关的备份文件夹archive下
	配置后端接口需要修改 env.xxx文件中的VUE_APP_XX_API的内容，同时需要注释vue.config.js中devServer下的before
	C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.1926656222353887212.8080\2023\02\09
	C:\Users\<USER>\Desktop
	
2023.02.10
done:
	susar邮件自动发送项目开发，完成本地文件上传测试，文件格式校验，后端文件存储逻辑、格式校验，前端页面样式调整，列表表格数据后端接口开发，整体进度10%
	
2023.02.13
plan:
	1.首页跳转，上传页面page title显示化合物文件夹
	2.SUSAR报告平台原型确认会议（1.0小时）
	
2023.02.14
done：
	1、完成susar邮件自动发送首页开发，定位并解决表格数据不展示问题
	
2023.02.15】
plan：
1.测试获取sftp文件夹下的路径，并展示到前端列表中
2.完成首页文件夹的筛选功能

done:
1.完成首页文件夹记录筛选功能，完成上传记录前端数据筛选功能，完成首页文件夹跳转显示标题显示对应文件夹名称
2.开发后端访问sftp服务器susar文件夹列表接口,目前存在访问https://clinical-ftp.hengrui.com/ 存在未开启脚本访问的限制问题


2023.02.16
done:
1.搭建本地数据库环境 创建化合物文件夹列表、上传历史记录表
2.完成操作记录表页面的后端数据填充，新增表头筛选框，动态筛选表格内的数据
3.完成本地测试Susar文件夹路径获取，并截取需要的Susar文件夹名称，接口返回列表


5.

待完成：
2.解决sftp服务器连接，获取文件路径列表信息(待完成)






3.文件上传后放Nuns服务上,smb存储
4.邮件自动导出

2023.02.17
1.会议：Argus测试库各界面简介+Demo Case 演示、BIP简介+BIP表格导出演示 1.0
2.会议:sas 1一月组会 1.5
2.完成邮件发送后端集成

2023.02.20

1.邮件自动发送工具类编写
2.完成文件本地上传接口整合邮件自动发送功能,自动签章
3.上传页面，获取当前化合物文件夹下的所有文件，本地
4.完成文件上传接口整合邮件自动发送功能
5.调整报告平台、操作记录页，上方筛选布局样式，操作记录筛选中的化合物类型选择去重实现



1.解决前端路由跳转，参数传递异常导致接口无法调用问题
2.获取上传路径文件夹下文件列表接口开发
3.文件上传后本地文件删除接口开发
questions:上传页面需要一个表格，展示当前文件夹下的所有文件内容

2023.02.21
1.xxl-job 实现定时任务
2.
3.org.susarauto.fileupload.xxl-job-core


(2023-02-22)下午3-4点培训
2023/02/24  周五上午09:30-10:30


2023.02.22
1.高效数据管理与医学监查：Rave EDC+PDS创新应用分享 1.0 hour
 内容： 8021个study，临床实验借助Rav EDC 数据采集，行业标杆，Risk-based quality managenment 实现将本增效
  DM CRA 临床数高依赖 数据流 实时更新的数据流，
 2.培训《解读HRTAU-EDC前台与数据集关系》  1.0 hour
 
 3.拼表 存放 化合物文件夹/邮箱/邮件正文文本
   定时任务：a、发送邮件：1、获取到发送的接收邮箱，获取到文件夹所在的文件，
   
			b、转移目录 archives
			
			
2023.02.23
1.编写功能开发文档
2.编写用户手册
3.进度确认会议：
4.左侧导航栏报告上传title bug修复	
5.去掉展示的文件uuid名称		
下周目标：月度包下载文件，查询结果导出，化合物选择下拉框复合支持输入查询,列表加后端分页，
			列表加后端分页，
			列表上传文件支持预览，
			nas对接，
			模板化邮件正文文本

2023.02.24
1.傲伟将介绍通过API实现Tableau权限管理   1.0 hour    lab  汇总 detail 项目单独建立
2.修复左侧导航栏bug

周计划（02.27-03.03）:
			1.月度包下载文件，查询结果导出，化合物选择下拉框复合支持输入查询
			列表加后端分页
			以上传日期为月度包的时间分类
			
			2.列表加后端分页，
			3.列表上传文件支持预览
			4.nas对接
			5.模板化邮件正文文本


2023.02.27
plan:
1.对其本周开发计划
2.测试群发邮件
3.开发下载接口


启动包: 累计式的月度包 IB更新（更新日期前的月度包全部删除）   IB更新存库，NAS服务器上的文件存库
		降级的文件删除 本地存储同名的文件，该文件也不删除
		后期发送的文件，有没有相同case号的记录，有就删除，保留最新的
   
done：
1.查询列表加excel 导出功能
2.删除接口新增 逻辑删除
3.邮件发送新增文件逻辑删除操作

2023.02.28
plan:
1.完成文件上传根据文件的上传时间创建备份文件夹
2.开发列表展示备份文件夹接口
3.开发批量下载备份文件夹下文件的接口

done:
1.文件邮件发送后，以发送时间归档到对应的月度文件夹
2.前端化合物列表展示月度文件夹
3.开发后端文件批量下载接口



文件发送逻辑：多文件 直接调用 多文件发送方法类,群发邮件列表 使用群发邮件方法



2023.03.01
1.优化邮件发送之后，备份文件逻辑，新增文件路径更新,邮件发送后更新文件位置，文件存放的文件夹位置，更新邮件发送标识位	
2.新增月度包文件列表接口，开发文件上传页面跳转月度报告列表页
3.完成月度包下载zip格式后端接口开发
4.非肿瘤临床研究部SUSAR报告发送流程沟通会 1.0


2023.03.02
plan:
1.完成月度包多选下载
2.完成操作记录下拉筛选组件更换
3.培训《解读Imedidata（RAVE）前台与数据库关系》 1.0 hour
4.新人Rave培训建库知识 培训 1.0hour

下周计划: 
1.多选多个文件打包成一个包，以一个包的形式保存(完成)
2.日期改成上传日期，excel(完成)
3.连接网盘，上传下载走网盘，地址:\\SHNVWSMB002\susar
4.“月度报告”和“操作记录”按照日期倒序排序（加后端分页）
5.操作记录的文件预览(完成)
6.邮件群发，单文件，多接收人只能形成一条记录(完成)


2023.03.03
思路：
1.查询结果导出 前端展示结果存放到本地临时文件，从后端统一读取 合并压缩
2.
3.
完成内容:
1.完成月度包列表查询结果导出，文件下载
2.完成操作列表页面查询结果的文件压缩包下载功能
3.完成月度包内文件下载后端接口开发



2023.03.04
1.测试月度包批量下载(未完成)


2023.03.06
plan:
1.月度包批量下载 zip 带文件夹(完成)
2.导出月度报告详情页，查询结果往后端传，存到本地的一个位置，然后合并为一个zip包返回给前端：
	传递参数 a.列表内查询的文件id b.前端excel blob文件流
2.后端分页
3.邮件群发 优化
4.连接nas文件服务器

done:
1.月度包批量下载 zip 带文件夹(完成)
2.导出月度报告详情页，查询结果往后端传，存到本地的一个位置，然后合并为一个zip包返回给前端(完成)
3.后端分页(完成)
got:
1.Arrays.asList(sourceFile.getPath().split("\\\\")).get(Arrays.asList(sourceFile.getPath().split("\\\\")).size()-2)
2.List<String> strList = new ArrayList<>();
  String[] strArray = strList.toArray(new String[strList.size()]);
3.post 多个参数请求
     const params = new FormData()
      for (var i = 0; i < fileList.length; i++) {
        params.append('file', fileList[i].raw)
      }
      params.append('folderName', this.param)
 4.multipartFile 和File 的互相转换
 5.mybatis 后端分页
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
@Configuration
public class pageConfig {
    //分页插件
    //配置拦截器组件
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        return new PaginationInterceptor();
    }
}


2023.03.07
1.操作记录列表页，文件预览(待完成，文件上传、邮件发送时，记录url)
2.表格后端排序(完成)
3..邮件群发 逻辑优化，多个文件，多个接收人，多文件--->接收人  只形成一条记录
4.

2023.03.08
1.操作记录，文件预览（昨天收尾）完成
2.优化群发邮件的逻辑，每个文件，群发的记录只在库里存一条
3.SMB网盘 连接  \\SHNVWSMB002\susar
共享网盘ip:**********


4.025-66777066 胡璠


2023.03.09
plan:
1.完成文件上传，查询，删除逻辑走共享网盘,包括创建文件夹
2.英才计划拓展培训_医学写作专场(1.0 hour）
3.peach book 分享
a.上传逻辑：
		pdf格式文件，现在本地加水印，然后将返回路径的文件上传到smb
		其他格式的文件直接上传
		
下周计划:
1.启动包逻辑完成
2.文件走通nas服务器


2023.03.10
1.定位上传下载超时问题，尝试使用线程池去解决这个问题（待完成）
2.完成上传页面批量删除操作
3.完成上传页面上传到NAS服务器

2023.03.11（周六）
1.学习PV培训知识：
	SUSAR:suspect unexpected serious adverse reaction
2023.03.12
1.完成月度包下载逻辑
2.邮件发送逻辑
3.批量导出（完成）
4.文件预览（待完成）
5.excel解析（待完成）

2023.03.13
1.完成月度包下载，走nas文件(完成)
2.完成操作记录预览，走nas文件(逻辑完成，存在问题)
3.拼表excel解析
4.nas文件上传下载存在严重问题
5.化合物类型选择逻辑有问题，待优化,解决方案，走后端接口，直接返回所有化合物类型

**********/susar/

2023.03.14
1.完成对文件上传预览问题的定位（待完成）
2.完成对excel解析（待完成）
3.药物安全培训 1.0 hour (Susar报告的发送)
  susar 报告的发送 是否是SAE 预期性 相关性
  FDA标准 ：企业判断有关
  ICH标准：企业和研究者判断任一有关
  潜在SUSAR：盲态实验，在揭盲中，需要揭盲
4.完成操作记录预览，走nas文件(逻辑完成）
5.HR18034-301 项目资料熟悉 1.0 hour
  ecrf填写指南（电子病例报告表） 
  edc 电子数据采集系统
  
2023.03.15
1.优化上传逻辑，完成平台首页进行文件上传，新增化合物列表行内上传文件逻辑 2.优化文件单独删除和批量删除操作，增加删除操作等待状态
3. 测试上传、下载、删除逻辑，优化删除接口


2023.03.16
1.代码巡检
2.优化前端页面样式，新增页面无数据提示，
3.回去自己虚拟机上部署下试试，环境mysql、xxl-job、kkFileView、fileUpload
会议纪要：
■ 本机下载文件速度慢，待连接服务器后再次确认，如果仍较慢，后面放到FTP
■ temp files改名
■ 查询记录表头修改，日期加修饰词
■ 多个附件合并发送，邮件收件人加密抄送
■ 发送失败显示测试
■ +加文字提示
■ 缩短页面按钮间距，上传文件及月度文件夹长度对齐保持一致
■ 连接服务器（周二下班前）@周辉03月21日 13:00
■ 读uap数据，用于创建compound文件夹及收件人，新增一张表，存到susar平台的业务库中，03月22日 09:00@周辉
■ 尔康提供表结构（3.17下班前）@周尔康03月17日 13:00
■ 启动包（3.27-3.31）
■ 账户系统（3.27-3.31）

下周计划：
1.susar服务部署
2.多文件发送，多个附件放在一个邮件内（完成）
3.邮件收件人,加密抄送（不显示其他抄送人）（完成）
4.月度包文件导出、压缩包内文件夹，改名（完成）
5.上传文件失败问题定位(完成)
6.uap数据，发送逻辑(完成)
7.新增逻辑，不允许上传同名文件（完成）



2023.03.17
1.完成多文件发送，多个附件放在一个邮件内逻辑修改,按compound类型分批发送
2.完成邮件转发加密抄送
3.调整前端样式
4.301项目资料熟悉,学习项目设计方案，数据核查说明，审核计划，病例报告表

2023.03.20
1.熟悉项目部署方式，susarautomail、xxl-job、kkFileView:
kkFileView问题：找不到office
\src\main\config\application.properties 无法解决
susarautomail问题：pom文件依赖冲突,源文件备份: 解决

2023.03.21
1.上午安装kkFileView及相关依赖包，验证服务是否启用,遇到如下问题：
 安装LibreOffice时，执行yum install -y libreoffice-headless报错：dejavu-fonts-common-2.35-7.el8.noarch依赖下载不了
 解决方案：找IT协助解决yum源在公司repo上无法下载的问题
2.下午安装前端及后端文件服务包:
/home/<USER>/dist/
3.下班前验证是否能够访问
4.建uap爬取数据表，修改业务逻辑（compound/邮件接收/）


2023.03.22
1.解决上传文件名重复问题:上传后先校验，不允许有重名文件:
	重名后不上传，退出for循环
	    isDuplicateFileName(file.name, this.modelForm.compoundFolder).then(response => {
        const dupNumber = response.data
        if (dupNumber > 0) {
          this.$message.error(file.name + '与' + this.modelForm.compoundFolder + '文件夹内文件重名，请重命名后上传!')
          this.handleRemove(file)
          return false
        }
      })
      
      
      handleRemove(removeFile) {
      debugger
      const filteredArray = this.fileList.filter((file) => {
        return file.uid !== removeFile.uid
      })
      this.fileList = filteredArray
    },
2.完成测试环境部署:
rewrite ^/api/(.*)$ /$1 break;

  location / {
            try_files $uri $uri/ @router;
            index  index.html index.htm;
        }
3。新增site表机构，编样例数据，写逻辑


2023.03.23
1.完成新增site、user表机构，编样例数据，写逻辑
2.完成测试环境能够正常使用功能
3.
下周计划：
4.首页样式调整，调整上传进度条为loading 
5.上传结果优化,提示上传成功了多少结果,失败的结果 有反馈提示
6.uap数据对接
7.环境部署测试
8.批量下载进度条有bug(修复完成)
9.去掉前端页面进度条(完成)

2023.03.24
计划:
1.修复批量下载进度条显示异常(完成)
2.修改首页上传进度条为等待loading(完成)
2.优化部署配置项(完成)
3.上传测试环境中文字体包并安装，修复预览中文乱码问题
4.优化文件上传后端逻辑，优化网盘连接逻辑


问题： 
1.文件导出的excel中内容不是全部结果，仅仅包含当前展示的内容,操作记录和月度包内都存在(无效)


2023.03.25
1.完成上传、删除、文件移动异步操作（完成）
2.修复文件导出的excel中内容不是全部结果（无效）
3.上传要等待所有文件都上传成功后，才返回上传成功的页面（完成）
4.邮件发送记录，显示用弹框(完成)
5.首页加后端分页(完成)
6.左侧导航栏跳转逻辑,存在点击本页导航参数丢失情况(待解决)

问题：
* windows下文件名中不能含有：\ / : * ? " < > | 英文的这些字符 ，这里使用"."、"'"进行替换。


2023.03.27
1.完成上传、删除、文件移动异步操作
2.优化文件导出的excel文件命名和sheet页命名
3.优化文件上传等待逻辑,所有文件上传成功后才跳转
4.优化邮件发送记录中的邮箱，显示用弹框
5.修复导出查询结果的excel日期格式展示问题
      const ws = XLSX.utils.json_to_sheet(document.querySelector('#out-table1'));
        // 设置数字格式
        XLSX.utils.sheet_set_column_style(ws, 'D', { numFmt: 'yyyy-mm-dd hh:mm:ss' });
        XLSX.utils.book_append_sheet(wb, ws);




exportExcel() { 
	let yourData = [ {name: 'John', date: new Date()}, {name: 'Jane', date: new Date()} ]; 
	let ws = XLSX.utils.json_to_sheet(yourData, {cellDates: true}); 
	let wb = {Sheets: {'data': ws}, SheetNames: ['data']}; 
	XLSX.writeFile(wb, 'your-excel-file.xlsx'); 
}

周计划：
1.启动包（3.27-3.31）
2.账户系统（3.27-3.31）调医学平台账户系统


印章测试(公章)_79c



2023.03.28
1.上传测试环境代码至gitlab
2.修复查询记录
3.演示汇报
4.筛选化合物

汇报完需求收集：
1. 邮件记录：文件发送记录文件名 发送状态, 发送失败的记录 要存表
2. 手动发送邮件，手动添加邮箱
3. 页面紧凑，样式调整
4. 邮件正文模板修改

2023.03.30
1.调整页面样式,列表宽度自适应
2.邮件发送记录新增发送文件名记录，发送失败记录，文件上传记录新增删除记录，发送状态
3.1.0hour 质量面授配培训
4.1.0 FDA new electronic system分享会

下周计划:
1.测试compound，邮箱
2.导航栏平级调整
3.登录功能开发
2023.03.31
1.加虚假邮箱地址校验逻辑,邮件发送不了
2.修改左侧导航栏

2023.04.02
1.修改侧边导航栏(完成)
2.开发前端登录逻辑
3.开发后端登录逻辑


2023.04.03
plan：
1.完成后端登录校验逻辑
2.完成前端登录逻辑
3.新建用户信息表(完成)


done:
1.前后端调通？
2.
3.

2023.04.04
plan:
1.调通前后端逻辑
2.熟悉登录逻辑
3.熟悉新人业务课程

done:
1.FDA new electronic system guidance 解读 (1.0 hour)
2.argus admin 培训


2023.04.05
1.完成jedis插件调试，前端登录功能完成
2.登入、登出功能完成


2023.04.06
1.手动导入uap数据（t_user数据问题待解决）
2.开发登录逻辑，优化登录超时后的提示框
3.完成邮件发送标题、正文模板替换
4.susar平台开发计划汇报 1.5 hour
5.fda new electronic system guide 1.0 hour
6.



ONLY_FULL_GROUP_BY,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION



sql-mode=NO_UNSIGNED_SUBTRACTION,NO_ENGINE_SUBSTITUTION


/var/lib/mysql/shnvlsusart01.log



NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION


CREATE TABLE susar_auto_mail.t_user_temp LIKEsusar_auto_mail.t_user;


source /home/<USER>/t_user.sql

下周计划：
1.文件操作要有用户log
2.创建用户信息的记录日志，上传、删除、下载需要记录日志，下载分类型记录,加到操作记录新增tab页
  加删除记录和下载记录
3.忘记密码要提供修改功能
4.手动发送邮件
5.首页调整
6.用户登陆后的操作日志
7.系统日志
8.系统配置
9.自动发送默认发送系统
10.手动发送设置登录人
11.邮件正文文本替换（完成）


2023.04.07
plan:
1.完成文件操作记录日志
2.完成前端tab页展示
3.月度包查询按钮丢失，临时发现

done:
1.argus admin培训-2 1.0 hour
2.完成删除文件操作记录日志后端逻辑
3.完成前端文件操作记录表格展示
4.安装python环境，安装依赖库第三方包定位问题
5.完成文件上传用户操作记录
6.完成月度包下载记录
7.完成邮件发送加操作用户



2023.04.08
plan：
解决动态路由死循环问题（未解决）
文件上传记录中上传人信息应该与登陆人名称一致(完成)


done:
1.安装py脚本入uap数据，定位脚本执行失败问题
2.申请开通服务器数据库端口
3.开发根据用户角色动态加载路由的前端逻辑


2023.04.11
plan:
1.完成用户新增前后端逻辑，保存到操作日志中，系统操作日志
2.完成管理员修改用户信息列表及后端逻辑
2.完成用户修改密码前后端逻辑

用户名称 用户密码  用户角色 用户邮箱

账户创建/重置密码/邮件手动发送/首页改造调整/系统配置

done:

文件预览问题

报告下载

过期后跳转的页面有问题

http://127.0.0.1:8085/susarAuto/file/susar/SHR7280-103/%E6%B5%8B%E8%AF%95%E4%B8%8A%E4%BC%A0%E7%9A%84%E6%96%87%E4%BB%B62023040501.pdf

WebUtils.decodeUrl(url)

首页排列

标签动态显示吟唱
document.querySelector('a[href="#/example/upload-excel"]').style.display="none"

document.querySelector('a[href="#/example/upload-excel"]').style.display="contents"

?redirect=${this.$route.fullPath}
?redirect=${this.$route.fullPath}

if(this.timegap){
        param.startTime = parseTime(this.timegap[0])
      param.endTime = parseTime(this.timegap[1])
      }else{
        param.startTime=""
        param.endTime=""
      }

// 要遍历文件列表
for (var i = 0; i < fileList.length; i++) {
    if (fileList[i].size > 1024 * 1024 * 10) {
      if (i === 0) {
			this.$message.warning('上传文件大小不能超过 10MB')
            }
            this.handleRemove(fileList[i])
            return false
          }
        }
        
  debugger
    if (performance.navigation.type === 1) {
    // 是刷新页面
      console.log('是刷新页面' + performance)
    } else {
    // 不是刷新页面
      console.log('不是刷新页面' + performance)
    }



2023.04.17

plan:
1.uap compoud文件名中存在、分隔符，并且文件名中存在/，需要将/转义为_
2.测试问题修复,文件预览失败
3.文件上传限制


done:
1.新增用户创建时，邮箱唯一限制
2.优化登录页标题
3.SUSAR平台UAT前培训会议 1.0
4.校验uap同步数据
5.定位文件名中文预览失败问题
6.uap文件名
  先java分隔，然后数据入库，下次查询时,先判断库中有无数据
  t_site表只能时原始表，t_site_clear表是存储的目标数据表 其中有id 名称 
  
         <if test="entity.keyWord!=null and entity.keyWord!='' ">
            AND compound_name like  concat('%',#{entity.keyWord},'%')
        </if>
        
        
  2023.04.18
  1.完成uap联合用药数据分批入业务表逻辑
  2.定位预览失败问题
  3.更新导出全部数据
  4.协助医学审核平台解决列表上方筛选组件的问题
  
  2023.04.19
  1.优化susar平台所有列表导出功能逻辑对接后端接口数据
  2.优化平台的登录失效时间从5分钟修改为15分钟
  3.优化用户管理界面，新增用户管理表记录导出功能
  
        
        


2023.04.20
1.定位登录超时问题
2.定位预览失败问题
3.修改签章位置,改为动态坐标(完成)
4.汇报本周进度
5.新增逻辑 不需要输入账户名直接进入密码找回，根据邮箱号去修改对应的账号密码


2023.04.21
1.解决测试compund路由中不显示的问题
2.调整登录页输入框的样式
3.定位在线预览问题：
	新版上传中文名文件 都打不开
	旧版上传中文名文件 都可以打开
	排查：
	   a.上传方法放开权限 无效
	   b.修改mysql连接中unicode 无效
	   c.跟下载预览文件接口无关
	   d.修改编译器编码格式 无效
	   e.修改server.prfix=http://localhost: 无效
	   f.url指定文件名&fullfilename=文件名 无效
4.文件预览流程，先上传文件到网盘，点击预览时会下载文件，然后将地址传给kkFileView


2023.04.27
1.susar周汇报 0.5 hour
2.生产环境部署
3.质量面授培训 1.0 hour
4.邮件正文变量名格式调整
5.列表分页查询优化

邮件服务器单次发送只能35MB

出差成果：
本次出差主要参与了以下的活动:
1. 参与质量控制研讨会，熟悉临床药物研发数据管理SOP规范，了解如何通过标准化数据管理工作流程，提升数据管理工作流程和效率，对软件开发人员熟悉业务背景和知识有很好的帮助。
2.参加临床数据科学中心2023年度会议，聆听各部门代表精彩的业务知识分享，熟悉公司组织架构和各部门的发展历史，感受到公司蓬勃发展的生命力和日益进取的工作氛围，了解公司其他业务领域的信息,拓展视野。分享内容涉及业务发展现状、面临的机遇和挑战等,可以助力员工更全面地了解公司,有利于提升公司认同感。 
3.参与公司年度表彰:通过表彰一年中表现突出的员工和团队及坚守岗位对公司有突出贡献的长期服务奖，可以激励大家追求卓越,爱岗敬业，不断提高工作效率和质量。被表彰者会倍感荣幸,同事们也会由衷地为他们喝彩。这种正面的激励机制可以营造积极向上的企业氛围。
3.参观5A级风景区武夷山,不仅可以增进员工感情,放松身心,还可以了解武夷山丹霞地貌生态与地方人文风情，天游峰、九曲溪漂流，武夷山风景名胜区让大家欣赏到独特的丹霞地貌和山水生态环境，感受到公司用心的关怀和温馨的团队氛围。
4. 观看印象大红袍:感受到的这场以武夷山红土地为主题的原创舞台剧,将武夷山的历史、风土人情和美丽风景融入其中,以剧情性强、视觉冲击力高见称。演员们的精彩表演也让在场的同事们目不转睛应接不暇，感受到中华文化的博大精深。
总之,此次公司年会团建活动内容丰富,通过知识学习、娱乐休闲等多种形式带给大家难忘的体验。这种集工作学习与娱乐于一体的活动,可以增进员工关系,激发工作热情,对公司文化建设具有非常积极意义。这是一个非常值得学习借鉴的团建范例，极大的提升我们员工的幸福感和认同感


site compound 


2023.04.28
1.定时执行mysql 数据备份脚本
CREATE EVENT export_and_truncate_t_site_clear ON SCHEDULE EVERY 1 DAY STARTS '2023-05-08 23:50:00' DO
BEGIN
		CREATE TABLE susar_auto_mail.t_site_clear_backup LIKE susar_auto_mail.t_site_clear;
	INSERT INTO susar_auto_mail.t_site_clear_backup SELECT NULL
	,
	site_id,
	compound_name 
	FROM
		susar_auto_mail.t_site_clear;
TRUNCATE TABLE susar_auto_mail.t_site_clear;
END;



2023.05.04
1.临床试验数据质量要求 知识学习，熟悉国家局和卫健委有关临床药物研发数据质量的相关管理规范和要求
2.调整pdf签章位置
3.定位uap同步数据问题
4.定位生产环境数据库脚本执行问题，申请开通生产环境备份数据库权限


问题：在线预览服务，如果显示文件不存在，则首先排查file-upload 中 SMBUtilConstant中的BASE_URL和Root_FOLDER_NAME的值是否配置准确



2023.05.05
1.全任务调度、全对象存储服务器申请
2.生产环境域名绑定后端资源请求问题定位
3.共享邮箱验证
4.rave业务系统相关知识学习

待办：
域名绑定问题
py脚本筛选范围，前端列表生成时按范围过滤
air flow sas code运行 api拉数据运行 
['SHR6390', 'SHR4640', 'INS068', 'HRS9531', 'HRS-1358', 'SHR-A1811','SHR-1316','SHR-1701','HRS-1358','Compound A','Compound B','Compound C', 'SHR-1802', 'HRS8179', 'HRS3797', 'APTN/YN968D1']



2023.05.06
1.location /api {
    proxy_pass http://your_backend_server;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header Host $http_host;
}
	proxy_ssl_verify off;
    proxy_ssl_server_name on;

redis缓存组件/kkfile文件预览服务/xxl-job任务调度服务/susar邮件发送平台前后端服务安装

/home/<USER>/8704241__hengrui.com.key
/home/<USER>/8704241__hengrui.com.pem
 ['SHR-1210', 'SHR-1701', 'SHR6390', 'HR070803', 'test']

在线预览bug解决  2023-05-20




启动包    2023-05-20
上传界面优化  2023-06-20
退信补发 2023-06-20
只读权限 2023-06-20
紧急情况允许用户手动触发邮件 2023-06-20
前台维护邮件模板、发送频率 2023-06-20
创建账户后初始密码邮件自动发送 2023-06-20

add_header Content-Security-Policy upgrade-insecure-requests;

#https端口号.
server.port=443
#证书的路径.
server.ssl.key-store=classpath:keystore.p12
#证书密码，请修改为您自己证书的密码.
server.ssl.key-store-password=123456（改为之前设置的密码）
#秘钥库类型
server.ssl.keyStoreType=PKCS12
#证书别名
server.ssl.keyAlias=tomcat


openssl pkcs12 -export -in <domain-name.crt> -inkey </path-to private.key> -name <alias-name> -out <domain-name.p12>
启动问题
https://cloud.tencent.com/developer/ask/sof/1198515
C:\Program Files\OpenSSL-Win64\bin;
     location = / {
            try_files $uri $uri/ @router;
            root   html;
           index  index.html index.htm;
        } 
        
        
        
     server {
        listen       443 ssl;
        server_name  susar.hengrui.com;
        ssl_certificate     /home/<USER>
        ssl_certificate_key /home/<USER>

        ssl_session_cache    shared:SSL:1m;
       ssl_session_timeout  5m;

         ssl_ciphers  HIGH:!aNULL:!MD5;
         ssl_prefer_server_ciphers  on;

        location = / {
              root   html;
              index  index.html index.htm;
           }

        location / {
            add_header Content-Security-Policy upgrade-insecure-requests;
            proxy_pass  http://localhost:8085;
        }
     }

http://***********:8012/onlinePreview?url=bnVsbDgwODUvc3VzYXJBdXRvL2ZpbGUvSFIwNzA4MDMvMjAyMy0wNS9QVlNVU0FSX1JFMDMzLnBkZg%3D%3D


http://***********:8012/onlinePreview?url=bnVsbDgwODUvc3VzYXJBdXRvL2ZpbGUvSFIwNzA4MDMvMjAyMy0wNS9QVlNVU0FSX1JFMDMzLnBkZg%3D%3D


http://***********:8012/onlinePreview?url=**************************************************************************************************%3D%3D
http://***********:8012/onlinePreview?url=bnVsbDgwODUvc3VzYXJBdXRvL2ZpbGUvSFIwNzA4MDMvMjAyMy0wNS9QVlNVU0FSX1JFMDMzLnBkZg%3D%3D


http://127.0.0.1:8085/susarAuto/file/SHR6390/2023-05/退信测试.pdf


2023.05.08
1.     :total="total"
3.192, 196, 204

1.生产环境部署，部分问题修复
2.定位文件预览问题,发现shiro鉴权1.7.0之后框架版本拦截含有中文restful请求的过滤器
3.协助IT公邮邮件发送测试，验证公邮邮箱邮件存储逻辑
4.协助导出uap同步数据，定位脚本同步逻辑问题


2023.05.09
1.解决文件预览含有中文文件名失败的问题
2.生产环境部署更新
3.
系统验证培训：
keywords 
文件存档验证记录  始终满足用户需求
验证的条件 ：软、硬件、人、流程变化都需要验证
需求和功能完成度拟合一致
系统权限，人员培训，audit trail
满足合规性 是临床医学药物研发业务系统的基本要求
operation qualification 和 performance qualification 的区别就是
OQ是功能完整度验证，PQ是流程验证

系统下线



2023.05.10
1.
2.
3.




 if(validMails.size()>200) {
                    //分批发送
                    // 每批数量
                    int batchSize = 200;

                    // 计算总批数
                    int totalBatches = validMails.size() / batchSize;
                    if (validMails.size() % batchSize != 0) {
                        totalBatches++;
                    }

                    // 遍历每一批
                    for (int i = 0; i < totalBatches; i++) {
                        // 计算当前批开始和结束索引
                        int startIndex = i * batchSize;
                        int endIndex = (i + 1) * batchSize;
                        if (endIndex > validMails.size()) {
                            endIndex = validMails.size();
                        }
                        // 获取当前批邮件列表
                        List<String> batchMails = validMails.subList(startIndex, endIndex);
                        //分批转换
                        ArrayList<String> batchSendMails=  new ArrayList<>(batchMails);
                    }
                }

./airflow db init

2023.05.11
1.系统验证培训 :
	系统变更管理
	QC 质量控制检查某一个工作基于当前工作内容
	QA 质量体系  基于整个工作流程 整个公司、项目组、部门
	audit 稽查
	inspection 政府层面核查
grant all PRIVILEGES on airflowdb.* to airflow@'localhost'  identified by '123456';


mysql://root:Hr@mysql1024@localhost:3306/airflowdb
explicit_defaults_for_timestamp = 1
  
  GRANT ALL ON airflowdb.* TO 'root'@'localhost';
  
  grant all PRIVILEGES on airflowdb.* to root@'localhost'  identified by 'Foton123456[zk]';
  
  
  pip3.8 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/ apache-airflow-providers-mysql
  
  pip install apache-airflow-providers-mysql
  
  
  Red Hat Enterprise Linux release 8.7 (Ootpa)
  
  
  mysql-connector-python  MySqldb


systemctl enable postgresql
systemctl start postgresql


/usr/pgsql-10/bin/postgresql-10-setup pos	


    su - postgres  切换用户，执行后提示符会变为 '-bash-4.2$'
    psql -U postgres 登录数据库，执行后提示符变为 'postgres=#'
    ALTER USER postgres WITH PASSWORD 'postgres'  设置postgres用户密码为postgres
    \q  退出数据库
    
    
    pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/ psycopg2
    
        vi /var/lib/pgsql/10/data/pg_hba.conf
    修改如下内容，信任指定服务器连接
    # IPv4 local connections:
    host    all            all      127.0.0.1/32      md5
    host    all            all      all  md5
    trust
    bash
yum install postgresql-devel

import pymysql
pymysql.install_as_MySQLdb()

    postgresql://postgres:postgres@127.0.0.1:5432/airflow
    
 




2023.05.15
1.susar测试环境安装包同步，与生产环境保持一致
2.airflow部署，环境验证，重新部署
3.系统退信获取方案设计，申请开通公邮imap协议

pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/  --upgrade apache-airflow==2.2.5; 

service airflow-webserver start 
        



创建用户
airflow users create --username admin --firstname admin --lastname admin --role Admin --email <EMAIL>
删除用户

airflow users delete --username admin


netstat -anp | grep 8021

2023.05.16
1.susar二期页面首页列表实现方案验证
2.airflow调度环境验证，本地运行dag任务
3.
4.
5.

Believer24:Zh19930315

https://Believer24:<EMAIL>/Believer24/SUSARMailAuto.git

git clone https://Believer24:<EMAIL>/Believer24/SUSARMailAuto.git

pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/  xmltodict;

pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/  json;

pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/  rwslib;

pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/  jsonline;



"C:\Program Files (x86) WinSCP WinScp.exe” /command "openftpes://erkang.zhou.hengrui.com:Zero2One4?@ftp01.ftp.mdsol.com/"cdhengruimedicine-ravex.mdsol.com/sasondemand”"lcdZ: Projects\GRP CDSC PENGR\SHR-1210-III-315\data\raw”"get-neweronly *SHR 1219 III 315 ???? *" "exit"/log=Z:\Projects GRP CDSC PENGR SHR-1210-III-315 data raw log file %ymzd%.txt


Rave ftp地址 用户 密码
https://ftp01.ftp.mdsol.com/WebInterface/login.html   账号：erkang.zhou.hengrui.com 密码：Zero2One4?

sas数据集下载 
wget https://clinical.hengruipharma.com:88/report/report/getRemoteFile?studyCode=HRS9531-102&environment=pro&loginId=<EMAIL>&pwd=Hr123456&filename=HRS9531-102-Daily --no-check-certificate

用户创建后无法登录，重启webserver：
停止
ps -ef|egrep 'scheduler|airflow-webserver'|grep -v grep|awk '{print $2}'|xargs kill -9
启动
nohup airflow webserver -p 8090 >>$AIRFLOW_HOME/airflow-webserver.log 2>&1 &

设置客户端
./mc alias set minios3 http://**********:9000 minioadmin minioadmin

//列出minio中的存储对象
mc ls minios3/bucketname 


airflow scheduler -D

airflow worker -D

airflow scheduler &







2023.05.16
1.rave数据下载/sas数据集下载
重启webserver和scheduler

su airflow
 ps -ef|egrep 'scheduler|airflow-webserver'|grep -v grep|awk '{print $2}'|xargs kill -9
 rm -rf /home/<USER>/airflow/airflow-scheduler.pid 
 airflow webserver -p 8080 -D
  airflow scheduler -Dps -
tail -f /home/<USER>/airflow/airflow-scheduler.err 
ps -ef|egrep 'serve_logs|celeryd'|grep -v grep
 rm -rf /home/<USER>/airflow/airflow-worker.pid
 airflow worker -D
tail -f /home/<USER>/airflow/airflow-worker.err   什么也不打印就是没有问题


2023.05.18
pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/ saspy
1.airflow 安装saspy
2.完成sas数据下载上传dag任务开发
3.完成rave数据上传dag开发验证

pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/  --upgrade saspy==4.4.3;


pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/ --upgrade   xmltodict==0.13.0;
pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/ json
pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/ --upgrade rwslib==1.2.9;
pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/ jsonline

sas_download_data


C:\\Users\\<USER>\\Desktop\\Rave Dataset\\temp\\

bash
mkdir -p /tmp/hsperfdata_root/api_download_data/architect_xml 
mkdir -p /tmp/hsperfdata_root/api_download_data/architect_json
mkdir -p /tmp/hsperfdata_root/api_download_data/temp


def download_and_execute_sas_script(**kwargs):
try:
    # 下载m_inits3.sas和m_post2s3.sas到/tmp/hsperfdata_root/sas_script
    download_cmds = [
        'mc cp minios3/raw/pgm/m_inits3.sas /tmp/hsperfdata_root/sas_script',
        'mc cp minios3/raw/pgm/m_post2s3.sas /tmp/hsperfdata_root/sas_script'
    ]
    for cmd in download_cmds:
        os.system(cmd)
    
    # 使用saspy执行m_inits3.sas和m_post2s3.sas
    import saspy
    sas = saspy.SASsession()
    # 读取m_inits3.sas和m_post2s3.sas代码
    with open('/tmp/hsperfdata_root/sas_script/m_inits3.sas') as f:
        init_sas_code = f.read()   

    with open('/tmp/hsperfdata_root/sas_script/m_post2s3.sas') as f:
        main_sas_code = f.read()   

    # 在m_inits3.sas开头追加%include ''
    init_sas_code = '%include \'\';\n' + init_sas_code

    # 写入临时文件m_temp.sas
    with open('/tmp/m_temp.sas', 'w') as f:
        f.write(init_sas_code)

    # 执行m_temp.sas    
    sas.sas_submit(file='/tmp/m_temp.sas')   

    # 在m_post2s3.sas开头追加%include 'm_inits3.sas'
    main_sas_code = '%include \'m_temp.sas\';\n' + main_sas_code

    # 写入临时文件m_temp2.sas
    with open('/tmp/m_temp2.sas', 'w') as f:
        f.write(main_sas_code)

    # 执行m_temp2.sas    
    sas.sas_submit(file='/tmp/m_temp2.sas')   

finally:
    sas.endsas()   
    # 删除临时文件
    os.remove('/tmp/m_temp.sas')
    os.remove('/tmp/m_temp2.sas') 

task3 = PythonOperator(
    task_id ='download_and_execute_sas_script', 
    python_callable = download_and_execute_sas_script,
    dag=dag
)



python
try: 
    # 连接SAS
    sas = saspy.SASsession(cfgname='iomlinux')
    
    # ...
    
    # 执行m_temp.sas
    sas.sas_submit(file='/tmp/m_temp.sas')
    
finally:
    sas.endsas()






def download_and_execute_sas_script(**kwargs):
try:
    # 下载m_inits3.sas和m_post2s3.sas到/tmp/hsperfdata_root/sas_script
    download_cmds = [
        'mc cp minios3/raw/pgm/m_inits3.sas /tmp/hsperfdata_root/sas_script',
        'mc cp minios3/raw/pgm/m_post2s3.sas /tmp/hsperfdata_root/sas_script'
    ]
    for cmd in download_cmds:
        os.system(cmd)
    
    # 使用saspy执行m_inits3.sas和m_post2s3.sas
    import saspy
    sas = saspy.SASsession()
    # 读取m_inits3.sas和m_post2s3.sas代码
    with open('/tmp/hsperfdata_root/sas_script/m_inits3.sas') as f:
        init_sas_code = f.read()   

    with open('/tmp/hsperfdata_root/sas_script/m_post2s3.sas') as f:
        main_sas_code = f.read()   

    # 在m_inits3.sas开头追加%include ''
    init_sas_code = '%include \'\';\n' + init_sas_code

    # 写入临时文件m_temp.sas
    with open('/tmp/m_temp.sas', 'w') as f:
        f.write(init_sas_code)

    # 执行m_temp.sas    
    sas.sas_submit(file='/tmp/m_temp.sas')   

    # 在m_post2s3.sas开头追加%include 'm_inits3.sas'
    main_sas_code = '%include \'m_temp.sas\';\n' + main_sas_code

    # 写入临时文件m_temp2.sas
    with open('/tmp/m_temp2.sas', 'w') as f:
        f.write(main_sas_code)

    # 执行m_temp2.sas    
    sas.sas_submit(file='/tmp/m_temp2.sas')   

finally:
    sas.endsas()   
    # 删除临时文件
    os.remove('/tmp/m_temp.sas')
    os.remove('/tmp/m_temp2.sas') 



2023.05.19
1.开发二期首页列表
2.
3.
CREATE DATABASE IF NOT EXISTS airflow DEFAULT CHARACTER SET utf8 DEFAULT COLLATE utf8_general_ci;


airflow users create --username xxxx --firstname xxx --lastname xxxx--role Admin --email <EMAIL>

airflow scheduler -D
https://airflow.apache.org/docs/apache-airflow/stable/index.html



2023.05.22
1.完成susar二期首页布局调整
2.
3.


[minio_default]
conn_id = minio_default
conn_type = S3
host = <minio-endpoint>
login = <minio-access-key>
password = <minio-secret-key>

ak : I0PvufXaIy9rnjTB
sk : x618SQECqVSJ1smGbjPIMRvzTyMhBNbt


2023.05.23
1.完成susar二期列表跳转，多选框选中取消逻辑开发:
  <template>
  <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
  <div style="margin: 15px 0;"></div>
  <el-checkbox-group v-model="checkedCities" @change="handleCheckedCitiesChange">
    <el-checkbox v-for="city in cities" :label="city" :key="city">{{city}}</el-checkbox>
  </el-checkbox-group>
</template>
2.完成saspy程序调用
3.完成rave ftp数据调用
4.完成大文件下载，任务执行顺序依赖的验证
5.挂在nas网盘到dag目录


2023.05.24
1.完成usar二期首页多个文件夹批量上传的后端逻辑开发
2.完成susar二期首页上传列表页面开发
3.完成susar二期系统管理页面前端布局开发
4.完成saspy程序调用dag验证




2.mysql开启远程连接权限：
   use mysql;
   update user set host = '%'  where user = 'root';
   flush privileges;
   
   
   
 2023.05.25
 1.完成系统配置中调度时间间隔的后端逻辑
 
 2.服务器
 3.启动包逻辑: 月度累计到一个启动包 case号   出现降级删除不放、相同删除 没有相同的直接放
   系统管理人员不参与业务 菜单屏蔽

4.暂未发送文件下载逻辑

nohup java -jar fileupload-0.0.1-SNAPSHOT.jar > log.txt



2023.05.25
1.启动包逻辑：
  实现逻辑，不进行物理层的操作，就是月度包里的文件和启动包的文件做映射，case号已经提取出来，根据compound和月份
  然后将该范围内相同case号的只保留一个，根据fileName like的结果，保留其中包含降级的数据
  
  
  2.lftp -p 990 ftp://erkang.zhou.hengrui.com:Zero2One4?@************
  
  
  ftps://erkang.zhou.hengrui.com:Zero2One4?@ftp01.ftp.mdsol.com
  
  connect ftps:ftp01.ftp.mdsol.com
  
  
  open ftps://************:990
  user erkang.zhou.hengrui.com Zero2One4?
  
  cd /hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand/
  
  H2023001201490
  
  mirror  /hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand /tmp/hsperfdata_root/rave_download_data
  get /hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand/clary_sage@1_SHR_1701_II_207_15624_20230524_081100.zip  /tmp/hsperfdata_root/rave_download_data/clary_sage@1_SHR_1701_II_207_15624_20230524_081100.zip
  :Zero2One4?
  sftp -p 990 <EMAIL>
  
  lftp ************ -p 990 -u erkang.zhou.hengrui.com,Zero2One4?
  


  
  
  user erkang.zhou.hengrui.com <password>
  
   SHR4640
 open ftps://************:990:990

2023.05.29
1.完成启动包列表页面和后端接口查询，完成启动包下载（完成）
2.验证ftp是否能够下载,完善rave ftp文件下载dag任务
3.提供服务器申请





python
import ftplib 
import ssl


# ... 连接并登录FTP 

print('Connected to FTP...')

file_names = ftps.nlst('/standard/v7/daily/00Z-23Z/201403/')
for name in file_names:
    print(name)

ftps.close()


2023.05.30
1.整合登录功能到uap登录系统

2.每次调用uap的接口登录成功后，


TS01dbda3d=0188b996a2621372e6f6a38d36e75544819d05366c2e665602abf3a83a87dec5dcaf6849cabc4301a9ca5bb2eaab8395562ba72f12; TS3fe3c2cb027=08476dfda8ab2000d4bad3464addccf8e8c5d4f01064cdd332af7abbc032a4a05d3fac5fc89b3502087c6aca5711300024abdcd5971c068640ed21326f648273839bf54316627caf3df8475ed9c06782cee6bd98ee0a21c4f677497cf5826797


TS01dbda3d 0188b996a2621372e6f6a38d36e75544819d05366c2e665602abf3a83a87dec5dcaf6849cabc4301a9ca5bb2eaab8395562ba72f12
clinical-tst.hengruipharma.com Session false false

import IMessage from './IMessage'
IMessage.error(res.msg || '网络通讯异常，请稍后再试！')




2023.05.31
1.定位raveftp 文件下载
pip install apache-airflow-providers-ftp

安装airflow需要重新设置mysql密码
UPDATE user SET password ='Hr_mysql1024' WHERE user = 'root';
AND 
      host = 'localhost';

FLUSH PRIVILEGES;
//更多请阅读：https://www.yiibai.com/mysql/changing-password.html


zhouh36:HR9cf3cbd8
**********/susar/

[root@xyarch mnt]# mount -t cifs   -o vers=2.0,user=xy,password=hahaha  //***************/xshare /mnt/smb 

mount -t cifs  user=zhouh36,password=HR9cf3cbd8  //**********/etl/Airflow_DAGs  /root/airflow/dags -o vers=2.0,file_mode=0666,dir_mode=0770

mount -t cifs -o user=zhouh36,password=HR9cf3cbd8,dir_mode=0777,file_mode=0770 //**********/etl/Airflow_DAGs /root/airflow/dags
 
 
mount.cifs //**********/etl/Airflow_DAGs /root/airflow/dags -o file_mode=0666,dir_mode=0770,username='zhouh36',password='HR9cf3cbd8',gid=root,uid=root
mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log /home/<USER>/blind_sas_log -o file_mode=0666,dir_mode=0770,username=zhouh36,password=HR9cf3cbd8,gid=root,uid=root

mount -t cifs //**********/susar/Airflow_DAGs /root/airflow/dags

 mount -t cifs -o file_mode=0666,dir_mode=0770,username='zhouh36',password='HR9cf3cbd8',gid=root,uid=root //**********/susar/Airflow_DAGs /root/airflow/dags
 mount -t cifs -o file_mode=0666,dir_mode=0770,username='zhouh36',password='HR9cf3cbd8',vers=2.1,gid=root,uid=root //**********/susar/Airflow_DAGs /root/airflow/dags
vi /etc/fstab
2.
3.[root@21yunwei_backup ~]# lsof | grep deleted
mysqld     1512   mysql    5u      REG              252,3          0    6312397 /tmp/ibzW3Lot (deleted)
cat       20464    root    1w      REG              252,3         23    1310722 /root/testdelete.py (deleted)
-o sec=ntlmssp
mount -t cifs -o user=zhouh36,password=HR9cf3cbd8 -o sec=ntlmssp,dir_mode=0777,file_mode=0770 //**********/susar/Airflow_DAGs/ ./

Broken DAG: [/root/airflow/dags/test_rave_ftp.py] Traceback (most recent call last):
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/root/airflow/dags/test_rave_ftp.py", line 3, in <module>
    from airflow.providers.operator import FTPDownloadOperator
ModuleNotFoundError: No module named 'airflow.providers.operator'




2023.06.01
1.今天需走通rave ftp的数据逻辑
erkang.zhou.hengrui.com,Zero2One4?
登录
	lftp erkang.zhou.hengrui.com:Zero2One4?@************:990          
路径
	/hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand/
ftps://ftp01.ftp.mdsol.com    
login erkang.zhou.hengrui.com  Zero2One4?

远程系统 = UNIX Type: L8
文件传输协议 = FTP
加密协议 = TLS/SSL隐式加密, TLSv1.2
加密算法 = TLSv1.2: ECDHE-RSA-AES128-GCM-SHA256, 2048 bit RSA, ECDHE-RSA-AES128-GCM-SHA256 TLSv1.2 Kx=ECDH     Au=RSA  Enc=AESGCM(128) Mac=AEAD

压缩 = 否

证书指纹
SHA-256 = bb:24:f2:56:b5:c4:db:7e:82:13:29:4e:92:b5:5f:31:82:35:0c:17:45:9b:f0:76:bb:1a:65:8b:20:13:92:d4
SHA-1 = a1:aa:56:74:6c:3f:cb:af:f1:a9:45:40:94:3e:ba:31:b3:60:d3:1e

2.rave Api的数据也需要走通(完成)

3.susar二期调整：a.启动包调整 b.手动发送 c.发送前文件大小限制 d.下载包带上compound名字+日期



2023.06.02
1.通过scp下载远程41、42服务器上的文件
sshpass -p Hr@airflow0509 scp -P 22  root@***********:/home/<USER>/test01.txt /home/



2023.06.05
1.开发airflow调度sas数据集dag，验证mysql查询文件路径及文件名
2.开发启动包修改后逻辑
3.
# 任务2:遍历JSON,下载文件并重命名
def download_files():
    data = json.loads(ti.xcom_pull(task_ids='mysql_query'))
    for file in data:
        bash_command = f'sshpass -p Hr@airflow0509 scp -P 22  root@***********:{file["fileloc"]} /home/<USER>"dag_id"]}' 
        print(bash_command)
        output = subprocess.check_output(bash_command,shell=True)

task2 = PythonOperator(
    task_id='download_files', 
    python_callable=download_files,
    dag=dag,
) 
2023.06.06
1.开发sas数据集dag
mc cp /home/<USER>/M00/00/50/CgoMKmLw0UWAFj7OAASihrzc31Y022.zip minios3/raw/SHR0410-104_PRO_V1.0_CRF导出-注释病例报告表(单一册)_20220808_170301.zip --tags "key1=HRTAU&key2=SHR0410-104_PRO_V1.0_CRF导出-注释病例报告表(单一册)_20220808_170301.zip"



2.getStartFoldeFiles    getAllStartFiles
3.const params = new FormData()
  params.append('ids', ids)
  
  
  
  2023.06.08
  1.启动包逻辑完善好（降级后再上传相同case号的，应该可以保存）, 测试环境部署,权限控制，登录人信息展示在头像处 ， 
  2. mc.exe alias set minios3 http://**********:9000 minioadmin minioadmin
	 mc.exe admin info minios3
	 mc.exe cp /D:/Program Files/jdk1.8.0_101/javafx-src.zip  minios3/raw/javafx-src.zip
	 mc.exe ls minios3/raw
	 
	 
	 
	 
	 mc.exe alias set minios3 http://***********:9000 minioadmin minioadmin
	 
	 C:\Users\<USER>\Desktop\HR070803-301_LAB_AE.csv
	 
	 mc.exe cp C:\Users\<USER>\Desktop\HR070803-301_LAB_AE.csv  minios3/dmreview/HR070803-301_LAB_AE_TEST.csv
	 mc.exe cp C:\MyFile\dm-platform\HR070803-301_LAB_AE.csv minios3/dmreview/HR070803-301_LAB_AE.csv
	 mc.exe cp C:/MyFile/dm-platform/HR070803-301_LAB_AE.zip  minios3/dmreview/HR070803-301_LAB_AE.zip
	 mc.exe cp C:/MyFile/dm-platform/HR070803-301_LAB_AE_xlsx.zip  minios3/dmreview/HR070803-301_LAB_AE_xlsx.zip
 
 2023.06.09
 1.mc.exe cp "C:\MyFile\rave_upload_test.zip"  minios3/raw/rave_upload_test.zip 
 "/k D:\SyncRaveDatasetAndUpload.bat" 
  D:\  
set-executionpolicy remotesigned
winrm quickconfig
winrm set winrm/config/client/auth '@{Basic="true"}'
winrm set winrm/config/service/auth '@{Basic="true"}'  
PS> winrm s winrm/config/client '@{TrustedHosts="http://**********"}'
winrm set winrm/config/client/auth @{Basic="true"}
winrm set winrm/config/client @{TrustedHosts="host1, host2, host3"}


sas = saspy.SASsession(cfgname='iomlinux', results='HTML', java='/usr/bin/java')

export PATH=/usr/bin:$PATH  
export PYTHONHOME=/usr/lib/python3.7 
export PYTHONPATH=/usr/lib/python3.7/site-packages

/usr/share/doc/python39


sudo update-alternatives --config python


export JAVA_HOME=/usr/local/java/jdk1.8.0_271
export PATH=$JAVA_HOME/bin:$PATH
export CLASSPATH=.:$JAVA_HOME/lib/dt.jar:$JAVA_HOME/lib/tools.jar

export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk-*********.b10-1.el7.x86_64 
export PATH=$PATH:$JAVA_HOME/bin


2023.06.12 
0、验证rave ftp 端口开通及脚本执行
1、部署二期到测试环境(未完成)
2、研究权限控制，操作记录，上传下载(未完成)
3、研究airflow的winrm远程调度控制windows服务器，修改相关windows环境配置


2023.06.13
1.优化susar上传组件，拖拽上传校验提示
  
2.python

3.# from airflow import configuration as conf  
from airflow.configuration import conf    # 最新版
try:
	tz = conf.get("core", "default_timezone")
	if tz == "system":
		utc = pendulum.local_timezone()
	else:
		utc = pendulum.timezone(tz)
except Exception:
	pass
	
	dt.datetime.now()

# 修改时区
from airflow.configuration import conf
try:
	tz = conf.get("core", "default_timezone")
	if tz == "system":
		utc = pendulum.local_timezone()
	else:
		utc = pendulum.timezone(tz)
except Exception:
	pass


'15 5 * * *'



netsh advfirewall firewall add rule name="WinRM-HTTP" dir=in localport=5985 protocol=TCP action=allow

winrm set winrm/config/service/auth '@{Basic="true"}'

winrm set winrm/config/service '@{AllowUnencrypted="true"}'




4.
"C:\Program Files (x86)\WinSCP\WinSCP.exe" /command "open ftp://xxxxx:xxxxx?@************:xxx -implicit" "cd /hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand" "lcd D:\Projects\GRP_CDSC_PENGR\SHR-A1811-III-301\data\raw" "get -neweronly CatherineZhuge4_SHR_A1811_III_301_16765_20230612_012930.zip" "exit" /log=D:\Projects\GRP_CDSC_PENGR\SHR-A1811-III-301\data\raw\log_file_%ymd%.txt 

erkang.zhou.hengrui.com:Zero2One4?



@echo off
set ftp_host=************    
set ftp_user=erkang.zhou.hengrui.co     
set ftp_pwd=Zero2One4?   
set local_dir=D:\Projects\GRP_CDSC_PENGR\SHR-A1811-III-301\data\raw
set remote_dir=/hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand
set file_pattern=*SHR_A1811_III_301*

for /f "delims=_ tokens=2" %%i in ('dir /b %local_dir% ^| find "%file_pattern%"') do (
    if %%i==%date:~-8,8% (
        for /f "delims=_ tokens=1" %%j in ("%%i") do (
            set file_name=%%j_SHR_A1811_III_301_?????_%%i
        )
    ) else (
        for /f "delims=_ tokens=1,2" %%j in ("%%i") do if %%k LSS %date:~-8,8% set prev_date=%%k & set prev_file=%%j_SHR_A1811_III_301_?????_%%k
    )
)
if defined prev_file ( 
    set file_name=%prev_file%
)  

"C:\Program Files (x86)\WinSCP\WinSCP.exe" /command ^  
"open ftp://%ftp_user%:%ftp_pwd%@%ftp_host% -implicit" ^   
"cd %remote_dir%" ^
"lcd %local_dir%" ^    
"get -neweronly %file_name%" ^    
"exit" /log=%local_dir%\log_file_%ymd%.txt


5.
erkang.zhou.hengrui.com:Zero2One4?
6.
"C:\Program Files (x86)\WinSCP\WinSCP.exe" /command "open ftp://xxxxx:xxxx@************:990 -implicit" "cd /hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand" "lcd D:\Projects\GRP_CDSC_PENGR\SHR-A1811-III-301\data\raw" "get -neweronly *SHR_A1811_III_301_?????_*" "exit" /log=D:\Projects\GRP_CDSC_PENGR\SHR-A1811-III-301\data\raw\log_file_%ymd%.txt 
@echo off
上述bat脚本中
"get -neweronly *SHR_A1811_III_301_?????_*" "
优化这条命令，只下载ftp服务器中包含SHR_A1811_III_301_字符串的最新的文件一个，根据文件名倒数第二个_分隔的数字比较，例如文件名为：CatherineZhuge4_SHR_A1811_III_301_16765_20230612_012930.zip,则用于比较的数字为20230612






 
 
 
 
 
@echo off

setlocal enabledelayedexpansion

"C:\Program Files (x86)\WinSCP\WinSCP.exe" /command "open ftp://xxx:xxxx@************:990 -implicit" "cd /hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand" "lcd D:\Projects\GRP_CDSC_PENGR\SHR-A1811-III-301\data\raw" "ls -1 *SHR_A1811_III_301_*" "exit" /log=D:\Projects\GRP_CDSC_PENGR\SHR-A1811-III-301\data\raw\log_file_%ymd%.txt > ftp_files.txt
for /f "tokens=2,3 delims=_" %%A in ('type ftp_files.txt ^| findstr SHR_A1811_III_301_') do (
    set "file_date=%%B"
    set "latest_file=%%A"
)
"C:\Program Files (x86)\WinSCP\WinSCP.exe" /command "open ftp://xxxx:xxxx?@************:990 -implicit" "cd /hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand" "lcd D:\Projects\GRP_CDSC_PENGR\SHR-A1811-III-301\data\raw" "get !latest_file!" "exit" /log=D:\Projects\GRP_CDSC_PENGR\SHR-A1811-III-301\data\raw\log_file_%ymd%.txt
del ftp_files.txt
exit

 
 
 
 
 
 
 
 
 
 erkang.zhou.hengrui.com:Zero2One4?
 
 
 
 
 
 
 
 
 
 
 
 
set path="C:\Projects\GRP_CDSC_PENGR\SHR-A1811-III-301\data\raw"


        
        
        
wenting.ding@hengrui.com5_SHR_A1921_I_101_16045_20230526_170100.zip
        
C:\Windows\System32\cmd.exe    
        
        
"/c D:\RaveDataDownload\SHR-A1904-I-101.bat"
"/c D:\RaveDataDownload\SHR-A1904-I-102.bat"
        
        
"/c D:\RaveDataDownload\HRS-7535-201.bat" 
"/c D:\RaveDataDownload\SHR-1210-II-217.bat"
"/c D:\RaveDataDownload\SHR-A1811-I-101.bat"
"/c D:\RaveDataDownload\SHR-A1811-I-103.bat"
"/c D:\RaveDataDownload\SHR-A1811-III-301.bat"
"/c D:\RaveDataDownload\SHR-A1921-I-101.bat"
 "/c D:\RaveDataDownload\SHR-1314-205.bat"
 
 
 
 




 /c D:\script1.bat D:\script2.bat D:\script3.bat
 
 
 
 "/k D:\RaveDataDownload\HRS-7535-201.bat D:\RaveDataDownload\SHR-1210-II-218.bat D:\RaveDataDownload\SHR-A1811-I-101.bat D:\RaveDataDownload\SHR-A1811-I-103.bat D:\RaveDataDownload\SHR-A1811-III-301.bat D:\RaveDataDownload\SHR-A1921-I-101.bat"
 
 
 
 <el-link style="font-size: medium;" type="primary" @click="toUploadPage(null,item.compoundFolder)">{{ item.compoundFolder }}</el-link>


 <el-table-column
          label="文件名称"
          prop="fileName"
          class-name="file_name"
          sortable="custom"
          header-align="left"
          :formatter="formatter"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.isDeleted === 'N'" class="center-span" @click="preivewFile(scope.row.id,scope.row.url,scope.row.fileName)">{{ scope.row.fileName }}</span>
            <span v-if="scope.row.isDeleted === 'Y'">{{ scope.row.fileName }}</span>
          </template>
        </el-table-column>

userInfo: {}
this.userInfo = localStorage.getItem('user')

checkedData.length ? true : false

SUSAR报告平台
测试环境
<EMAIL>


v-if="username==='<EMAIL>'?false:true"


<EMAIL>
Wangm202306


2023.06.15
<EMAIL>
<EMAIL>


  <el-date-picker
            v-model="timegap"
            type="daterange"
            align="right"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
          />



this.timegap = ''



 pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },





<template>
  <el-time-select
    v-model="value"
    start="00:00"
    step="01:00"
    end="23:59"
    placeholder="Select time"
    format="hh:mm A"
  />
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const value = ref('')
</script>



1.8




2023.06.16
1.验证 双语编码操作文档，关于jsessionId获取的逻辑，沟通相关获取方式的问题
2.退信获取：
		SMTPTransport t = (SMTPTransport)session.getTransport("smtps");
		t.send(message);
		String response = t.getLastServerResponse();
		boolean s = t.getReportSuccess();
		int code = t.getLastReturnCode();
		return response;
-------------------------------------------------------------------------------------------------------------

		SMTPTransport t = (SMTPTransport)session.getTransport("smtp"); 
		t.connect(); 
		t.sendMessage(message,message.getAllRecipients()); 
		String response = t.getLastServerResponse();
		boolean s = t.getReportSuccess();
		int code = t.getLastReturnCode();
		return response; 




忽略本地证书方式
SSLContext sslContext = SSLContexts.custom() 
    .loadTrustMaterial(null, new TrustStrategy() {
        public boolean isTrusted(X509Certificate[] chain, String authType) 
                throws CertificateException {
            return true;
        }
    })
    .build();

CloseableHttpClient httpClient = HttpClients.custom()
    .setSSLContext(sslContext)
    .setSSLHostnameVerifier(new NoopHostnameVerifier())
    .build();

//按修改时间排序
dir /b /o:d "%path%\*.zip*"





TSc4f971ff027=08476dfda8ab200085db7b0afc85cfb51af0af799dc64c5a78b7fab3f108b7adc383c926b0d8bfdc0840c1d04f1130006a1703f68b0f18ffd9239da2f4208e68c9c8b295d53712d341cc950d29bd4bef00cff6c1d079e6712ae2a03732278be6; Path=/




import java.io.IOException;
import java.net.URL;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

public class TestCookies {

    public static void main(String[] args) {
        try {
            URL url = new URL("https://www.example.com");
            Connection conn = Jsoup.connect(url.toString());
            conn.header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36");
            Document doc = conn.method(Connection.Method.GET).execute().parse();
            String requestHeaders = conn.request().headers().toString();
            System.out.println(requestHeaders);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}


0 0 9,13,15 16/18 6-6 ? 
00 9,12,15 16/17 6-6 2023

0 0/15 * * * ?

0 9,13,23 * 6-12 1-21 2023


master slave_1




cron范式：
           日    月份
0 0 5,9,21 17-18 6-6 ? *

         日 月份   年
0 0 5 16-18 6-7 ? 2023-2024


      configSchedule(param).then((response) => {
  
        })
   
      })
      
      
2023.06.19
1.邮件配置:
	表 地址 账号 密码 主题 内容
2.邮件发送修改
3.获取jsessionid
      
      
      
      
        INSERT INTO mail_config (mail_address,account,password,title, content)
        VALUES
            (
                #{address},
                #{account},
                #{password},
                #{title},
				#{content}
            ) 
      
      :rules="rules"
      
       rules: {
        password: [
          { validator: validatePass, trigger: 'blur' },
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 1000, message: '长度在 6 到 10 个字符', trigger: 'blur' }
        ],
        checkPass: [
          { validator: validatePass2, trigger: 'blur' },
          { required: true, message: '请再次确认密码', trigger: 'blur' },
          { min: 6, max: 25, message: '长度在 6 到 25 个字符', trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入用户名称', trigger: 'blur' },
          { min: 1, max: 25, message: '长度在 1 到 25 个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ]
      },




          <el-form-item
            label="邮箱"
            prop="email"
          >
            <el-input v-model="form.email" placeholder="请输入邮箱地址" style="width: max-content;" />
          </el-form-item>
      
      
         rules: {
        title: [{ required: false, message: '请输入邮箱地址', trigger: 'blur' },
          { min: 1, max: 255, message: '长度在 1 到 225 个字符', trigger: 'blur' }],
        email: [{ required: false, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
        password: [{ required: false, message: '请输入邮箱地址', trigger: 'blur' },
          { min: 1, max: 255, message: '长度在 1 到 225 个字符', trigger: 'blur' }],
        account: [{ required: false, message: '请输入邮箱地址', trigger: 'blur' },
          { min: 1, max: 255, message: '长度在 1 到 225 个字符', trigger: 'blur' }]
      },   
      
      
this.$refs[formName].validate((valid) => {
 if (valid) {
 
 
 }
      
       })
       
       
           public static String MAIL_HOST = "mail.hengrui.com";
       
     public static String MAIL_FROM = "<EMAIL>";
     // 用户名
    public static String MAIL_USER = "<EMAIL>";
    //密码
    public static String MAIL_PASS = "HR9cf3cbd8";
      
      

"的撒大艰苦环境开会看见
"撒旦教案设计的垃圾立刻解开了   即可路 撒旦
"爱神的箭喀什假大空撒肯定就拉开   埃里克森大家拉萨的独立
"阿三大苏打尽快了

  components: {
    'app-footer': Footer
  },


    public static String MAIL_FROM = "<EMAIL>";
     // 用户名
    public static String MAIL_USER = "<EMAIL>";
    //密码
    public static String MAIL_PASS = "HR9cf3cbd8";

import Footer from '../../components/Footer/footer.vue'




恒瑞医药【SHR6390】的临床试验中SUSAR安全性信息通知信



尊敬的研究者、机构/伦理老师，
您好！
感谢您对江苏恒瑞医药股份有限公司申办的临床试验的支持。
根据《药物临床试验质量管理规范》的公告（2020年第57号）第四十八条规定：申办者应将可疑且非预期严重不良反应快速报告给所有参加临床试验的研究者及临床试验机构、伦理委员会。
现向您提供在中国及境外开展的包含【】的临床试验中SUSAR报告。
请您及时阅读，并按需存档。
此邮件为系统邮件，请勿回复。如有问题，请联系项目组。

此致
敬礼！


江苏恒瑞医药股份有限公司



2023.06.21
1.定位邮件服务器问题
	邮件服务器偶发性问题，不稳定，抽风
2.修改右侧导航栏样式
3.修改上传dialog样式





待完成：
1.upload上传文件名超长省略号，悬浮显示
2.页脚信息固定










文件名长度的控制可以通过CSS的text-overflow属性来实现，同时使用title属性可以在鼠标悬停时显示完整文件名。具体实现可以参考以下代码：

```
<el-upload
  class="upload-demo"
  action="/upload"
  :on-success="handleSuccess"
  :file-list="fileList">
  <el-button size="small" type="primary">点击上传</el-button>
  <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
  <div slot="tip">文件名长度控制示例</div>
</el-upload>

<style>
.el-upload-list__item-name {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 150px; /* 最大显示长度 */
}

.el-upload-list__item-name:hover {
  cursor: default;
  max-width: none;
}

.el-upload-list__item-name:hover::before {
  content: attr(title);
  position: absolute;
  left: 100%;
  top: 0;
  z-index: 9999;
  width: auto;
  white-space: nowrap;
  background: #fff;
  padding: 2px 4px;
  border: 1px solid #ccc;
  box-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.el-upload-list__item-name:hover::after {
  content: "";
  position: absolute;
  left: calc(100% - 5px);
  top: 48%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 5px 0 5px 5px;
  border-color: transparent transparent transparent #ccc;
}
</style>
```

以上代码实现了在el-upload组件中，用CSS控制文件名的最大显示长度，并使用省略号表示超出部分。同时，在鼠标悬停时，使用title属性显示完整文件名，并增加了一个样式来使title属性的内容以气泡形式显示。


el-upload-list__item-name



 document.querySelector('.el-icon-document').addEventListener('mouseover', handleMouseOver);
 
 
 
 2023.06.25
 1. .el-upload-list__item-name {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        max-width: 100%; /* 最大显示长度 */
      }
 2. .el-upload-list__item-name:hover {
        white-space: normal;
        overflow: visible;
        text-overflow: clip;
      }
      
2023.06.26
1.完成了测试环境的上传页面文本显示样式优化
2.调整启动包文件夹下载名称显示
3.调整登录前端校验逻辑，移除uap登录接口调用
4.优化跟据不同角色加载不同的路由菜单




@RequestBody参数的GET请求：
HttpClient client = HttpClientBuilder.create().build();
HttpGet request = new HttpGet("http://example.com/api/resource");
StringEntity params = new StringEntity("{\"param1\":\"value1\",\"param2\":\"value2\"}");
request.addHeader("content-type", "application/json");
request.setEntity(params);
HttpResponse response = client.execute(request);



localStorage.setItem('user', res.data.data.name)
localStorage.setItem('role', res.data.data.uapRole)




 if (res.code === 200 && res.msg === 'OK') {
 localStorage.setItem('user', res.data.user.username)
 localStorage.setItem('role', res.data.user.role)
}

2023.06.27
1.完成airflow配置说明文档编写
2.完成susar平台生产、测试环境大日志文件拆分：
	split -d -b 10m slow3306_9105.log slow.log
	for i in `ls|grep slow`; do a=`echo $i|awk -F '.log' '{print $1$2".log"}'`; mv $i $a; done
3.介绍airflow dag文件定义，数据来源及使用说明

from datetime import timedelta, datetime
import airflow
from airflow import DAG
from airflow.contrib.operators.ssh_operator import SSHOperator
default_args = {
    'owner': 'zhouhui',
    'depends_on_past': False,
    'start_date': datetime.now() - timedelta(minutes=20)
}
dag = DAG(dag_id='testing_ssh_conn',
          default_args=default_args,
          schedule_interval="* 0/1 * * * ?"

t1_bash = """
mkdir /home/<USER>
"""
t1 = SSHOperator(
    ssh_conn_id='10.10.5.51',
    task_id='test_ssh_operator',
    command=t1_bash,
    dag=dag)
      
t1



6月工作内容
1.susar二期需求开发，解决测试过程中产生的问题
2.完成rave ftp数据下载同步到minio服务器
3.完成hengruiTAU数据每天全量更新同步至airflow服务器

7月工作计划
1.完成编码双语报告下载
2.完成susar系统验证工作

2023.06.28
1.调整签章置于pdf最底层，生产、测试环境更新部署
2.申请susar验证环境服务器及配置
3.



2023.06.29
1.双语报告下载

1. token
https://clinical-tst.hengruipharma.com:4080/usersyn/gettoken?projectid=medcoding_hr&secret=bioknow@228

2. 任务
https://clinical-tst.hengruipharma.com:4080/usersyn/funcstart?token=6142729C789C458098248F406BFC4A27&funcid=MC_BILINGUAL_REPORT&language=EN&otherLanguate=CN&range=batch&studyid=SHR-A2102-I-102&tableId=meddraimportdetail&ver=25.0&sn=M-EDC-SHR-A2102-I-102-pro&importId=521895940&outputformat=excel

3.查看
https://clinical-tst.hengruipharma.com:4080/usersyn/funcresult?token=6142729C789C458098248F406BFC4A27&funcid=MC_BILINGUAL_REPORT&taskid=0C5B04A46ECC4F7F81D5055F788AE800

4.下载
https://clinical-tst.hengruipharma.com:4080/dbplug/output/temp/shr-a2102-i-102_m-edc-shr-a2102-i-102-pro_medical%20history%2Cadverse%20event_20230629_1104_en_cn.zip


&key3={time_value}



set path="D:\Projects\GRP_CDSC_PENGR\SHR-A1811-III-301\data\raw"

rem Get current time with format yyyy/MM/dd/HH/mm/ss
for /f "tokens=2-4 delims=/ " %%a in ('date /t') do (set mydate=%%c/%%a/%%b) 
for /f "tokens=1-3 delims=/:" %%a in ('time /t/24') do (set mytime=%%a/%%b)
set timestamp=%mydate%/%mytime% 


&key3=%timestamp%



2023.07.03
1.批量下载双语报告导出 报告
 462487581, GLOBALB3Mar20, W-20210506-0010 SHR-1210-III-308

	String pattern = "^[A-Z0-9]+$";

	if("ABC123".matches(pattern)) {
		System.out.println("Matches!"); 
	} else {
		System.out.println("Does not match.");
	}

	// 输出 Matches!

 文件命名：SHR-1210-III-306_M-20210906-0014_既往病史_20230702_2204.zip
 
 
 
2.下午写文档
将SUSAR_BACK_V1.0.jar的项目压缩包以及程序安装包上传至服务器目录/home/<USER>/fileUpload下。
在/home/<USER>/fileUpload目录下执行SUSAR_BACK_V1.0.jar包运行命令“nohup java -jar SUSAR_BACK_V1.0.ja >log.txt &”。


2023.07.04
1.java

    String filePath = "C:\\Work\\medcoding_file\\" + fileName;
    FileOutputStream fos = new FileOutputStream(filePath);  
    fos.write(buffer, 0, len);
    fos.close();  
    
 2.医学编码测试环境：https://clinical-tst.hengruipharma.com:4080       
   医学编码生产环境：https://clinical.hengruipharma.com:8094   

***********	22、3306
***********	3306
***********	22
**********	3306
***********	22357
********** 1521
*********** 3306
*********** 3306

jdk1.8 IT安装
nginx1.22.0 IT安装
Python 3.6.8 IT安装
mysql 8.0.28 IT安装
airflow位置 root/airflow
airflow安装
    pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/ apache-airflow;
    pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/ pymysql；
- 设置home 目录 export AIRFLOW_HOME=~/airflow
- 数据库赋权：
    - 创建airflow安装库
        CREATE DATABASE IF NOT EXISTS airflow DEFAULT CHARACTER SET utf8 DEFAULT COLLATE utf8_general_ci;
    - 赋权
        grant all privileges on airflow.* to '战账户'@'%' identified by '密码';
        flush privileges;
    - 修改airflow.cfg配置文件中启动库和执行器配置：
        sql_alchemy_conn = mysql+pymysql://账户:密码@localhost:3306/airflow?charset=utf8
        executor = LocalExecutor
	- 启动
		nohup airflow webserver -p 8090 >>$AIRFLOW_HOME/airflow-webserver.log 2>&1 &
		
		
2023.07.05
1.SHR3162-III-305_medical rave数据下载bat开发、验证
2.susar验证环境文档编写，redis安装
3.airflow生产环境运维，定位调度器下线问题，关闭相关dag任务后，重启成功
SHR-A2102-I-101_medical
SHR-4602-I-101_medical
SHR3162-III-305_medical
HRS9531-201_medical



SHR3162-III-305




C:\Windows\System32\cmd.exe


"/c D:\RaveDataDownload\SHR-A1811-212.bat"



HRS-7535-201


"C:\Program Files (x86)\WinSCP\WinSCP.exe" /command "open ftp://erkang.zhou.hengrui.com:Zero2One4?@************:990 -implicit" "cd /hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand" "lcd D:\Projects\GRP_CDSC_PENGR\HRS-7535-201\data\raw" "get -neweronly *HRS_7535_201_?????_*" "exit" /log=D:\Projects\GRP_CDSC_PENGR\HRS-7535-201\data\raw\log_file_%ymd%.txt 
@echo off

set path="D:\Projects\GRP_CDSC_PENGR\HRS-7535-201\data\raw"

if exist "%path%" (
	 dir /b "%path%\*.zip*" > D:\Projects\GRP_CDSC_PENGR\HRS-7535-201\data\raw\output.txt
    )  

for /F "delims=" %%a in (D:\Projects\GRP_CDSC_PENGR\HRS-7535-201\data\raw\output.txt) do (set "lastLine=%%a")
echo %lastLine%

rem Get current time with format yyyy/MM/dd/HH/mm/ss
for /f "tokens=2-4 delims=/ " %%a in ('date /t') do (set mydate=%%c/%%a/%%b) 
for /f "tokens=1-3 delims=/:" %%a in ('time /t/24') do (set mytime=%%a/%%b)
set timestamp=%mydate%/%mytime% 

rem Set environment variables  
set MC_PATH=C:\Users\<USER>\Desktop\mc.exe
set SOURCE_FILE=D:\Projects\GRP_CDSC_PENGR\HRS-7535-201\data\raw\%lastLine%   
set DEST_PATH=minios3/raw/HRS-7535-201_sas.zip
set TAGS="key1=RAVE&key2=HRS-7535-201&key3=%timestamp%"  

echo Upload started at %time% on %date%...  

rem Execute mc to upload file with tags
"%MC_PATH%" cp %SOURCE_FILE% %DEST_PATH% --tags %TAGS%  

if %errorlevel% neq 0 goto error 
goto success

:error  
echo Upload failed, retrying...   
"%MC_PATH%" cp %SOURCE_FILE% %DEST_PATH% --tags %TAGS%
if %errorlevel% neq 0 goto error  

:success
echo Upload completed at %time% on %date%.   

:end




ps -ef|egrep 'scheduler|airflow-webserver'|grep -v grep|awk '{print $2}'|xargs kill -9


nohup airflow webserver -p 8090 >>$AIRFLOW_HOME/airflow-webserver.log 2>&1 &
rm -rf /xxxx/airflow/airflow-scheduler.pid

airflow scheduler -D

root:Hr_mysql1024



如何验证pymysql能否脸上本地数据库





将SUSAR_BACK_V1.0.jar的项目压缩包以及程序安装包上传至服务器目录/home/<USER>/fileUpload下。
在/home/<USER>/fileUpload目录下执行SUSAR_BACK_V1.0.jar包运行命令“nohup java -jar SUSAR_BACK_V1.0.ja >log.txt &”。




上传redis-6.2.11.tar.gz至服务器目录/home/<USER>
在/home目录下解压redis-6.2.11.tar.gz, tar -xzf redis-6.2.11.tar
在/home/<USER>
yum install -y gcc tcl


https://clinical-tst.hengruipharma.com:4080/usersyn/funcstart?token=79968A1AD1FD451998777698C2ED6F9C&funcid=MC_BILINGUAL_REPORT&language=EN&otherLanguate=CN&range=batch&studyid=HR-TPO-SAA-Ⅰ-Ⅱ&tableId=meddraimportdetail&ver=23.0_update&sn=M-20201130-0018&importId=521895940&outputformat=excel


41045581CD8E4BD1A54ED47C496D5A9B


https://clinical-tst.hengruipharma.com:4080/usersyn/funcresult?token=79968A1AD1FD451998777698C2ED6F9C&funcid=MC_BILINGUAL_REPORT&taskid=41045581CD8E4BD1A54ED47C496D5A9B



/dbplug/output/temp/hr-tpo-saa-%E2%85%B0-%E2%85%B1_m-20201130-0018_%E4%B8%8D%E8%89%AF%E4%BA%8B%E4%BB%B6_20230706_1110_en_cn.zip



https://clinical-tst.hengruipharma.com:4080/dbplug/output/temp/hr-tpo-saa-%E2%85%B0-%E2%85%B1_m-20201130-0018_%E4%B8%8D%E8%89%AF%E4%BA%8B%E4%BB%B6_20230706_1110_en_cn.zip



2023.07.06
1.系统配置列表处理
2.历史设置加操作人
3.频率显示为每天一次、两次、三次
4.medcoding 重复上传



2023.07.07
1.medcoding文件上传下载文件名超长导致下载失败定位，优化下载逻辑，超长字符文件过滤，仅下载文件名长度正常的文件
2.优化susar系统配置调度频率设置选项，定位修改调度未生效问题，当天修改调度时间的生效时间过期
3.定位rave数据更新延迟问题，定位下载服务器网络连接rave ftp服务器端口权限过期，联系IT续期，并约定该服务器该端口权限过期提醒续期由IT负责





pkill -f medcodingDownload-0.0.1-SNAPSHOT.jar && nohup java -jar /home/<USER>/home/<USER>

pkill -f medcodingDownload-0.0.1-SNAPSHOT.jar

nohup java -jar /home/<USER>/home/<USER>

/home/<USER>/




2023.07.08
1.系统验证文档编写
2.susar系统配置更改



2023.07.10
1.定位rave sas数据集未更新问题，脚本内的根据日期排序代码遗漏
2.编写susar系统安装验证文档
3.完成susar系统配置历史设置列表优化
4.优化medcoding文件上传文件名处理为指定格式


2023.07.11
1.完系统安装验证文档

2.



0 23 * * * python3 /home/<USER>

********** 1521 
gousy hrgsy@cdtms

server {
  listen 80;
  server_name localhost;
  location / {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Credentials' 'true';
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
    add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
  if ($request_method = 'OPTIONS') {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
    add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
    add_header 'Access-Control-Max-Age' 1728000;
    add_header 'Content-Type' 'text/plain; charset=utf-8';
    add_header 'Content-Length' 0;
    return 204;
  }
    proxy_pass http://localhost:8088;
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }
}


location / {
  add_header 'Access-Control-Allow-Origin' '*';
  add_header 'Access-Control-Allow-Credentials' 'true';
  add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
  add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
  if ($request_method = 'OPTIONS') {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
    add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
    add_header 'Access-Control-Max-Age' 1728000;
    add_header 'Content-Type' 'text/plain; charset=utf-8';
    add_header 'Content-Length' 0;
    return 204;
  }
}




2023.07.12
1.SHR-1210-III-329项目rave数据集下载上传batch脚本
2.superset图表iframe方式引用实现
3.


C:\Windows\System32\cmd.exe

"/c D:\RaveDataDownload\SHR-1210-III-329.bat"


http://localhost:9528/#/login

411611f7344d
/app/superset/config.py


jdk1.8 /data/jdk8 IT安装
nginx1.22.1 /data/nginx IT安装
mysql8.0.28 /opt/mysql/ IT安装
Apache Tomcat/9.0.73  /data/tomcat/ IT安装
python3.6.8 yum 安装




***********：3306
\\**********\\susar\\




1. 安装 MinIO SDK for Python
bash
pip install minio
2. 初始化 MinIO client,需要传入 endpoint、access key、secret key
python
from minio import Minio

client = Minio('localhost:9000',
               access_key='minioadmin',
               secret_key='minioadmin',
               secure=False)
3. 调用 list_object_versions 方法列出指定文件在所有版本
python 
versions = client.list_object_versions(bucket_name='mybucket',
                                       prefix='myfile.txt')
4. 遍历版本列表,打印出每个版本的 version_id 字段
python
for version in versions:
    print(version.version_id)
version_id 即为该对象的版本号。
这样我们就可以列出这个对象所有历史版本的版本号。你可以根据需要选择访问或操作特定版本的对象。 (edited) 

version_ids = ['1', '2', '3'] 
现在我们要访问其中的某个历史版本的文件,可以这样做:
1. 用版本号初始化一个 VersionedObject 对象
python
from minio.versioned_object import VersionedObject

object_version = VersionedObject('mybucket', 'myfile.txt', version_id=version_ids[1])
这里我们传入版本号 '2',表示选择该文件的第二个版本。
2. 使用 get_object 方法下载该版本对象
python
data = client.get_object(object_version)
get_object 会返回一个存储该版本文件内容的 BytesIO 对象。
3. 将内容写入到本地文件
python
with open('myfile_v2.txt', 'wb') as file_data:
    for d in data.stream(32*1024):
        file_data.write(d)
这样我们就下载了该文件的第二个版本到本地。
对其他版本的访问也类似,只需要传入不同的 version_id 即可。
这样我们就可以方便地访问 MinIO 中任意对象的任意版本了。 (edited) 


2023.07.13
1.全量、增量备份数据：
	mysqldump -uroot -pHr@mysql1024 --databases susar_auto_mail>/home/<USER>
	mysqldump -uroot -pHr@mysql1024 --databases xxl_job>/home/<USER>
2.按时间点，前端样式优化
3.0 0 17,18  * * ? *

DATE=$(date +%Y%m%d)

mysqldump -uroot -pHr@mysql1024 susar_auto_mail table1 > /home/<USER>/table1_${DATE}.sql
echo "备份数据库 susar_auto_mail 表 table1" >> /home/<USER>/backup.log


mysqldump -uroot -pHr@mysql1024 susar_auto_mail table2 > /home/<USER>/table2_${DATE}.sql
echo "备份数据库 susar_auto_mail 表 table2" >> /home/<USER>/backup.log



2023.07.14
1.系统验证文档编写
2.编写SHR-6390-III-303 rave数据下载batch脚本
3.更新hengruiTAU minio上传tag的时间字段为系统导出时间的代码逻辑，验证通过
4.解决susar平台系统配置修改xxl-job cron调度时间未立即生效的问题，验证通过
	
	
	
2023.07.17
1.susar验证环境mysql无法启动
[mysqld]                                                                                                                
bind-address = 0.0.0.0                                                                                                    
user=root                                                                                                               
pid-file = /var/lib/mysql/mysqld.pid                                                                                
socket = /var/lib/mysql/mysqld.sock                                                                               
port =3306
[client]                                                                                                                
port  = 3306                                                                                                      
socket  =/var/lib/mysql/mysqld.sock


var/lib/mysql/



systemctl {start|stop|restart|status} mysqld

systemctl start mysqld

2.


2023.07.18
1.
datadir=/var/lib/mysql
socket=/var/lib/mysql/mysql.sock

log-error=/var/log/mysqld.log
pid-file=/var/run/mysqld/mysqld.pid

default_authentication_plugin=mysql_native_password
innodb_buffer_pool_size = 4G

lower_case_table_names=1

sql-mode="NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION"
	
	
2.
SELECT * FROM tbl_xsht  ORDER BY  COL_CREATETIME DESC   WHERE  COL_ZT !='40' or  COL_ZT !='50' or  COL_ZT !='60' or  COL_ZT !='70' 

log-bin=/usr/local/mysql/data/mysql-bin
binlog_format=MIXED 
	



#!/bin/bash
## zeng liang bak sql
Logdir=/usr/local/mysql/logs
Time=$(date +%F)
mkdir $Logdir/"log-"$Time
find $Logdir -type f -mmin 1 -exec cp {} $Logdir/"log-"$Time/ \;

if [ -f $Logdir/"log-"$Time/mysql-bin.* ];then
  echo "mysql is zeng backup success on time-$(date +%F)" >>$Logdir/"log-"$Time/mysql_zeng_backup.log
else
  echo "mysql is zeng backup fail on time-$(date +%F)" >>$Logdir/"log-"$Time/mysql_zeng_backup.log
fi
mysqladmin -uroot -p123456 flush-logs >/dev/null	

              




























bash
#!/bin/bash

Logdir=/usr/local/mysql/logs
Time=$(date +%F)
mkdir -p $Logdir/"log-"$Time
if [ $? -ne 0 ]; then
  echo "mkdir failed" >> /usr/local/mysql/logs/db_backup.log
  exit 1
fi  

find $Logdir -type f -mmin 1 -exec cp {} $Logdir/"log-"$Time/ \;

if [ -f $Logdir/"log-"$Time/mysql-bin.* ]; then
  echo "mysql backup success on $(date +%F)" >> /usr/local/mysql/logs/db_backup.log  
else
  echo "mysql backup failed on $(date +%F)" >> /usr/local/mysql/logs/db_backup.log
fi

/usr/bin/mysqladmin -uroot -pHr@mysql1024 flush-logs >/dev/null



export MYSQL_PWD=Hr@mysql1024;
mysqlbinlog --no-defaults --start-datetime='2023-07-18 00:00:00' --stop-datetime='2023-07-18 17:21:00' /usr/local/mysql/logs/mysql-bin.000024 | mysql -uroot;



1.


0 0 */3 * * sh /home/<USER>

0 22 * * * sh /home/<USER>



2023.07.19
1.mysqlbinlog --no-defaults mysql-bin.000025 > home/bk000025.txt

2023.07.20
1.完成medcoding 传参配置下载任务dag任务
2.
	susar平台 token免登录功能实现：
	url参数中带uap token,请求
	https://clinical-tst.hengruipharma.com/login/userInfo/getUser.do
	Header参数是 
	l:zh_CN
	token:
	获取到用户信息，维护到登录系统中，
	如果超时跳转回uap登录页面


 console.log('获取地址栏的参数token:' + getUrlParam().split('=')[1])
  var url_token = getUrlParam().split('=')[1]
  if (url_token) {
    debugger
    // 有token，调用获取登录信息，拿到登录人信息,处理跳转逻辑
    const userInfo = await getUapLoginInfo(url_token)
    console.log('登录人信息:', userInfo)
    debugger
  } else {
    console.log('没有登录!')
  }
  
  
  
H2023001201491TEST1.pdf
H2023001201493TEST3.pdf
H2023001201492TEST2.pdf
  
  
H2023001201491TEST2.pdf
H2023001201490TEST1.pdf
 
 SHR3824-302
 
nohup java -jar /home/<USER>/home/<USER>




nohup java -jar /home/<USER>/home/<USER>
  
nohup java -jar /home/<USER>/home/<USER>
  
2023.07.24
1.写汇报ppt，初稿
2.写dcoding dag
3.调整对接uap一体化的代码
4.
查不到role就会死循环



{
	"code":200,
	"data":{
		"COL_SYSTEMCODE":"Susar",
		"COL_USERNAME":"周尔康",
		"COL_SYSTEMINFO":571146240,
		"COL_UNITID":473595906,
		"COL_REGION":"",
		"COL_UNITNAME":"江苏恒瑞医药股份有限公司",
		"COL_JOBNAME":"SAS程序员",
		"COL_EMAIL":"<EMAIL>",
		"COL_ROLENAME":"Uploader,Admin",
		"COL_USERTYPE":"3",
		"COL_PROVINCIAL":"",
		"ID":571179011,
		"COL_LASTMODIFYTIME":1690248390000,
		"COL_LOGINID":"<EMAIL>",
		"COL_DEPARTMENT":"临床数据科学中心",
		"COL_COUNTRY":"China",
		"COL_CREATETIME":1690248381000,
		"COL_MODIFYUSERID":472350743,
		"COL_USERNAME_EN":"Erkang Zhou",
		"COL_SUBUNIT":"",
		"COL_AUTHORIZATIONTIME":1690214400000,
		"COL_CITY":"",
		"COL_STATUS":"1",
		"COL_UUID":"075F4D1F989D469DA550A6B5D0BD3502",
		"VERSION":1,
		"COL_ROLE":"Uploader,Admin",
		"COL_UNITTYPE":"",
		"COL_USERID":472350743
	},
	"msg":"",
	"success":true
	
	
	2023.07.25
	1.完成susar uap一体化对接，更新部署测试环境
	2.
	http://localhost:9528?token=06bc8583b772498b9b35d0e481f17338
	
	
	
	db311d988483462e95e8123455ec7add
	
	86ea740bce314d3c8652c7769f727f30
	#87717f
	
	
	#66b1ff
	
	
	
	64, 158, 255
	
	
	122, 98, 127
	
	102, 177, 255
	重共通点，其他人能听懂
	版本控制
	干啥的 干了啥 还能怎么做
	public\home.png
	C:\Work\SusarPlatformPhaseIIDevelopment\SUSARMailAuto\public\home.png
	public\home.png
	<imag src="public\home.png">
	
	
	
	查阅开源社区资料，定位问题，发现与使用的插件版本不适配有关，通过调整插件至适配版本

	重构文件上传功能模块代码，以单例模式创建SMB连接，控制文件在操作网盘时并发场景下以最小带宽独占共享文件资源，保证文件的完整性

	获取的发件服务器状态码与实际结果有差异，无法从发件逻辑中解决，向上级反馈该问题，协调外部资源让IT部门从邮件服务器收件端获取相关记录并解决
	
	与上级共同研究具体方案，确定该部分数据保持原有客户端方式获取，以客户端所在系统环境为基础进行任务调度，并以数据更新到存储服务器的时间点为基线，进行后续的数据处理和同步操作
	21, 66, 144



2023-07-25 19:00:02


http://localhost:8080/webroot/decision
  
  
2023.07.27
1.

7月工作总结
1.susar系统安装验证文件编写，登录入口对接uap平台，优化对接后产生的问题
2.新增4个项目rave ftp数据同步脚本
3.医学编码数据定时同步程序开发、验证

8月工作计划
1.完成susar系统验证环境上线
2.实现fineBI工具视图创建、配置和外部平台挂载
3.确定airflow集成外部权限控制的实现方案和实现dag任务版本控制工作

  
  0 18 * * * python3 /home/<USER>/SyncSiteAndUser.py
  
  
  sshpass -p Hr@0601
  
  ssh satableau@***********
  
 2023.07.28
 1.更新验证环境脚本
 2.
	 all_data_1['sitenm']=all_data_1['TABLE_SCHEMA']+'.'+all_data_1['TABLE_NAME']
	 
	 
2023.07.31
1.完善susar系统验证文档
2.研究airflow dag版本控制
3.fineBI外挂的方式实践
4.修改susar文件记录筛选查询日期包含结束日期当天的数据：
  http://localhost:9528?token=0de8b2f9f53344308a6f6bc50e824b4d
  
  nohup java -jar /home/<USER>/home/<USER>
  
  

    
    
    var url = 'http://***********:8080/webroot/decision'
    var username = document.getElementById('username').value
    var password = document.getElementById('password').value
    jq.ajax({
      url: url + '/login/cross/domain?fine_username=' + username + '&fine_password=' + password + '&validity=-1',
      type: 'GET',
      dataType: 'jsonp',
      timeout: 10000,
      success: function(data) {
        alert(JSON.stringify(data))
        if (data.status === 'success') {
          console.log('successful!')
        } else if (data.status === 'fail') {
          console.log('login fail!')
        }
      },
      error: function() {
        alert('login error!')
      }
    })
    
 2023.08.01
 1.研究fineBI外挂
 2.
 3.
 
2023.08.02
1.解决fineBI突破iframe跨域限制问题
2.协助pdf电子签名服务器端调试、定位bug
3.与帆软BI供应方沟通回写功能实现方案，获取相关资料


2323.08.03
1.实践帆软报表回写功能


/home/<USER>/cdtms-sign/storage/output/20230801/48 0046b327831c44b4 /home/<USER>/cdtms-sign/storage/tasks/20230801/48_page.txt 电子签验证eCRF填写指南.pdf 0 1

fc-list | grep "arial"




/usr/share/fonts/chinesefonts


2023.08.04
1.完成fineBi 图表配置挂载
2.完成单元测试java gitlab连接



系统信息：5.4.17-2136.316.7.el8uek.x86_64 #2 SMP Mon Jan 23 18:37:18 PST 2023 x86_64 x86_64 x86_64 GNU/Linux
CPU: 4核Intel(R) Xeon(R) Gold 5218 CPU @ 2.30GHz
内存：8GB
硬盘：500GB


2023.08.07
1.研究java git api
  a.目前有开源的airflow console 实现dag在线编辑
2.研究fineBI 填报回写
	设计器:
	跨模板回写权限 &op=write
	
3.完成文档编辑
"node-sass": "^4.9.0",
"sass-loader": "^7.1.0",

4.airflow权限控制



Hr%40db0316
Hr@db0316


Hr%40mysql1024
Hr@mysql1024

2023.08.08
1.外部数据模块需求沟通：

2.airflow web端权限控制：



SHR-A1811-I-101, SHR-A1811-I-103, SHR-A1811-III-301, SHR-A1921-I-101, SHR-A2102-I-101, SHR-4602-I-101, SHR3162-III-305, SHR-1316-III-302-EN, SHR-1210-III-329, SHR-1210-II-218, INS068-302, INS068-301, HRS9531-201, HRS9531-102, HRS-7535-201, HRS-4642-I-101, HRS-1167-I-101, SHR0410-302, HRS-1167-I-101
  
***********
9030
root
initdata


2023.08.10
1.完成报表单元格数据远程调用
2.完成提交后远程接口请求
3.uap验证环境地址+验证环境前端包安装
4.定位medcoding下载报告数据问题，为接口文档中说明的调用参数传递顺序颠倒导致，重新部署服务包下载数据
5.讨论外部数据平台交互设计及部分前端实现逻辑 3.协助同步minio测试环境部分数据


2AAA447B9AB267C42A6CC30D127C1E07

RAVE SHR-1316-III-302-EN 2023/08/10/01/31


mc tag set minios3/raw/SHR-1316-III-302-EN_sas.zip "key1=RAVE&key2=SHR-1316-III-302-EN&key3=2023/08/10/01/31" 

mc tag minios3/raw/SHR-1316-III-302-EN_sas.zip "key1=RAVE&key2=SHR-1316-III-302-EN&key3=2023/08/10/01/31" 


2023.08.11
1.https://susar-tst.hengrui.com/susarAuto/user/login



FR.ajax({
   url:"https://susar-tst.hengrui.com/susarAuto/user/login",
   data:{
       password: 
"WvCpziGt6ETUiwLvfGOIt0hnJMC8m18M4njz6FqBz6LM6eITFumwpi4LJauyGZwsefvCCAVslWzof93/lY9ERDHrGtDO5YwqDQq6f55Imdq3fgOA+/KMXEuJCbHDXSiRl0svWQOc0ucT24oGdUwn6IJcC8y/cKEgnTXYPOn0T78eKm0BkGHILGOpCer4TMBuqBlcMha48B288iUU6DWG2pVl+6FE+X4Qpq7IX/1tT1chyhT447cTAYIwmtPPiq0dT4QZPGAwULOnAoKLrvRKF7YourGnHkM/tQ3BLE/A4dEWueEQzqWIetEQiTXKy8X1OhqZ8XVkRg36vGQS33T1Nw==",
username: 
"yLza3ZZoCCag5gb4K+qr+KHYXA1mApPdGUuh0n3ON6WSv6waWpJv9/9H/34CHLX+MpIJgxP2vgpuK8kpbIFkRkQFZxLAXrCxlAY/swM+qb5s9BxS5Qjp0Tq5yHlIXGdAYkU4XiM28C8Q7QEXsZbNf88JLa49afOvqlJMWDdey09hxhpzgMxmdfqNzZ3O38c1uTZZR+D25CJVa6KukqEbPD+KXEMnJT+n2NXXE/YHzn7rlU+RVHIiGVZkdffy6Ev6dSxq+36X3OAjOJe2Xy+tF2NLPBp67jk+6CWO5s+icSzmFwV57eHBTNKovdqGNtAuIJw9JOBitAmvamJGB7Mlaw=="
   },
   dataType:"jsonp",//跨域采用jsonp方式     
   timeout:5000,//超时时间（单位：毫秒）
async: false,
complete: function (res, msg) {
if (msg == 'ok') {
var u = FR.jsonDecode(res.data);
if (u != null && u.message) {
FR.Msg.alert("通知",u.message);
//u.data.ip u.data.time u.data.city 分别可返回上次登录ip、时间、城市
}else{
FR.Msg.alert("通知",u.message);
}
}
}});




FR.ajax({
	url: "https://susar-tst.hengrui.com/susarAuto/user/login",
	data: {
		password: "WvCpziGt6ETUiwLvfGOIt0hnJMC8m18M4njz6FqBz6LM6eITFumwpi4LJauyGZwsefvCCAVslWzof93/lY9ERDHrGtDO5YwqDQq6f55Imdq3fgOA+/KMXEuJCbHDXSiRl0svWQOc0ucT24oGdUwn6IJcC8y/cKEgnTXYPOn0T78eKm0BkGHILGOpCer4TMBuqBlcMha48B288iUU6DWG2pVl+6FE+X4Qpq7IX/1tT1chyhT447cTAYIwmtPPiq0dT4QZPGAwULOnAoKLrvRKF7YourGnHkM/tQ3BLE/A4dEWueEQzqWIetEQiTXKy8X1OhqZ8XVkRg36vGQS33T1Nw==",
		username: "yLza3ZZoCCag5gb4K+qr+KHYXA1mApPdGUuh0n3ON6WSv6waWpJv9/9H/34CHLX+MpIJgxP2vgpuK8kpbIFkRkQFZxLAXrCxlAY/swM+qb5s9BxS5Qjp0Tq5yHlIXGdAYkU4XiM28C8Q7QEXsZbNf88JLa49afOvqlJMWDdey09hxhpzgMxmdfqNzZ3O38c1uTZZR+D25CJVa6KukqEbPD+KXEMnJT+n2NXXE/YHzn7rlU+RVHIiGVZkdffy6Ev6dSxq+36X3OAjOJe2Xy+tF2NLPBp67jk+6CWO5s+icSzmFwV57eHBTNKovdqGNtAuIJw9JOBitAmvamJGB7Mlaw=="
	},
	dataType: "jsonp", //跨域采用jsonp方式     
	timeout: 5000, //超时时间（单位：毫秒）
	async: false,
	complete: function(res, msg) {
		if (msg == 'ok') {
			var u = FR.jsonDecode(res.data);
			if (u != null && u.message) {
				FR.Msg.alert("通知", u.message);
				//u.data.ip u.data.time u.data.city 分别可返回上次登录ip、时间、城市
			} else {
				FR.Msg.alert("通知", u.message);
			}
		}
	}
});




2.
3.



https://clinical-tst.hengruipharma.com
https://meduap-tst.hengrui.com/



代码上传部分：
1、可以上传code文件。
2、可以把上传的code的文件名作为code名
3、可以选择code上传的位置（功能）（通用、医学审核、sas-ec。。。。作为codelist可以新增）
4、可以选择已有code作为依赖
5、可以把git 流程做进去（pull\push add commit compare ）
6、提交服务器gitlab的程序能在页面生成版本号，同时可以对应gitlab上的的版本

自动化dag部分：
1、可以选择已有的功能组织代码
2、dag中需调整的部分可以实现界面化
3、页面展示Dag模板列表，查看模板dag代码
4、支持dag页面创建，模板Dag选择，编辑，删除，提交（提交至gitlab）
5、dag提交gitlab后的ci/cd：代码自动检测、dag_task检测，通过检查后dag代码自动合并到主分支并邮件通知，不通过则以邮件方式提醒分支合并失败
5、airflow定时拉取gitlab主分支dag代码更新dag
代码审阅部分
1、可以按照功能选择已经在gitlab上已上传未审核的代码
2、可以对该code进行comments，并且返回通知至提交人
3、对已确认的代码
4
代码管理
1、可以新建失效（功能）codelist
2、可以生成依赖性地图




 dag_folder from a remote source
 <EMAIL>
 
 2023.08.11
 1.
 2.
 3.


 FileRepositoryBuilder builder = new FileRepositoryBuilder();
        Repository repository = builder.setGitDir(new File("myrepo.git"))
                .readEnvironment()
                .findGitDir()
                .build();

        // 打开一个 Git 操作实例
        Git git = new Git(repository);

        // 添加文件并提交更改
        git.add().addFilepattern(".").call();
        git.commit().setMessage("Initial commit").call();

        // 推送到远程仓库
        UsernamePasswordCredentialsProvider credentialsProvider = new UsernamePasswordCredentialsProvider("your-username", "your-password");
        git.push().setCredentialsProvider(credentialsProvider).call();

        // 关闭 Git 操作实例
        git.close();


2023.08.14
1.JGit add commit push pull操作实现 
2.uap验证环境验证
3.

2023.08.15
1.定位医学编码报告下载问题，修改代码下载逻辑，待验证
2.JGit本地代码提交接口验证，定位本地文件不推送远程仓库问题，优化部分逻辑
3.验证java sas 连接程序是否能够连接服务器


C:\Program Files\SAS\SASHome\SASFoundation\9.4\core\sasext\sspiauth.dll

2023.08.16


2023.08.17
1.JGit
2.fineR 实现带单元格参数  ajax请求


ldconfig 
LD_LIBRARY_PATH
/usr/lib/libXinerama.so.1
/usr/lib/libcairo.so.2


2023.08.21
1.编写帆软免登录嵌入文档、编写airflow下载hrtau文档
2.验证imap协议读取收件箱数据单元测试，验证退信数据分拣读取的可行性

2023.08.22
1.系统退信邮件分拣逻辑开发
multipart/related



2023.08.24


















-- ----------------------------
-- Table structure for email_bounce_info
-- ----------------------------
DROP TABLE IF EXISTS `email_bounce_info`;
CREATE TABLE `email_bounce_info`  (
                                        `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        `subject` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退信主题',
                                        `receiveTime` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退信时间',
                                        `receiver` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退信邮箱地址',
                                        `errorCode` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退信邮箱地址',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25029 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;




2023.08.25
1.susar退信后端逻辑开发，新增退信记录获取接口
2.susar邮件配置历史记录后端接口开发，完成邮件配置历史记录关联信息获取

2023.08.27
1. 退信补发


8月工作总结：
1.帆软BI工具本地部署，实现免登录嵌入业务系统页面，实现报表填报回写逻辑
2.协助电子签调试、定位线上bug
3.定位并解决medcoding双语报告下载数据问题
4.外部数据平台需求调研
5.JGit代码版本控制后端接口开发
6.susar退信功能开发

9月工作计划：
1.外部数据平台开发
2.susar退信功能开发、完善
3.帆软BI服务生产、测试环境部署，报表实现医学审核列表审核回写等操作


2023.08.28
1.补发需要的信息：
 接收人邮箱，化合物名称，退信记录id，文件名称
  
  
  props.setProperty("mail.transport.protocol","imap");
  props.setProperty("mail.imaps.auth","true");      
  props.setProperty("mail.debug","true"); 
  props.
  setProperty("mail.imap.sasl.mechanisms.oauth2.oauthToken",  password);
  Session session = Session.getDefaultInstance(props);  
  Store store = session.getStore(protocol);     
  return new JavaMailReader (store, host, port, username, password, "inbox");


fineBI 备注表逻辑，将修改列id全部存入备注表，增加相应的备注字段，但是没有哪个字段值被修改的记录
退信获取 exchange：
https://learn.microsoft.com/en-us/answers/questions/1230605/a1-no-authenticate-failed-using-javamail-and-oauth


2023.08.29
1.
HR070803-301
SHR-1210-III-331
SHR-1703-201
SHR-1701-215（查不到）
HRS-2261-201
SHR8735-301
SHR-A1921-201
SHR-1905-201
SHR-1906-201
SHR-1210-II-222
SHR-1501-I-103(查不到)
SHR-A1811-206
SHR-A1811-III-306
SHR8735-201
SHR-A1811-Ib/II-205(查不到)
SHR-A1811-209
SHR-A1811-207
SHR-1905-202


RAVE:
SHR4640-303
SHR_1701_III_307
SHR_1316_III_303
RSJ10535(查不到)
FZPL-III-303
SHR_1210_III_336
SHR-A1811-II-202
SHR0302-305
SHR0302-303
SHR-1314-204
HR-TPO-ITP-III-PED
SHR-A1811-II-201
SHR-A1811-II-203

SHR-1210-Ⅲ-336



SHR_1210_Ⅲ_336
SHR_1210_III_336

2023.08.30
1.角色信息新增下载和只读
2.新增系统配置历史记录导出


this.judgeRole = localStorage.getItem('role')



v-if="judgeRole==='Read_Only'?false:true"

v-if="judgeRole==='Downloader' || judgeRole==='Read_Only'?false:true"













 

airflow dags pause MedCoding_Trans_INS068-302   
airflow dags pause SHR-1210-II-218_medical      
airflow dags pause SHR-1210-II-222_medical      
airflow dags pause SHR-1210-III-329_medical      
airflow dags pause SHR-1210-III-331_medical     
airflow dags pause SHR-1210-III-336_medical    
airflow dags pause SHR-1314-204_medical         
airflow dags pause SHR-1316-III-302-EN          
airflow dags pause SHR-1316-III-303_medical     
airflow dags pause SHR-1501-I-103_medical       
airflow dags pause SHR-1701-215_medical        
airflow dags pause SHR-1703-201_medical        
airflow dags pause SHR-1802-II-202_medical      
airflow dags pause SHR-1905-201_medical         
airflow dags pause SHR-1906-201_medical        
airflow dags pause SHR-4602-I-101_medical      
airflow dags pause SHR-A1811-206_medical         
airflow dags pause SHR-A1811-I-101_medical     
airflow dags pause SHR-A1811-I-103_medical     
airflow dags pause SHR-A1811-II-201_medical    
airflow dags pause SHR-A1811-II-202_medical     
airflow dags pause SHR-A1811-II-203_medical    
airflow dags pause SHR-A1811-III-301_medical   
airflow dags pause SHR-A1921-I-101_medical     
airflow dags pause SHR-A2102-I-101_medical     
airflow dags pause SHR0302-303_medical          
airflow dags pause SHR0302-305_medical        
airflow dags pause SHR3162-III-305_medical     
airflow dags pause SHR4640-303_medical          
airflow dags pause SHR6508-201_medical          
airflow dags pause SHR8735-201_medical          
airflow dags pause SHR8735-301_medical         
airflow dags pause airftp                      
airflow dags pause airminio                    
airflow dags pause download_hengruiTAU41_data   
airflow dags pause download_hengruiTAU_data     
airflow dags pause download_medcoding_data     
airflow dags pause medical_pro_clinical    



parsing_processes
concurrency
max_active_runs

***********
edc_report5

/home/<USER>/data



group1/M00/05/09/CgoMKmTvVgmALPIAAAGPkdFGpRA90..zip



new Font(Font.HELVETICA,Font.BOLD,12);

      response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setContentLength(new Long(f.length()).intValue());
            try {
                response.setHeader("Content-Disposition", "attachment; filename=" + new String(fileName.getBytes("UTF-8"), "UTF-8"));
                //新增文件下载记录
                OperateEntity operate = new OperateEntity(userId, fileId, OperateType.DOWNLOAD.toString(), ObjectType.FILE.toString(), fileName, 'Y');
                fileUploadMapper.addOperateRecord(operate);
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            
            
            


{{"区域", "总销售额(万元)", "总利润(万元)简单的表格"}, {"江苏省", 9045, 2256}, {"广东省", 3000, 690}}



 var param = {}
      param.type = this.select
      param.keyWord = this.input3
      if (this.timegap) {
        param.startTime = parseTime(this.timegap[0])
        param.endTime = parseTime(this.timegap[1]).split(' ')[0] + ' 23:59:59'
      } else {
        param.startTime = ''
        param.endTime = ''
      }
      param.compoundFolder = this.urlParam.compoundFolder
      param.folderName = this.urlParam.folderName
      param.sortOrder = this.sortOrder
      param.sortColumn = this.sortColumn
      this.listLoading = true
      param.pageNum = this.pageNum
      param.pageSize = this.pageSize
      
      
      
      
      Admin,Uploader,Read_Only,Downloader
      
      
      
      
      
          //写PDF内容
        InputStream ins = null;//创建文件输入流
        try {
            ins = new FileInputStream(tmpFile);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }
        OutputStream ops = null;//从response创建文件输出流
        try {
            ops = response.getOutputStream();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        BufferedInputStream bis = new BufferedInputStream(ins);//利用带缓冲的输入输出流
        BufferedOutputStream bos = new BufferedOutputStream(ops);

        byte[] buffer = new byte[1024];
        int len = -1;
        try {
            while ((len = bis.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        try {
            bos.flush();
            bos.close();
            bis.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
      
      
      
      
      
      
      
      
      
      
      
      
      
      
   //把前端文件转换成File 暂存
        File tempFile = null;
        try {
            // 新的文件或目录
            tempFile = new File(UtilConstant.TEMP_File_Path + UtilConstant.FILE_SEPARATOR + "query_result.xlsx");
            if (tempFile.exists()) {  //  确保新的文件名不存在
                tempFile.delete();
            }
            excelFile.transferTo(tempFile);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List<String> filesPath = new ArrayList<>();
        String filePath = "";
        int totalLength = ids.size() + 1;
        File[] tempList = new File[totalLength];
        tempList[0] = tempFile;
        filesPath.add(tempFile.getPath());
        int index = 1;
        //ids 从库里获取地址，然后获取相应的文件
        for (int i = 1; i < totalLength; i++) {
            FileUploadEntity entity = fileUploadMapper.getUnSendFileById(ids.get(i - 1));
            filePath = entity.getFilePath();
            String tempPath = "";
            //获取nas文件到本地
            if (!StringUtils.isBlank(filePath)) {
                try {
                    SMBUtils smb = SMBUtils.getInstance(filePath);
                    SmbFile smbFile = smb.getSmbFile();
                    tempPath = smbUtils.smbGetFile(smbFile, UtilConstant.TEMP_File_Path + UtilConstant.FILE_SEPARATOR + "report");
                    smb.close();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                File file = new File(tempPath);
                tempList[index++] = file;
            }
        }
        filesPath.add(UtilConstant.TEMP_File_Path + UtilConstant.FILE_SEPARATOR + "report");
        // 创建临时文件
        File zipFile = null;
        FileInputStream fis = null;
        BufferedInputStream buff = null;
        try {
            //临时文件名称
            zipFile = File.createTempFile("test", ".zip");
            FileOutputStream fot = new FileOutputStream(zipFile);
            // 为任何OutputStream产生校验，第一个参数是制定产生校验和的输出流，第二个参数是指定Checksum的类型 (Adler32(较快)和CRC32两种)
            CheckedOutputStream cos = new CheckedOutputStream(fot, new Adler32());
            // 用于将数据压缩成Zip文件格式
            ZipOutputStream zos = new ZipOutputStream(cos);
            String[] arrays = filesPath.toArray(new String[filesPath.size()]);
            //保留文件目录
            FileUtils.toZip(arrays, fot, true);
            ServletOutputStream os = response.getOutputStream();
            //下载文件,使用spring框架中的FileCopyUtils工具
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            //设置响应头,attachment表示以附件的形式下载，inline表示在线打开

            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("Susar-1231-2023-01", "UTF-8"));
            fis = new FileInputStream(zipFile);
            buff = new BufferedInputStream(fis);
            FileCopyUtils.copy(buff, os);
            //新增用户下载记录
            for (int id : ids) {
                FileUploadEntity entity = fileUploadMapper.getUnSendFileById(id);
                int fileId = entity.getId();
                String fileName = entity.getFileName();
                String compound = entity.getCompoundFolder();
                OperateEntity operate = new OperateEntity(userId, fileId, OperateType.DOWNLOAD.toString(), ObjectType.FILE.toString(), fileName, compound, 'Y');
                fileUploadMapper.addOperateRecord(operate);
            }

        } catch (Exception e1) {
            try {
                throw new BusinessException("批量下载失败");
            } catch (BusinessException e) {
                throw new RuntimeException(e);
            }
            // 关闭流
        } finally {
            try {
                if (null != fis) {
                    fis.close();
                }
                if (null != buff) {
                    buff.close();
                }
            } catch (IOException e) {
                log.error("流关闭异常");
            }
            // 删除临时文件
            if (null != zipFile) {
                zipFile.delete();
            }
            // 删除临时文件
            if (tempList.length > 0) {
                for (File file : tempList) {
                    file.delete();
                }
            }
        }    
      
      
      
      export function downloadQueryResult(data) {
  return request({
    url: 'susarAuto/downloadQueryResult',
    method: 'post',
    data,
    headers: { 'content-type': 'application/json' },
    responseType: 'blob'
  })
}
      
      exportToPdf
      
      前端下载pdf接口返回之后需要的操作：
          const blob = new Blob([res], { type: 'application/pdf' })
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a') // 创建a标签
          link.href = url
          link.download = fileName // 重命名文件
          link.click()
          URL.revokeObjectURL(url) // 释放内存
          loading.close()
      
      
      smb://zhouh36:HR9cf3cbd8@**********/susar/localTest/2023-06/H2023001201111 SHR-TEST-215_SAE03_FU4_INV_最新.pdf
      
      
      
      
      
      https://clinical.hengruipharma.com:8094/usersyn/gettoken
      
      
      
      
      
      
       jsonObject.put("isDeleted", file.getIsDeleted() == "N" ? "已上传" : file.getIsDeleted() == "Y" ? "已删除" : file.getIsDeleted());
            jsonObject.put("isSend", file.getIsSend() == "N" ? "未发送" : file.getIsSend() == "Y" ? "已发送" : file.getIsSend());
            
            
 var currentTime=this.getTimeStr();           
 '文件上传记录-'+currentTime+'.xlsx'
 
 judgeRole: '',
      this.judgeRole = localStorage.getItem('role')
      v-if="judgeRole!=='adm'?false:true"
      
      judgeRole.indexOf('Admin') === -1
      
      end_month < 10 ? '0' + end_month : end_month
      
      SHR0302_303
      
      SHR0302-303
      
      C:\Windows\System32\cmd.exe
      "/c D:\RaveDataDownload\SHR-6390-III-303.bat"
      
		708.0F, 1000.0F
		
		842
		
		    <Connector port="8080" protocol="HTTP/1.1"
               connectionTimeout="20000"
               redirectPort="8443" 
			   URIEncoding="utf-8" 
    <!-- A "Connector" using the shared thread pool-->
    
    
    <Connector port="8080" protocol="HTTP/1.1"
               connectionTimeout="20000"
               redirectPort="8443" 
			   URIEncoding="utf-8" 
			   relaxedPathChars="|{}[],%"
			   relaxedQueryChars="|{}[],%"/>
    <!-- A "Connector" using the shared thread pool-->
		
		
relaxedPathChars="|{}[],%"
relaxedQueryChars="|{}[],%"/>

key1:HRTAU, key2:HR070803-301, key3:2023/09/06/20/44

 SELECT
            max(id) as id ,
            max(COL_VER) as COL_VER,
            max(COL_SN) as COL_SN,
            DATE ( COL_LASTMODIFYTIME ) as exportTime
        FROM
            tbl_meddraimport
        WHERE
                ( id, COL_CREATETIME ) IN (
                SELECT
                    MAX( id ),
                    MAX( COL_CREATETIME )
                FROM
                    tbl_meddraimport
                WHERE
                    COL_STUDYID = #{studyId}
                GROUP BY
                    COL_FILDESC
            )
          and COL_FILDESC not like '%,%'
          and DATE ( COL_LASTMODIFYTIME ) = DATE (
            NOW()) and IFNULL(COL_COMMENT, '')  not like '%错%';
            
            
            
            
                     .tags(Collections.singletonMap("key1", "medCoding")) // 添加tags
                        .tags(Collections.singletonMap("key2", file.getName())) // 添加tags
                        .tags(Collections.singletonMap("key3", exportTime)) // 添加tags
                        
                        
                        
                        
                        minioConfig.getMinioClient().setObjectTags(
     SetObjectTagsArgs.builder()
         .bucket("my-bucketname")
         .object("my-objectname")
         .tags((map)
         .build());
         
         
         
          SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                .bucket(buckName)
                .object("tagMethod/objectTags.txt")
                .tags(new HashMap<String, String>(1) {{
                    this.put("blessing", "star");
                }})
                .build();

2023.09.11
1.
mc config host add myminio https://play.min.io ACCESSKEY SECRETKEY

mc.exe alias set minios3 http://***********:9000 minioadmin minioadmin

2. 化合物文件夹 this.select
    关键词 this.input3
    开始时间-结束时间
     if(this.timegap){
        param.startTime = parseTime(this.timegap[0])
      param.endTime = parseTime(this.timegap[1]).split(' ')[0]+' 23:59:59'
      }else{
        param.startTime=""
        param.endTime=""
      }
 var header = [{
        '记录名称':'文件上传记录','化合物':this.select,'开始日期':startTime,'结束日期':endTime,'文件名/用户名':this.input3,'导出时间': currentTime, '操作人': localStorage.user
      }]



   var startTime = ''
      var endTime = ''
      if (this.timegap) {
        startTime = parseTime(this.timegap[0])
        endTime = parseTime(this.timegap[1]).split(' ')[0] + ' 23:59:59'
      } else {
        startTime = ''
        endTime = ''
      }
      var header = [{
        '模块名称': '月度包', '化合物文件夹': this.urlParam.compoundFolder, '月份': this.urlParam.folderName, '开始日期': startTime, '结束日期': endTime, '文件名/上传者': this.input3, '导出时间': currentTime, '操作人': localStorage.user
      }]
      
      
      
     root/mc cp /tmp/hsperfdata_root/WEB-INF/ minios3/finebi-tst --tags "key1=fineBI&key2=WEB-INF"
     
     
     
     HR-TPO-SAA-Ⅰ-Ⅱ_M-20201130-0018_不良事件_20230706_2109_CN_EN.csv
     
     
     
     nohup java -jar /home/<USER>/home/<USER>/medcodingDownload_$(date +%Y%m%d).log &
     
     
     SHR-A1921-I-101_M_AdverseEvent.csv
     SHR-A1921-I-101_M_MedicalHistory.csv
     SHR-A1921-I-101_W_Prior andConcomitantMedication.csv
     
     pip3 install apache-airflow-providers-amazon
     
     r"(^H\s|\sH\s)"
     
     /^(H\s|\sH\s)/
     
     SHR6390-III-303_sas.zip
     SHR-6390-III-303_sas.zip
     
     SHR-6390-III-303
     
     SHR-1210-III-315
     
	
	 pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/ pip --upgrade
	 pip3 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/ pyopenssl --upgrade
     
     
      是否存在重复代码
	  测试挂载.xlsx
	  
	  
	  SHR-1316-III-303
	  
	  C:\Windows\System32\cmd.exe
	  "/c D:\RaveDataDownload\SHR-1316-III-303-EN.bat"
	  
	  
	  
	  2023.09.20
	  1.SHR-6390-III-303
	  
	  
	  SHR3162-III-305_M-20230905-0036_Adverse Event_20230919_2302_EN_CN.csv
	  SHR3162-III-305_W-20230904-0001_Prior and Concomitant Medication_20230919_2302_EN_CN.csv
	  
	  
	  nohup java -jar /home/<USER>/home/<USER>/medcodingDownload_$(date +%Y%m%d).log &
	  
	  
	  
	  f3e88c0c08292add8fdffa34075eea66-2
	  f3e88c0c08292add8fdffa34075eea66-2
	  
	  ad6d6888117271a3c79ab804257005bf-2
      ad6d6888117271a3c79ab804257005bf-2
      ad6d6888117271a3c79ab804257005bf-2
      
      0cb53febe6896ea21cc43e61d49e52ce-2
      0cb53febe6896ea21cc43e61d49e52ce-2
      
      $.store.HRS-7535-201_CLIN_CONSIS_01[*]

      
      ce4cae3ee2dacb7d1a770154e53ce078
      ce4cae3ee2dacb7d1a770154e53ce078
      如果有-则是分片，需要查询数据库
      
      
      
      
2023.09.22
1.测试用例ID+.步骤序号+.截图序号
2.

Hr@db0316
Hr%40mysql1024
Hr@


mc cp /tmp/hsperfdata_root/WEB-INF  minios3/finebipro --tags "key1=fineBI&key2=WEB-INF"


SHR-1210-III-323.bat
C:\Windows\System32\cmd.exe
"/c D:\RaveDataDownload\SHR-1210-III-323.bat"


SHR-1802-II-202

group1/M00/04/68/CgoMKWURi5SAf37KAAT2lmR9qv875..zip

group1/M00/04/69/CgoMKWUSRoeAfvScAANl844DygY66..zip



nohup java -jar fileupload-1.0.0-SNAPSHOT.jar >log.txt &


2023.09.27
1.xxl-job调度器的appName应该与fileupload代码中配置的xxl.job.executor.appname一致
2.
    <select id="getSusarJobId" parameterType="string" resultType="int">
        SELECT
            id
        FROM
            xxl_job_info
        WHERE
            executor_handler = 'mileageJobHandler' and author='马源培'
    </select>
    记得确认author的名字
    
    
    
    weix5
    weix@edc
    edc_report5
    /home/<USER>/data
    
    
  chown mysql:mysql /var/run/mysqld
    
//模板路径
var url = encodeURI(encodeURI(
"/webroot/decision/view/report?viewlet=批注.cpt&op=write&a="+a));
//窗体
var $iframe = $("<iframe id='inp' name='inp' width='100%' height='100%' scrolling='no' frameborder='0'>");
//将窗体的src属性设置为模板路径
$iframe.attr("src", url);
//窗体的属性
var o = {
    title: "对话框",    //标题
    destroyOnClose:true,   // 是否在关闭对话框的时候将对话框从dom中移除
    width: 680,         //宽度
    height: 640,        //高度
    //closable:true,    //是否显示关闭按钮，默认true
    //confirm:true,     //是否添加确认取消按钮,默认false
    //draggable:true   //是否可拖动，默认true
};
//弹出窗体
FR.showDialog(o.title, o.width, o.height, $iframe, o);



118	<EMAIL>	<EMAIL>	HR9cf3cbd8	恒瑞医药【test】的临床试验中SUSAR安全性信息通知信	

	
	
	
	2023.10.10
	
	meduap-tst.hengrui.com
	
	
	clinical-tst.hengruipharma.com
	
	
	
	
	
	
	




C:\Windows\System32\cmd.exe

"/c D:\RaveDataDownload\>SHR-1701-III-308.bat"


2023.10.11
HR_BLTN_III_NSCLC

    HR-BLTN-III-NSCLC
    
    /hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand
    
    "C:\Program Files (x86)\WinSCP\WinSCP.exe" /command "open ftp://erkang.zhou.hengrui.com:Zero2One4?@************:990 -implicit" "cd /hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand" "lcd D:\Projects\GRP_CDSC_PENGR\HR-BLTN-III-NSCLC\data\raw" "get -neweronly *NSCLC*" "exit" /log=D:\Projects\GRP_CDSC_PENGR\HR-BLTN-III-NSCLC\data\raw\log_file_.txt
    
  
D:\Projects\GRP_CDSC_PENGR\NSCLC\data\raw
D:\Projects\GRP_CDSC_PENGR\HR-BLTN-III-NSCLC\data\raw




/hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand
/hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand





  Map mailInfo = fileUploadMapper.getMailInfo();
        String mailAddress = (String) mailInfo.get("mail_address");
        String mailAccount = (String) mailInfo.get("account");
        String password = (String) mailInfo.get("password");
        
        
        
        
        
        kill -9 26609
        nohup java -jar fileupload-1.0.0-SNAPSHOT.jar >log.txt &
        nohup java -jar fileupload-1.0.0-SNAPSHOT.jar >log.txt &
        
        
        
2023.10.12
1.配置nginx

2. 批注审核功能 复选框 审核、批注是一对多的关系，非一对一


IF(LEN(H5)>0,H5,RANDBETWEEN(1,100000))



SHR-1316-III-303-EN

"/c D:\RaveDataDownload\SHR-1314-204.bat"
"/c D:\RaveDataDownload\HR-BLTN-III-NSCLC.bat"

C:\Windows\System32\cmd.exe
HR-BLTN-III-NSCLC.bat

D:\Projects\GRP_CDSC_PENGR\NSCLC\data\raw
*HR_BLTN_III_NSCLC*


group1/M00/04/7F/CgoMKWUr6emAQhpeAAmd8ob8VRw17..zip
/home/<USER>/data/04/7F






yum install -y --setopt=protected_multilib=false gcc gcc-c++ make cmake automake autoconf gd file bison patch mlocate flex diffutils zlib zlib-devel pcre pcre-devel libjpeg libjpeg-devel libpng libpng-devel freetype freetype-devel libxml2 libxml2-devel glibc glibc-devel glib2 glib2-devel bzip2 bzip2-devel ncurses ncurses-devel curl curl-devel libcurl libcurl-devel e2fsprogs e2fsprogs-devel krb5 krb5-devel openssl openssl-devel  kernel-devel libtool-libs readline-devel gettext-devel libcap-devel php-mcrypt libmcrypt libmcrypt-devel recode-devel 


tar zxvf nginx-1.24.0.tar.gz
cd nginx-1.24.0

./configure --user=root --group=nginx --prefix=/usr/local/nginx --with-stream --with-stream_ssl_preread_module --with-stream_ssl_module --with-http_stub_status_module --with-http_ssl_module --with-http_gzip_static_module --with-http_realip_module --with-openssl=/usr/bin/openssl




#创建nginx用户
groupadd nginx 
useradd -g nginx -s /sbin/nologin -M root

https://************:8080/webroot/decision/login?origin=558da30d-4836-4e66-a7bb-7aeac13412d0















"C:\Program Files (x86)\WinSCP\WinSCP.exe" /command "open ftp://erkang.zhou.hengrui.com:Zero2One4?@************:990 -implicit" "cd /hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand" "lcd D:\Projects\GRP_CDSC_PENGR\123\data\raw" "get -neweronly *HR_BLTN_III_NSCLC*" "exit" /log=D:\Projects\GRP_CDSC_PENGR\123\data\raw\log_file_%ymd%.txt 
@echo off

set path="D:\Projects\GRP_CDSC_PENGR\123\data\raw"

if exist "%path%" (
	 dir /b /o:d "%path%\*.zip*" > D:\Projects\GRP_CDSC_PENGR\123\data\raw\output.txt
    )  

for /F "delims=" %%a in (D:\Projects\GRP_CDSC_PENGR\123\data\raw\output.txt) do (set "lastLine=%%a")
echo %lastLine%

rem Get current time with format yyyy/MM/dd/HH/mm/ss
for /f "tokens=2-4 delims=/ " %%a in ('date /t') do (set mydate=%%c/%%a/%%b) 
for /f "tokens=1-3 delims=/:" %%a in ('time /t/24') do (set mytime=%%a/%%b)
set timestamp=%mydate%/%mytime% 

rem Set environment variables  
set MC_PATH=C:\Users\<USER>\Desktop\mc.exe
set SOURCE_FILE=D:\Projects\GRP_CDSC_PENGR\123\data\raw\%lastLine%   
set DEST_PATH=minios3/raw/HR-BLTN-III-NSCLC_sas.zip
set TAGS="key1=RAVE&key2=HR-BLTN-III-NSCLC&key3=%timestamp%"  

echo Upload started at %time% on %date%...  

rem Execute mc to upload file with tags
"%MC_PATH%" cp %SOURCE_FILE% %DEST_PATH% --tags %TAGS%  

if %errorlevel% neq 0 goto error 
goto success

:error  
echo Upload failed, retrying...   
"%MC_PATH%" cp %SOURCE_FILE% %DEST_PATH% --tags %TAGS%
if %errorlevel% neq 0 goto error  

:success
echo Upload completed at %time% on %date%.   

:end



   server {
        listen       443 ssl;
        server_name  finebi-tst.hengrui.com;
        ssl_certificate     /home/<USER>
        8704241__hengrui.com.key;
        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

         ssl_ciphers  HIGH:!aNULL:!MD5;
         ssl_prefer_server_ciphers  on;

        location = / {
              root   html;
              index  index.html index.htm;
           }

  location / {
            proxy_http_version 1.1;
            proxy_set_header Connection "";

            
            proxy_buffering off;
            proxy_next_upstream http_500 http_502 http_503 error timeout invalid_header non_idempotent;

            proxy_redirect off;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # 把 https 的协议告知 Tomcat，否则 Tomcat 可能认为是 http 的请求
            proxy_set_header X-Forwarded-Proto $scheme;
	        
            proxy_connect_timeout    20;
            proxy_read_timeout       1000;
            proxy_send_timeout       300;

            proxy_pass http://finebi-tst.hengrui.com;
        }
     }



localhost;
        server_name finebi-tst.hengrui.com;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Server $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

return 301 https://$host$request_uri;

        location / {
			proxy_pass http://127.0.0.1:8080;
			proxy_connect_timeout 600;
			proxy_read_timeout 600;
        }


mysqldump　-t　susar_auto_mail　-uroot　-pHr@mysql1024　>　alldata.sql



https://clinical.hengruipharma.com



*SHR_1316_III_302_* !(*SHR_1316_III_302_EN*)!

SHR_1316_III_302


2023.10.23
1.susar完成验证环境验证安装、生产环境susar版本升级，退信功能待开发，瓶颈在程序连接公司邮箱服务器账号权限认证失败
2.fineReport完成医学审核列表审核、批注功能模板开发，医学审核列表二级页面挂载待开发，功能与业务需求是否匹配待确认
  SMO进展报告BI视图替代为report实现方案待确认，样例模板待开发
  
  
  
  C:\Windows\System32\cmd.exe
  "/c D:\RaveDataDownload\SHR-1316-III-302-EN.bat"
  
  
  SHR-8068-Ⅱ-201-NSCLC__M


2023.11.1
1.
        SUBJID: 'CN001001',
        RANDNUM: '',
        ARM: '',
        PCREFID: '',
        PCSPEC: 'plasma',
        PCTEST: '给药前0.5h内',
        PCORRES: 'BLQ',
        PCORRESU: 'ng/mL',
        PCLLOQ: '0.02',
        PCULOQ: 5,
        DLUFAC: 1,
        PCCOM: '',
        PCDTC: '',
        PCTIM: ''




2023.11.03
1.退信获取



2023.11.07
1. hrs-3738-i-102_pk_xihua_20230512_prod.csv
2.rave数据集 


hrtau.support@hengrui.com1_
SHR-1210-III-325.bat
2023.11.08
1.脚本优化
2.FZPL-III-302  SHR-A2009-I-101 HRS8807-I-101 HRS2398-I-101 脚本新增/任务创建
  C:\Windows\System32\cmd.exe
  "/c D:\RaveDataDownload\SHR-1210-III-325.bat"
  
  
  
 FZPL-III-303
 SHR-1314-204
 SHR-1316-III-303-EN
 SHR-A1811-II-201
 SHR-A1921-I-101
 SHR3162-III-305
 
 
 
2023.11.16
1.
this.blind_method='',
this.rule='',
 this.doubleCodeForm={
        domains: [{
          value: '',
          replvar: '',
          replace_var_type: '',
          is_range: '',
          upper: '',
          lower: ''
        }]
      },
this.label='',
this.randvar1='',
this.clearvar_s='',
this.mask_range='',
this.mask_method='',
this.maskvar_s='',
this.blindCodeForm= {
        domains: [{
          maskval: '',
          maskop: '',
          maskvar: ''
        }]
      },
      
      
 2023.11.17
 hrtau.support@hengrui.com1_
 
 
 
 
 
 
set timestamp
setlocal enabledelayedexpansion

:: Extract the time substring
set "time=%lastLine%"
set "filename=%lastLine%"
for /f "tokens=6 delims=_" %%a in ("%time%") do (
    set "time=%%a"
)
for /f "tokens=7 delims=_" %%b in ("%filename%") do (
    set "filename=%%b"
)

:: Convert the time format to YYYY/MM/DD hh/mm
set "year=%time:~0,4%"
set "month=%time:~4,2%"
set "day=%time:~6,2%"
set "hour=%filename:~0,2%"
set "minute=%filename:~2,2%"
set "result=%year%/%month%/%day%/%hour%/%minute%"
echo %result%
set "timestamp=%result%"
rem Get current time with format yyyy/MM/dd/HH/mm/ss
for /f "tokens=2-4 delims=/ " %%a in ('date /t') do (set mydate=%%c/%%a/%%b) 
for /f "tokens=1-3 delims=/:" %%a in ('time /t/24') do (set mytime=%%a/%%b)





testt-06Jun2022.zip*202206DDCE4BF611CA46419CF0F68348876096.zip|
针对这个记录，索引的前六位是：202206
索引的第七位:D  索引的第八位:D
 



       /**
         * 将JSON数据格式化并保存到文件中
         * @param jsonData 需要输出的json数
         * @param filePath 输出的文件地址
         * @return
         */
        public static boolean createJsonFile(Object jsonData, String filePath) {
            String content = JSON.toJSONString(jsonData, SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue,
                    SerializerFeature.WriteDateUseDateFormat);
            // 标记文件生成是否成功
            boolean flag = true;
            // 生成json格式文件
            try {
                // 保证创建一个新文件
                File file = new File(filePath);
                if (!file.getParentFile().exists()) { // 如果父目录不存在，创建父目录
                    file.getParentFile().mkdirs();
                }
                if (file.exists()) { // 如果已存在,删除旧文件
                    file.delete();
                }
                file.createNewFile();
                // 将格式化后的字符串写入文件
                Writer write = new OutputStreamWriter(new FileOutputStream(file), "UTF-8");
                write.write(content);
                write.flush();
                write.close();
            } catch (Exception e) {
                flag = false;
                e.printStackTrace();
            }
            return flag;
        }
        
        
        
        
        
        
        domains: [{
          value: '',
          replvar: '',
          replace_var_type: '',
          is_range: '',
          upper: '',
          lower: ''
        }]
        
        
2023.11.20
1.完成json文件上传minio
2.csv文件下载
3.

public static final String UTF8_BOM="\uFEFF";
String rowMessage =header[j];
if(rowMessage.startsWith(UTF8_BOM)) {
    rowMessage=rowMessage.substring(1);
}


2023.11.23
1.
getBlindOperate

LinkedHashMap<String, Object> jsonMap = JSON.parseObject(goodsSpec,LinkedHashMap.class, Feature.OrderedField);
JSONObject goodsSpecs = new JSONObject(true);
goodsSpecs.putAll(jsonMap);












   <el-select v-model="sensitiveForm.domains[index].delval" multiple style="max-width:auto" placeholder="请选择值">
                <el-option
                  v-for="item in sensitiveForm.domains[index].sensitive_values"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
              
              
              
 11月工作总结：
 1.hrtau、rave数据集调整部分项目技术支持导出配套脚本
 2.医学编码报告下载逻辑调整
 3.susar平台优化功能点开发，完成化合物发送容量剩余页面进度展示及上传控制逻辑
 3.外部数据设盲功能前后端开发，完成前端交互页面，后端主要接口开发、本地测试
 
 12月工作计划：
 1.完成外部数据设盲功能第一版开发、联调、部署
 2.完成susar优化功能点的开发



2023.12.5
1.完成线上调试
2.接收域名参数对接
3.申请域名


https://externalBlind.hengrui.com/?projectName=SHR-1210&fileName=test-06.csv&role=EDM&account=<EMAIL>

  debugger
  // 从地址栏获取相关参数
  var paramstr = decodeURI(window.location.search.substring(1))
  const str = paramstr.split('&')
  const [projectName, fileName, role, account] = str.map(item => item.split('=')[1])
  console.log('项目名:', projectName)
  console.log('文件名:', fileName)
  console.log('角色:', role)
  console.log('账户:', account)
  localStorage.setItem('user', account)
  return role







        <dependency>
            <artifactId>sas</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/sas.core.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.apache.axis</groupId>
            <artifactId>axis</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/axis.jar</systemPath>
        </dependency>
        <!--引入第三方wsdl4j jar -->
        <dependency>
            <groupId>wsdl4j</groupId>
            <artifactId>wsdl4j</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/wsdl4j-1.5.1.jar</systemPath>
        </dependency>
    
    
    mount.cifs //**********/susar/Airflow_DAGs /root/airflow/dags -o file_mode=0666,dir_mode=0770,username='zhouh36',password='HR9cf3cbd8',gid=root,uid=root

mount.cifs //************/home/<USER>/home/<USER>/ -o file_mode=0666,dir_mode=0770,username='root',password='Hr@cdtms1123',gid=root,uid=root

C:\Work\Blinded clinical trial\blinded_clinical_trial\front\.env.development



http://localhost:9528/?projectName=SHR-TEST&fileName=TEST_File&role=EDM&account=<EMAIL>


https://cdtms-tst.hengrui.com:82/edmfile.download.do?studycode=EDM_002_ZMM&filename=%E5%8F%97%E8%AF%95%E8%80%85%E6%97%A5%E5%BF%97128764I000.xlsx
https://cdtms-tst.hengrui.com:82/edmfile.download.do?EDM_002_ZMM&filename=%E5%8F%97%E8%AF%95%E8%80%85%E6%97%A5%E5%BF%97128764I000.xlsx


http://localhost:9528/?projectName=HRS8807-I-101&fileName=hrs8807_i_101_hrs8807_plasma_20231201_dft_rev1.csv&role=EDM&account=<EMAIL>&dataType=PK&token=296BD8F48B21428888133D1B69A86E29
https://externalBlind.hengrui.com/?projectName=HRS8807-I-101&fileName=hrs8807_i_101_hrs8807_plasma_20231201_dft_rev1.csv&role=EDM&account=<EMAIL>&dataType=PK&token=296BD8F48B21428888133D1B69A86E29
 
 
 
 
 http://externalBlind-tst.hengrui.com/?projectName=SHR-1210&fileName=test-06.csv&role=EDM&account=<EMAIL>&dataType=PK



C:\Work\Blinded clinical trial\blinded_clinical_trial\front\src\views\table\index.vue



<div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
 {{ emails }}
</div>


专员

一、岗位定义
岗位职责：
主要参与临床数据科学中心业务相关软件功能、系统的项目开发，维护：
1. 参与项目需求讨论,理解业务流程和需求
2. 完成编码开发任务,编写项目业务代码
3. 协助高级工程师完成技术设计和架构
4. 参与项目测试用例设计和测试
5. 编写开发文档,如API文档、部署文档等
6. 不断学习新技术,提高自身技术素质
核心岗位能力：
1. 扎实的Java基础知识
包括Java语言、JVM、集合、并发、IO等知识。
2. Spring Boot框架的基本应用，如:配置文件、日志、AOP等,可以完成简单的RESTful API开发。
3. 熟练使用MySQL、Redis等常用中间件，可以设计简单的业务表结构,实现基本的CRUD操作。
4. 掌握Git、Maven等开发工具的基本使用，可以完成代码管理、依赖管理和简单部署。
5. 具有一定的软件设计和架构知识，可以协助高级工程师完成简单的架构设计。
6. 良好的编程习惯和团队合作能力，遵循项目开发规范,有一定的代码质量意识,能够与团队其他成员进行有效沟通。
7. 勤于学习,有一定的自我提高能力，关注技术动态,善于查阅技术文档,不断学习新知识和技能。
二、资质条件
工作年限： 1年以内java开发经验/硕士与本科应届生



高级专员
一、岗位定义
岗位职责：
主要参与临床数据科学中心业务相关软件功能、系统的项目开发，维护：
1. 参与项目需求分析,制定项目开发规范和方案
2. 负责项目的代码编写、调试、测试和部署等
3. 负责项目的性能优化
4. 负责项目的技术文档编写和维护
5. 参与软件的持续集成与发布管理
核心岗位能力：
1. 扎实的Java基础和理论知识
包括Java语言、JVM、并发编程、异常、集合等知识。
2. Spring、Spring Boot等框架的熟练应用，包括依赖注入、事务管理、RESTful API开发等。
3. 了解各种Java企业级技术和解决方案，如:MySQL、RabbitMQ、Redis、Nginx等。
4. 熟悉各种开发工具和持续集成工具，如:IDEA、Maven、Git、Jenkins等。
5. 高度的代码编写习惯和代码质量意识，包括代码的可读性、健壮性、性能等。
6.了解前端技术，如JavaScript、HTML和CSS，以及前端框架，如React、Angular或Vue。
7. 优秀的沟通能力和团队合作精神，能够确保与产品、测试等相关同事开展有效沟通和协作。
二、资质条件
工作年限： 1-2年内java开发经验/硕士与本科应届生

主管

一、岗位定义
岗位职责：
主要参与临床数据科学中心业务相关软件功能、系统的项目开发，维护：
1. 参与项目需求分析,制定项目开发规范和方案,需求调研收集
2. 负责项目的代码编写、调试、测试和部署等
3. 负责项目的技术架构设计与实现
4. 负责项目的性能优化和安全加固
5. 负责项目的技术文档编写和维护
6. 负责培训和指导其他开发人员
7. 参与软件的持续集成与发布管理
8. 跟踪项目进度,编写项目周报和状态报告
核心岗位能力：
1. 扎实的Java基础和理论知识
包括Java语言、JVM、并发编程、异常、集合等知识。
2. Spring、Spring Boot等框架的熟练应用，包括依赖注入、事务管理、RESTful API开发等。
3. 熟悉各种Java企业级技术和解决方案，如:MySQL、RabbitMQ、Redis、Nginx等。
4. 熟练使用各种开发工具和持续集成工具，如:IDEA、Maven、Git、Jenkins等。
5. 扎实的软件设计和架构设计能力，包括SOA架构、微服务架构等企业架构设计能力。
6. 高度的代码编写习惯和代码质量意识，包括代码的可读性、健壮性、性能等。
7.了解前端技术，如JavaScript、HTML和CSS，以及前端框架，如React、Angular或Vue
8. 优秀的沟通能力和团队合作精神，能够确保与产品、测试等相关同事开展有效沟通和协作。
二、资质条件
工作年限： 2-3年内java开发经验/硕士与本科应届生


高级主管
一、岗位定义
岗位职责：
1. 负责项目关键技术和业务的设计与研发
2. 负责技术体系和技术规范的制定与推广应用
3. 负责新技术的研究与试验,并推动技术创新
4. 负责技术人才的培养和团队建设
5. 参与部门产品规划和技术发展战略制定
6. 主导项目核心技术的优化、升级和迭代
7. 解决项目或产品研发过程中的技术难题
核心岗位能力：
1. 深厚的Java技术积累和丰富的实践经验，在Java技术领域有丰富积累,熟悉spring、springBoot、springCloud等框架源码和底层原理。
2. 精通前端技术，如JavaScript、HTML和CSS，以及前端框架，如React、Angular或Vue
2. 强大的系统架构设计和技术管理能力，能够设计高可用、高并发、分布式等大型系统架构,并组织实施。
3. 扎实的项目管理知识和丰富的实务经验，如了解瀑布模型、敏捷模型等,有1-2年、3个以上项目管理经验
4. 前瞻的技术视野和创新思维，密切跟踪技术发展趋势,有远见卓识,擅长技术创新和探索。
5. 扎实的产品意识和商业思维，理解业务需求和企业发展,善于将技术与业务结合解决业务中的痛点问题。
6. 卓越的管理能力和领导力，能够管理技术团队,进行技术决策,并推动技术变革。
7. 浓厚的学习热情和分享精神，热衷学习新技术,并善于总结和分享工作经验,提升团队整体技术水平。
8. 优秀的沟通能力和全局视角，能够同公司各部门开展高效沟通,把握部门整体技术方向
二、资质条件
工作年限： 3年以上java开发经验/硕士与本科应届生


经理
一、岗位定义
岗位职责：
1. 参与项目的技术选型和架构设计
2. 负责简单模块或子系统的技术架构设计
3. 参与项目编码规范的制定和代码评审
4. 指导开发团队的技术实现和问题解决
5. 负责系统的性能优化和技术改进
6. 编写项目的技术文档,如架构设计文档、API文档等
7. 研究新技术并推动技术创新
核心岗位能力：
1. 扎实的Java技术基础和开发经验，熟悉Java核心技术体系,3-5年项目开发经验。
2. 一定的系统架构设计能力，能够设计模块或子系统层面的架构,理解分布式、高并发、高可用等架构模式。
3. 熟悉常用技术框架和中间件，如Spring、Mybatis、RabbitMQ、Redis等,能够选型和设计架构。
4. 一定的软件设计和编码能力，能够进行模块的OO设计,review代码并提出改进意见。
5. 善于学习新技术和解决问题，关注技术发展动态,能够学习新技术和解决技术难题。
6. 较好的沟通和表达能力，能够同开发团队进行有效沟通,并编写清晰的技术文档。
7. 了解软件开发流程和质量保证，能够制定项目规范和流程,review需求和设计,保证设计的质量。
二、资质条件
工作年限： 3年以上java开发经验/硕士与本科应届生

总监
一、岗位定义
岗位职责：
1. 参与项目的技术选型和架构设计
2. 负责简单模块或子系统的技术架构设计
3. 参与项目编码规范的制定和代码评审
4. 指导开发团队的技术实现和问题解决
5. 负责系统的性能优化和技术改进
6. 编写项目的技术文档,如架构设计文档、API文档等
7. 研究新技术并推动技术创新
核心岗位能力：
1. 扎实的Java技术基础和开发经验，熟悉Java核心技术体系,5年以上Java项目开发经验。
2. 一定的系统架构设计能力，能够设计模块或子系统层面的架构,理解分布式、高并发、高可用等架构模式。
3.  熟练使用常用技术框架和中间件，如Spring、springBoot、springCloud、Mybatis、RabbitMQ、Redis等,能够选型和设计架构。
4. 一定的软件设计和编码能力，能够进行模块的OO设计,review代码并提出改进意见。
5. 善于学习新技术和解决问题，关注技术发展动态,能够学习新技术和解决技术难题。
6. 较强的沟通和表达能力，能和团队及相关部门进行有效协作,并编写清晰的技术文档。
7. 了解软件开发流程和质量保证，能够制定项目规范和流程,review需求和设计,保证设计的质量。
二、资质条件
工作年限： 5年以上java开发经验/硕士与本科应届生

SELECT
            user_id,
            user_name,
            CASE
                type
                WHEN '2' THEN
                    ex_blind_email_record.receive_emails ELSE ''
                END AS receive_emails,
            ex_blind_operation_record.update_time AS qc_time,
            CASE
                type
                WHEN '2' THEN
                    ex_blind_email_record.create_time ELSE ''
                END AS send_time,
            CASE
                type
                WHEN '0' THEN
                    '通知EDM(QC)'
                WHEN '1' THEN
                    '通知EDM'
                WHEN '2' THEN
                    '通知项目组' ELSE ''
                END AS type,
            role,
            CASE
                is_approve
                WHEN '0' THEN
                    '未通过QC'
                WHEN '1' THEN
                    '通过QC' ELSE '未知'
                END AS is_approve
        FROM
            ex_blind_operation_record
                LEFT JOIN ex_blind_email_record ON ex_blind_operation_record.file_id = ex_blind_email_record.file_id
                AND ex_blind_operation_record.ex_data_id = ex_blind_email_record.ex_data_id
        WHERE ex_blind_operation_record.ex_data_id ='2858811393'
        
        
        
        
        
        
        
        
        
        SELECT
            user_id,
            role,
            CASE
                is_approve
                WHEN '0' THEN
                    '未通过QC'
                WHEN '1' THEN
                    '通过QC' ELSE '未知'
                END AS is_approve
        FROM
            ex_blind_operation_record
        WHERE ex_data_id ='2858811393'

2858811393

在项目开发中，我乐于探索新技术、新思路，以不断提升产品的质量和用户体验。
我的务实精神使我能够在面对挑战时保持冷静并找到解决问题的最佳途径。我善于分析现实情况，制定可行的计划，并坚持执行，确保项目进展顺利，达成既定目标。
专注是我工作中的重要品质，我注重细节，追求完美。在编码过程中，我时刻保持高度集中，以确保代码质量和系统稳定性。同时，我也注重团队合作，积极分享经验与知识，促进团队整体水平的提升。
奋进是我的动力源泉，我持续学习、不断进步，不断追求个人和团队的突破。在迅速变化的科技领域，我始终保持求知的心态，积极参与同事间的业务、技术交流，以保持竞争力并创造更大的价值。
总而言之，我将创新、务实、专注、奋进融入工作与生活中，不断提升自我，助力团队达成更辉煌的业绩;

/home/<USER>/ex_blinded_csv/1dfc7f0f79f9027f

/home/<USER>/ex_blinded_csv/1dfc7f0f79f9027f/hrs8807_i_101_hrs8807_plasma_20230207_dft_rev1-hf_coding.csv

51ef7f2e77ff4024

Hr@mysql1024

grant all privileges on *.* to 'root'@'%' identified by 'Hr@mysql1024' with grant option;

flush privileges;

GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' IDENTIFIED BY 'Hr@mysql1024' WITH GRANT OPTION;

select* from ex_blind_operation_record where file_name='hrs8807_i_101_hrs8807_plasma_20231201_dft_rev1';

select  column_name, column_comment from information_schema.columns where table_schema ='external_data'  and table_name = 'ex_blind_operation_record';



select id,file_id,blind_records,data_type,blind_status,create_time  from ex_blind_operation_record where file_name='hrs8807_i_101_hrs8807_plasma_20230207_dft_rev1.csv';


hrs8807_i_101_hrs8807_plasma_20230207_dft_rev1-hf-3有效2csv.csv

mount.cifs //**********/etl/Airflow_DAGs/ /root/airflow/dags -o file_mode=0666,dir_mode=0770,username='sasasdm',password='SHSDYY@600276',gid=root,uid=root
mount -t cifs -o file_mode=0666,dir_mode=0770,username='sasasdm',password='SHSDYY@600276',gid=root,uid=root //**********/etl/Airflow_DAGs /root/airflow/dags

mount -t cifs -o user=sasasdm,password=SHSDYY@600276 -o sec=ntlmssp,dir_mode=0777,file_mode=0770 //**********/etl/Airflow_DAGs/ ./


http://localhost:8087/external_data/file/b5fced3cffff2544\hrs8807_i_101_hrs8807_plasma_20230207_dft_rev1-hf-3位小数_compare.xlsx





import cx_Oracle
# Connection details
username = 'gousy'
password = 'hrgsy@cdtms'
dsn = 'orcl'  # e.g., 'localhost:1521/XE'

# SQL statement
sql = """
SELECT DISTINCT COL_STUDYID
FROM CDTMSEN_VAL.TBL_XSHT
WHERE COL_ZT != '40'
   OR COL_ZT != '50'
   OR COL_ZT != '60'
   OR COL_ZT != '70'
"""

# Establish connection
connection = cx_Oracle.connect(username, password, dsn)

# Create a cursor
cursor = connection.cursor()

# Execute the SQL statement
cursor.execute(sql)

# Fetch all rows
rows = cursor.fetchall()

# Print the results
for row in rows:
    print(row[0])

# Close the cursor and connection
cursor.close()
connection.close()

 
  and COL_EMAIL='<EMAIL>'
  
  
  DROP DATABASE external_data;
  
  
  /home/<USER>/external_data.sql
  
  pip3.8 install -i https://nexus.hengrui.com/repository/pypi-proxy/simple/ cx_Oracle
  
  
  
  
12月工作总结：
 1.完成外部数据设盲功能第一版开发、与cdtms联调、部署，测试环境上线
 2.testfile后端接口开发联调
 3.rave、医学编码部分新增项目数据下载配置
24年1月工作计划：
 1.外部数据设盲演示后优化项开发
 2.承接DM平台项目开发需求
 3.完成airflow新项目数据下载流程整合并上线
 
 
grant all on *.* to root@'%' identified by 'Hr@mysql1024' with grant option;
grant all privileges on *.* to 'root'@'%' identified by 'Hr@mysql1024' with grant option;
 
 
 
 压缩 加密
 
<div class="language-">package base.web.tools;import java.io.FileNotFoundException;import org.apache.commons.lang.StringUtils;import net.lingala.zip4j.core.ZipFile;import net.lingala.zip4j.exception.ZipException;import net.lingala.zip4j.model.ZipParameters;import net.lingala.zip4j.util.Zip4jConstants;/*** &lt;ul&gt;* &lt;li&gt;支持多层次目录ZIP压缩的工具&lt;/li&gt;* &lt;li&gt;用法 :&lt;/li&gt;* &lt;li&gt;1.普通压缩, Zipper.packageFolder(folder, target);&lt;/li&gt;* &lt;li&gt;2.加密码的压缩 , Zipper.packageFolderWithPassword(folder, target, password);&lt;/li&gt;* &lt;li&gt;参数说明：&lt;/li&gt;* &lt;ol&gt;* &lt;li&gt;folder 待压缩的目录&lt;/li&gt;* &lt;li&gt;target 目标文件路径&lt;/li&gt;* &lt;/ol&gt;* &lt;/ul&gt;*/public class Zipper { /**  * 压缩文件夹  * @expalin 如果已经存在相同的压缩包，则会覆盖之前的压缩包  * @param folder 要打包的文件夹的磁盘路径  * 比如，D:\\aa\\bb  * @param target 存放打包后的压缩包的磁盘路径  * 构成：路径+压缩包名称.后缀名  * 比如，D:\\aa\\bb.zip  * @throws ZipException  */ public static void packageFolder(String folder, String target) throws ZipException {   

packageFolderWithPassword(folder, target, null); }  
public static void packageFolderWithPassword(String folder, String target, String password) throws ZipException{
   ZipFile zip = new ZipFile(target);  
    ZipParameters parameters = new ZipParameters();
       parameters.setCompressionMethod(Zip4jConstants.COMP_DEFLATE);
           parameters.setCompressionLevel(Zip4jConstants.DEFLATE_LEVEL_NORMAL);
               // 加密   if(StringUtils.isNotBlank(password)){     parameters.setEncryptFiles(true);  
                  parameters.setEncryptionMethod(Zip4jConstants.ENC_METHOD_STANDARD);
                       parameters.setPassword(password);
                          }      
                          zip.addFolder(folder, parameters);
                           } 
                           public static void main(String[] args) throws FileNotFoundException {
                              try {     packageFolder("D:\\aa\\bb", "D:\\aa\\bb.zip"); 
                              } catch (ZipException e) {     e.printStackTrace();   } }}
                              
                              
                              
                              
                              
                              
                              
                              
                              
                              
                              
                              
1.预警处置辅助任务派发：通过公司自研前端开发平台根据交互设计图搭建前端页
面，设计并采用SSM框架开发该模块后端接口，实现不同部门间不同人员的任务流
转、审批、办理、归档等功能，实现
线上任务处置流程的高效流转
2.主题库：通过梳理业务数据表之间的业务联系，基于Hadoop的上层应用平台工
具进行相应的数据治理工作，负责原始数据的接入、清洗，并进行数据开发工作，
提取企业、单证、查验、货物的流转关系，去重合并补充相关关键字段信息，形
成主题库，将治理后的约1亿条数据通过数据库摆渡工具同步到相对应的pqsql业
务库中，确保的业务功能的数据质量
3.财务数据统计分析模块：通过配置导入工具的数据模板实现解析，并结构化客户
原始业务数据，形成依据时间、业务类型的基本分类，基于该分类，根据年、月、
部门、预算、支出等维度开发前后端业务，统计分析单位的财务收支趋势，为客户
后期的资金使用提供决策依据


企业档案模块的前后端开发，以主题库固化同步的数据为支撑，实现对企业详情
的展示、编辑、删除和新增及相关关联信息的管理




1.重点场所管理功能：前端交互原型图设计、信息的新增、编辑、删除、详情的功
能实现
2.特殊监管物处置功能：监管物本体信息登记、流转信息登记、销毁、审批、状态
变更、操作历史记录的后端开发
3.重点人员统计：通过公司自研可视化工具，提取业务表中重点监管人的数据，按
时间、地域、来源等多个维度进行展示，对关键字段信息做过滤筛选，剔除脏数据
技术应用：SSM框架+Jwt+Mybatis+Pgsql+Redis
开发工具：IDEA、VSGode、Git、Navicat Premium

2023.12.29
1.文件预览实现
2.开发文件获取及下载存储逻辑


C:\MyFile\external_data\compare_file
C:\MyFile\external_data\edm_uap\dtattach\69aebdf7e7ec93d0\hrs8807_i_101_hrs8807_plasma_20231201_dft_rev1_coding.csv

mc cp /tmp/hsperfdata_root/hrs8807_i_101_hrs8807_plasma_20231201_dft_rev1_compare.xlsx minios3/externalfile/HRS8807-I-101/csv_blind/ 


hrs8807_i_101_hrs8807_plasma_20231201_dft_rev1_compare.xlsx


nohup java -jar xxl-job-admin-2.4.0-SNAPSHOT.jar >log.txt &

redis-server redis.conf

http://***********:8088/xxl-job-admin/jobinfo


cat /proc/cpuinfo| grep "cpu cores"| uniq
parsing_processes
max_threads
max_active_tasks_per_dag

parallelism
dagrun_timeout


default_timezone
Asia/Shanghai


ps -ef|egrep 'airflow'|grep -v grep|awk '{print $2}'|xargs kill -9


https://downloadarchive.documentfoundation.org/libreoffice/old/*******/rpm/x86_64/LibreOffice_*******_Linux_x86-64_rpm.tar.gz


netstat -tunlp | grep 8012



[root@curry program]# /opt/libreoffice7.3/program/soffice --headless --accept="socket,host=127.0.0.1,port=2001;urp;" --nofirststartwizard &


ld -libXinerama.so.1 --verbose

gcc -libXinerama.so.1 --verbose

locate libhdf5.so





[root@curry program]# yum install -y libxinerama1

//usr/lib/libibXinerama.so.1.so
//usr/lib/libibXinerama.so.1.a
./usr/lib/fin.so.1



cd /opt/libreoffice7.1/program
 ./soffice --version
 
 
 ldconfig -p |grep -i libXinerama.so.1
 
 
 rpm -qa | grep libXinerama*
 
 rpm -e  -- libXinerama-1.1.4-1.el8.i686
 
 
 yum install -y libXinerama
 
 
 https://externalBlind.hengrui.com/external_data/file/
 
 https://externalBlind.hengrui.com/external_data/file/d18ed773bffcffcc/hrs8807_i_101_hrs8807_plasma_20230207_dft_rev1_compare.xlsx
 
 
 https://externalblindcheck.hengrui.com/onlinePreview?url
 
 
 https://externalblindcheck.hengrui.com/demo/hrs8807_i_101_hrs8807_plasma_20231201_dft_rev1_compare.xlsx
 https://externalblindcheck.hengrui.com/onlinePreview?url=
 
 https://externalblindcheck.hengrui.com/onlinePreview?url=
server.servlet.context-path = /preview
base.url = https://externalblindcheck.hengrui.com


nohup java -jar blind_back-0.0.1-SNAPSHOT.jar >log.txt &






<el-dialog
      :close-on-click-modal="false"
      :append-to-body="true"
      center
      :modal="false"
      :visible="addDialog"
      :destroy-on-close="true"
      width="450px"
      height="600px"
      @close="closeDialog"
    >
      <el-form ref="form" :model="modelForm" label-width="100px" size="mini" label-position="top">
        <el-row :gutter="10" type="flex" justify="center" style="margin-left: -5px;">
          <el-col :span="22">
            <el-upload
              ref="upload"
              :before-upload="beforeFileUpload"
              :file-list="fileList"
              action=""
              :on-remove="handleRemove"
              :disabled="upload.isUploading"
              :on-success="handleFileSuccess"
              :on-error="handleFileError"
              :auto-upload="false"
              :multiple="true"
              :on-change="handleChange"
              :on-exceed="handleExceed"
              :limit="10"
              limit-size="10MB"
              :data="{folderName:modelForm.compoundFolder}"
              drag
            >

              <i class="el-icon-upload" style="color: #167ad6" />
              <el-tooltip class="item" effect="dark" content="只支持pdf文件,单个文件不超过10MB，单次上传不超过10个文件" placement="top">

                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>

              </el-tooltip>
              <div slot="tip" class="el-upload__tip" style="margin-left: 5%;font-family: monospace;">只支持pdf文件,单个文件不超过10MB，单次上传不超过10个文件</div>
            </el-upload>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-button type="primary" style="margin-top: 10px;" @click="onSubmit">确认</el-button>
          <el-button type="warn" style="margin-top: 10px;" @click="closeDialog">取消</el-button>
        </el-row>
      </el-form>
    </el-dialog>


BlindConstant.FILE_URL_PREFIX

http://127.0.0.1:8012/onlinePreview?url=aHR0cHM6Ly9leHRlcm5hbEJsaW5kLmhlbmdydWkuY29tL2V4dGVybmFsX2RhdGEvZmlsZS83ZjhlZjllMzc3ZDNlNDU0L0NEVE1T5omL5YaM5LyY5YyW5rWL6K%2BV6aG555uuX2hyczg4MDdfcGxhc21hXzIwMjMxMjA3X2RmdF9yZXYxLeebsuW6lS0xW0JANGNjOGVkZDA%3D

http://127.0.0.1:8012/onlinePreview?url=http://localhost:8087/external_data/file/19dddffdd71b9544/hrs8807_i_101_hrs8807_plasma_20230207_dft_rev1-hf-3位小数_compare.xlsx


https://externalblindcheck.hengrui.com/onlinePreview?url=aHR0cHM6Ly9leHRlcm5hbEJsaW5kLmhlbmdydWkuY29tL2V4dGVybmFsX2RhdGEvZmlsZS9mN2JlZGRmYzc3ZWIyZjk3L0NEVE1T5omL5YaM5LyY5YyW5rWL6K%2BV6aG555uuX2hyczg4MDdfcGxhc21hXzIwMjMxMjA3X2RmdF9yZXYxLeebsuW6lS0xX2NvbXBhcmUueGxzeA%3D%3D


https://externalblindcheck.hengrui.com/onlinePreview?url=aHR0cHM6Ly9leHRlcm5hbEJsaW5kLmhlbmdydWkuY29tL2V4dGVybmFsX2RhdGEvZmlsZS9IUlM4ODA3LUktMTAxLWMzOWRkZGJkMWNlZjk4MzIvaHJzODgwN19pXzEwMV9ocnM4ODA3X3BsYXNtYV8yMDIzMDIwN19kZnRfcmV2MV9jb21wYXJlLnhsc3g%3D

F60CB32D94E743E3B973A32E2FE240ED
F60CB32D94E743E3B973A32E2FE240ED
F60CB32D94E743E3B973A32E2FE240ED


mounted() {        //写在mounted或者activated生命周期内即可
    window.onbeforeunload = e => {      //刷新时弹出提示
        return ''
    };
},



2024.01.09
1.clickHouse 数据连接 配置

<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
    <version>3.5.2</version>
</dependency>


**********

-1 草稿版
0 审核版
1 定稿版



        <!-- https://mvnrepository.com/artifact/org.mybatis.spring.boot/mybatis-spring-boot-starter -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.1.1</version>
        </dependency>
 
        <!-- https://mvnrepository.com/artifact/com.baomidou/mybatis-plus-boot-starter -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.3.2</version>
        </dependency>
        
        
        
        selectedMember
        
        
         proxy: { 
      '/production': {
        // 管理
        target: 'http://www.1111.com',
        pathRewrite: {
          '^/production': ''
        }
      },
      '/user': {
        // 用户登录
        target: 'http://www.2222.com',
        pathRewrite: {
          '^/user': ''
        }
      },
      
      
      (this.$route.query.project,this.$route.query.filename);
      
    [  
      {
                "LAB临床意义": "异常有临床意义",
                "LAB检查日期": "2023-10-04",
                "LAB正常值范围": "2~7.5",
                "不良事件/既往病史名称(检查类)": "中性粒细胞计数降低",
                "中心": "CN001",
                "修改日期": ".",
                "关联检查项": "中性粒细胞绝对值",
                "受试者代码": "CN001001",
                "受试者状态": "已入组",
                "序号(检查类)": 63,
                "开始日期(检查类)": "2023-10-04",
                "检查结果": "1.52(10^9/L)",
                "知情同意书签字日期": "2023-06-13",
                "编码描述-CN": "中性粒细胞绝对值",
                "表名称": "血常规",
                "表名称(检查类)": "不良事件",
                "访视": "C4D1",
                "转归/是否持续(检查类)": "恢复/解决",
                "转归日期(检查类)": "2023-10-11",
                "首选语(检查类)": "中性粒细胞计数降低"
            },
            {
                "LAB临床意义": "正常",
                "LAB检查日期": "2023-10-11",
                "LAB正常值范围": "2~7.5",
                "不良事件/既往病史名称(检查类)": "中性粒细胞计数降低",
                "中心": "CN001",
                "修改日期": ".",
                "关联检查项": "中性粒细胞绝对值",
                "受试者代码": "CN001001",
                "受试者状态": "已入组",
                "序号(检查类)": 63,
                "开始日期(检查类)": "2023-10-04",
                "检查结果": "2.5(10^9/L)",
                "知情同意书签字日期": "2023-06-13",
                "编码描述-CN": "中性粒细胞绝对值",
                "表名称": "血常规",
                "表名称(检查类)": "不良事件",
                "访视": "计划外访视_14",
                "转归/是否持续(检查类)": "恢复/解决",
                "转归日期(检查类)": "2023-10-11",
                "首选语(检查类)": "中性粒细胞计数降低"
            }
        ]
        
        
        Cause: ru.yandex.clickhouse.except.ClickHouseException: ClickHouse exception, code: 159
        
        
        #main > div:nth-child(1) > canvas
        
        C:\MyFile\external_data\compare_file\HRS8807-I-101-438f1fbeafdfd7d5\hrs8807_i_101_hrs8807_plasma_20230207_dft_rev1_compare.xlsx
        
        
        
        
        http://127.0.0.1:8012/onlinePreview?url=aHR0cDovL2xvY2FsaG9zdDo4MDg3L2V4dGVybmFsX2RhdGEvZmlsZS9IUlM4ODA3LUktMTAxLTQzOGYxZmJlYWZkZmQ3ZDUvaHJzODgwN19pXzEwMV9ocnM4ODA3X3BsYXNtYV8yMDIzMDIwN19kZnRfcmV2MV9jb21wYXJlLnhsc3g%3D
        http://127.0.0.1:8012/onlinePreview?url=aHR0cDovL2xvY2FsaG9zdDo4MDg3L2V4dGVybmFsX2RhdGEvZmlsZS9IUlM4ODA3LUktMTAxLTUxOGQ1YmZkZmJlZWU3ZTgvaHJzODgwN19pXzEwMV9ocnM4ODA3X3BsYXNtYV8yMDIzMDIwN19kZnRfcmV2MV9jb21wYXJlLnhsc3g%3D
      root:Hr_mysql1024@localhost:3306/airflow?charset=utf8
        
        C:\MyFile\external_data\compare_file
**********

**********
     edmInfo = edmcdtmsInfoMapper.getUserInfoByMail(userName);
        if (!ObjectUtils.isEmpty(edmInfo)) {
            phoneNumber = edmInfo.get("COL_SJH");
            signName = edmInfo.get("COL_RY");
        }
        
        
        
        
        
        
         WITH
            minio_tbl AS
                (
                    SELECT
                        *
                    FROM s3(#{filePath}, 'minioadmin', 'minioadmin','CSVWithNames')
            ),
        mysql_tbl AS
        (
        SELECT *
        FROM mysql('localhost:3306', 'dm_platform', 'dm_lab_ae_comments', 'root', '123456')
        ),
        result AS (
           select  minio_tbl.*,
                    mysql_tbl.lab_comment,
                    mysql_tbl.comment_level
           from minio_tbl left join mysql_tbl on minio_tbl.`记录ID`=mysql_tbl.lab_data_id
        )
        SELECT *
        FROM result
        <if test="centerCode != null and centerCode != ''">
            where equals(`中心`, #{centerCode}) and `质疑` is not  null and `质疑`!=''
        </if>
        
        
        
        
        
         (#{item.id},#{item.labDataId},#{item.fileName},#{item.operateUser},current_timestamp,current_timestamp,#{item.comment},{item.commentLevel},#{item.levelContent})
         
         
         
         ('Ⅳ','IV').replace('Ⅲ','III').replace('Ⅱ','II').replace('Ⅰ','I')
         
        concat(toString(`质疑`), toString(`中心`), toString(`受试者代码`), toString(`受试者状态`),toString(`知情同意书签字日期`),toString(`关联检查项`),toString( `访视`),toString(`表名称`),toString(`检查项`),toString(`编码描述-CN`),toString(`LAB检查日期`),toString(`检查结果`),toString(`LAB正常值范围`),toString(`LAB临床意义`),toString(`LAB临床意义Comment`),toString( `修改日期`),toString(`表名称(检查类)`),toString(`不良事件/既往病史名称(检查类)`),toString(`首选语(检查类)`),toString(`序号(检查类)`),toString( `开始日期(检查类)`),toString(`转归日期(检查类)`),toString( `转归/是否持续(检查类)`),toString(`表名称(诊断类)`),toString(`不良事件/既往病史名称(诊断类)`),toString( `首选语(诊断类)`),toString(`序号(诊断类)`),toString(`开始日期(诊断类)`),toString(`转归日期(诊断类)`),toString(`转归/是否持续(诊断类)`),toString(`质疑位置`),toString(`记录ID`))  
         `质疑`, `中心`, `受试者代码`, `受试者状态`, `知情同意书签字日期`, `关联检查项`, `访视`, `表名称`, `检查项`, `编码描述-CN`, `LAB检查日期`, `检查结果`, `LAB正常值范围`, `LAB临床意义`, `LAB临床意义Comment`, `修改日期`, `表名称(检查类)`, `不良事件/既往病史名称(检查类)`, `首选语(检查类)`, `序号(检查类)`, `开始日期(检查类)`, `转归日期(检查类)`, `转归/是否持续(检查类)`, `表名称(诊断类)`, `不良事件/既往病史名称(诊断类)`, `首选语(诊断类)`, `序号(诊断类)`, `开始日期(诊断类)`, `转归日期(诊断类)`, `转归/是否持续(诊断类)`, `质疑位置`, `记录ID`
         
         
         
         
         for /F "delims=" %%a in (D:\Projects\GRP_CDSC_PENGR\SHR0302-305\data\raw\output.txt) do (set "lastLine=%%a")
echo %lastLine%
set timestamp
setlocal enabledelayedexpansion

:: Extract the time substring
set "time=%lastLine%"
set "filename=%lastLine%"
for /f "tokens=5 delims=_" %%a in ("%time%") do (
    set "time=%%a"
)
for /f "tokens=6 delims=_" %%b in ("%filename%") do (
    set "filename=%%b"
)
9598301043723586595


          WITH
            minio_tbl AS
                (
                    SELECT
                        toString(sipHash64(*)) AS aeid,
                        *
                    FROM s3(#{filePath}, 'minioadmin', 'minioadmin','CSVWithNames')
                ),
            mysql_lab_comments AS
                (
                    SELECT *
                    FROM mysql('***********:3306','dm_platform', 'dm_lab_ae_comments', 'susaruser', 'Hr@db0316')
                ),
            mysql_lab_review AS
                (
                    SELECT *
                    FROM mysql('***********:3306','dm_platform', 'dm_lab_ae_review', 'susaruser', 'Hr@db0316')
                ),
            temp1 AS (
                select  minio_tbl.*,
                        mysql_lab_comments.lab_comment,
                        mysql_lab_comments.comment_level
                from minio_tbl left join mysql_lab_comments on GLOBAL minio_tbl.aeid=mysql_lab_comments.lab_data_id
            )
        SELECT temp1.*,
               mysql_lab_review.is_viewed
        FROM temp1
                 left join mysql_lab_review on GLOBAL temp1.aeid=mysql_lab_review.lab_data_id
                 
                 
                 
                 
                 
                 
                 
                 
     WITH
        minio_tbl AS
        (
        SELECT
        toString(sipHash64(*)) AS ae_id,
        *
        FROM s3(#{filePath}, 'minioadmin', 'minioadmin','CSVWithNames')
        ),
        mysql_lab_comments AS
        (
        SELECT *
        FROM mysql('***********:3306','dm_platform', 'dm_lab_ae_comments', 'susaruser', 'Hr@db0316')
        ),
        mysql_lab_review AS
        (
        SELECT *
        FROM mysql('***********:3306','dm_platform', 'dm_lab_ae_review', 'susaruser', 'Hr@db0316')
        ),
        temp1 AS (
        select  minio_tbl.*,
        mysql_lab_comments.lab_comment,
        mysql_lab_comments.comment_level
        from minio_tbl left join mysql_lab_comments on minio_tbl.`记录ID`=mysql_lab_comments.lab_data_id
        )
        SELECT temp1.*,
        mysql_lab_review.is_viewed
        FROM temp1
        left join mysql_lab_review on temp1.`中心`=mysql_lab_review.lab_data_id
        <if test="centerCode != null and centerCode != ''">
            where equals(`中心`, #{centerCode}) and `质疑` is not  null and `质疑`!=''
        </if>
                 
                 
       WITH
            minio_tbl AS
                (
                    SELECT
                        toString(sipHash64(*)) AS lab_data_id,
                        *
                    FROM s3('http://***********:9000/dmreview/HR070803-301_LAB_AE.csv', 'minioadmin', 'minioadmin','CSVWithNames')
                    LIMIT 5
                ),
            mysql_lab_review AS
                (
                    SELECT *
                    FROM mysql('***********:3306','dm_platform', 'dm_lab_ae_review', 'susaruser', 'Hr@db0316')
                ),
        temp1 AS(
                    SELECT  minio_tbl.*
                    FROM minio_tbl
                ),
        temp2 AS(
        SELECT temp1.*, mysql_lab_review.is_viewed FROM temp1,mysql_lab_review WHERE temp1.lab_data_id = mysql_lab_review.lab_data_id AND mysql_lab_review.is_viewed = '1'
        ),
        temp3 AS(
            SELECT temp1.*, '0' AS is_viewed FROM temp1,mysql_lab_review WHERE temp1.lab_data_id != mysql_lab_review.lab_data_id
        )
        SELECT * FROM temp2
        UNION
        SELECT * FROM temp3
        
        
        
        
        jdbc  Error querying database  Cause: ru.yandex.clickhouse.except.ClickHouseUnknownException: ClickHouse exception, code: 1002 Software caused connection abort: recv failed
        
        
        
        '﻿\"质疑\"' ? '质疑' : (value === '\u001D' ? '' : value),
        
        
        '\u001D' ? '' : value,
        
        SELECT temp1.*,mysql_lab_review.is_viewed FROM temp1 left join mysql_lab_review on
        temp1.lab_data_id = mysql_lab_review.lab_data_id
        
        <EMAIL>
        
        
        
        
        -- 重复子查询
select t1.*, t2.*
from (
     select *
     from test1_all
     where uid global in (
         select distinct uid
         from test_all
         where toDate(totalDate) = yesterday()
     )
) as t1 global
left join(
    select distinct uid
    from test2_all
    where uid global not in (
        select uid
        from test_all
        where toDate(totalDate) = yesterday()
    )
) as t2 using uid;






with minio_tbl as (SELECT
                      *,
                      toString(sipHash64(*)) AS lab_data_id
                    FROM s3(#{projectName}, 'minioadmin', 'minioadmin','CSVWithNames'))







	with minio_tbl as (SELECT
                      *,
                      toString(sipHash64(*)) AS lab_data_id
                    FROM s3(#{projectName}, 'minioadmin', 'minioadmin','CSVWithNames'))
    SELECT temp1.*,mysql_lab_review.is_viewed,mysql_lab_comments.lab_comment FROM temp1,mysql_lab_review
                (
                    SELECT
                      *
                    FROM minio_tbl
            ) as temp1 global
           left join
           (
            SELECT *
            FROM mysql('***********:3306','dm_platform', 'dm_lab_ae_review', 'susaruser', 'Hr@db0316')
            ) as  mysql_lab_review
            global
            left join
            (
				SELECT *
				FROM mysql('***********:3306','dm_platform', 'dm_lab_ae_comments', 'susaruser', 'Hr@db0316')
            ) as mysql_lab_comments
             using lab_data_id
        
        
        
        
        
        
        WITH
        minio_tbl AS
        (
        SELECT
        toString(sipHash64(*)) AS lab_data_id,
        *
        FROM s3(#{filePath}, 'minioadmin', 'minioadmin','CSVWithNames')
        ),
        mysql_lab_comments AS
        (
        SELECT *
        FROM mysql('***********:3306','dm_platform', 'dm_lab_ae_comments', 'susaruser', 'Hr@db0316')
        ),
        mysql_lab_review AS
        (
        SELECT *
        FROM mysql('***********:3306','dm_platform', 'dm_lab_ae_review', 'susaruser', 'Hr@db0316')
        ),
        temp1 AS (
        select  minio_tbl.*,
        mysql_lab_comments.lab_comment,
        mysql_lab_comments.comment_level
        from minio_tbl left join mysql_lab_comments on minio_tbl.`记录ID`=mysql_lab_comments.lab_data_id
        )
        SELECT temp1.*,
        mysql_lab_review.is_viewed
        FROM temp1
        left join mysql_lab_review on temp1.`中心`=mysql_lab_review.lab_data_id
        <if test="centerCode != null and centerCode != ''">
            where equals(`中心`, #{centerCode}) and `质疑` is not  null and `质疑`!=''
        </if>
        
        
        
        
        
        with minio_tbl AS (SELECT
                               *,
                               toString(sipHash64(*)) AS lab_data_id
                           FROM s3(#{projectName}, 'minioadmin', 'minioadmin','CSVWithNames'))
        SELECT
            temp1.*,
            mysql_lab_review.is_viewed
        FROM
                ( SELECT * FROM minio_tbl ) AS temp1
            GLOBAL LEFT JOIN ( SELECT * FROM mysql ( '***********:3306', 'dm_platform', 'dm_lab_ae_review', 'susaruser', 'Hr@db0316' ) ) AS mysql_lab_review USING lab_data_id
        
        
        
        
        WITH
        minio_tbl AS
        (
        SELECT
        toString(sipHash64(*)) AS lab_data_id,
        *
        FROM s3(#{filePath}, 'minioadmin', 'minioadmin','CSVWithNames')
        ),
        mysql_lab_comments AS
        (
        SELECT *
        FROM mysql('***********:3306','dm_platform', 'dm_lab_ae_comments', 'susaruser', 'Hr@db0316')
        ),
        mysql_lab_review AS
        (
        SELECT *
        FROM mysql('***********:3306','dm_platform', 'dm_lab_ae_review', 'susaruser', 'Hr@db0316')
        ),
        temp1 AS (
        select  minio_tbl.*,
        mysql_lab_comments.lab_comment,
        mysql_lab_comments.comment_level
        from minio_tbl left join mysql_lab_comments on minio_tbl.`记录ID`=mysql_lab_comments.lab_data_id
        )
        SELECT temp1.*,
        mysql_lab_review.is_viewed
        FROM temp1
        left join mysql_lab_review on temp1.`中心`=mysql_lab_review.lab_data_id
        <if test="centerCode != null and centerCode != ''">
            where equals(`中心`, #{centerCode}) and `质疑` is not  null and `质疑`!=''
        </if>
        
        
        

lab_data_id,
file_name,
operate_user,
lab_comment,
comment_level,
level_content



        
        
        
        
        
        
        DROP TABLE IF EXISTS `dm_operation_records`;
CREATE TABLE `dm_operation_records`  (
                                       `id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                       `lab_data_id` varchar(255) NOT NULL,
                                       `operate_user` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                       `operate_type`int(0) NULL DEFAULT NULL,
                                       `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
        
        
        
         param.fileName = sessionStorage.getItem('fileName')
      param.centerCode = ''
      param.sortOrder = ''
      param.sortColumn = ''
      param.type = ''
        
        
        
        
        WITH minio_tbl AS ( SELECT *, toString ( sipHash64 ( * ) ) AS lab_data_id FROM s3 ( 'http://***********:9000/dmreview/HR070803-301_LAB_AE.csv', 'minioadmin', 'minioadmin', 'CSVWithNames' ) ),
temp2 AS (
SELECT
        temp1.*,
        mysql_lab_review.is_viewed 
FROM
        ( SELECT * FROM minio_tbl WHERE lab_data_id ='2382922906268174546' ) AS temp1
        GLOBAL LEFT JOIN ( SELECT * FROM mysql ( '***********:3306', 'dm_platform', 'dm_lab_ae_review', 'susaruser', 'Hr@db0316' ) ) AS mysql_lab_review USING lab_data_id 
        ) SELECT
        temp2.*,
        mysql_lab_comments.lab_comment 
FROM
        temp2
        GLOBAL LEFT JOIN ( SELECT * FROM mysql ( '***********:3306', 'dm_platform', 'dm_lab_ae_comments', 'susaruser', 'Hr@db0316' ) ) AS mysql_lab_comments USING lab_data_id
        
        fileName
        
        
        
        
        
          with minio_tbl AS (SELECT
        *,
        toString(sipHash64(*)) AS lab_data_id
        FROM s3(#{entity.fileName}, 'minioadmin', 'minioadmin','CSVWithNames')),
        temp2 AS(
        SELECT
        temp1.*,
        mysql_lab_review.is_viewed
        FROM
        ( SELECT * FROM minio_tbl
        ) AS temp1
        GLOBAL LEFT JOIN ( SELECT * FROM mysql ( '***********:3306', 'dm_platform', 'dm_lab_ae_review', 'susaruser', 'Hr@db0316' ) ) AS mysql_lab_review USING lab_data_id
         )
        SELECT temp2.*,mysql_lab_comments.lab_comment FROM temp2  GLOBAL LEFT JOIN
        (
        SELECT *
        FROM mysql('***********:3306','dm_platform', 'dm_lab_ae_comments', 'susaruser', 'Hr@db0316')
        ) as mysql_lab_comments
        USING lab_data_id
        
        
             <if test="entity.centerCode != null and entity.centerCode != '' and entity.type==2">
            where  `中心`= #{entity.centerCode} and `质疑` is not  null and `质疑`!='' and notEquals(lab_comment,'')
        </if>
        <if test="entity.centerCode == ''and entity.type==2">
            where notEquals(lab_comment,'')
        </if>
        
        
        commentsClick
        
        
       审核 批注 导出 
       批注 导出
       列表间距调整
       
       
       
       
       1月工作总结：
       1.完成外部数据编盲整体页面功能开发
       2.开发DM平台审核、批注页面及后端接口，定位并修复数据库连接问题zhou
       3.配置部分Rave项目数据集下载脚本
       4.更新Susar启动包跨年数据查询异常问题
       
       2月工作计划：
       1.开发外部数据编盲调整优化项
       2.开发DM平台其他功能模块
       
       c31 -> 
       
       
       NOT LIKE '%\\u001D%'
                    u001D
       and `质疑` NOT LIKE '%\\u001D%'
       toNullable(质疑) -> 
       
       toString(sipHash64(质疑)) -> 15150039259969081993
       
       15150039259969081993
       
       2024.01.25
       1.完成外部数据测试环境部署，网盘挂载:
       mount.cifs -o file_mode=0666,dir_mode=0770,username='zhouh36',password='HR9cf3cbd8',gid=root,uid=root //SHNVWSASECT01/EDMtst/EDM/csv_raw /home/<USER>/ex_original_csv
       2.完成DM平台批量一键审核
       
       mount.cifs -o file_mode=0666,dir_mode=0770,username='zhouh36',password='HR9cf3cbd8',gid=root,uid=root //SHNVWSASECT01/EDMtst/EDM/json_blind /home/<USER>/ex_json_file
       
       
       
       
      mount.cifs -o file_mode=0666,dir_mode=0770,username='zhouh36',password='HR9cf3cbd8',gid=root,uid=root  //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log
       
       
       
       
       
       
      with minio_tbl AS (SELECT
        *,
        toString(sipHash64(*)) AS lab_data_id
        FROM s3(#{entity.fileName}, 'minioadmin', 'minioadmin','CSVWithNames'))
        SELECT   distinct `${columnName}` FROM minio_tbl
        <if test="entity.centerCode != null and entity.centerCode != '' and entity.type==2">
            where  `中心`= #{entity.centerCode} and `质疑` is not  null and `质疑`!='' and toString(sipHash64(`质疑`))!='15150039259969081993' and notEquals(lab_comment,'')
        </if>
        <if test="entity.centerCode == ''and entity.type==2">
            where notEquals(lab_comment,'')
        </if>
        <if test="entity.centerCode != null and entity.centerCode != '' and entity.labAeId != null and entity.labAeId != ''and entity.type==2">
            where  `中心`= #{entity.centerCode} and `质疑` is not  null and `质疑`!='' and toString(sipHash64(`质疑`))!='15150039259969081993' and notEquals(lab_comment,'') and equals(lab_data_id,#{entity.labAeId})
        </if>
        <if test="entity.centerCode == '' and entity.labAeId != null and entity.labAeId != ''and entity.type==2">
            where   equals(lab_data_id,#{entity.labAeId})
        </if>
       
       
       {"fileName":"CDTMS手册优化测试项目_hrs8807_plasma_20231207_dft_rev1-盲底-2.csv","dataType":"PKPD","role":"EDM","del_si":"0","yn_blindcode":"0","sensitiveForm":[{"delvar":"","delop":"","delval":[]}],"blind_method":"3","rule":"","rand":"","corresponding":"","randvalues":[],"clearvar_s":[],"blindCodeForm":[{"maskval":[],"maskop":"","maskvar":""}],"mask_range":"1","mask_method":"1","maskvar_s":["ARM"],"doubleCodeForm":[{"value":"","replvar":"","replace_var_type":"","is_range":"","upper":"","lower":""}]}
       
       zhou
       satableau
       Hr@tableau0401
       
       
       
       
       
       
 UPDATE `file_upload_record` SET `file_path` = replace (`file_path`,'HR9cf3cbd8','Zh123456') ,folder_path= replace (`folder_path`,'HR9cf3cbd8','Zh123456') WHERE `file_path`  LIKE '%HR9cf3cbd8%' or folder_path LIKE '%HR9cf3cbd8%';

UPDATE `file_store_folder` SET `folder_path` = replace (`folder_path`,'HR9cf3cbd8','Zh123456')  WHERE `folder_path`  LIKE '%HR9cf3cbd8%';


/home/<USER>/ex_blinded_csv/6faff97feb5f3a53/CDTMS手册优化测试项目_hrs8807_plasma_20231207_dft_rev1-盲底-2_coding.csv

https://externalBlind-tst.hengrui.com/external_data/file/HRS8807-I-101-6faff97feb5f3a53/CDTMS手册优化测试项目_hrs8807_plasma_20231207_dft_rev1-盲底-2_compare.xlsx

2024.01.29
1.susar更新部署 SMB网盘配置信息变更
2.Rave 数据集下载 调度任务 域账号密码变更
3.外部数据编盲联动选项确认

 var params = []

              var param = {}

              param.labDataId = element.lab_data_id
              param.fileName = sessionStorage.getItem('fileName')
              param.operateUser = this.userName
              param.status = '1'
              params.push(param)
       
              setReview(params).then(res => {
          if (res.data === 'success') {
            this.$message.success('审核成功')
            this.queryDataType = '0'
            this.initPage()
          } else if (res.data === 'warning') {
            this.$message.warning('请先取消审核已选择的数据，再审核！')
          } else {
            this.$message.error(res.data)
          }
        })
       
       
       
       
       
       temp1
       
       
       
       
       
       
     case WHEN comment_level=1 then '中心级别'
        WHEN comment_level=2 then '受试者级别'
        WHEN comment_level=3 then '记录层面'
        ELSE '未知'
        END as comment_level,   
       
       
       center_code
       multiIf(comment_level = 1, level_content,'') AS center_code
       multiIf(comment_level = 2, level_content,'') AS tester_code
       
          CASE WHEN
        comment_level=1 then level_content
        ELSE ''
        END as center_code,
        CASE WHEN
        comment_level=2 then level_content
        ELSE ''
        END as tester_code,
        REPLACE(DATE_FORMAT( create_time, '%Y-%m-%dT%H:%i:%s' ),
        'T',
        ' ')
        as create_time
       
       
       
       
       
       
       
       
       
       
       
        GLOBAL LEFT JOIN (SELECT
        *,
        toString(sipHash64(*)) AS lab_data_id
        FROM s3(#{entity.filePath}, 'minioadmin', 'minioadmin','CSVWithNames')
        ) AS minio_tbl USING lab_data_id
       
       
       
       
       
       
       
       
       
        SELECT * FROM ( SELECT id,lab_data_id,file_name,operate_user,lab_comment,level_content,toString(create_time,'Asia/Shanghai') as createTime,
        multiIf(comment_level = 1, '中心级别', comment_level = 2, '受试者级别', comment_level=3, '记录层面', '未知') AS comment_level
        FROM mysql('***********:3306','dm_platform', 'dm_lab_ae_comments', 'susaruser', 'Hr@db0316')
        WHERE is_deleted='N'  and equals(file_name,#{entity.fileName})
         ) t1
        JOIN (SELECT
        *,
        toString(sipHash64(*)) AS lab_data_id
        FROM s3(#{entity.filePath}, 'minioadmin', 'minioadmin','CSVWithNames')
        ) minio_tbl
        ON t1.lab_data_id = minio_tbl.lab_data_id
       
       multiIf(operate_type = 0, '取消审核', operate_type = 1, '审核', operate_type=2, '批注',  operate_type=3, '取消批注', '未知') AS operate_type
       
       {"file_name","operate_user","lab_comment","level_content","comment_level","createTime"}
          case WHEN operate_type=1 then '审核'
                 WHEN operate_type=0 then '取消审核'
                 WHEN operate_type=2 then '批注'
                 WHEN operate_type=3 then '取消批注'
                 ELSE '未知'
       multiIf( operate_type < 1, '取消审核', operate_type > 0, '审核', '未知')
        (case when operate_type = 0 then '取消审核' when operate_type = 1 then '审核' when operate_type = 2 then '批注' when operate_type = 3 then '取消批注' else '未知' end )
          with minio_tbl AS (SELECT
        *,
        toString(sipHash64(*)) AS lab_data_id
        FROM s3(#{entity.filePath}, 'minioadmin', 'minioadmin','CSVWithNames'))
        SELECT
        mysql_lab_review.*,
        temp1.*
        FROM
        ( SELECT * FROM minio_tbl
        ) AS temp1,
      (
        SELECT
        lab_data_id,
        operate_user,
        operateType,
        toString(create_time,'Asia/Shanghai')
        as create_time
        FROM  mysql('***********:3306','dm_platform', 'dm_operation_records', 'susaruser', 'Hr@db0316')
        WHERE equals(file_name,#{entity.fileName})  and or(equals(operate_type,0),equals(operate_type,1))
        ) AS mysql_lab_review where equals(temp1.lab_data_id,mysql_lab_review.lab_data_id)


        <if test="entity.columnName !=null and entity.columnName.size()>0">
             and lab_data_id in #{entity.columnName}
        </if>
       
       
       
       
       
       
       
       with minio_tbl AS (SELECT *, toString(sipHash64(*)) AS lab_data_id FROM s3( 'http://***********:9000/dmreview/HR070803-301_LAB_AE.csv', 'minioadmin', 'minioadmin','CSVWithNames')) SELECT mysql_lab_review.*, temp1.* FROM ( SELECT * FROM minio_tbl ) AS temp1, ( SELECT lab_data_id, operate_user, multiIf( operate_type < 1, 'aa', operate_type > 0, 'bb', 'cc') AS operate_type, toString(create_time,'Asia/Shanghai') as create_time FROM mysql('***********:3306','dm_platform', 'dm_operation_records', 'susaruser', 'Hr@db0316') WHERE equals(file_name,'HR070803-301_LAB_AE.csv') and or(equals(operate_type,0),equals(operate_type,1)) ) AS mysql_lab_review where equals(temp1.lab_data_id,mysql_lab_review.lab_data_id)
       
       
       
       
       
       
       WITH minio_tbl AS
    (
        SELECT
            *,
            toString(sipHash64(*)) AS lab_data_id
        FROM s3(#{entity.filePath}, 'minioadmin', 'minioadmin', 'CSVWithNames')
    )
SELECT
    mysql_lab_review.*,
    temp1.*
FROM
(
    SELECT *
    FROM minio_tbl
) AS temp1,
(
    SELECT
        *,
        multiIf(operate_type &lt; 1, '取消审核', operate_type &gt; 0, '审核', '未知') AS operate_type_new
    FROM
    (
        SELECT
            lab_data_id,
            operate_user,
            operate_type,
            toString(create_time, 'Asia/Shanghai') AS create_time
        FROM mysql('***********:3306', 'dm_platform', 'dm_operation_records', 'susaruser', 'Hr@db0316')
        WHERE (file_name = #{entity.fileName}) AND ((operate_type = 0) OR (operate_type = 1))
    ) AS t1
) AS mysql_lab_review
WHERE temp1.lab_data_id = mysql_lab_review.lab_data_id














 String localFileName=entity.getFilterColumn()+"_"+entity.getFileName();
            //导出到本地
            File file=fileUtil.exportCSV(entity);
            ByteArrayOutputStream baos = null;
            InputStream inStream= null;
            try {
                inStream = new FileInputStream(file);
            } catch (FileNotFoundException e) {
                throw new RuntimeException(e);
            }
            byte[] buffer = new byte[1024];
            int len;
            baos = new ByteArrayOutputStream();
            while (true) {
                try {
                    if (
                            !((len = inStream.read(buffer)) != -1)
                    )
                        break;
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                baos.write(buffer, 0, len);

            }
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + localFileName);
            response.setCharacterEncoding("UTF-8");
            byte[] uft8bom={(byte)0xef,(byte)0xbb,(byte)0xbf};
            OutputStream out = null;
            try {
                out = response.getOutputStream();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            try {
                out.write(uft8bom);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

            try {
                OutputStreamWriter writer = new OutputStreamWriter(out,"UTF-8");
                writer.write(new String(baos.toByteArray(),"UTF-8"));

            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            
            
            
            
            
            
            
            
             <if test="centerCode != null and centerCode != '' and type==2">
            where  `中心`= #{centerCode} and `质疑` is not  null and `质疑`!='' and toString(sipHash64(`质疑`))!='15150039259969081993' and notEquals(lab_comment,'')
        </if>
        <if test="centerCode == ''and type==2 and labAeId == null and labAeId == ''">
            where notEquals(lab_comment,'')
        </if>
        <if test="centerCode != null and centerCode != '' and labAeId != null and labAeId != ''and type==2">
            where  `中心`= #{centerCode} and `质疑` is not  null and `质疑`!='' and toString(sipHash64(`质疑`))!='15150039259969081993' and notEquals(lab_comment,'') and equals(lab_data_id,#{labAeId})
        </if>
        <if test="centerCode == '' and labAeId != null and labAeId != ''and type==2">
            where   equals(lab_data_id,#{labAeId})
        </if>
        
        
        
        
        
        
        
        
        
        with minio_tbl AS (SELECT *, toString(sipHash64(*)) AS lab_data_id FROM s3(?, 'minioadmin', 'minioadmin','CSVWithNames')), temp1 AS ( SELECT * FROM minio_tbl ) SELECT count(1) AS count FROM temp1 where temp1.lab_data_id IN ( SELECT distinct lab_data_id FROM mysql('***********:3306','dm_platform', 'dm_lab_ae_comments', 'susaruser', 'Hr@db0316') where notEquals(lab_comment,'') and equals(is_deleted,'N') ) AND ( `质疑位置` in ? )


2024.02.01
1.DM审核平台自测，优化动态多列筛选结果跟随当前页面筛选条件，重置表格数据清空表头列筛选
2.优化外部数据编盲登录校验，失效已使用的token保持权限与系统一致
3.更新Rave FTP数据下载服务器调度任务的账号为公共账号



       //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/ on /home/<USER>
       
       
       mount.cifs //SHNVWSASECT01/EDMtst/EDM/json_blind /home/<USER>/ex_json_file -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root
       
       
       /opt/libreoffice7.5
       
       
       
       
       
       
       
       
       
       
       
       
       
       
       WITH TEMP AS (
            SELECT
                TBL_CRA.COL_CRA_NAME AS NAME,
                TBL_CRA.COL_CRA_UNIT AS unit,
                TBL_CRA.COL_CRA_EMAIL AS email,
                TBL_CRA.COL_BLINDING AS blindStatus,
                TBL_CRA.COL_SYZT AS memberStatus,
                CONCAT( CONCAT( TBL_CRA.COL_CRA_EMAIL, '   ' ), TBL_EXT_DATA.COL_BINDING_ROLE ) AS info,
                TBL_EXT_DATA.COL_EXT_DATE
            FROM
                CDTMSEN_VAL.TBL_XSHT
                    LEFT JOIN CDTMSEN_VAL.TBL_CRA ON TBL_XSHT.id = TBL_CRA.COL_STUDYID
                    LEFT JOIN CDTMSEN_VAL.TBL_EXT_DATA ON TBL_XSHT.id = TBL_EXT_DATA.COL_STUDYID
            WHERE
                TBL_XSHT.COL_STUDYID = 'HRS8807-I-101'
              AND COL_SYZT = '1'
              AND COL_EXT_VERSION LIKE '%.0%' and TBL_EXT_DATA.COL_CRF_ZT='10'
            ORDER BY
                COL_EXT_DATE DESC
        ),
             TEMP1 AS (
                 SELECT
                     max(TBL_EXT_DATA.COL_EXT_DATE) as update_time
                 FROM
                     CDTMSEN_VAL.TBL_XSHT
                         LEFT JOIN CDTMSEN_VAL.TBL_CRA ON TBL_XSHT.id = TBL_CRA.COL_STUDYID
                         LEFT JOIN CDTMSEN_VAL.TBL_EXT_DATA ON TBL_XSHT.id = TBL_EXT_DATA.COL_STUDYID
                 WHERE
                     TBL_XSHT.COL_STUDYID = 'HRS8807-I-101'
                   AND COL_SYZT = '1'
                   AND COL_EXT_VERSION LIKE '%.0%' and TBL_EXT_DATA.COL_CRF_ZT='10'
                 ORDER BY
                     COL_EXT_DATE DESC
             ) SELECT NAME,unit,email,blindStatus,memberStatus,info FROM TEMP,TEMP1 WHERE TEMP.COL_EXT_DATE=TEMP1.update_time
             
             
             
TRUNCATE ex_csv_store_data;
TRUNCATE ex_blind_operation_record;
TRUNCATE ex_blind_email_record;
TRUNCATE ex_blind_original_file_record;
TRUNCATE ex_blind_operation_record;
 
 
2月工作内容：
1.DM审核平台开发，完成新增排序字段等5个优化项
2.外部数据遮盲测试环境更新上线，定位并修复测试阶段的问题，开发并更新优化项需求
3.SUSAR平台优化上传容量限制逻辑
4.定位医学编码报告数据部分项目报告下载数据问题，处理项目编号中罗马字符转换
3月工作计划：
上旬：完成外部数据遮盲正式环境部署并上线，完成SUSAR收件人功能模块开发
中旬：完成DM审核平台lab_ae数据审核、批注开发



'259f3f9f2fd7bf6d_医学经理,统计师-掩码 ,259f3f9f2fd7bf6d_统计编程经理-变量不传输 ' 




set timestamp
setlocal enabledelayedexpansion

:: Extract the time substring
set "time=%lastLine%"
set "filename=%lastLine%"
for /f "tokens=6 delims=_" %%a in ("%time%") do (
    set "time=%%a"
)
for /f "tokens=7 delims=_" %%b in ("%filename%") do (
    set "filename=%%b"
)

:: Convert the time format to YYYY/MM/DD hh/mm
set "year=%time:~0,4%"
set "month=%time:~4,2%"
set "day=%time:~6,2%"
set "hour=%filename:~0,2%"
set "minute=%filename:~2,2%"
set "result=%year%/%month%/%day%/%hour%/%minute%"
echo %result%
set "timestamp=%result%"
rem Get current time with format yyyy/MM/dd/HH/mm/ss
for /f "tokens=2-4 delims=/ " %%a in ('date /t') do (set mydate=%%c/%%a/%%b) 
for /f "tokens=1-3 delims=/:" %%a in ('time /t/24') do (set mytime=%%a/%%b)


{"loginid":"<EMAIL>","username":"张怡宁","email":"<EMAIL>","remoteproject":"BBA639A8B0DA4BB8841338256F8B7EB9","projectid":"cdtmsen_val","status":"200","note":"OK","role":"EDM","visittime":"Feb 22, 2024 2:12:02 PM","mapInfo":{"fileName":"CDTMS手册优化测试项目_hrs8807_plasma_20240206_zyn.csv","venderName":"江苏恒瑞(CRO)","role":"EDM","dataType":"PKPD","testFileID":"c3bfdff3bbddd4f5","id":"**********","projectName":"CDTMS手册优化测试项目","type":"p","DTABMList":"[{"role":"统计编程经理","method":"变量不传输","testFileID":"99efeffaee7c53aa"},{"role":"医学经理","method":"掩码","testFileID":"c3bfdff3bbddd4f5"}]","account":"<EMAIL>"}}












WITH result AS (
            SELECT
                any_value ( blind_records ) AS blind_records,
                max(file_id) as file_id,
                max(id) as operate_record_id,
                any_value ( user_id ) AS user_id,
                any_value ( user_name ) AS user_name,
                max( receive_emails ) AS receive_emails,
                any_value ( create_time ) AS create_time,
                any_value ( project_name ) AS project_name,
                any_value ( file_name ) AS file_name,
                any_value ( qc_time ) AS qc_time,
                max( send_time ) AS send_time,
                any_value ( type ) AS type,
                any_value ( role ) AS role,
                any_value ( is_approve ) AS is_approve,
                any_value ( file_path ) AS file_path,
                any_value ( qc_user_id ) AS qc_user_id,
                any_value ( blind_status ) AS blind_status,
                max( qc_comments ) AS qc_comments,
                any_value ( operate_name ) AS operate_name,
                max( compare_file_path ) AS preview_url,
                max( demand ) AS demand,
                max( project_member ) AS project_member
            FROM
                (
                    SELECT
                        ex_blind_operation_record.ex_data_id,
                        ex_blind_operation_record.id,
                        ex_blind_operation_record.file_id ,
                        blind_records,
                        user_id,
                        user_name,
                        CASE
                            type
                            WHEN '2' THEN
                                ex_blind_email_record.receive_emails ELSE ''
                            END AS receive_emails,
                        DATE_FORMAT( ex_blind_operation_record.create_time, '%Y-%m-%dT%H:%i:%s.000+00:00' ) AS create_time,
                        ex_blind_operation_record.project_name,
                        ex_blind_operation_record.file_name,
                        DATE_FORMAT( ex_blind_operation_record.update_time, '%Y-%m-%dT%H:%i:%s.000+00:00' ) AS qc_time,
                        CASE
                            type
                            WHEN '2' THEN
                                DATE_FORMAT( ex_blind_email_record.create_time, '%Y-%m-%dT%H:%i:%s.000+00:00' ) ELSE ''
                            END AS send_time,
                        CASE
                            type
                            WHEN '0' THEN
                                '通知EDM(QC)'
                            WHEN '1' THEN
                                '通知EDM'
                            WHEN '2' THEN
                                '通知项目组' ELSE ''
                            END AS type,
                        role,
                        CASE
                            is_approve
                            WHEN '0' THEN
                                '未通过QC'
                            WHEN '1' THEN
                                '通过QC' ELSE '未知'
                            END AS is_approve,
                        file_path,
                        qc_user_id,
                        CASE

                            WHEN type = '0' THEN
                                '0'
                            WHEN type = '2' THEN
                                '1'
                            WHEN type = '1'
                                AND is_approve = '0' THEN
                                '0'
                            WHEN type = '1'
                                AND is_approve = '1' THEN
                                '1' ELSE '-1'
                            END AS blind_status,
                        qc_comments,
                        operate_name,
                        compare_file_path,
                        demand,
                        project_member
                    FROM
                        ex_blind_operation_record
                            LEFT JOIN ex_blind_email_record ON ex_blind_operation_record.file_id = ex_blind_email_record.file_id
                            AND ex_blind_operation_record.ex_data_id = ex_blind_email_record.ex_data_id
                ) AS result
            WHERE
                ex_data_id = '2895609857'
            GROUP BY
                blind_records,
                create_time,
                qc_user_id,
                qc_time
        ) select
              file_id,
              operate_record_id,
              blind_records,
              user_id,
              user_name,
              receive_emails,
              create_time,
              project_name,
              file_name,
              CASE

                  WHEN qc_user_id IS NULL THEN
                      '' ELSE qc_time
                  END AS qc_time,
              send_time,
              type,
              role,
              CASE

                  WHEN qc_user_id IS NULL THEN
                      '' ELSE is_approve
                  END AS is_approve,
              file_path,
              qc_user_id,
              blind_status,
              qc_comments,
              operate_name,
              preview_url,
              demand,
              project_member
        FROM
            result



ALTER TABLE ex_blind_operation_record ADD COLUMN blind_member_with_mail TEXT;

blindMemberWithMail

select * from ex_blind_operation_record order by create_time desc limit 1;

2895609860

'<EMAIL>   医学经理,<EMAIL>   统计师,<EMAIL>   医学经理            '


getMemberWithMailByFileId
 
 
 
 getMemberInfoByFileId
 
 
 
 
 [{\"role\":\"统计编程经理\",\"method\":\"变量不传输\",\"testFileID\":\"db8fd7f7d3f636b1\"},{\"role\":\"医学经理\",\"method\":\"掩码\",\"testFileID\":\"bf9ffffe7ca711ca\"},{\"role\":\"统计师\",\"method\":\"二次编码\"}]
 
 [{\"role\":\"统计编程经理\",\"method\":\"变量不传输\",\"testFileID\":\"ad9fcfedffbe5456\"},{\"role\":\"医学经理\",\"method\":\"掩码\",\"testFileID\":\"3bbdd5bbfbfb6467\"},{\"role\":\"统计师\",\"method\":\"二次编码\"}
 
 
 

    var projectParam={}
    projectParam.keyWord=this.projectKeyWord
    projectParam.pageNum = this.projectCurrentPage
    projectParam.pageSize = this.projectPageSize
    projectParam.sortOrder = this.projectSortOrder
    projectParam.sortColumn = this.projectSortColumn
      if(this.projectTimeSearch){
        projectParam.startTime = parseTime(this.projectTimeSearch[0])
        projectParam.endTime = parseTime(this.projectTimeSearch[1]).split(' ')[0]+' 23:59:59'
      }else{
        projectParam.startTime=""
        projectParam.endTime=""
      }
    getProjectInfo(projectParam).then((res)=>{
        if(res.code===200 ){
          this.projectData=res.data.records
          this.projectTotal=res.data.total
        }
    })
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    [
        {
            "compound_no": "sadasdsa",
            "compound_name": "测试代码"
        },
        {
            "compound_no": "sadas",
            "compound_name": "测klklkljkljkl"
        },
        {
            "compound_no": "sadsadsa",
            "compound_name": "的撒旦撒旦"
        },
        {
            "compound_no": "sadas",
            "compound_name": "按时艰苦艰苦艰苦"
        },
        {
            "compound_no": "SHR-TEST",
            "compound_name": "SHR测试化合物"
        },
        {
            "compound_no": "localTest",
            "compound_name": "localTest",
            "study_no": "localTest"
        },
        {
            "compound_no": "SHR-1210",
            "compound_name": "SHR-1210",
            "study_no": "SHR-1210-III-325"
        },
        {
            "compound_no": "SHR-1210",
            "compound_name": "SHR-1210",
            "study_no": "SHR-1210-III-316"
        },
        {
            "compound_no": "SHR-1210",
            "compound_name": "SHR-1210",
            "study_no": "SHR-1210-II-217"
        },
        {
            "compound_no": "SHR-1210",
            "compound_name": "SHR-1210",
            "study_no": "SHR-1210-II-213"
        },
        {
            "compound_no": "SHR-1210",
            "compound_name": "SHR-1210",
            "study_no": "SHR-1210-APA-EXT"
        },
        {
            "compound_no": "SHR-1701",
            "compound_name": "SHR-1701 注射液",
            "study_no": "SHR-1701-II-205"
        },
        {
            "compound_no": "SHR-1701",
            "compound_name": "SHR-1701 注射液",
            "study_no": "SHR-1701-II-204"
        },
        {
            "compound_no": "SHR-1701",
            "compound_name": "SHR-1701 注射液",
            "study_no": "SHR-1701-II-203"
        },
        {
            "compound_no": "SHR6390",
            "compound_name": "SHR6390",
            "study_no": "SHR6390-I-109"
        },
        {
            "compound_no": "test",
            "compound_name": "测试项目",
            "study_no": "test"
        }
    ].map(item => item.compound_no);
    
    
    [    {
            "compound_no": "SHR-1701",
            "compound_name": "SHR-1701 注射液",
            "study_no": "SHR-1701-II-203"
        },
        {
            "compound_no": "SHR6390",
            "compound_name": "SHR6390",
            "study_no": "SHR6390-I-109"
        },
        {
            "compound_no": "test",
            "compound_name": "测试项目",
            "study_no": "test"
        }
     ]
     
     selectCompoundNameChange
     
     selectStudyNoChange
     
     
     
     
     
     
     
     s_rule_form.site_compound_no
     s_rule_form.site_compound_name
     s_rule_form.site_project_no
     s_rule_form.site_project_name
     s_rule_form.site_study_no
     s_rule_form.site_study_name
     
     
     
     receiver_roles
         s_compound_no:[{ required: true, message: '请选择化合物代码', trigger: 'blur' }],
        s_compound_name:[{ required: true, message: '请选择化合物名称', trigger: 'blur' }],
        s_project_no:[{ required: true, message: '请选择项目代码', trigger: 'blur' }],
        s_project_name:[{ required: true, message: '请选择项目代码', trigger: 'blur' }],
        s_study_no:[{ required: true, message: '请选择项目代码', trigger: 'blur' }],
        s_study_name:[{ required: true, message: '请选择项目代码', trigger: 'blur' }]
        
        
       项目代码 s_rule_form.site_project_no
      化合物代码  s_rule_form.site_compound_no
     中心编号  s_rule_form.site_study_no
      中心名称         s_rule_form.site_study_name 
      角色        receiver_role
     是否接收报告   radio
        
        
     
     角色代码，1 代表 Site_PI, 2 代表 Site_INV, 3 代表机构SUSAR人员
     
     
     
     
             var param={}
            param.role=this.receiver_role
            param.roleName=this.getRoleLabel(this.receiver_role)
            param.email=this.ruleForm.receiverEmail
            param.studyNo=this.s_rule_form.site_project_no
            param.siteNo=this.s_rule_form.site_study_no
            param.isReceive=this.radio
            
            
            
            
             editTUserInfo(param).then((res)=>{
                      if(res.code===200 && res.data==='success'){
                        this.$message.success('新增收件人成功!')
                        this.addReceiverVisible=false
                        this.userInfoSearch()
                      }else {
                        this.$message.error('新增收件人失败!')
                        return false;
                  }
                    })
            
            
            
            this.receiver_role
			this.ruleForm.receiverEmail
			this.radio
			
			        <el-select  v-model="s_rule_form.site_compound_name" @change="selectCompoundNameChange" filterable placeholder="请选择">
                <el-option
                  v-for="item in compoundNameList"
                  :key="item"
                  :label="item"
                  :value="item">
                </el-option>
              </el-select>
              
              
              
              
			 this.getProSelectInfo()
			
			
			<EMAIL>
			
			
			
			
			siteUserSortColumn
			
			
			sortUserChangeOperate
	 this.getTSiteData()
      this.getTUserData()
      this.getCompoundData()
      this.getProjectData()
      this.getOperateLogData()
			
			
			
			this.operateSortColumn
			
			
			
			
			
			
			
			
			this.getProSelectInfo()
			
			
			
compoundKeyWord
projectKeyWord
siteKeyWord
siteUserKeyWord
operateKeyWord


getProjectInfoNoPage




       {value:1,label:'研究者'},
        {value:2,label:'机构/伦理'},
        {value:4,label:'恒瑞公共邮箱'},
        {value:5,label:'项目经理'},
        {value:6,label:'临床监查员'},
        {value:7,label:'临床研究助理'}









     // 定义列名行
             var header = [{
        '记录名称':'文件上传记录','化合物':this.select,'开始日期':startTime,'结束日期':endTime,'文件名/用户名':this.input3,'导出时间': currentTime, '操作人': localStorage.user
      }]
        var wbout
        const newData = this.noPageUploadHistory.reverse().map(obj => {
        obj['文件名称'] = obj.fileName
        delete obj.fileName

        obj['上传者'] = obj.userName
        delete obj.userName

        obj['化合物文件夹'] = obj.compoundFolder
        delete obj.compoundFolder

        if (obj.isDeleted === 'Y') {
          obj.isDeleted = '已删除';
        }else if(obj.isDeleted === 'N') {
          obj.isDeleted = '已上传';
        }
        obj['文件状态'] = obj.isDeleted
        delete obj.isDeleted

        if (obj.isSend === 'Y') {
          obj.isSend = '已发送';
        }else if(obj.isSend === 'N') {
          obj.isSend = '未发送';
        }
        obj['发送状态'] = obj.isSend
        delete obj.isSend

        obj['上传日期'] = obj.createTime // 替换address键
        delete obj.createTime
        delete obj.filePath
        delete obj.folderPath
        delete obj.url
        delete obj.userName
        delete obj.id
        return obj
      })
      const data = XLSX.utils.json_to_sheet(newData)
      const headerData = XLSX.utils.json_to_sheet(header)
        const wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, headerData, '导出记录')
        XLSX.utils.book_append_sheet(wb, data, '文件上传导出记录-' + currentTime)
        /* get binary string as output */
        wbout = XLSX.write(wb, { bookType: 'xlsx', bookSST: true, type: 'array' })
        try {
          FileSaver.saveAs(new Blob([wbout], { type: 'application/octet-stream'}), '文件上传记录-'+currentTime+'.xlsx')
        } catch (e) { if (typeof console !== 'undefined') console.log(e, wbout) }
        this.fetchData()
        return wbout








   const exparam = Object.assign({}, param)
        exparam.pageNum = 1
        exparam.pageSize = -1
        getUpoloadHistory(exparam).then(response => {
          this.noPageUploadHistory = response.data.records
        })




StringUtils.isEmpty(row.getStr("content"))?row.getStr("content"):row.getStr("content").replace(",", ";");





.replace(/,/g, ';')



addTSiteInfo


useGeneratedKeys="true" keyProperty="id"





SELECT
            object_name AS content,
            user_name,
            operation_type,
            object_type,
            object_id,
            DATE_FORMAT( operate_time, '%Y-%m-%d %H:%i:%s' ) AS operate_time
        FROM
            user_operation_log
                LEFT JOIN user_info ON user_operation_log.user_id = user_info.id
                LEFT JOIN t_compound_info ON user_operation_log.object_id = t_compound_info.id
        WHERE
            ( operation_type = '添加' OR operation_type = '失效' OR operation_type = '激活')
            AND object_type='化合物列表'
          AND compound_name IS NOT NULL
          AND compound_no IS NOT NULL
        <if test="entity.startTime!=null and entity.startTime!='' ">
            AND operate_time &gt;= str_to_date( #{entity.startTime}, '%Y-%m-%d %H' )
        </if>
        <if test="entity.endTime!=null and entity.endTime!='' ">
            AND operate_time &lt; str_to_date( #{entity.endTime}, '%Y-%m-%d %H' )
        </if>
        <if test="entity.keyWord!=null and entity.keyWord!='' ">
            AND (user_name like  concat('%',#{entity.keyWord},'%') or object_name like  concat('%',#{entity.keyWord},'%') or operation_type like  concat('%',#{entity.keyWord},'%') or object_type like  concat('%',#{entity.keyWord},'%')  )
        </if>
        UNION
        SELECT
        object_name AS content,
        user_name,
        operation_type,
        object_type,
        object_id,
        DATE_FORMAT( operate_time, '%Y-%m-%d %H:%i:%s' ) AS operate_time
        FROM
        user_operation_log
        LEFT JOIN user_info ON user_operation_log.user_id = user_info.id
        LEFT JOIN t_project_info ON user_operation_log.object_id = t_project_info.id
        WHERE
        ( operation_type = '编辑' OR operation_type = '添加'  )
        AND object_type='项目信息'
        AND study_no IS NOT NULL
        AND compound_no IS NOT NULL
        <if test="entity.startTime!=null and entity.startTime!='' ">
            AND operate_time &gt;= str_to_date( #{entity.startTime}, '%Y-%m-%d %H' )
        </if>
        <if test="entity.endTime!=null and entity.endTime!='' ">
            AND operate_time &lt; str_to_date( #{entity.endTime}, '%Y-%m-%d %H' )
        </if>
        <if test="entity.keyWord!=null and entity.keyWord!='' ">
            AND (user_name like  concat('%',#{entity.keyWord},'%') or object_name like  concat('%',#{entity.keyWord},'%') or operation_type like  concat('%',#{entity.keyWord},'%') or object_type like  concat('%',#{entity.keyWord},'%')   )
        </if>

        UNION

        SELECT
        object_name AS content,
        user_name,
        operation_type,
        object_type,
        object_id,
        DATE_FORMAT( operate_time, '%Y-%m-%d %H:%i:%s' ) AS operate_time
        FROM
        user_operation_log
        LEFT JOIN user_info ON user_operation_log.user_id = user_info.id
        LEFT JOIN t_site ON user_operation_log.object_id = t_site.id
        WHERE
        ( operation_type = '添加' OR operation_type = '编辑' )
        AND object_type='研究中心'
        <if test="entity.startTime!=null and entity.startTime!='' ">
            AND operate_time &gt;= str_to_date( #{entity.startTime}, '%Y-%m-%d %H' )
        </if>
        <if test="entity.endTime!=null and entity.endTime!='' ">
            AND operate_time &lt; str_to_date( #{entity.endTime}, '%Y-%m-%d %H' )
        </if>
        <if test="entity.keyWord!=null and entity.keyWord!='' ">
            AND (user_name like  concat('%',#{entity.keyWord},'%') or object_name like  concat('%',#{entity.keyWord},'%') or operation_type like  concat('%',#{entity.keyWord},'%') or object_type like  concat('%',#{entity.keyWord},'%')  )
        </if>
        UNION

        SELECT
        object_name AS content,
        user_name,
        operation_type,
        object_type,
        object_id,
        DATE_FORMAT( operate_time, '%Y-%m-%d %H:%i:%s' ) AS operate_time
        FROM
        user_operation_log
        LEFT JOIN user_info ON user_operation_log.user_id = user_info.id
        LEFT JOIN t_site ON user_operation_log.object_id = t_site.id
        WHERE  ( operation_type = '添加' OR operation_type = '编辑' )
        AND object_type='收件人'
        <if test="entity.startTime!=null and entity.startTime!='' ">
            AND operate_time &gt;= str_to_date( #{entity.startTime}, '%Y-%m-%d %H' )
        </if>
        <if test="entity.endTime!=null and entity.endTime!='' ">
            AND operate_time &lt; str_to_date( #{entity.endTime}, '%Y-%m-%d %H' )
        </if>
        <if test="entity.keyWord!=null and entity.keyWord!='' ">
            AND (user_name like  concat('%',#{entity.keyWord},'%') or object_name like  concat('%',#{entity.keyWord},'%') or operation_type like  concat('%',#{entity.keyWord},'%') or object_type like  concat('%',#{entity.keyWord},'%')  )
        </if>
        UNION

        SELECT
        object_name AS content,
        user_name,
        operation_type,
        object_type,
        object_id,
        DATE_FORMAT( operate_time, '%Y-%m-%d %H:%i:%s' ) AS operate_time
        FROM
        user_operation_log
        LEFT JOIN user_info ON user_operation_log.user_id = user_info.id
        LEFT JOIN t_site ON user_operation_log.object_id = t_site.id
        WHERE
        ( operation_type = '添加' OR operation_type = '编辑' )
        AND object_type='操作记录'
        <if test="entity.startTime!=null and entity.startTime!='' ">
            AND operate_time &gt;= str_to_date( #{entity.startTime}, '%Y-%m-%d %H' )
        </if>
        <if test="entity.endTime!=null and entity.endTime!='' ">
            AND operate_time &lt; str_to_date( #{entity.endTime}, '%Y-%m-%d %H' )
        </if>
        <if test="entity.keyWord!=null and entity.keyWord!='' ">
            AND (user_name like  concat('%',#{entity.keyWord},'%') or object_name like  concat('%',#{entity.keyWord},'%') or operation_type like  concat('%',#{entity.keyWord},'%') or object_type like  concat('%',#{entity.keyWord},'%')   )
        </if>








 OperateEntity operateReceiver = new OperateEntity(userId, String.valueOf(entity.getSiteId()), OperateType.EDIT_SITE.toString(), ObjectType.RECEIVER_INFO.toString(), "项目代码：" + entity.getStudyNo()+"化合物代码：" + entity.getCompoundNo() + " 中心编号:" + entity.getSiteNo()+" 中心名称：" + entity.getSiteName(), "", 'Y');



   StringBuilder message = new StringBuilder();
            message.append("项目代码:").append(entity.getStudyNo())
                    .append(" 中心编号:").append(entity.getSiteNo())
                    .append(" 角色:").append(entity.getRoleName())
                    .append(" 邮箱:").append(entity.getEmail())
                    .append(" 是否接收SUSAR报告:").append(entity.getIsReceive().equals("Y") ? "是" : "否");


resetSiteData

resetProjectData

resetCompoundData



2.Red Hat Enterprise Linux release 8.7 (Ootpa)


 localStorage.count++
 
 2024.03.29
 1.
 with minio_tbl AS (SELECT *, toString(sipHash64(*)) AS lab_data_id FROM s3('http://***********:9000/dmreview/HR070803-301_LAB_AE.csv', 'minioadmin', 'minioadmin','CSVWithNames')) SELECT distinct temp1.`表名称` as text, temp1.`表名称` as value FROM ( SELECT * FROM minio_tbl ) AS temp1 LEFT JOIN ( SELECT * FROM mysql ( '***********:3306', 'dm_platform', 'dm_lab_ae_review', 'susaruser', 'Hr@db0316' ) ) AS mysql_lab_review USING lab_data_id where or(equals(is_viewed,''), equals(is_viewed,'0')) AND ( `知情同意书签字日期-D` in ( '2023-07-19' , 'D-二聚体' ) OR `关联检查项` in ( '2023-07-19' , 'D-二聚体' ) OR `表名称` in ( '2023-07-19' , 'D-二聚体' ) order by `检查结果` asc
 
 
 2. http://***********:9000/dmreview/HR070803-301_LAB_AE.csv(String), 2023-07-19(String), D-二聚体(String), 2023-07-19(String), D-二聚体(String), 2023-07-19(String), D-二聚体(String)
 
 
 getColumnSelectValues
 getCommentColumnSelectValues
 
 
 HRB1L03175
 
 5CG234380V
 
 江苏恒瑞医药股份有限公司.上海盛迪医药有限公司.临床数据科学中心
 本地开发需使用mysql数据库，方便开发过程中数据同步、维护和变更，简化逻辑校验流程，启动mysql服务需以管理员权限启动mysqld.exe
 
 
 
 
 
 
 角色:1.研究者 2.机构/伦理 4.恒瑞公共邮箱 5.项目经理 6.临床监查员 7.临床研究助理 8.管线经理 9.区域经理
 
 
 
 
 
 import cn.smallbun.screw.core.Configuration;
import cn.smallbun.screw.core.engine.EngineConfig;
import cn.smallbun.screw.core.engine.EngineFileType;
import cn.smallbun.screw.core.engine.EngineTemplateType;
import cn.smallbun.screw.core.execute.DocumentationExecute;
import cn.smallbun.screw.core.process.ProcessConfig;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@SpringBootTest
class DatabaseExplainApplicationTests {

   @Autowired
   ApplicationContext applicationContext;

   @Test
   void contextLoads() {
      DataSource dataSourceMysql = applicationContext.getBean(DataSource.class);

      // 生成文件配置
      EngineConfig engineConfig = EngineConfig.builder()
            // 生成文件路径，自己mac本地的地址，这里需要自己更换下路径
            .fileOutputDir("/Users/<USER>/Desktop")
            // 打开目录
            .openOutputDir(false)
            // 文件类型
            .fileType(EngineFileType.HTML)
            // 生成模板实现
            .produceType(EngineTemplateType.freemarker).build();

      // 生成文档配置（包含以下自定义版本号、描述等配置连接）
      Configuration config = Configuration.builder()
            .version("1.0.3")
            .description("生成文档信息描述")
            .dataSource(dataSourceMysql)
            .engineConfig(engineConfig)
            .produceConfig(getProcessConfig())
            .build();

      // 执行生成
      new DocumentationExecute(config).execute();
   }


   /**
    * 配置想要生成的表+ 配置想要忽略的表
    * @return 生成表配置
    */
   public static ProcessConfig getProcessConfig(){
      // 忽略表名
      List<String> ignoreTableName = Arrays.asList("aa","test_group");
      // 忽略表前缀，如忽略a开头的数据库表
      List<String> ignorePrefix = Arrays.asList("a","t");
      // 忽略表后缀
      List<String> ignoreSuffix = Arrays.asList("_test","czb_");

      return ProcessConfig.builder()
            //根据名称指定表生成
            .designatedTableName(new ArrayList<>())
            //根据表前缀生成
            .designatedTablePrefix(new ArrayList<>())
            //根据表后缀生成
            .designatedTableSuffix(new ArrayList<>())
            //忽略表名
            .ignoreTableName(ignoreTableName)
            //忽略表前缀
            .ignoreTablePrefix(ignorePrefix)
            //忽略表后缀
            .ignoreTableSuffix(ignoreSuffix).build();
   }
}
 
 
 
 
 
 
 
 
 TBL_ECRF
 
 
 
VERSION
ID
COL_UUID
COL_USERID
COL_UNITID
COL_TNAME
COL_TID
COL_STUDYID
COL_STD_TID
COL_REF_TID
COL_MODIFYUSERID
COL_LASTMODIFYTIME
COL_FIELD_TYPE
COL_FIELD_NAME
COL_FIELD_ID
COL_CREATETIME
 
 
 
 studyId
 表名 tableNameCH
 表   tableNameEN
 标准表 standardTableName
 引用表 referenceTableName
 变量名 variableName
 变量  variable
 变量类型  variableType
    
    
  http://************:8087/sas_online/getSubjectInfoLast  
    
    
2024.08.19
1.ecrf 受试者交接 匹配excel中的受试者号的ecrf数据
2.rave数据集密码设置 token查询登录及项目信息 校验
3.taskId     server     projectId

getLoginInfo
https://meduap-tst.hengrui.com:8085/remoteButtonTask/getInfo?taskId=3524919307&projectId=cdtmsen_val

https://meduap-tst.hengrui.com:8085/home.jsp?projectid=cdtmsen_val&msgcode=01

http://localhost:9528/#/dashboard?taskId=3524919307&projectId=cdtmsen_val
{"createtime":"2024-08-19 11:14:21","modifyuserid":"SuperSA","center":"","userid":"SuperSA","uuid":"CB19E1D6076F471A90CE8EA41812FE86","zq":"6","name":"SuperSA","unitid":"","studyid":"HRS-4357-101","lastmodifytime":"2024-08-19 11:14:21","id":"3525050369","dept_id":"","subject_identification":"","visit_date":"2024-08-20"}











function setReceiver(){
    var transferReason = '$form{transfer_reason}';
    var receiver;
    var receiverUnit;
	if('$form{transfer_reason}'=='1'){
		var receiver = '$link{cra,cra_name,obj.studyid=$form{studyid} and obj.limitnum='3',obj.cra_active_dt desc}';
		var receiverUnit = '$link{cra,cra_unit,obj.studyid=$form{studyid} and obj.limitnum='3',obj.cra_active_dt desc}';
	}else if('$form{transfer_reason}'=='2'){
		var receiver = '$link{cra,cra_name,obj.studyid=$form{studyid} and obj.limitnum='5',obj.cra_active_dt desc}';
		var receiverUnit = '$link{cra,cra_unit,obj.studyid=$form{studyid} and obj.limitnum='5',obj.cra_active_dt desc}';
	}
	
	return [receiver,receiverUnit];
}
setReceiver();

obj.studyid=$form{studyid} and 
(('$form{transfer_reason}'='1' and  obj.limitnum ='3' ) or ('$form{transfer_reason}'='2' and  obj.limitnum ='5' ))

************:9528 绑定https:/sasOnlineDTAPass-tst.hengrui.com
***********:9528 绑定https:/sasOnlineDTAPass.hengrui.com
************:9528 绑定https:/sasOnlineDTAPass-val.hengrui.com



 //1.get CDTMS file from research case
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        //1.1 get token
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "studyinfo");
        //1.2 get studyId
        String studyId = formInfo.get("studyId");
        String filePath= SASOnlieConstant.SAS_DATA_LOCAL_FOLDER + studyId + "-protocol.docx";
        String dataListInfo = CDTMSAPI.getDataListInfo(token,"xsht","obj.studyid='"+studyId+"'","","");
        String temp = JSON.parseArray(dataListInfo).get(0).toString();
        String id = JSON.parseObject(temp).get("id").toString();
        String  getFileInfo = CDTMSAPI.getDataListInfo(token,"xshtbbxx","obj.studyid='"+id+"'","edit","obj.revised_date desc");
        String fileInfo = JSON.parseArray(getFileInfo).get(0).toString();
        String input = JSON.parseObject(fileInfo).get("revised_doc").toString();
        String regex = "\\*([A-Z0-9]+\\.docx)\\|";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        String ufn="";
        if (matcher.find()) {
                ufn = matcher.group(1);
                ProtocolChatServiceImpl.log.info(ufn);
        } else {
            ProtocolChatServiceImpl.log.info("No match found");
        }
        ProtocolChatServiceImpl.log.info("-------------------------------------------------------found the file name is {}---------------------------",ufn);
        if(!ufn.isEmpty()){
            try {
                CDTMSAPI.downloadDataByUserSync("xshtbbxx",token,ufn,filePath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }














https://meduap-tst.hengrui.com:8085/cdtmsen_val


这里提示 ./configure:error:C compiler cc is not found，是缺少依赖包，先装一下：

ALTER TABLE data_set_pass
ADD COLUMN remark VARCHAR(255);

Refused to display 'https://sas-online-tst.hengrui.com/' in a frame because it set 'X-Frame-Options' to 'sameorigin'.

file2
subject_list
http://************:8087/sas_online/ECRFMatcheByList


add_header X-Frame-Options "ALLOW-FROM https://meduap-tst.hengrui.com:8085/";
add_header Content-Security-Policy "frame-ancestors 'self' https://meduap-tst.hengrui.com:8085/";










2024.08.26
1.随机化生成平台 需求沟通
2.密码设置，密码存储到json,上传到minio 指定目录




2024.08.27
1.onlyOffice 页面实现
2.保存接口编写：
@Restful(value = "/save/*/*/*/*", scope = RestfulScope.PUBLIC)
    public void fileSave(HttpServletRequest request, HttpServletResponse response) {
        try {
            PrintWriter writer = response.getWriter();
            String body = "";
            try {
                Scanner scanner = new Scanner(request.getInputStream());
                scanner.useDelimiter("\\A");
                body = scanner.hasNext() ? scanner.next() : "";
                scanner.close();
            } catch (Exception ex) {
                return;
            }
            if (body.isEmpty()) {
                return;
            }
            JsonParser parser = new JsonParser();
            JsonElement parse = parser.parse(body);
            JsonObject object = parse.getAsJsonObject();
            String key = object.get("key").getAsString();
            int status = object.get("status").getAsInt();
            //Log.info("OnlyOffice保存状态位：" + status);
            int saved = 0;
            if (status == 2) {
                String downloadUri = object.get("url").getAsString();
                try {
                    URL url = new URL(downloadUri);
                    java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
                    InputStream stream = connection.getInputStream();
                    if (stream == null) {
                        throw new Exception("Stream is null");
                    }
                    // 从请求中获取要覆盖的文件参数定义"path"
                    String[] parameterA = RestfulUtil.getParameters();
                    String projectId = parameterA[2];
                    String tableId = parameterA[3];
                    String fileUuid = parameterA[4];
                    AttachDAO attachDAO = new AttachDAO(projectId);
                    File savedFile = attachDAO.getFile(fileUuid, tableId);
                    String key1 = OnlyOfficeUtil.getKey(savedFile);
                    //比较key值
                    if (!StringUtils.equals(key1, key)) throw new RuntimeException("Key Error!");
                    //保存文件
                    OnlyOfficeUtil.saveFile(savedFile, stream);
                    //md5值计算
                    String snNum = Md5Util.encodeToHexString(LocalInfoUtil.getClientNoFromBioknowReg() + "--" + savedFile.getPath());
                    TimerCacheUtil.remove(snNum);
                    connection.disconnect();
                } catch (Exception ex) {
                    saved = 1;
                    ex.printStackTrace();
                }
            }
            writer.write("{\"error\":" + saved + "}");
        } catch (IOException e) {
            Log.error("", e);
        }
    }
    
    D4282A0ABDD14D8F9239B87746DD53E8
    
    function setReceiver(){
    var transferReason = '$form{transfer_reason}';
    var receiver;
    var receiverUnit;
	if('$form{transfer_reason}'=='1'){
		var receiver = '$link{cra,cra_name,obj.studyid=$form{studyid} and obj.limitnum='3',obj.cra_active_dt desc}';
		var receiverUnit = '$link{cra,cra_unit,obj.studyid=$form{studyid} and obj.limitnum='3',obj.cra_active_dt desc}';
	}else if('$form{transfer_reason}'=='2'){
		var receiver = '$link{cra,cra_name,obj.studyid=$form{studyid} and obj.limitnum='5',obj.cra_active_dt desc}';
		var receiverUnit = '$link{cra,cra_unit,obj.studyid=$form{studyid} and obj.limitnum='5',obj.cra_active_dt desc}';
	}
	
	return [receiver,receiverUnit];
}
setReceiver();
    
    
    
    
    
    
    
    function setReceiver(){
    var transferReason = '$form{transfer_reason}';
    var receiver;
    var receiverUnit;
	if('$form{transfer_reason}'=='1'){
		var receiver = '$link{cra,cra_name,obj.studyid=$form{studyid} and obj.limitnum='3',obj.cra_active_dt desc}';
		var receiverUnit = '临床运营部';
	}else if('$form{transfer_reason}'=='2'){
		var receiver = '$link{cra,cra_name,obj.studyid=$form{studyid} and obj.limitnum='5',obj.cra_active_dt desc}';
		var receiverUnit = '生物计量部';
	}
	
	return [receiver,receiverUnit];
}
setReceiver();

项目锁库后



   String transferReason="";
        if(!formData.isEmpty()){
            JSONObject formInfoData = new JSONObject(formData);
            transferReason=formInfoData.get("transfer_reason").toString();
        }



  if(!transferReason.isEmpty()&&transferReason.equals("NDA递交")){
            ENVInfo.put("ftp", "/Projects/CDTMS手册优化测试项目/EDM_Unblind/PK/");
        }










  //1.call getInfo API
            Map<String, String> formInfoByTaskId = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
            //1.1 get id from formInfoByTaskId
            String data = formInfoByTaskId.get("param");
            String dataId="";
            if(!data.isEmpty()){
                JSONObject formInfoData = new JSONObject(data);
                dataId=formInfoData.get("id").toString();
            }
            //5.put id into data
            JSONObject temp = new JSONObject();
            JSONObject param=new JSONObject();
            temp.put("id",dataId);
            temp.put("server","FTP");
            temp.put("file_position","/Projects/CDTMS手册优化测试项目/EDM_Unblind/PD/");
            param.put("data",temp);
            ECRFTransferServiceImpl.log.info("call dataSave params is :" + param.toString());
            String newFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
            param.put("formId",newFormId);
            param.put("taskId",taskId);
            param.put("projectId",projectId);
            ECRFTransferServiceImpl.log.info("最新的formId是 :" + newFormId);
            CDTMSAPI.dataSave(param);


" + ENVInfo.get("studyId") + "'"

			a_fill
			
			
			
受试者crf和清单筛选按钮合并，受试者crf按钮 隐藏



15150554308

sftp -p 22357 <EMAIL>@clinical-ftp.hengrui.com



/home/<USER>/edcserver/HRS-4357-101_PRO_6df1b3ffbdccfbe5_a.zip



2024.09.02
1.
2.
3.





String downloadUri = object.get("url").toString();
            URL url = new URL(downloadUri);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
            InputStream stream = connection.getInputStream();

            File savedFile = new File("/home/<USER>/8087/onlyOfficeFile/"+key+".docx");
            try (FileOutputStream out = new FileOutputStream(savedFile)) {
                int read;
                final byte[] bytes = new byte[1024];
                while ((read = stream.read(bytes)) != -1) {
                    out.write(bytes, 0, read);
                }
                out.flush();
            }
            connection.disconnect();
            
            
            
 https://oos-tst.hengrui.com/cache/files/data/Khirz6zTPdfd8_6103/output.docx/output.docx?md5=wbcXeTdajCuZ0T_eeEyvRQ&expires=1725333570&WOPISrc=Khirz6zTPdfd8&filename=output.docx      
            
            
Request processing failed; nested exception is java.lang.RuntimeException: java.net.MalformedURLException: no protocol: "https://oos-tst.hengrui.com/cache/files/data/Khirz6zTPdfd8_5219/output.docx/output.docx?md5=-TBsJtFcWB_viDXVGjteXw&expires=1725332844&WOPISrc=Khirz6zTPdfd8&filename=output.docx"



#{id},#{userId},#{userName},#{fileName},#{key},#{studyId}



1.后台生成记录，如何保存taskId
2.如何利用现有的接口逻辑，实现后台调度
3.定时器 如果调度的执行时间在非办公时段，是否有影响
4.



this.config.document.url = 'https://sas-online-tst.hengrui.com/sas_online/files/TEST_' + response.file_key + '.docx'




      this.config.document.fileType = 'docx'
      this.config.document.title = 'Example Document Title.docx'
      this.config.documentType = 'word'
      const url = new URL(window.location.href)
      const username = url.searchParams.get('username')
      this.config.editorConfig.user.name = username || localStorage.getItem('username')
      this.config.editorConfig.user.id = this.generateUserId()
      this.userName = username || localStorage.getItem('username')
      this.config.editorConfig.callbackUrl = 'http://************:8087/sas_online/saveOnlyOfficeFile?studyId=' + 'TEST' + '&userName=' + this.userName
      this.config.document.key = '95dffbefbbfb41f8'
      this.config.document.url = 'https://sas-online-tst.hengrui.com/sas_online/files/TEST_Khirz6zTPdfd8.docx'


962A73AD5CFE433C9A20A661578795D4


"token": "80AB9AD4C34641EA909F3FF4C85FD8C0",
    "tableid": "crf_handover",
    "formid": "34F2781C49984F13B221A394330CE811",

obj.studyid=(select obj2.id from Xsht as obj2 where obj2.studyid='HRS-4357-101')



String response = CDTMSAPI.usersyndataSave(token, "ecrf", formId, "<EMAIL>", "list", JSON.toJSONString(dbDefineDataList));



A1E904EB7D5E412DBFF3B6E97BD32A6E

859EE3B53DAF4C32BE33C3ED4A5DE1DA

crf_handover


https://meduap-tst.hengrui.com:8085/usersyn/upload?token=A973B3C253C74A9AA961060416296F92&formId=4296DF481D3D4C29A5FAD82754CA1289&tableid=crf_handover&fid=file&recordid=3569352713&fn=ecrf.zip

2024.09.09
1.
2.
3.


https://meduap-tst.hengrui.com:8085/usersyn/datalist?token=3832B863368E49429704807A69D748F4&tableid=crf_handover&type=edit&where=obj.studyid%3D%27HRS-4357-101%27and+obj.id%3D%273570827267%27



https://meduap-tst.hengrui.com:8085/remoteButtonTask/dataSave?taskId=B71514396C354C7CBCD5DFE6DF5699C2&formId=9D439BE3EF954966B91F32A4CE8A89A4&projectId={
    "studyid": "HRS-4357-101",
    "tableId": "crf_handover",
    "formData": {
        "notes": "test1",
        "receiver": "test1",
        "zq": 7
    }
}


CDTMSAPI.usersyndataSave(token, tableId, formId, "", "", formData.toString());





		String recordId="";
        String tableId="";
		if(!ObjectUtils.isEmpty(formInfo.get("recordId"))){
            recordId= formInfo.get("recordId");
            tableId=formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id",recordId);
            object.put("formData",formData);
            projectId=object.toJSONString();
        }else{
            tableId=projectId;
            recordId=taskId;
        }








python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='Query_Summary_for_Protocol_Violation' --env='PRO' --file_list='HRS-4357-101' --pro_test='val' --uuid='41fdef33cf3f6119' --data_format='Excel'

python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='data_set' --env='PRO' --file_list='HRS-4357-101' --pro_test='val' --uuid='41fdef33cf3f6119' --data_format='Excel'




周辉，获取质疑偏离和excel







python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='data_set' --env='UAT' --file_list='HRS-5635-101' --pro_test='val'  --data_format='SAS'




   //创建定期审核的记录
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "crf_handover");
        String formId = CDTMSAPI.getFormIdByToken("https://meduap-tst.hengrui.com:8085/", token);
        String studyId ="HRS-5635-101";
        //根据表单接口，查询对应的项目的studyId 整数值
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
        JSONObject json = new JSONObject();
        json.put("reviewer_name", "System");
        json.put("study_id", studyInt);
        json.put("zq", 6);
        //创建新审核记录
        String params = CDTMSAPI.usersyndataSave(token, "study_regular_review", formId, "", "", json.toString());
        String recordId=JSON.parseObject(params).get("id").toString();
        String result = CDTMSAPI.getDataListInfo(token, "study_regular_review", "obj.study_id='" + studyInt + "'" + "and obj.id='" + recordId + "'", "edit", "");
        //查询uuid
        String uuid = JSONArray.parseArray(result).getJSONObject(0).get("uuid").toString();


/home/<USER>/8087/schedule_node_info.sql

数据集密码录入
{}
|({})
| eClinical

https://sas-online-tst.hengrui.com/sas_online/files/HRS-5635-101_随机化与研究药物分配申请表模板.docx

getQueryReviewRecord
getTrailReviewRecord

{caseVersionNum=HRS-4357-101_V3.0, caseNum=V3.0, rtsmApplyVersion=V1.0, reviewer=李习, title=test, caseVersionDate=2024-08-06 00:00:00}

es2e1tC6aH_rA4VI7YE5mA
SsDKglZQO4Bgv5iuRhlw1Q






import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;

public class CronExpressionGenerator {

    public enum Frequency {
        WEEKLY,
        BIWEEKLY,
        HALF_MONTHLY
    }

    public static String generateCronExpression(LocalDate startDate, LocalDate endDate, LocalTime executionTime, Frequency frequency) {
        int minute = executionTime.getMinute();
        int hour = executionTime.getHour();
        int dayOfMonth = startDate.getDayOfMonth();
        int dayOfWeek = startDate.getDayOfWeek().getValue();

        String cronExpression;

        switch (frequency) {
            case WEEKLY:
                cronExpression = String.format("%d %d ? * %d", minute, hour, dayOfWeek);
                break;
            case BIWEEKLY:
                // For biweekly, we'll use the day of month and increment by 14 days
                cronExpression = String.format("%d %d %d/14 * ?", minute, hour, dayOfMonth);
                break;
            case HALF_MONTHLY:
                // For half-monthly, we'll use the 1st and 15th of each month
                if (dayOfMonth <= 15) {
                    cronExpression = String.format("%d %d 1,15 * ?", minute, hour);
                } else {
                    cronExpression = String.format("%d %d 15,L * ?", minute, hour);
                }
                break;
            default:
                throw new IllegalArgumentException("Unsupported frequency");
        }

        return cronExpression;
    }

    public static void main(String[] args) {
        LocalDate startDate = LocalDate.of(2023, 9, 20);
        LocalDate endDate = LocalDate.of(2023, 12, 31);
        LocalTime executionTime = LocalTime.of(9, 0); // 9:00 AM

        String weeklyCron = generateCronExpression(startDate, endDate, executionTime, Frequency.WEEKLY);
        String biweeklyCron = generateCronExpression(startDate, endDate, executionTime, Frequency.BIWEEKLY);
        String halfMonthlyCron = generateCronExpression(startDate, endDate, executionTime, Frequency.HALF_MONTHLY);

        System.out.println("Weekly Cron: " + weeklyCron);
        System.out.println("Biweekly Cron: " + biweeklyCron);
        System.out.println("Half-Monthly Cron: " + halfMonthlyCron);
    }
}

0 0 9 ? * 3, End Date = 2024-12-31






manual_rev_prog
manual_rev_prog


Map<String, String> formInfo
String studyId, String jsonPath
formInfo.get("studyId"), formInfo.get("jsonMinioPath"),formInfo.get("language")
ENVInfo.get("requestPrefix").toString();
ENVInfo.get("taskId").toString(), ENVInfo.get("projectId").toString()


 String result = CDTMSAPI.getDataListInfo(token, "study_regular_review", "obj.study_id='" + finalStudyId + "'" + "and obj.id='" + irecordId + "'", "edit", "");
            //查询uuid
            String uuid = JSONArray.parseArray(result).getJSONObject(0).get("uuid").toString();


getItem('studyId')   ===> localStorage.getItem('projectCode')


  var role
  if (localStorage.getItem('positionId')) {
        role = "rtsm";
      } else {
          role = "account";
     }
     
     
     
  
http://localhost:9528/?username=%E5%BC%A0%E4%B8%89&studyId=HRS-5635-101&role=account     

    
http://localhost:9528/#/onlyOffice?username=%E5%BC%A0%E4%B8%89&studyId=HRS-5635-101&role=account

measures






 function setReceiver(){
    var transferReason = '$form{transfer_reason}';
    var receiver;
    var receiverUnit;
	if('$form{transfer_reason}'=='1'){
		var receiver = '$link{cra,cra_name,obj.studyid=$form{studyid} and obj.limitnum='3',obj.cra_active_dt desc}';
		var receiverUnit = '临床运营部';
	}else if('$form{transfer_reason}'=='2'){
		var receiver = '$link{cra,cra_name,obj.studyid=$form{studyid} and obj.limitnum='5',obj.cra_active_dt desc}';
		var receiverUnit = '生物计量部';
	}
	
	return [receiver,receiverUnit];
}
setReceiver();












2024.09.25
1.随机申请表可新增空模板记录，审核绑定模板记录
2.随机申请表邮件通知需发送验证码，待与对应的审核记录绑定后，校验通过才能审核
3.
4.



2024.09.26
1.更新保存，手动保存
2.统计师页面保存之后，验证码无法同步问题
3.
4.


{
    "title": "test",
    "content": "test content",
    "receiver": "<EMAIL>"
}

  
   
    
      /   4545545asdasdasdas         
      
      
2024-09-29
1.rtsm_server6 weix5/weix@edc
               




function setMeasures(){
    var project_status = '$form{project_status}';
    var measures;
	if('$form{project_status}'=='00'){
		var measures = '附件是截至'+'$form{cdate}'+'数据导出的进展报告。摘要如下：\n① 访视缺失：共X个，\n② 页面缺失：共X条，\n③ 质疑：xxx条质疑未回复。CRA待关闭质疑xxx条。\n④ SDV%: 待核查变量共X个，\n以上内容烦请转发相关CRA督促尽快处理。';
	}else if('$form{project_status}'=='10'){
		var measures = '本月发送XX条质疑，关闭XX条质疑';
	}
	
	return [measures];
}
setMeasures();


00,定期进展报告;10,数据清理;13,方案违背;99,其他



http://************:8087/sas_online/getProgressReport

***********

rtsm_build6
select * from  tbl_project_info where  COL_STUDYID ='INS068-303'


193527811

select uploadfilename from tbl_randlist_193527811_dev;

select uploadfilename from tbl_cure_object_193527811_dev;


SELECT f.FILEPATH
FROM tbl_files_193527811_dev f
JOIN tbl_cure_object_193527811_dev c ON f.ID = c.uploadfilename;



SELECT f.FILEPATH
FROM tbl_files_193527811_dev f
JOIN tbl_randlist_193527811_dev c ON f.ID = c.uploadfilename;


    String getTableId(String studyId);


<select id="getTableId" parameterType="string" resultType="string">
        select ID from  tbl_project_info where  COL_STUDYID =#{studyId};
</select>


2722168832



    try {
            FileUtil.downloadFileromCDTMS(taskId, projectId, "rand_protocol_design_file", "_随机化计划_"+dateString+".docx");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }



1.完成随机化计划节点 随机盲底文件、药物盲底文件名回填文档列表内容
2.完成进展报告文档回填接口
3.新增随机化申请表手动保存逻辑
4.对接rtsm team uat节点文件获取逻辑，待催
5.邮箱发件历史需要 邮件服务器开通IMAP 或 pop3 协议


edc/cdtms API  minio 配置 数据库



随机盲底文件列表查询
1.查询studyId
select ID from  tbl_project_info where  COL_STUDYID ='INS068-303';  193527811
2.根据studyId 查询对应的文件名和路径

select REPORTFILEPATH ,
	REPORTFILENAME from tbl_report where STUDYID ='193527811' and STATUS ='导出成功' and env='dev' and REPORTNAME ='随机盲底比对报告' order by EXPORTTIME desc limit 1;
select REPORTFILEPATH ,
	REPORTFILENAME from tbl_report where STUDYID ='193527811' and STATUS ='导出成功' and env='dev' and REPORTNAME ='药物盲底比对报告' order by EXPORTTIME desc limit 1;
select REPORTFILEPATH ,
	REPORTFILENAME from tbl_report where STUDYID ='193527811' and STATUS ='导出成功' and env='dev' and REPORTNAME ='药物稽查轨迹报告' order by EXPORTTIME desc limit 1;
select REPORTFILEPATH ,
	REPORTFILENAME from tbl_report where STUDYID ='193527811' and STATUS ='导出成功' and env='dev' and REPORTNAME ='受试者稽查轨迹报告' order by EXPORTTIME desc limit 1;
select REPORTFILEPATH ,
	REPORTFILENAME from tbl_report where STUDYID ='193527811' and STATUS ='导出成功' and env='dev' and REPORTNAME ='运单稽查轨迹报告' order by EXPORTTIME desc limit 1;
	
	
	getMedBlindFilePath
	
	getMedTrailFilePath
	
	getSubjectTrailPath
	
	getWayBillTrailPath
	
	
	
// 连接到指定的服务器
        try {
            // 1、首先远程连接ssh
            SSHRemoteCall.getInstance().sshRemoteCallLogin(ipAddress, userName, password);
            // 打印信息
            System.out.println("0、连接***************,ip地址: " + ipAddress + ",账号: " + userName + ",连接成功.....");

            // 2、执行相关的命令
            // 查看目录信息
            // String command = "ls /home/<USER>/package ";
            // 查看文件信息
            // String command = "cat /home/<USER>/package/test ";
            // 查看磁盘空间大小
            // String command = "df -lh ";
            // 查看cpu的使用情况
            // String command = "top -bn 1 -i -c ";
            // 查看内存的使用情况
            String command = "free ";
            SSHRemoteCall.getInstance().execCommand(command);

            // 3、上传文件
            String directory = "/home/<USER>/package/poi.xlsx";// 目标文件名
            String uploadFile = "E:\\poi.xlsx";// 本地文件名
            SSHRemoteCall.getInstance().uploadFile(directory, uploadFile);

            // 4、下载文件
            // src 是linux服务器文件地址,dst 本地存放地址,采用默认的传输模式：OVERWRITE
            //test为文件名称哈
            String src = "/home/<USER>/package/test";
            String dst = "E:\\";
            SSHRemoteCall.getInstance().fileDownload(src, dst);

            // 5、刪除文件
            String deleteDirectoryFile = "/home/<USER>/package/test";
            SSHRemoteCall.getInstance().deleteFile(deleteDirectoryFile);

            // 6、展示目录下的文件信息
            String lsDirectory = "/home/<USER>/package";
            SSHRemoteCall.getInstance().listFiles(lsDirectory);

            // 7、关闭连接
            SSHRemoteCall.getInstance().closeSession();
        } catch (Exception e) {
            // 打印错误信息
            System.err.println("远程连接失败......");
            e.printStackTrace();
        }
        
        
        
        sshpass -p <EMAIL> scp root@***********:/home/<USER>/overlay2/f85e3a001c5604b4b0506e706450817b2bf88a2d45f4ac6b81e2ab1e120f6a53/merged/home/<USER>/data/02/93/CgoMLWWc91SAc-7NAAOekNOpi9g93.xlsx /home/<USER>/8087
        
        
         sshpass -p <EMAIL> scp -P 22 -r root@*********** /home/<USER>/overlay2/f85e3a001c5604b4b0506e706450817b2bf88a2d45f4ac6b81e2ab1e120f6a53/merged/home/<USER>/data/02/93/CgoMLWWc91SAc-7NAAOekNOpi9g93.xlsx /home/<USER>/8087
         
         
         
         

   @Override
    public void getRTSMUatReport(String taskId, String projectId) {
        //查项目编码
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId = "";
        String tableId = "";
        if (!ObjectUtils.isEmpty(formInfo.get("recordId"))) {
            recordId = formInfo.get("recordId");
            tableId = formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }
        String studyId = formInfo.get("studyId");
        //下载文件
        String filePath=RTSMAPI.downloadRTSMFile(studyId,"随机信息揭盲报告");
        File file = new File(filePath);
        if(file.exists()){
            //回传文件
            
        }

    }

         
10月工作总结：
1.对接rtsm-team-uat 报告获取需求，完成5种报告文件获取并回填cdtms逻辑的开发
2.完成rtsm模板文档自动回填表格数据接口逻辑
3.完成数据清理节点文件获取并回填cdtms的接口
4.更新rtsm随机申请表手动保存功能
5.定位并协助修复随机一致性检查接口调用sas程序的问题，并优化该接口代码逻辑
6.对接邮件历史记录获取逻辑，开发邮件发件记录获取逻辑

11月工作计划
1.完成rtsm team uat节点的文件回填接口测试与开发
2.对接随机生成器在线签字功能的需求，并开发相应的接口     
         
         
obj.checkarea='01' and obj.version_zt!='2'



		文件名*索引名|文件名2*索引名2|

        String studyIdNum = CDTMSAPI.getDataListInfo(paramA, "Xsht", "obj.studyid='" + studyid + "'", "edit", "");
        String studyId = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();

        com.alibaba.fastjson.JSONObject scheduleParam = new com.alibaba.fastjson.JSONObject();
        scheduleParam.put("study_id", studyInt);
        scheduleParam.put("reviewer_name", "System");


        //创建新审核记录
        String saveRecord = CDTMSAPI.usersyndataSave(token, "rtsm_jc", formId, "", "", scheduleParam.toString());




logo icon
Sider
Sider Fusion
GPT-4o
Sider Fusion



methods: {  
  triggerCtrlS() {  
    const event = new KeyboardEvent('keydown', {  
      key: 's',  
      ctrlKey: true,  
      bubbles: true  
    });  
    document.dispatchEvent(event);  
  }  
}  


html:

<v-btn class="mt-2 mb-2 mr-2" @click="triggerCtrlS">Save</v-btn>  





`




methods: {
  triggerSaveShortcut() {
    // Create and dispatch Ctrl+S keyboard event
    const event = new KeyboardEvent('keydown', {
      key: 's',
      code: 'KeyS',
      ctrlKey: true
    });
    document.dispatchEvent(event);
  },

  handleKeydown(event) {
    console.log('keydown fired', event);
  }
},

mounted() {
  // Add keydown event listener when component is mounted
  document.addEventListener('keydown', this.handleKeydown);
},

beforeUnmount() {
  // Clean up event listener when component is unmounted
  document.removeEventListener('keydown', this.handleKeydown);
}


:python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='Subject_CRF' --env='PRO'  --pro_test='val' --uuid='4f93d1d37bbf35ee' --data_format='zip' --second='YES'

40,LAB-AE;50,AE-EX-EOT;70,Recist1.1;00,自定义;

http://************:8087/sas_online/getLAB-AE
http://************:8087/sas_online/getAE-EX-EOT
http://************:8086/sas/getRecist1.1


01,RAVE数据集设定;03,影像学数据传输;30,药物安全数据一致性比对;40,LAB-AE;50,AE-EX-EOT;70,Recist1.1;85,随机化一致性检查;00,自定义;


testGetSpecificDataFromData

"checkarea":"03"

REMOTE_SERVER_API

String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "studyinfo");
        //1.2 get studyId
        String studyId = "HRS-5635-101";
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "manual_rev_prog", "obj.studyid=(select obj2.id from Xsht as obj2 where obj2.studyid='" + studyId + "') and obj.version_zt=4 and obj.checkarea="+"70", "", URLEncoder.encode("createtime desc"));
        JSONArray jsonArray = JSONArray.parseArray(dataListInfo);
        String tmp = jsonArray.get(0).toString();
        JSONObject jsonObject = JSONObject.parseObject(tmp);
        
        
        temp.put("crf_zt", "05");
        
        
CDTMSAPI.updateWorkFlowStatus(taskId, projectId, "crf_zt","05");
           
obj.studyid = '${studyid}' and obj.checkarea='${checkarea}' and obj.change_control_code = '${change_control_code}'
           
           
           
           
           
           
           
           
           
           
           
           
           
           
           
           
            //获取数据集日期
        Map<String,String> tagInfo= minioUtil.getObjectTags("raw", formInfo.get("studyId").toString() + "_sas.zip");
        String date=tagInfo.get("key3");
        String EDCDate = FileUtil.formatDate(date);


        //核查程序日期
        LocalDate currentDate = LocalDate.now();

        // Define the desired format
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // Format the date as a string
        String sasCheckDate = currentDate.format(formatter);



        String dataId = "";
        if (!data.isEmpty()) {
            cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(data);
            cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();
            dataId = formInfoData.get("id").toString();
            cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
            temp.put("id", dataId);
            temp.put("edc_data_dt", EDCDate);
            temp.put("edc_dataset_t", formInfo.get("studyId").toString() +"_sas.zip");
            temp.put("mr_plan_version_date", sasCheckDate);
            temp.put("manual_rev_prog_version", "AE-EX-EOT");
            params.put("data", temp);
            String newFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
            params.put("formId", newFormId);
            params.put("taskId", taskId);
            params.put("projectId", projectId);
            CDTMSAPI.dataSave(params);
        }
           
           
           
           
           
           
           
            
           exi|exi1
           exo
           
           
{
  "c": "forcesave",
  "key": "Khirz6zTPdfd7",
  "userdata": "sample userdata"
}
           
 2024.11.04
 1.
 2.
 3.
 4.
 
 
 
 nohup java -jar /home/<USER>/home/<USER>/medcodingDownload_$(date +%Y%m%d).log &
           
           
           
           
           SHR-1314-106_W-20240520-0013_既往及合并用药_20241105_1024_CN_EN.csv
           SHR-1314-106_M-20240520-0015_1._不良事件_20241105_1024_CN_EN.csv
           
           
           
           
           
           
           
1    
APP0183
BioKnowUAP      	
***********
************
8087
永久

2	
APP0183
BioKnowUAP
***********
************
8086
永久

3	
APP0183
BioKnowUAP
**********
***********
8087
永久

4	
APP0183
BioKnowUAP
**********
***********
8086
永久

5	
APP0183
BioKnowUAP
***********
************
8087
永久

6	
APP0183
BioKnowUAP
***********
************
8086
永久

           
           
           HRS-2023-0410-EN PRO SAS ************.zip
           
           
           百奥知cdtms业务功能需要调用远程API，申请开通百奥知cdtms服务生产环境访问***********：5000端口，测试环境访问************：5000、验证环境访问************：5000，模拟环境访问************：5000/8086/8087端口部署的远程API服务的网络权限
           
           SAS_EC_001_UAT_SAS_20241106_092000.zip
           
		String uuid = ULIDGenerator.generateULID();
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        ENVInfo.put("ENV", "UAT");
        ENVInfo.put("data_type", "data_set");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", taskId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", projectId);
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("data_format", "SAS");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfo.put("isLatest", "YES");
        ENVInfo.put("fileSuffix", "a".toString());
        String fileName = callPython.downloadEDCServerFile(ENVInfo, uploadFilesFromEDC);
        
        
             //7.upload the  sas output report to cdtms API
        Map<String, String> sasOutputFile = new HashMap<>();
        sasOutputFile.put("fid", "output");
        sasOutputFile.put("outputName", outputName);
        sasOutputFilesInfo.add(sasOutputFile);

        Map<String, String> comOutputFile = new HashMap<>();
        comOutputFile.put("fid", "output");
        comOutputFile.put("outputName", outputName);
        sasOutputFilesInfo.add(comOutputFile);
        
        
        
        drug_name":"","dose_level":"","factor":"","isadd":"","cohort":""
        
        
        
2024.11.11
1.修复随机申请表手动保存按钮失效，改为API调用
2.定位minio医学编码数据集下载文件名异常问题，更新文件下载文件名处理逻辑，修复导出的文件名中带有异常字符.1_
3.ecrf交接移除变更状态位、AE_EX_EOT节点设计、进行阶段edc数据集名称、时间获取逻辑修改
4.sas核查程序-随机一致性比对接口开发，新增运行比对按钮配置     


1.随机申请表的需求变动
2.在线签字流程熟悉，需求收集，开发文档熟悉

2024.11.13
1.study_partner_contacts
2.crf_handover/data_submission


http://************:8087/sas_online/getECRFTransferFile   

        
2024.11.18
1.更新数据清理质疑汇总报告中英文数据集下载逻辑，新增英文报告下载，默认先下载中文
2.随机一致性比对调整edc数据集获取逻辑,更新sas核查程序ae-eox ,recisit节点更新sas入口程序
3.数据导出与传输节点新增受试者crf和清单crf获取按钮，调整接口开发逻辑
4.定位进行阶段sas核查程序ae edc数据获取失败问题
5.外部数据供应商管理培训记录获取逻辑开发
6.fsfv,lslv




SAS核查程序测试
方案违背定义
eCRF填写指南
UAT测试
数据库上线质控
eCRF上线审批
eCRF上线与备份计划
EDC生产环境使用
实验室参考值
EDC修订计划
EDC修订确认
EDC修订报告
医学编码计划
外部数据管理计划
数据传输协议
外部数据传输测试        
        

        
        
https://clinical-esign-tst.hengrui.com/api/signs/task
playload
{"email":"************************************","task_id":"08750c15-7be4-4c1a-a0a2-b8c9d9444aa9","name":"周辉","signs":[{"sign_id":1131,"sign_type":3,"opacity":1,"position_x":378.********,"position_y":515.*************,"width":95,"height":38},{"sign_id":1132,"sign_type":3,"opacity":1,"position_x":477.75,"position_y":517.48125,"width":86,"height":35}],"sign_time":"2024/11/18 GMT+8 14:46:33","sign_reason":"我是该文件作者"}
response
{"status":200,"message":"success"}








        Map<String, String> studyInfo = CDTMSAPI.getStudyInfo("HRS-5635-101");
        //2.通过studyId查询该项目的统计师邮箱
        Map<String, String> rtsmAccountInfo = CDTMSAPI.getRTSMAccountEmail("HRS-5635-101");
        Map<String, String> rtsmAccountManagerInfo = CDTMSAPI.getRTSMAccountManagerEmail("HRS-5635-101");
        String accountName = rtsmAccountInfo.get("name");
        String accountManagerName = rtsmAccountManagerInfo.get("name");
		String templateFileName = "/home/<USER>/8087/onlyOfficeFile/随机化与研究药物分配申请表模板_随机+管药_队列研究_镜像替换_20241119模板.xlsx";
        String fileName = "/home/<USER>/8087/onlyOfficeFile/" + studyId + "_随机化与研究药物分配申请表.xlsx";
        Map<String, Object> map = MapUtils.newHashMap();
        map.put("title", studyInfo.get("title").toString());
        map.put("caseNum", studyInfo.get("caseNum").toString());
        map.put("caseVersionNum", studyInfo.get("caseVersionNum").toString());
        map.put("caseVersionDate", studyInfo.get("caseVersionDate").toString().substring(0, 10));
        map.put("accountName", accountName);
        map.put("versionNum", "V1.0");
        map.put("accountManagerName", accountManagerName);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // Parse current time to date
        LocalDate currentDate = LocalDate.now();
        String currentDateStr = currentDate.format(formatter);
        map.put("currentDate", currentDateStr);
        //获取表单信息
        String formResult = onlyOfficeFileMapper.getFormInfo(batchNum);
        //formResult 转换成json对象
        JSONObject jsonObject = JSONObject.parseObject(formResult);
        String applyType = jsonObject.get("applyType").toString();
        if (applyType.equals("1")) {
            map.put("applyType", "初始");
        } else if (applyType.equals("2")) {
            map.put("applyType", "扩展");
        }else if (applyType.equals("3")) {
            map.put("applyType", "修订/替换");
        }
        EasyExcel.write(fileName).withTemplate(templateFileName).sheet(0).doFill(map);





HRS-5635-101_119bfaf7f7dd58df.xlsx


https://sas-online-tst.hengrui.com/sas_online/files/record/HRS-5635-101_d5fb577dfd7f262d.xlsx



		Map<String, Object> map = MapUtils.newHashMap();
        //获取表单信息
        String formResult = onlyOfficeFileMapper.getFormInfo(batchNum);
        //formResult 转换成json对象
        JSONObject jsonObject = JSONObject.parseObject(formResult);
        String applyType = jsonObject.get("applyType").toString();
        if (applyType.equals("1")) {
            map.put("applyType", "初始");
        } else if (applyType.equals("2")) {
            map.put("applyType", "扩展");
        }else if (applyType.equals("3")) {
            map.put("applyType", "修订/替换");
        }
        EasyExcel.write(fileName).withTemplate(templateFileName).sheet(0).doFill(map);





{
    "account": "test",
    "applyType": "1",
    "levelFactor": "0",
    "medDesignNum": "3",
    "queueNum": "2",
    "rmdm": "1",
    "secondRand": "0",
    "studyDesign": "2",
    "subjectReplace": "1"
}



curl -X GET -H "Content-Type: application/json" http://************:8087/sas_online/addBatchRecord?studyId=HRS-5635-101&userName=周辉&applyDate=2024-11-22&param={"applyType":"1","rmdm":"1","levelFactor":"0","studyDesign":"2","secondRand":"0","subjectReplace":"1","account":"test","medDesignNum": "3","queueNum": "2"}




http://localhost:9528/?username=%E6%9D%8E%E5%9B%9B&studyId=HRS-5635-101&role=account&batchNum=c9bf286aaea95683#/dashboard



11月工作总结：
1.随机申请表更新手动强制保存接口
2.定位医学编码数据集下载文件名异常问题，更新代码逻辑，增加文件名异常字符处理逻辑
3.更新cdtms负责的sas核查程序节点的远程接口，更新edc数据集文件名及日期的获取逻辑，更新sas程序调用入口程序
4.cdtms sas核查程序节点新增随机一致性比对运行按钮及接口
5.更新数据清理质疑汇总报告中英文数据集下载逻辑，默认先下载中文
6.开发外部数据供应商管理培训记录获取逻辑，管理培训记录文件获取API待百奥知升级后提供
7.数据导出与传输节点新增受试者crf和清单crf获取按钮及接口
8.FSFV LSLV受试者信息同步获取时间字段排序更新，关联查询subject表获取中心名称
9.开发随机申请表参数录入接口,申请表封面信息回填方法,申请表模板随机队列、药物设计列表动态生成逻辑开发 


12月工作计划：
1.开发随机申请表模板生成接口，完成随机申请表新模板参数录入内容填充逻辑
2.开发并集成在线签字功能到随机生成器平台
3.完成外部数据供应商管理培训记录获取逻辑


上周
1.FSFV LSLV受试者信息同步时间获取字段更新，查询表替换为SV表，关联查询受试者信息表获取中心名称，
定位4357-101项目不在4.1.20版本范围,查询不到数据
2.更新随机一致性比对节点设计阶段和进行阶段的sas入口程序调用
3.数据清理接口获取质疑统计数，修改python接口调用方式,获取返回值，更新已完成数据管理-质疑审核汇总信息回填
4.随即申请表excel模板封面内容替换逻辑开发，参数录入接口开发，申请表内容根据表单录入参数动态增加空表逻辑开发
5.进展报告邮件模板配置 

https://sas-online-tst.hengrui.com/sas_online/files/record/HRS-5635-101_e5dff4f28b6e53b8.xlsx



模板文件加 批次号batchNum   

python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='Overall_Progress_Report' --env='PRO'  --pro_test='val'  --data_format='Excel'



HRS-4357-101_PRO_进展报告汇总_20241115_094235.xlsx

mc cp --attr "project=test;status=new" /path/to/local/file.txt minios3/bucket/path/

mc tag set minios3/datamanagement/report_online/HRS-4357-101_PRO_进展报告.xlsx "key1=2024-11-15&key2=HRS-4357-101_PRO_进展报告汇总_20241115_094235.xlsx"
mc cp -r --tags "key1=2024-11-15;key2=HRS-4357-101_PRO_进展报告汇总_20241115_094235.xlsx" ./HRS-4357-101_PRO_进展报告汇总.xlsx minios3/datamanagement/report_online/HRS-4357-101_PRO_进展报告.xlsx




        //查询ecrf设计与搭建 uat审核版的文件，将该文件上传到minio doc目录下

        cdtmsapi.uploadDBS(studyId);



在线签字:
https://clinical-esign-tst.hengrui.com/set-sign?file_id=c114354a-d05a-47ec-8cf1-64b3ea36ab00&task_id=7e87cc2e-6be2-445a-afca-b924748432fa





eec2e22f-6f16-45e3-b59a-8b6a447a8fdc

file_id:173d5912-68bf-4f95-9bed-440f7e04bb55
task_id: 574abfac-4fd7-4fd2-a10f-9965da6db0bb
id:1106 1107


https://clinical-esign-tst.hengrui.com/set-sign?file_id=d419aac3-d6ca-4c76-812f-fd3105da9848&task_id=a063322f-f0c4-480b-b4e8-a3643c51e83f

https://clinical-esign-tst.hengrui.com/set-sign?file_id=173d5912-68bf-4f95-9bed-440f7e04bb55&task_id=29363d3e-3ae0-47b7-af8d-453e559e81df




https://clinical-esign-tst.hengrui.com/?task_id=29363d3e-3ae0-47b7-af8d-453e559e81df&email=************************&verification_code=719818


签字页面:
https://clinical-esign-tst.hengrui.com/?task_id=29363d3e-3ae0-47b7-af8d-453e559e81df&email=************************&verification_code=123456

{"output_url":"https://clinical-esign-tst.hengrui.com/resource/output/20241127/801/45319e70-9fab-4dc4-bbd2-a4361039967e.pdf","sign_status":1,"status":4,"task_id":"29363d3e-3ae0-47b7-af8d-453e559e81df","type":2,"updated_at":1732697863}

		// 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 创建日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String currentDateStr = currentDate.format(formatter);
        
        
        
        
附件是截至2024-11-26数据导出的进展报告。摘要如下：
① 访视缺失：共X个，
② 页面缺失：共X条，
③ 质疑：xxx条质疑未回复。待关闭质疑xxx条。
④ 待SDV: 待核查变量共X个，
以上内容烦请转发相关CRA督促尽快处理。






 //下载统计数文件
 minioUtil.downloadObjectWithPath("datamanagement", "output/HRS-4357-101_metrix_return.xlsx", "C:\\Users\\<USER>\\Downloads");
 //解析统计数文件
 String result ="";
        try {
            // Specify the path to your Excel file
            String excelFilePath ="C:\\Users\\<USER>\\Downloads\\HRS-4357-101_metrix_return.xlsx";
            FileInputStream inputStream = new FileInputStream(new File(excelFilePath));

            // Create Workbook instance from excel file
            Workbook workbook = new XSSFWorkbook(inputStream);

            // Get first sheet
            Sheet sheet = workbook.getSheetAt(0);

            // Read first 5 rows
            for (int rowIndex = 0; rowIndex < 5; rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    // Iterate through each cell in the row
                    for (Cell cell : row) {
                        result += cell.getStringCellValue() + "\t";
                     //   System.out.print(cell.getStringCellValue() + "\t");

                    }

                }

            }
            System.out.println(result);
            // Close workbook and stream
            workbook.close();
            inputStream.close();

        } catch (IOException e) {
            e.printStackTrace();
        }
        // Method 1: Using regex to find all numbers
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(result);

        List<Integer> numbers = new ArrayList<>();
        while (matcher.find() && numbers.size() < 5) {
            numbers.add(Integer.parseInt(matcher.group()));
        }

        // Print results
        System.out.println("Extracted numbers: " + numbers);
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 创建日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String currentDateStr = currentDate.format(formatter);
        String resultStr="附件是截至"+currentDateStr+"数据导出的进展报告。摘要如下：\n" +
                "① 访视缺失：共"+numbers.get(0)+"个，\n" +
                "② 页面缺失：共"+numbers.get(1)+"条，\n" +
                "③ 质疑："+numbers.get(2)+"条质疑未回复。待关闭质疑"+numbers.get(3)+"条。\n" +
                "④ 待SDV: 待核查变量共"+numbers.get(4)+"个，\n" +
                "以上内容烦请转发相关CRA督促尽快处理。";
        System.out.println(resultStr);
        
        
        
        
        
2024.12.02
1.随机申请表模板记录编辑、删除接口开发，新增随机申请表列表回填参数,随机申请表联调
2.ae-ex-eot recist 随机一致性比对新增dbs文件获取上传minio逻辑
3.调整进展报告获取逻辑,将下载的edc报告文件上传到minio并tag文件日期,更新ec编程与测试状态位值
4.本地验证在线签字API，在线签字接口文档实现，根据文档接口流程实现签字功能，开发API工具类
5.进展报告统计数文件获取解析逻辑开发，从sas输出的excel文件中获取最终统计数
6.设计阶段sas核查程序测试-影像学数据传输/进行阶段IRC数据传输 数据集和输出文件的加上日期，已完成数据管理工作-数据清理调整python调用的传参，日期范围90天

本周计划：
1.外部数据供应商-外部数据管理培训记录
2.DVS节点 数据核查说明 excel内容回填
3.在线签字API工具类





SHR8735-116_外部数据管理培训记录_Jiangsu Hengrui(CRO)_TEST_2024-11-12.docx*202411DDA888AFD2E643078A86A7A0E74C3E2B.docx|
	


查不到edc数据集不调用sas程序


nohup java -jar /home/<USER>/home/<USER>


SHR-A1904-I-101

/home/<USER>/8087/onlyOfficeFile/SHR-8068-201-BTC_随机化与研究药物分配申请表_封面1.xlsx


edc_dataset_t

PriorCancerSystemicTherapy[1],PriorCancerSystemicTherapy[2],PriorCancerSystemicTherapy[3],PriorCancerSystemicTherapy[4],PriorCancerSystemicTherapy[5],PriorCancerSystemicTherapy[6]
Follow-UpCancerSystemicTherapy[1],Follow-UpCancerSystemicTherapy[2],Follow-UpCancerSystemicTherapy[3]
PriorCancerSystemicTherapy[1],PriorCancerSystemicTherapy[2],PriorCancerSystemicTherapy[3],PriorCancerSystemicTherapy[4],PriorCancerSystemicTherapy[5],PriorCancerSystemicTherapy[6],PriorCancerSystemicTherapy[7],PriorCancerSystemicTherapy[8]




{"data":{"createtime":"2024-08-19 11:14:21","modifyuserid":"126844929","center":"CN001","userid":"1","uuid":"CB19E1D6076F471A90CE8EA41812FE86","zq":"6","name":"SuperSA","unitid":"","studyid":"2463039488","lastmodifytime":"2024-08-30 18:01:07","id":"3525050369","dept_id":"12","subject_identification":"23232","visit_date":"2024-08-20 00:00:00"},"user":{"loginId":"<EMAIL>","userid":"2378629120","username":"周辉"},"projectId":"cdtmsen_val","tid":"lslv"}

"and obj.limitnum='TDM' and  obj.active='1' "

2378629120


python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='DB_Definition' --env='UAT'  --pro_test='val'  --data_format='Excel'

1,医学总监;10,药品管理员;11,EDA;12,药理经理;13,稽查组长;14,统计师总监;15,统计编程总监;16,药理总监;17,转化医学;2,医学经理;21,CRO DM;3,项目经理;4,CRA;5,统计师;6,统计编程经理;7,药物安全警戒人员;8,CTA;9,项目总监

00,定期进展报告;10,数据清理;13,方案违背
	shzt   00

http://************:8087/sas_online/getDVS

D：	status
00,草稿;98,TDM已退回;05,等待TDM审批;10,已锁定


python3.9 /home/<USER>/8087/DVS/xlsxConcatenate.py --source_file='SHR-1703-201_UAT' --output_file='test'


 python3.9 /home/<USER>/8087/DVS/xlsx2xlsx.py --source_file='116数据核查说明_V1.001' --target_file='116数据核查说明_V1.002' --sheet_name='人工核查'
 
 
 
 
 python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='Logic_Check_Setting' --env='UAT'  --pro_test='val'  --data_format='Excel'
 
 
 
 
 SHR-1703-201_V1.002
 SHR-1703-201_V1.002.xlsx
 
 
  String command="python3.9 /home/<USER>/ecrf_guide_fill/ecrf_guide_fill.py  --studyid='" + ENVInfo.get("studyId")+"'"
                            +" --filename='" + name +  "'";
 
 
 
 "python3.9 /home/<USER>/8087/DVS/xlsxConcatenate.py  --source_file='" + EDCFN+"'"
                +" --output_file='" + studyId+"_"+bbh+"'";
 
 
 
 runPyBak
 
 
 python3.9 ./xlsx2xlsx.py --source_file='SHR-1703-201_V1.002_last' --target_file='SHR-1703-201_V1.003' --sheet_name='人工核查'
 
 
 研究中心名称
 {访视阶段序号=15, 受试者状态=已入组, 访视缺失距今天数=85, 研究中心名称=第二军医大学长海医院, 项目代码=HR-BLTN-III-EBC, 预期随访日期=2023/8/29, 研究中心编号=2, 访视阶段=随访期V2-2, 受试者代码=2010, close=2023/9/26, open=2023/7/26, visitnum=15}
 
 
 
 
 版本号从0.001 开始
 定稿 1.0是
 
 'https://sas-online-tst.hengrui.com/sas_online/files/SHR-8068-201-BTC_77fbffef3edfca2d_随机化与研究药物分配申请表.xlsx'
 
 
 
 
 1.周四前 统计服务器 硬件 情况， 扩容硬盘或内存
 2.
 3.
 
 REMOTE_SERVER_API_PREFIX
 
 SASOnlieConstant.REMOTE_SERVER_API          FileUtils.getCDTMSAPI(server)  
 SASOnlieConstant.REMOTE_SERVER_API_PREFIX   FileUtils.getCDTMSAPIPre(server)
 SASOnlieConstant.REMOTE_SERVER_PROJECTID
 
 cdtmsen_val

FileUtils.getCDTMSAPI(server);
FileUtils.getCDTMSAPIProjectId(server);
FileUtils.getCDTMSAPIPre(server);


	
V1.001
V1.1
V1.001D



模拟环境：https://cdtms-val.hengrui.com/home.jsp?projectid=cdtmsen_val2&msgcode=01
账号：sa/HRlc20220506

https://clinical-esign-tst.hengrui.com/set-sign?file_id=093f4ca1-f3f5-4e70-b04b-8f9ab3ef088a&task_id=fd9a9acc-accc-4f54-a966-8059d76d7265
./cursor-vip.exe

 String dataListInfo = CDTMSAPI.getDataListInfo(token, "xsht", "obj.studyid='" + studyId + "'", "", "");

  String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "manual_rev_prog", "obj.studyid=(select obj2.id from Xsht as obj2 where obj2.studyid='" + studyId + "') and obj.version_zt=2", "", URLEncoder.encode("createtime desc"));
        
        
        https://tarly.aoscdn.com
      f 59f7ec5e-c31a-4232-8f66-5a25dc600f0f
      t 3934e78d-1e58-48f9-aa19-9e0c4bec8789
      user  37
        
https://clinical-esign-tst.hengrui.com/set-sign?file_id=154667f7-66cf-4f7f-93e4-8e71dcc27c00&task_id=f82264a1-ba7a-45b5-b171-fa519441c859



https://clinical-esign-tst.hengrui.com/set-sign?file_id=871282fc-5ebe-4e6b-872f-d5cd60e25e5e&task_id=8394e8b5-acaa-4dbf-9e12-8f3fab64fea2












签字调整页面:
https://clinical-esign-tst.hengrui.com/?task_id=8394e8b5-acaa-4dbf-9e12-8f3fab64fea2&email=************************************&verification_code=669051

签字拦截页面:https://meduap-tst.hengrui.com:8082/lightpdfSign.checkIsLogin.do?token=BE45AB69964A422DAD167162A40DC29C&url=https://meduap-tst.hengrui.com:8085&rspsystem=cdtmsen_val&system=clinicalinte


python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='DB_Definition' --env='UAT'  --pro_test='val'  --data_format='Excel'




"meduap-tst"
"cdtms-val"




表edc_name

EDC API 请求头对应的信息
meduap 环境下：
4.1.0：84
4.1.8：88
4.1.20：89




生产（clinical）环境：
4.1.0：88
4.1.8：89
4.1.20：91


python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='Logic_Check_Setting' --env='UAT'  --pro_test='val'  --data_format='Excel'



https://sas-online-tst.hengrui.com?tableid=manual_rev_prog&pageuuid=13887D68F6A4448994B972F911E87CE5&recordid=3623878659&readonly=&refinfo=studyid__2463039488&defvalue=zq%3A5%3B&menuid=5_7&where=&hiddenBtns=&entrytid=xsht&entryrid=2463039488&readonlyfields=&unique=&selids=3623878659&taskId=3850731533&server=https%3A%2F%2Fmeduap-tst.hengrui.com%3A8085&projectId=cdtmsen_val



已完成数据管理工作



a1a149621639462181a42f0aa8e9d5b9


https://meduap-tst.hengrui.com:84



and obj.studyid in (select obj2.id from Xsht obj2 where obj2.id = obj.studyid and obj2.server_cluster like '%4.1%')



1.随机申请表版本号逻辑更新 
2.SMO访视却表数据筛选导出接口逻辑开发
3.新增cdtms环境动态变更请求url和projectid及edc 接口的请求地址
4.更新数据核查说明封面字体格式为宋体 ，更新数据核查说明节点获取上一版本文件的逻辑，改为获取最近一次的DVS文件，修改文件名截取逻辑修复bug
6.


python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='Subject_CRF' --env='PRO'  --pro_test='val'  --data_format='zip' --second='YES'











found the file name is 2024120FA9F0E58270443A819B81EAECACB179.zip



found the file name is 202412930E9D3F5FF049E78A51BE9AE29DB832.xlsx


Zip file created at: /home/<USER>/8087/ecrf_list/HR091506-302_death_ae_disc_受试者CRF_20241210.zip


上传的本地文件是:/home/<USER>/8087/ecrf_list/HR091506-302_death_ae_disc_受试者CRF_20241210.zip


待解压缩文件的路径为:/home/<USER>/8087/sas_call_logHR091506-302_PRO_受试者病例报告表_20241210_133003_无稽查历史.zip压缩好的文件存放地址为:/home/<USER>/8087/ecrf_listxlsx文件路径为:/home/<USER>/8087/sas_call_logdeath_ae_disc_list(2).xlsx
















[{"server":"","jszd":"","subject_crf_path":"","notes":"","data":"","data_version":"02","userid":"1","uuid":"9407C50F0DF34C09AC9328DFAA79C47A","mode":"3","path":"","file":"","subject_crf":"HR091506-302_PRO_受试者病例报告表_20241210_133003_无稽查历史.zip*2024120FA9F0E58270443A819B81EAECACB179.zip|","nda_or":"2","subject_list":"death_ae_disc_list(2).xlsx*202412930E9D3F5FF049E78A51BE9AE29DB832.xlsx|","oda_id":"","bz":"","studyid":"2346123264","id":"3849486344","recipient_date":"","mode_1":"","createtime":"2024-12-12 15:47:53","modifyuserid":"2","if_equal":"","author":"SuperSA","cssjlx":"6","application_form_files":"","email_file":"","subject_list_2":"","jsdw":"","submit_dsname":"","zq":"7","recipient":"","submit_dsformat":"3","unitid":"","shzt":"00","lastmodifytime":"2024-12-16 09:46:25","subject_crf_audit_trial":"HR091506-302_PRO_受试者病例报告表_20241210_142305_稽查历史.zip*20241275EA84F306A34F29B8BB7B387CC0EB07.zip|","rq":"2024-12-12 00:00:00"}]
2024-12-16 09:46:25.289  INFO 666165 --- [io-8087-exec-10] c.h.b.e.s.impl.ECRFTransferServiceImpl   : [{"server":"","jszd":"","subject_crf_path":"","notes":"","data":"","data_version":"02","userid":"1","uuid":"9407C50F0DF34C09AC9328DFAA79C47A","mode":"3","path":"","file":"","subject_crf":"HR091506-302_PRO_受试者病例报告表_20241210_133003_无稽查历史.zip*2024120FA9F0E58270443A819B81EAECACB179.zip|","nda_or":"2","subject_list":"death_ae_disc_list(2).xlsx*202412930E9D3F5FF049E78A51BE9AE29DB832.xlsx|","oda_id":"","bz":"","studyid":"2346123264","id":"3849486344","recipient_date":"","mode_1":"","createtime":"2024-12-12 15:47:53","modifyuserid":"2","if_equal":"","author":"SuperSA","cssjlx":"6","application_form_files":"","email_file":"","subject_list_2":"","jsdw":"","submit_dsname":"","zq":"7","recipient":"","submit_dsformat":"3","unitid":"","shzt":"00","lastmodifytime":"2024-12-16 09:46:25","subject_crf_audit_trial":"HR091506-302_PRO_受试者病例报告表_20241210_142305_稽查历史.zip*20241275EA84F306A34F29B8BB7B387CC0EB07.zip|","rq":"2024-12-12 00:00:00"}]




cdtms_sas_online-0.0.1-SNAPSHOT.jar


home/sas-online/8087/sas_call_log/death_ae_disc_list(2).xlsx


/home/<USER>/8087/sas_call_log/HR091506-302_PRO_受试者病例报告表_20241210_133003_无稽查历史.zip

202412695FC68055C04D87B486CCF7EF39DB1F.zip

待解压缩文件的路径为:/home/<USER>/8087/sas_call_log/HR091506-302_PRO_受试者病例报告表_20241210_133003_无稽查历史.zip压缩好的文件存放地址为:/home/<USER>/8087/ecrf_listxlsx文件路径为:/home/<USER>/8087/sas_call_log/death_ae_disc_list(2).xlsx


/home/<USER>/8087/ecrf_list/HR091506-302_death_ae_disc_受试者CRF_20241210.zip


<EMAIL> Zh12345678 Zh12345678
<EMAIL>
clinical-ftp.hengrui.com
clinical-ftp.hengrui.com
clinical-ftp.hengrui.com
/home/<USER>/8087/sas_call_log/HR091506-302_PRO_受试者病例报告表_20241210_133003_无稽查历史.zip



/home/<USER>/8087/sas_call_log/death_ae_disc_list(2).xlsx

/home/<USER>/8087/ECRF/death_ae_disc_list(2).xlsx
/home/<USER>/8087/ECRF/HR091506-302_PRO_受试者病例报告表_20241210_133003_无稽查历史.zip

python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='Logic_Check_Setting' --env='UAT'  --pro_test='val'  --data_format='Excel'

role='项目经理' or role='盲态项目经理'



{"server":"","jszd":"","subject_crf_path":"","notes":"","data":"","data_version":"Submitted","userid":"SuperSA","uuid":"DCC3A06518E84DF28B66505C4BADF511","mode":"sFTP","path":"","file":"","subject_crf":"HR091506-302_PRO_受试者病例报告表_20241210_133003_无稽查历史.zip","nda_or":"是","subject_list":"death_ae_disc_list.xlsx","oda_id":"","bz":"","studyid":"HR091506-302","id":"3862560768","recipient_date":"","mode_1":"","createtime":"2024-12-16 11:04:39","modifyuserid":"SA","if_equal":"","author":"SuperSA","cssjlx":"受试者CRF","application_form_files":"","email_file":"","subject_list_2":"HR091506-302_death_ae_disc_受试者CRF_20241210.zip","jsdw":"","submit_dsname":"","zq":"7","recipient":"","submit_dsformat":"PDF","unitid":"","shzt":"未提交","lastmodifytime":"2024-12-16 15:33:05","subject_crf_audit_trial":"HR091506-302_PRO_受试者病例报告表_20241210_142305_稽查历史.zip","rq":"2024-12-16"}

https://meduap-tst.hengrui.com:84/




SUBJID -> CN001002

SUBJID -> CN001001








python3.9 /home/<USER>/8087/rtsm_file/pythonDemo/demo.py --parameters='{
    "study_type": "A",
    "need_randomization": false,
    "administration_method": "oral",
    "visit_frequency": 3
}' --file_path='/home/<USER>/8087/rtsm_file/pythonDemo/demo.xlsx'





curl -X GET -H "Content-Type: application/json" "http://**********:8087/sas_online/getSMOProjectLan?studyId=SHR0302-303"

curl -X POST -H "Content-Type: application/json" 
-d '{
    "siteNum": [
        "40",
        "10"
    ],
    "fileName": "SHR0302-303_未回复质疑.csv",
    "type": "CH"
}' "http://**********:8087/sas_online/getSMODataTst"



if(edcFileName == null||edcFileName.isEmpty()){
            //没有下载到数据集，使用表单上传的附件，上传到minio raw下,tag  example env:uat, key1:RAVE, key2:SHR-2010-201, key3:2024/09/27/13/17
        }


randCompareCheck
submitToAEXOTSAS
submitToRecistSAS




02:13;70:23,6,7,8,25;30:9,15,16,6,7,8,25;50:11,12,6,7,8,25;80:14;85:10,19,20,21,6,7,8,25;00:17;03:24,6,7,8,25;40:6,7,8,25


python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='data_set' --env='UAT' --file_list='HRS9531-201' --pro_test='val' --uuid='41fdef33cf3f6119' --data_format='Excel'
python3.9 /home/<USER>/edc_to_minio/edc_to_minio.py --data_type='data_set' --env='UAT' --file_list='HRS-4357-101' --pro_test='val' --uuid='19b3eefffe0b56e4' --data_format='Excel'


02:14;70:11,24,6,7,8,26;30:9,16,17,6,7,8,26;50:11,12,13,6,7,8,26;80:15;85:11,10,20,21,22,6,7,8,26;00:18;03:25,6,7,8,26;40:6,7,8,26


随机号规则 J45
药物号规则  D55
随机号格式  D10 D18 D26 D36 D46  Z4


and obj.studyid in (select obj2.studyid from Edc_name obj2 where obj2.studyid=obj.studyid and obj2.edc = 'TAU') 


obj.studyid in (select obj2.studyid from Edc_name obj2 where obj2.studyid=obj.studyid and obj2.edc = 'TAU')  and obj.dvs_doc is null

CDTMSAPI.updateWorkFlowStatus(taskId,projectId,"status","10");
submitToDMStageSAS
getProgressReport
submitToDMDateClean


account  项目统计师  字符
applyType 申请类型   1初始 2扩展 3修订/替换
rmdm 管理类型   1随机分配管理  2药物供应管理  3随机分配+药物供应管理
medDesignNum 几种药物号设计类型  数字
studyDesign 研究设计    1平行对照  2队列研究 3阶段研究 4其他
queueNum 队列数量       数字
levelFactor 有分层因素   1是 0否
levelNum 分层因素数     数字
subjectReplace 受试者替换  1倒序替换 2镜像替换 3不替换
secondRand 是否二次随机     1是 0否
firstRandGroup  是否按一次随机组别分层  1是 0否
secondRandLevel  二次随机有分层因素    1是 0否
secondRandLevelNum 二次随机分层因素层数    数字
secondRandDesignType 几种二次随机号设计类型   数字




1 修改涉及的edc和rtsm api 动态变更逻辑 x
2 申请开通rtsm 报告下载的账号权限  x 等百奥知解决
3 跟踪电子签回调接口 开通外网权限 x 直接部署到模拟环境
4 随机申请表动态加载逻辑











ALTER TABLE rtsm_apply  MODIFY apply_date text not  NULL;



taskId is:3933470722


projectid is:cdtmsen_val




      

http://127.0.0.1:8087/sas_online/getLoginInfo?taskId=3933470722&projectId=null

     



 int insertRaveDataUploadRecord(String id, String userName, String studyId, String originalFilename, String fileLocalPath, String fileMinioPath);



    INSERT INTO rave_data_set_upload_record
        (id,file_name,user_name,role,project_id,file_path,minio_path)
        values
        (#{id},#{originalFilename},#{userName},#{userName},#{studyId},#{fileLocalPath},#{fileMinioPath})




SHR-8068-201-GC-TEST_sas.zip

影像学数据传输 LAB-AE核查工具 AE-EX-EOT工具 RECIST1.1工具    随机化一致性检查

submitToRecistSAS
submitToAEXOTSAS
getIRCData
randCompareCheck

回填的拿到的参数是:{"items":[{"email":"************************************","name_en":"zs","name_zh":"张三"}],"task_id":"951b88da-0150-4751-9ac3-95c5de2aae6a","type":3}

fileId:  07adeb21-cefb-45dc-8adb-c42b90965c3e
url: https://clinical-esign-val.hengrui.com/resource/files/20241225/574cf635-c566-4c53-90ac-1c94fcbeae60.pdf

  "task_id": "951b88da-0150-4751-9ac3-95c5de2aae6a"
  
  {"items":[{"email":"************************","name_en":"ls","name_zh":"李四"}],"task_id":"951b88da-0150-4751-9ac3-95c5de2aae6a","type":3}
  
  

"task_edit_url": "https://clinical-esign-val.hengrui.com/set-sign?file_id=07adeb21-cefb-45dc-8adb-c42b90965c3e&task_id=951b88da-0150-4751-9ac3-95c5de2aae6a"

 "id": 3
 
 
 https://tarly.aoscdn.com/api/tasks/3934e78d-1e58-48f9-aa19-9e0c4bec8789
 
 https:/clinical-esign-val.hengrui.com/api/tasks/951b88da-0150-4751-9ac3-95c5de2aae6a
 
 
 
 {"items":[{"email":"************************","name_en":"ls","name_zh":"李四"}],"task_id":"e23c4799-c523-4313-8196-e841331ea599","type":3}
 
 
 
 签署地址： https://clinical-esign-tst.hengrui.com/?task_id=任务id&email=base64加密邮箱&verification_code=文件码
 https://clinical-esign-tst.hengrui.com/?task_id=a523fc1c-15ef-4054-b030-1399f986ce0d&email=************************************&verification_code=553894
 
 
 
 https://clinical-esign-val.hengrui.com/set-sign?file_id=eea2f005-d5db-4113-a116-00c64225f642&task_id=e23c4799-c523-4313-8196-e841331ea599
 
  https://clinical-esign-val.hengrui.com/?task_id=e23c4799-c523-4313-8196-e841331ea599&email=************************&verification_code=123456
  
  
  
  
  
  https://clinical-esign-val.hengrui.com/?task_id=d5803b5d-f50c-48bf-849e-d8aba05c53c5&email=************************&verification_code=123456
  
  
  
  
  签完字：{"email":"************************","reason":"我是审阅","sign_time":"2024/12/26 GMT+8 10:07:30","task_id":"e23c4799-c523-4313-8196-e841331ea599","type":1,"updated_at":1735178940}
  {"error_msg":"调用签字程序执行shell失败","sign_status":-1,"status":4,"task_id":"e23c4799-c523-4313-8196-e841331ea599","type":2,"updated_at":1735178940}
  
  https://clinical-esign-val.hengrui.com/set-sign?file_id=60330e0a-55ea-4bd2-8cc0-9aa3544a9d70&task_id=b67db4c8-b84d-4d9e-9baa-fb6834dfe951
  
  
  https://clinical-esign-val.hengrui.com/?task_id=b67db4c8-b84d-4d9e-9baa-fb6834dfe951&email=************************&verification_code=123456
  
  
  
  
  
  
  
  
  SAS_LABAE_CODE_PATH
  
  
  
  
  
  
nohup java -jar /home/<USER>




https://clinical-esign-val.hengrui.com/?task_id=d1bed0c6-9685-494e-a02a-f3db52650647&email=************************&verification_code=123456









curl http://**********:8089/medcoding_download/getMedCodingFile?taskId=1&server=12312&projectId=1231231


curl -X GET -H "Content-Type: application/json" "http://**********:8087/sas_online/getSMODataTst"


CREATE DATABASE `medcoding_pro` 
DEFAULT CHARACTER SET = utf8mb4 
DEFAULT COLLATE = utf8mb4_unicode_ci;



CREATE TABLE `medcoding_big_file_md5` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `file_name` VARCHAR(255) DEFAULT NULL,
  `md5_value` TEXT DEFAULT NULL,
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



3955523585
cdtmsen_val


curl -X GET -H "Content-Type: application/json" "**********:8089/medcoding_download/getMedCodingFile?taskId=3955523585&server=https://meduap-tst.hengrui.com:8085&projectId=cdtmsen_val"



nohup java -jar /home/<USER>/test-0.0.1-SNAPSHOT.jar > java.log


nohup java -jar blind_back-0.0.1-SNAPSHOT.jar >log.txt &

 mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/json_blind /home/<USER>/ex_json_file -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root
 mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/csv_raw /home/<USER>/ex_original_csv -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root
 mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log /home/<USER>/blind_sas_log -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root
 mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/ /home/<USER>'zhouh36',password='Zh123456',gid=root,uid=root
 mount.cifs //SHNVWSASECT01/EDMtst /home/<USER>/ -o file_mode=0666,dir_mode=0770,username='zhouh36',password='HR9cf3cbd8',gid=root,uid=root
 mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log /home/<USER>/blind_sas_log -o file_mode=0666,dir_mode=0770,username=zhouh36,password=Zh123456,gid=root,uid=root
 mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log /home/<USER>/blind_sas_log -o file_mode=0666,dir_mode=0770,username=zhouh36,password=HR9cf3cbd8,gid=root,uid=root
 mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/json_blind /home/<USER>/ex_json_file -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root
 mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/csv_raw /home/<USER>/ex_original_csv -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root
  mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log /home/<USER>/blind_sas_log -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root
 mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/ /home/<USER>'zhouh36',password='Zh123456',gid=root,uid=root
 mount.cifs //SHNVWSASECT01/EDMtst /home/<USER>/ -o file_mode=0666,dir_mode=0770,username='zhouh36',password='HR9cf3cbd8',gid=root,uid=root
 mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log /home/<USER>/blind_sas_log -o file_mode=0666,dir_mode=0770,username=zhouh36,password=Zh123456,gid=root,uid=root
 mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log /home/<USER>/blind_sas_log -o file_mode=0666,dir_mode=0770,username=zhouh36,password=HR9cf3cbd8,gid=root,uid=root
   mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/json_blind /home/<USER>/ex_json_file -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root
  mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/csv_raw /home/<USER>/ex_original_csv -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root
   mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log /home/<USER>/blind_sas_log -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root
    mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/ /home/<USER>'zhouh36',password='Zh123456',gid=root,uid=root
   mount.cifs //SHNVWSASECT01/EDMtst /home/<USER>/ -o file_mode=0666,dir_mode=0770,username='zhouh36',password='HR9cf3cbd8',gid=root,uid=root
   mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log /home/<USER>/blind_sas_log -o file_mode=0666,dir_mode=0770,username=zhouh36,password=Zh123456,gid=root,uid=root
    mount.cifs //**********/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log /home/<USER>/blind_sas_log -o file_mode=0666,dir_mode=0770,username=zhouh36,password=HR9cf3cbd8,gid=root,uid=root
    
    
    
2025.01.06
https://clinical-esign-val.hengrui.com/?task_id=d5803b5d-f50c-48bf-849e-d8aba05c53c5&email=************************&verification_code=123456
https://clinical-esign-val.hengrui.com/?task_id=d5803b5d-f50c-48bf-849e-d8aba05c53c5&email=************************&verification_code=123456


https://clinical-esign-val.hengrui.com/?task_id=1aa5ca45-0bf5-496b-b6cd-058c85362dc6&email=************************************&verification_code=123456




eCRF上线审批 表名称修改为“EDC上线审批”；eCRF上线与备份计划 表名称修改为“EDC上线与备份计划”





https://clinical-esign-val.hengrui.com/resource/output/20250106/21/1b13db16-0c1b-4c43-9042-55ecd26dd596.pdf


https://clinical-esign-val.hengrui.com/resource/output/20250106/21/1b13db16-0c1b-4c43-9042-55ecd26dd596.pdf



http://localhost:9530/#/dashboard?taskId=3965059073&projectId=cdtmsen_val&server=https%3A%2F%2Fmeduap-tst.hengrui.com%3A8085

宾文申请域名绑定 ：************:9530 绑定https://sas-online-rave-tst.hengrui.com
                 ***********:9530 绑定https://sas-online-rave-pro.hengrui.com
                 ************:9530 绑定https://sas-online-rave-val.hengrui.com
                 
                 
                 
                 
                 
                 
    
<repositories>
    <repository>
        <id>com.e-iceblue</id>
        <url>https://repo.e-iceblue.cn/repository/maven-public/</url>
    </repository>
</repositories>


<dependencies>
<dependency>
     <groupId>e-iceblue</groupId>
     <artifactId>spire.office.free</artifactId>
     <version>3.9.0</version>
</dependency>
<dependency>
    <groupId>e-iceblue</groupId>
    <artifactId>spire.xls.free</artifactId>
    <version>3.9.1</version>
</dependency>

链接:https://pan.baidu.com/s/1AeCs3FRZJ0Hhm0mLpc6sRQ 密码:vdbf


/home/<USER>/8087/onlyOfficeFile/record/SHR8735-301_8ddc7bd2ffbb45f3.xlsx

signFilePath


edc_dataset


"obj.id='" + userid + "'"



2025.01.14
1.DTR 记录行按钮 DTT列表上方按钮 

getIRCData

submitToRecistSAS



SHR6390-III-303 

        String  customcode= "";
        String sascodeStr="";
        String parmStr="";

        if (!data.isEmpty()) {
            cn.hutool.json.JSONObject formInfos = new cn.hutool.json.JSONObject(data);
            customcode = formInfos.get("customcode").toString();
            parmStr=FileUtils.extractInsideBrackets(customcode);
            sascodeStr= FileUtils.extractOutsideBrackets(customcode);
        }
        JSONObject param = new JSONObject();
        param.put("parm", parmStr);
        param.put("sascode", sascodeStr);
        
        
        
        
  config.xml 
  
  
  project_report_doc
  
cdtms代码：  VUE版本按钮跳转页面的请求方式:
          try {
            HashMap map = new HashMap();
            map.put("url", "/LightpdfSignIntergrate.signCreate.do");
            response.getOutputStream().write(ApiResult.ok("ok", map).getBytes(StandardCharsets.UTF_8));
            response.getOutputStream().close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }



{"note":"upload success","ufn":"2025018528DAA6F9234C23820D2C2F49AD616A.zip","status":"200"}

https://sas-online-tst.hengrui.com/sas_online/files/'HRS-5635-101_abdbfffff3e8b9f1_%E9%9A%8F%E6%9C%BA%E5%8C%96%E4%B8%8E%E7%A0%94%E7%A9%B6%E8%8D%AF%E7%89%A9%E5%88%86%E9%85%8D%E7%94%B3%E8%AF%B7%E8%A1%A8%20(1).pdf'

      //执行excel转pdf命令
            String command="/home/<USER>/xlsx2pdf/venv/bin/python /home/<USER>/xlsx2pdf/xlsx2pdf.py /home/<USER>/8087/onlyOfficeFile/record/ "+fileName;
            CallPython.executeLatestFill(command);
            
            
            
            
            
          C:\MyFile\百奥知\源码\HRUAP\BioknowCdtms\out  
            
            
String filePath = WebPath.getRootPath() + DAOFileup.tempfolder + "/eSign";

setRTSMCOMPParam
randCompareCheck






//请求远程xlsx转换接口，返回pdf文件路径
            String fileName=signFileArr[0].substring(0, signFileArr[0].lastIndexOf(".")) + ".xlsx";
            String uploadResult = LightpdfSignIntegrateUtil.uploadFile("https://sas-online-tst.hengrui.com/sas_online/transferXlsxToPdf", signFilePathFullURI, fileName);
            //判断接口是否成功
            Map uploadFileMsgMap = gson.fromJson(uploadResult, new TypeToken<Map<String, Object>>() {
            }.getType());
            if (StringUtils.equals(String.valueOf(uploadFileMsgMap.get("code")), "200.0")) {
                //下载文件流，保存到本地
                String folderPath = WebPath.getRootPath() + DAOFileup.tempfolder + "/eSign";
                String pdfName = signFileArr[0].substring(0, signFileArr[0].lastIndexOf(".")) + ".pdf";
                String downloadUrl="https://sas-online-tst.hengrui.com/sas_online/files/"+pdfName;
                LightpdfSignIntegrateUtil.downloadByNIO(downloadUrl,folderPath,pdfName);
                return new String[]{pdfName, folderPath+"/"+pdfName};
            }else{
                return null;
            }
            
            
            
            
            "" + fileName.toString().split("\\*")[0] + "*" + fileUuid + "|" ;





2025.1.22
1.(select obj2.used_language from Xsht as obj2 where obj2.id= obj.studyid)='zh_CN' and obj.ectesting_sign is null and obj.version_zt='4'

2.(select obj2.used_language from Xsht as obj2 where obj2.id= obj.studyid)='en_US' and obj.ectesting_sign is null and obj.version_zt='4'



String used_language = CDTMSAPI.getStudyLanguage(studyId);
formInfo.put("language", used_language);

_dsrand_informtable.xlsx


  //随机一致性比对
        if(file.getName().contains(".xlsx")&&(file.getName().contains("随机信息列表")||file.getName().contains("随机与药物信息列表")||file.getName().contains("dsrand_informtable")||file.getName().contains("dsrand_druginformtable"))){
            String date= FileUtils.extractDate(file.getName());
            log.info("-------------------------随机一致性比对文件录入的时间戳date为:"+date+"----------------------------------");
            map.put("date", date);
        }
        
  	
RandSpecialist      
        
2025.02.05
1.随机在线签字：
	a.审批后的文件转换为pdf
	b.回调逻辑设计
	b.数据库表结构:esign_instance\esign_log\esign_signer\esign_file\esign_file_signer\esign_engine\esign_account
	\esign_log_group_day
2.医学编码定时下载优化
