<template>
  <div>
    <div class="header-menu">
      <el-row>
        <el-col :span="16" style="width: 70%;">
          <div style="width: 100%;height: 150px" id="main" v-show="showTable"></div>
        </el-col>
      </el-row>
      <el-row style="padding-bottom: 0px;">
      </el-row>

      <el-col :span="1.5" style="margin-left: 2%; margin-top: 5px;">
      </el-col>
      <el-col :span="12" style="margin-top: 5px;">
        <el-button type="primary" @click="UIaddConfirms()" size="mini" v-if="onlyread">审核</el-button>
        <el-button type="primary" @click="UIdelConfirms()" size="mini" v-if="onlyread">取消审核</el-button>
        <el-button type="primary" @click="UIaddComments()" size="mini" v-if="onlyread">批注</el-button>
        <!-- <el-button type="primary" @click="UIaddConfirms()" size="mini">审核</el-button>
        <el-button type="primary" @click="UIdelConfirms()" size="mini">取消审核</el-button>
        <el-button type="primary" @click="UIaddComments()" size="mini">批注</el-button> -->

        <el-button @click="selectFilter(2)" size="mini" plain type="danger">未审核：{{ this.NumUnconfirmed }}</el-button>
        <el-button @click="selectFilter(1)" size="mini" plain type="success">已审核：{{ this.NumConfirmed }}</el-button>
        <el-button @click="selectFilter(3)" size="mini" plain type="warning">已批注：{{ this.NumMarked }}</el-button>
        <!-- 弹出框 -->
        <el-popover placement="bottom" trigger="click" style="margin-top: 5px; margin-left: 10px; margin-bottom: 5px; ">
          <div>
            <el-popover placement="left-end" trigger="click" width="500" style="padding: 0;">
              <MultipleSelect v-model="value" :options="InvisiableOptions"></MultipleSelect>
              <el-button @click="InvisiableTitle" size="mini">确定</el-button>
              <el-button slot="reference" size="mini" style="margin-top: 5px;">隐藏列设置</el-button>
            </el-popover>
            <br>
            <el-button size="mini" style="margin-top: 5px;" @click="viewcomment2()">批注历史</el-button>
            <br>
            <el-button size="mini" style="margin-top: 5px;" @click="viewConfirms()">审核历史</el-button>
          </div>
          <el-button slot="reference" circle icon="el-icon-more" size="mini"></el-button>
        </el-popover>
      </el-col>
      <el-col :span="4" style="width: 30%;">
        <conditionGroup :floor="floor" :conditionList="conditionList" :parentData="this" :key-list="keyOptions"
          :condition-map="conditionOptions" :val-list="valueOptions">
        </conditionGroup>
      </el-col>
      <el-col :span="2">
        <el-button type="primary" @click="query()" style="float: right; margin-right: 10px; margin-top: 5px;"
          size="mini">查询</el-button>
      </el-col>
      <el-col :span="2">
        <el-button type="info" plain @click="reload()" icon="el-icon-refresh-left" circle size="mini"
          style=" margin-top: 5px; margin-left: 10px;"></el-button>

        <el-button @click="invisiableTable()" circle icon="el-icon-caret-top" size="mini"
          style="margin-top: 5px; margin-left: 10px;"></el-button>
      </el-col>
    </div>

    <!-- 表格 -->
    <el-table :data="reportData" size="mini" :highlight-current-row="true" @selection-change="handleSelection"
      style="width: 98%; margin: auto; margin-top: 10px; margin-left: 2%; " :border=true @sort-change="sortChange"
      :header-cell-class-name="handleHeadAddClass" :row-class-name="tableRowClassName" @filter-change="filterChange" fit>
      <el-table-column type="selection" align="center" fixed="left" min-width="3.4%"></el-table-column>
      <el-table-column prop="operation" label="批注" align="center" fixed="left" min-width="4.2%">
        <template slot-scope="scope">
          <el-button circle icon="el-icon-s-comment" @click="viewcomment(scope.row)" plain size="mini"
            v-if="scope.row.marked == 1" type="text"></el-button>
          <!-- <el-button circle icon="el-icon-s-comment" @click="viewcomment(scope.row, 1)"
            plain size="mini" v-if="scope.row.marked == 1" :type="buttonType(scope.row.marked)"></el-button> -->
        </template>
      </el-table-column>

      <!-- <el-table-column prop="subjid" label="受试者代码" align="center" v-if="issubjid"
        :filters="getfilterSubjidItem()" column-key="filterSubjid" fixed="left" min-width="8%"> -->
      <el-table-column prop="subjid" label="受试者代码" align="center" v-if="issubjid" column-key="filterSubjid" fixed="left"
        min-width="8%">
        <template slot="header" slot-scope="scope">
          <span>受试者代码</span>
          <el-popover placement="bottom" trigger="click">
            <div>
              <el-select ref="selectRef" v-model="FilterSubjid2" multiple size="mini" filterable
                style="width: 130px; margin-right: 20px;">
                <el-option v-for="item in FilterSubjidList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
              <el-button slot="reference" size="mini" type="text" @click="filterChangeOverride()">筛选</el-button>
              <el-button slot="reference" size="mini" type="text" @click="ReloadOverride(1)">重置</el-button>
            </div>
            <el-button slot="reference" icon="el-icon-arrow-down" size="mini" type="text"></el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="visitname" label="访视" align="center" v-if="isvisitname" show-overflow-tooltip
        min-width="5%"></el-table-column>
      <!-- <el-table-column prop="lbcat" label="表名称" align="center" v-if="islbcat" :filters="getfilterLabcatItem()"
        column-key="filterLabcat" show-overflow-tooltip min-width="6%"> -->
      <el-table-column prop="lbcat" label="表名称" align="center" v-if="islbcat" show-overflow-tooltip min-width="6%"
        column-key="filterLabcat">
        <template slot="header" slot-scope="scope">
          <span>表名称</span>
          <el-popover placement="bottom" trigger="click">
            <div>
              <el-select ref="selectRef" v-model="FilterLabcat2" multiple size="mini" filterable style="width: 130px;">
                <el-option v-for="item in FilterLabcatList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
              <el-button slot="reference" size="mini" type="text" @click="filterChangeOverride()">筛选</el-button>
              <el-button slot="reference" size="mini" type="text" @click="ReloadOverride(3)">重置</el-button>
            </div>
            <el-button slot="reference" icon="el-icon-arrow-down" size="mini" type="text"></el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="lbdat" label="采样日期" align="center" v-if="islbdat" min-width="8%"
        sortable="custom"></el-table-column>
      <!-- <el-table-column prop="lbtest" label="检查项" align="center" v-if="islbtest" :filters="getfilterLabtestItem()"
        column-key="filterLabtest" show-overflow-tooltip min-width="6%"> -->
      <el-table-column prop="lbtestOrig" label="检查项" align="center" v-if="islbtest" column-key="filterLabtest"
        show-overflow-tooltip min-width="6%">
        <template slot="header" slot-scope="scope">
          <span>检查项</span>
          <el-popover placement="bottom" trigger="click">
            <div>
              <el-select ref="selectRef" v-model="FilterLabtest2" multiple size="mini" filterable style="width: 130px;">
                <el-option v-for="item in FilterLabtestList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
              <el-button slot="reference" size="mini" type="text" @click="filterChangeOverride()">筛选</el-button>
              <el-button slot="reference" size="mini" type="text" @click="ReloadOverride(2)">重置</el-button>
            </div>
            <el-button slot="reference" icon="el-icon-arrow-down" size="mini" type="text"></el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="lborres" label="检查结果" align="center" v-if="islborres" sortable="custom"
        min-width="8%"></el-table-column>
      <el-table-column prop="lborresu" label="单位" align="center" v-if="islborresu" min-width="5%"></el-table-column>
      <el-table-column prop="labflag" label="Flag" align="center" v-if="islabflag" min-width="4%"></el-table-column>
      <el-table-column prop="lbclsig" label="临床意义" align="center" v-if="islbclsig" min-width="6%"></el-table-column>
      <el-table-column prop="pct1" label="超出范围%" align="center" v-if="ispct_1" min-width="7%"></el-table-column>
      <el-table-column prop="analytevalueM1" label="CS边界" align="center" v-if="isAnalyteValue_m_1"
        min-width="6%"></el-table-column>
      <el-table-column prop="lbornrhi" label="上限" align="center" v-if="islbornrhi" min-width="4.7%"></el-table-column>
      <el-table-column prop="lbornrlo" label="下限" align="center" v-if="islbornrlo" min-width="4.7%"></el-table-column>
      <el-table-column prop="warning" label="提醒" align="center" v-if="iswarning" show-overflow-tooltip
        min-width="15%"></el-table-column>
    </el-table>

    <el-dialog title="添加批注" :visible.sync="visibleForm">
      <el-form :model="commentdata" size="mini" :stripe="true" :highlight-current-row="true" ref="commentdata">
        <el-form-item label="请选择批注的级别" prop="levelId">
          <el-select v-model="commentdata.levelId" placeholder="请选择批注的级别" clearable
            @change="levelIdChange(commentdata.levelId)">
            <el-option label="中心层面" value="1"></el-option>
            <el-option label="受试者层面" value="2"></el-option>
            <el-option label="记录层面" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="请选择中心代码" prop="levelvalue1" v-if="isChose1">
          <el-select v-model="commentdata.levelvalue1" placeholder="请选择对应代码" clearable>
            <el-option v-for="item in valuelist1" :key="item" :value="item" :label="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="请选择受试者代码" prop="levelvalue2" v-if="isChose2">
          <el-select v-model="commentdata.levelvalue2" placeholder="请选择对应代码" clearable>
            <el-option v-for="item in valuelist2" :key="item" :value="item" :label="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="请填写批注：" prop="comments">
          <el-input v-model="commentdata.comments" placeholder="请填写批注：" clearable type="textarea" :rows="5"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelhandler()">关闭页面</el-button>
        <el-button type="primary" @click="addcommit('commentdata')">提 交</el-button>
      </div>
    </el-dialog>

    <el-dialog title="批注记录" :visible.sync="viewCommmentDataForm" width="70%">
      <el-button icon="el-icon-circle-plus-outline" plain size="mini" type="primary" @click="showInnerAddcommit()"
        v-if="onlyread">添加批注</el-button>
      <el-table :data="viewCommmentData" size="mini" :stripe="true" :highlight-current-row="true">
        <el-table-column prop="centerCode" label="中心编号" width="80"></el-table-column>
        <el-table-column prop="patientCode" label="受试者代码" width="90"></el-table-column>
        <!-- <el-table-column prop="recordCode" label="记录" width="80"></el-table-column> -->
        <el-table-column prop="comment" label="批注内容"></el-table-column>
        <el-table-column prop="creater" label="操作者" width="70"></el-table-column>
        <el-table-column prop="create_time" label="操作时间" width="135" :formatter="filterCreateTime"></el-table-column>
        <el-table-column label="" width="60">
          <template slot-scope="scope">
            <el-button circle icon="el-icon-circle-close" @click="delComments(scope.row.id)" plain size="mini"
              v-if="isOwner(scope.row.creater)" type="text"></el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="footer-button">
        <el-pagination @size-change="handleSizeChange2" @current-change="handleCurrentChange2"
          :current-page="currentPage2" :page-size="pageSize2" :page-sizes="pageSizes2"
          layout="total, sizes, prev, pager, next, jumper" :total="totalCount2" align="center"
          style="margin-top: 5px; margin-bottom: 5px;" :pager-count="7">
        </el-pagination>
      </div>

      <el-form :model="commentdata" size="mini" :stripe="true" :highlight-current-row="true" ref="commentdata"
        v-if="isshowaddcommit" style="padding-top: 20px; padding-bottom: 10px;">
        <el-form-item label="请选择批注的级别" prop="levelId">
          <el-select v-model="commentdata.levelId" placeholder="请选择批注的级别" clearable
            @change="levelIdChange(commentdata.levelId)">
            <el-option label="中心层面" value="1"></el-option>
            <el-option label="受试者层面" value="2"></el-option>
            <el-option label="记录层面" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="请选择中心代码" prop="levelvalue1" v-if="isChose1">
          <el-select v-model="commentdata.levelvalue1" placeholder="请选择对应代码" clearable>
            <el-option v-for="item in valuelist1" :key="item" :value="item" :label="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="请选择受试者代码" prop="levelvalue2" v-if="isChose2">
          <el-select v-model="commentdata.levelvalue2" placeholder="请选择对应代码" clearable>
            <el-option v-for="item in valuelist2" :key="item" :value="item" :label="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="请填写批注：" prop="comments">
          <el-input v-model="commentdata.comments" placeholder="请填写批注：" clearable type="textarea" :rows="5"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" v-if="isshowaddcommit">
        <el-button type="info" @click="cancelInnerAddcommit()" size="mini">取消添加</el-button>
        <el-button type="primary" @click="addcommit3('commentdata')" size="mini">添加批注</el-button>
      </div>

    </el-dialog>

    <el-dialog title="批注记录" :visible.sync="viewCommmentDataForm2" width="70%">
      <el-table :data="viewCommmentData2" size="mini" :stripe="true" :highlight-current-row="true"
        @filter-change="filterChange2">
        <el-table-column prop="level" label="层级" width="80"
          :filters="[{ text: '中心层面', value: '1' }, { text: '受试者层面', value: '2' }, { text: '记录层面', value: '3' }]"
          column-key="filterlevelId" :filter-multiple="false"></el-table-column>
        <el-table-column prop="centerCode" label="中心编号" width="80"></el-table-column>
        <el-table-column prop="patientCode" label="受试者代码" width="80"></el-table-column>
        <el-table-column prop="comment" label="批注内容"></el-table-column>
        <el-table-column prop="creater" label="操作者" width="60"></el-table-column>
        <el-table-column prop="create_time" label="操作时间" width="135" :formatter="filterCreateTime"></el-table-column>
        <el-table-column label="" width="60">
          <template slot-scope="scope">
            <el-button size="mini" type="info" @click="ClickTableLocation(scope.row.pid, scope.row.uuid)"
              icon="el-icon-link" plain circle v-if="scope.row.levelId == 3"></el-button>
          </template>
        </el-table-column>
        <div slot="footer" class="dialog-footer">
          <el-button @click="viewCommmentDataForm2 = false">关闭页面</el-button>
        </div>
      </el-table>
      <div class="footer-button">
        <el-pagination @size-change="handleSizeChange2" @current-change="handleCurrentChange2"
          :current-page="currentPage2" :page-size="pageSize2" :page-sizes="pageSizes2"
          layout="total, sizes, prev, pager, next, jumper" :total="totalCount2" align="center"
          style="margin-top: 5px; margin-bottom: 5px;" :pager-count="7">
        </el-pagination>
      </div>
    </el-dialog>

    <el-dialog title="审核记录" :visible.sync="viewConfirmDataForm" width="60%">
      <el-table :data="viewConfirmData" size="mini" :stripe="true" :highlight-current-row="true">
        <el-table-column prop="username" label="操作人"></el-table-column>
        <el-table-column prop="operation" label="操作"></el-table-column>
        <el-table-column prop="create_time" label="操作时间" :formatter="filterCreateTime"></el-table-column>
        <el-table-column label="链接" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="info" @click="ClickTableLocation(scope.row.pid, scope.row.uuid)"
              icon="el-icon-link" plain circle></el-button>
          </template>
        </el-table-column>
        <div slot="footer" class="dialog-footer">
          <el-button @click="viewConfirmDataForm = false">关闭页面</el-button>
        </div>
      </el-table>
      <div class="footer-button">
        <el-pagination @size-change="handleSizeChange2" @current-change="handleCurrentChange2"
          :current-page="currentPage2" :page-size="pageSize2" :page-sizes="pageSizes2"
          layout="total, sizes, prev, pager, next, jumper" :total="totalCount2" align="center"
          style="margin-top: 5px; margin-bottom: 5px;" :pager-count="7">
        </el-pagination>
      </div>
    </el-dialog>

    <div class="footer-button">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
        :page-size="pageSize" :page-sizes="pageSizes" layout="total, sizes, prev, pager, next, jumper" :total="totalCount"
        align="center" style="margin-top: 5px; margin-bottom: 5px;" :pager-count="9">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {
  querys, querys2, select, filterCount, confirmed, Cancelconfirmed, addComments, CancelComments, viewComments, viewComments2,
  getData, getValueListData, getConfirmData, getSiteIdFilterList, getSubjIdFilterList, getLabtestFilterList, getLabcatFilterList,
  filter, location, getBarChartData, filterByBarData, getBarChartData2, filterByBarData2
} from "@/api/lab"
import conditionGroup from "./conditionGroup";
import MultipleSelect from "./MultipleSelect"
import customHeader from "./customHeader.vue"
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/components/mixins/resize'

const condition = {
  id: 1,
  index: 1,
  condition: '',
  operate: 'and',
  field: '',
  value: '',
  header: true,
  checked: false,
  pid: -1,
  floor: 1,
  projectId: ''
}

export default {
  mixins: [resize],
  components: {
    conditionGroup,
    MultipleSelect,
    customHeader,
  },

  data() {
    return {
      currentPage: 1, //默认显示第几页
      totalCount: 1, //总条数
      pageSize: 15, //每页数据的数量
      pageSizes: [10, 15, 20, 30, 50], //选择每页的数据量

      currentPage2: 1, //默认显示第几页
      totalCount2: 1, //总条数
      pageSize2: 5, //每页数据的数量
      pageSizes2: [5, 10, 15], //选择每页的数据量

      NumConfirmed: 0,
      NumUnconfirmed: 0,
      NumMarked: 0,
      flag: '',

      pid: '',
      id: '',
      reportData: [],
      filterVO: {
        pid: '',
        // filterSiteid: [],
        filterSubjid: [],
        filterLabtest: [],
        filterLabcat: [],
        currentPage: 1,
        pageSize: 10,
        flag: '',
        sortProp: [],
      },

      ishidden: true,
      commentdata: {
        levelId: '',
        levelvalue1: '',
        levelvalue2: '',
        comments: '',
      },
      commentdata2: {
        levelvalue: '',
        comments: '',
      },
      valuelist: [],
      valuelist1: [],
      valuelist2: [],
      isChose1: false,
      isChose2: false,
      isshowaddcommit: false,

      viewCommmentData: [],
      viewCommmentData2: [],
      viewConfirmData: [],
      commentVO: {
        levelId: '',
        levelvalue: '',
        comments: '',
        rowlist: [],
        pid: '',
        creater: '',
      },
      ExecutionParams: {
        pid: '',
        id: 1,
        conditionList: [],
        flag: ''
      },
      command: '',
      visibleForm: false,
      visibleForm2: false,
      commentRowId: '',
      viewCommmentDataForm: false,
      viewCommmentDataForm2: false,
      viewConfirmDataForm: false,
      confirmedVO: {
        pid: '',
        rowlist: [],
        uid: '',
        username: ''
      },
      selectionList: [],
      filterFlag: '',
      filterFlag2: '',
      filterFlag3: '',
      filterLevel: '',

      issiteid: true,
      issubjid: true,
      // isstatus: true,
      // iscrfsts: true,
      isvisitname: true,
      islbcat: true,
      islbdat: true,
      islbtest: true,
      islborres: true,
      islborresu: true,
      islabflag: true,
      islbclsig: true,
      ispct_1: true,
      isAnalyteValue_m_1: true,
      islbornrhi: true,
      islbornrlo: true,
      // ismtime: true,
      iswarning: true,
      value: [],
      InvisiableOptions: [
        // { label: '中心编号', value: 'siteid' },
        { label: '受试者代码', value: 'subjid' },
        // { label: '受试者状态', value: 'status' },
        // { label: 'CRF状态', value: 'crfsts' },
        { label: '访视', value: 'visitname' },
        { label: '表名称', value: 'lbcat' },
        { label: '采样日期', value: 'lbdat' },
        { label: '检查项', value: 'lbtest' },
        { label: '检查结果', value: 'lborres' },
        { label: '单位', value: 'lborresu' },
        { label: 'LabFlag', value: 'labflag' },
        { label: '临床意义', value: 'lbclsig' },
        { label: '超出参考值范围%', value: 'pct_1' },
        { label: '当前CS边界%', value: 'AnalyteValue_m_1' },
        { label: '参考值范围上限', value: 'lbornrhi' },
        { label: '参考值范围下限', value: 'lbornrlo' },
        // { label: '修改时间', value: 'mtime' },
        { label: '提醒', value: 'warning' },
      ],

      conditionList: [
        Object.assign({}, condition)
      ],

      floor: 1,

      keyOptions: [
        // { 'key': '中心编号', 'val': 'siteid', "type": "string" },
        { 'key': '受试者代码', 'val': 'subjid' },
        // { 'key': '受试者状态', 'val': 'status' },
        // { 'key': 'CRF状态', 'val': 'crfsts' },
        { 'key': '访视', 'val': 'visitname' },
        { 'key': '表名称', 'val': 'lbcat' },
        { 'key': '采样日期', 'val': 'lbdat' },
        { 'key': '检查项', 'val': 'lbtest_orig' },
        { 'key': '检查结果', 'val': 'lborres' },
        { 'key': '单位', 'val': 'lborresu' },
        { 'key': 'LabFlag', 'val': 'labflag' },
        { 'key': '临床意义', 'val': 'lbclsig' },
        { 'key': '超出参考值范围%', 'val': 'pct_1' },
        { 'key': '当前CS边界%', 'val': 'AnalyteValue_m_1' },
        { 'key': '参考值范围上限', 'val': 'lbornrhi' },
        { 'key': '参考值范围下限', 'val': 'lbornrlo' },
        // { 'key': '修改时间', 'val': 'mtime' },
        { 'key': '提醒', 'val': 'warning' },
      ],
      // conditionOptions: {
      //   type: [
      //     { 'key': '=', 'val': 'eq' },
      //   ],
      //   source: [
      //     { 'key': '=', 'val': 'eq' },
      //   ]
      // },
      conditionOptions: [
        { 'key': '>', 'val': 'gt' },
        { 'key': '=', 'val': 'eq' },
        { 'key': '<', 'val': 'lt' },
        { 'key': '>=', 'val': 'gtq' },
        { 'key': '<=', 'val': 'ltq' },
        { 'key': '包含', 'val': 'like' },
        { 'key': '不包含', 'val': 'not like' },
        { 'key': '为空', 'val': 'is null' },
        { 'key': '不为空', 'val': 'is not null' }

      ],
      valueOptions: {
        type: {
          dom: 'select',
          data: [
            { 'key': 'time', 'val': 'time' },
            { 'key': 'date', 'val': 'date' },
          ]
        },
        source: {
          dom: 'select',
          data: [
            { 'key': 'm1', 'val': 'm' },
            { 'key': 'a1', 'val': 'a' },
          ]
        },
        create_time: {
          dom: 'date'
        },
        update_time: {
          dom: 'date'
        },
      },

      //图表数据
      xData: [],
      yData: [],
      barchartflag: '',
      siteid: '',
      paramsFlag: '',
      showTable: true,
      showTable2: false,

      customFlag: false, // 自定义筛选是否显示
      customParams: {}, //自定义筛选参数
      conditionList2: [], //自定义筛选条件
      labelColorList: [], //已经在使用的筛选条件，染色用
      tableDataCopy: [], // table数据 拷贝，我们不操作原数据

      // FilterSiteid: [],
      FilterSubjid: [],
      FilterLabtest: [],
      FilterLabcat: [],

      //排序用
      sortField: {},
      orderBys: [],
      onlyread: false,

      //重写筛选
      FilterSubjidList: [],
      FilterLabtestList: [],
      FilterLabcatList: [],
      FilterSubjid2: [],
      FilterLabtest2: [],
      FilterLabcat2: [],

      efectiveRole: '',
      studyCode: '',
      realname: ''
    }
  },
  created() {
    this.getParams(this.$route.query.pid)
    this.getData(this.$route.query.pid)
    this.getBarChartData(this.$route.query.pid)
  },
  mounted() {
    this.readonly();
    this.filterVO.pid = this.$route.query.pid
    this.getfilterSubjidItem()
    this.getfilterLabtestItem()
    this.getfilterLabcatItem()
  },
  methods: {
    getParams(pid) {
      if (pid == null) {
        this.$message.error('请先选择项目再进入本页面')
      } else {
        querys(pid, this.currentPage, this.pageSize).then(res => {
          // console.log(res);
          this.reportData = []
          this.totalCount = res.data.total
          this.pageSize = res.data.size
          this.currentPage = res.data.current
          // console.log(res.data.records);
          this.reportData.push(...res.data.records)
        }).catch(error => {
          // this.$message.error('报告获取出错')
        })
      }
    },

    getParams2(pid) {
      this.paramsFlag = 1
      querys2(pid, this.currentPage, this.pageSize).then(res => {
        // console.log(res);
        this.reportData = []
        this.totalCount = res.data.total
        this.pageSize = res.data.size
        this.currentPage = res.data.current
        // console.log(res.data.records);
        this.reportData.push(...res.data.records)
      }).catch(error => {
        // this.$message.error('报告获取出错')
      })

    },

    getData(pid) {
      getData(pid).then(res => {
        // console.log(res);
        this.NumConfirmed = res.data.confirmed
        this.NumUnconfirmed = res.data.unconfirmed
        this.NumMarked = res.data.marked
      }).catch(error => {
        this.$message.error('统计数据获取出错')
      })
    },

    query() {
      this.ExecutionParams.conditionList = this.conditionList
      this.ExecutionParams.pid = this.$route.query.pid
      this.ExecutionParams.flag = this.flag
      // console.log(this.ExecutionParams)
      select(this.ExecutionParams, this.currentPage, this.pageSize).then(res => {
        this.reportData = []
        this.totalCount = res.data.length
        this.reportData = res.data.slice(
          (this.currentPage - 1) * this.pageSize,
          this.currentPage * this.pageSize
        )
        // this.reportData.push(...res.data)
      }).catch(error => {
        // this.$message.error('报告获取出错')
      })
    },

    reload() {
      this.conditionList = [Object.assign({}, condition)]
      this.command = ''
      this.filterFlag = ''
      this.filterFlag2 = ''
      this.flag = ''
      this.barchartflag = ''
      this.ExecutionParams.conditionList = []
      this.paramsFlag = ''
      this.FilterSubjid2 = []
      this.FilterLabtest2 = []
      this.FilterLabcat2 = []
      this.getParams(this.$route.query.pid)
      this.getBarChartData(this.$route.query.pid)
      this.getfilterSubjidItem()
      this.getfilterLabtestItem()
      this.getfilterLabcatItem()
    },

    selectFilter(flag) {
      this.filterFlag = 1;
      this.ExecutionParams.conditionList = []
      this.command = ''
      this.filterFlag2 = ''
      if (this.flag == '' || this.flag != flag) {
        this.currentPage = 1
      }
      this.flag = flag
      this.getBarChartData2(this.$route.query.pid)
      filterCount(this.$route.query.pid, flag, this.currentPage, this.pageSize).then(res => {
        this.reportData = []
        this.totalCount = res.data.total
        this.pageSize = res.data.size
        this.currentPage = res.data.current
        this.reportData.push(...res.data.records)
        // // this.reportData.push(...res.data)
        // this.$message.success('筛选完成')
      }).catch(error => {
        // this.$message.error('筛选出错')
      })
    },

    handleSelection(row) {
      // console.log(row);
      this.selectionList = []
      this.selectionList.push(...row)
    },

    UIaddComments() {
      if (this.selectionList.length != 0) {
        this.commentdata.levelvalue1 = ''
        this.commentdata.levelvalue1 = this.selectionList[0].siteid
        this.commentdata.levelvalue2 = ''
        this.commentdata.levelvalue2 = this.selectionList[0].subjid
        this.valuelist1 = []
        this.valuelist2 = []
        for (var i = 0; i < this.selectionList.length; i++) {
          if (this.valuelist1.indexOf(this.selectionList[i].siteid) === -1) {
            this.valuelist1.push(this.selectionList[i].siteid)
          }
          if (this.valuelist2.indexOf(this.selectionList[i].subjid) === -1) {
            this.valuelist2.push(this.selectionList[i].subjid)
          }
        }
        this.visibleForm = true
      } else {
        this.$message.error('请先选中记录！')
      }
    },

    levelIdChange(levelId) {
      if (levelId == 1) {
        this.isChose1 = true
        this.isChose2 = false
      } else if (levelId == 2) {
        this.isChose1 = false
        this.isChose2 = true
      } else if (levelId == 3) {
        this.isChose1 = false
        this.isChose2 = false
      }
    },

    showInnerAddcommit() {
      this.isshowaddcommit = true
    },

    cancelInnerAddcommit() {
      this.isshowaddcommit = false
    },

    addcommit() {
      this.commentVO.levelId = this.commentdata.levelId
      if (this.commentdata.levelId == 1) {
        this.commentVO.levelvalue = this.commentdata.levelvalue1
      } else if (this.commentdata.levelId == 2) {
        this.commentVO.levelvalue = this.commentdata.levelvalue2
      }
      this.commentVO.comments = this.commentdata.comments
      this.commentVO.rowlist = []
      this.commentVO.rowlist.push(...this.selectionList)
      // this.commentVO.pid = this.$route.query.pid
      this.commentVO.pid = this.$route.query.id
      this.commentVO.creater = localStorage.getItem('realname')
      // this.commentVO.creater = this.$store.getters.realname
      addComments(this.commentVO).then(res => {
        this.$refs['commentdata'].resetFields();
        if (this.ExecutionParams.conditionList.length != 0) {
          this.query()
        } else if (this.filterFlag != '') {
          this.selectFilter(this.flag)
        } else if (this.filterFlag2 != '') {
          this.FilterChange()
        } else if (this.barchartflag != '') {
          this.filterByBar(this.$route.query.pid, this.siteid)
        } else if (this.barchartflag == 2) {
          this.filterByBar2(this.$route.query.pid, this.siteid, this.flag)
        } else if (this.paramsFlag != '') {
          this.getParams2(this.$route.query.pid)
        } else {
          this.getParams(this.$route.query.pid)
        }
        this.getData(this.$route.query.pid)
        this.visibleForm = false
        this.$message.success('批注添加成功')
      }).catch(error => {
        // this.$message.error('批注过程中出现错误')
      })
    },

    addcommit3() {
      this.commentVO.levelId = this.commentdata.levelId
      if (this.commentdata.levelId == 1) {
        this.commentVO.levelvalue = this.commentdata.levelvalue1
      } else if (this.commentdata.levelId == 2) {
        this.commentVO.levelvalue = this.commentdata.levelvalue2
      }
      // this.commentVO.levelvalue = this.commentdata.levelvalue
      this.commentVO.comments = this.commentdata.comments
      // this.commentVO.pid = this.$route.query.pid
      this.commentVO.pid = this.$route.query.id
      this.commentVO.creater = localStorage.getItem('realname')
      // this.commentVO.creater = this.$store.getters.realname
      addComments(this.commentVO).then(res => {
        this.$refs['commentdata'].resetFields();
        if (this.ExecutionParams.conditionList.length != 0) {
          this.query()
        } else if (this.filterFlag != '') {
          this.selectFilter(this.flag)
        } else if (this.filterFlag2 != '') {
          this.filterChange(filterObj)
        } else if (this.barchartflag == 1) {
          this.filterByBar(this.$route.query.pid, this.siteid)
        } else if (this.barchartflag == 2) {
          this.filterByBar2(this.$route.query.pid, this.siteid, this.flag)
        } else if (this.paramsFlag != '') {
          this.getParams2(this.$route.query.pid)
        } else {
          this.getParams(this.$route.query.pid)
        }
        this.getData(this.$route.query.pid)
        this.viewCommmentDataForm = false
        this.isChose1 = false
        this.isChose2 = false
        this.$message.success('批注添加成功')
      }).catch(error => {
        // this.$message.error('批注过程中出现错误')
      })
    },

    delComments(id) {
      console.log(id);
      this.$confirm('确认删除已选中的批注', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        CancelComments(id).then(res => {
          if (this.viewCommmentDataForm == true) {
            viewComments(this.$route.query.id, this.commentRowId).then(res => {
              this.viewCommmentData = []
              this.totalCount2 = res.data.length
              this.viewCommmentData = res.data.slice(
                (this.currentPage2 - 1) * this.pageSize2,
                this.pageSize2 * this.currentPage2
              )
            }).catch(error => {
            })
          } else if (this.viewCommmentDataForm2 == true) {
            viewComments2(this.$route.query.id, null).then(res => {
              this.viewCommmentData2 = []
              // this.viewCommmentData2.push(...res.data)
              this.totalCount2 = res.data.length
              this.viewCommmentData2 = res.data.slice(
                (this.currentPage2 - 1) * this.pageSize2,
                this.pageSize2 * this.currentPage2
              )
            }).catch(error => {
            })
          }
        }).catch(() => {
        });
        this.getData(this.$route.query.pid)
        this.$message.success('已删除批注')
      }).catch(error => {
      })
    },

    cancelhandler() {
      this.$refs['commentdata'].resetFields();
      this.visibleForm = false
    },

    cancelhandler2() {
      this.$refs['commentdata2'].resetFields();
      this.visibleForm2 = false
    },

    UIaddConfirms() {
      if (this.selectionList.length != 0) {
        this.$confirm('确认审核已选数据', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // this.loading = true
          // this.confirmedVO.pid = this.$route.query.pid
          this.confirmedVO.pid = this.$route.query.id
          this.confirmedVO.rowlist = []
          this.confirmedVO.rowlist.push(...this.selectionList)
          this.confirmedVO.uid = localStorage.getItem('uesrid')
          // this.confirmedVO.uid = this.$store.getters.id
          this.confirmedVO.username = localStorage.getItem('realname')
          // this.confirmedVO.username = this.$store.getters.realname
          // console.log(this.confirmedVO);
          confirmed(this.confirmedVO).then(res => {
            if (this.flag != '') {
              this.selectFilter(this.flag)
            } else if (this.filterFlag2 != '') {
              this.FilterChange()
            } else if (this.flag == '') {
              this.getParams(this.$route.query.pid)
            }
            this.getData(this.$route.query.pid)
            this.$message.success('审核完成')
          }).catch(error => {
            // this.$message.error('审核过程出错')
          })
          // this.getData(this.$route.query.pid)
        }).catch(() => {
        });

      } else {
        this.$message.error('请先选中记录！')
      }
    },

    UIdelConfirms() {
      if (this.selectionList.length != 0) {
        // console.log(this.selectionList);
        this.$confirm('是否取消已选数据的审核状态', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // this.confirmedVO.pid = this.$route.query.pid
          this.confirmedVO.pid = this.$route.query.id
          this.confirmedVO.rowlist = []
          this.confirmedVO.rowlist.push(...this.selectionList)
          this.confirmedVO.uid = localStorage.getItem('uesrid')
          // this.confirmedVO.uid = this.$store.getters.id
          this.confirmedVO.username = localStorage.getItem('realname')
          // this.confirmedVO.username = this.$store.getters.realname
          Cancelconfirmed(this.confirmedVO).then(res => {
            if (this.flag != '') {
              this.selectFilter(this.flag)
            } else if (this.filterFlag2 != '') {
              this.FilterChange()
            } else if (this.flag == '') {
              this.getParams(this.$route.query.pid)
            }
            this.getData(this.$route.query.pid)
            this.$message.success('审核失效完成')
          }).catch(error => {
          })
        }).catch(() => {
        });

      } else {
        this.$message.error('请先选中记录！')
      }
    },

    viewConfirms() {
      this.filterFlag3 = 2
      getConfirmData(this.$route.query.id).then(res => {
        // console.log(res.data);
        this.viewConfirmData = []
        // this.viewConfirmData.push(...res.data)
        this.totalCount2 = res.data.length
        this.viewConfirmData = res.data.slice(
          (this.currentPage2 - 1) * this.pageSize2,
          this.pageSize2 * this.currentPage2
        )
        this.viewConfirmDataForm = true
        // this.$message.success('查询成功')
      }).catch(error => {
        // this.$message.error('查询过程出错')
      })
    },

    viewcomment(row) {
      this.isshowaddcommit = false
      this.commentdata.levelvalue1 = row.siteid
      this.valuelist1 = []
      this.valuelist1.push(row.siteid)
      this.commentdata.levelvalue2 = row.subjid
      this.valuelist2 = []
      this.valuelist2.push(row.subjid)
      this.commentVO.rowlist = []
      this.commentVO.rowlist.push(row)

      this.filterFlag3 = 3
      this.commentRowId = row.uuid
      this.viewCommmentDataForm = true
      viewComments(this.$route.query.id, this.commentRowId).then(res => {
        // console.log(res.data);
        this.viewCommmentData = []
        this.totalCount2 = res.data.length
        this.viewCommmentData = res.data.slice(
          (this.currentPage2 - 1) * this.pageSize2,
          this.pageSize2 * this.currentPage2
        )
        // this.$message.success('查询成功')
      }).catch(error => {
        // this.$message.error('查询过程出错')
      })
      this.viewCommmentData = []
    },

    viewcomment2() {
      this.filterFlag3 = ''
      this.viewCommmentDataForm2 = true
      viewComments2(this.$route.query.id, null).then(res => {
        // console.log(res);
        this.viewCommmentData2 = []
        // this.viewCommmentData2.push(...res.data)
        this.totalCount2 = res.data.length
        this.viewCommmentData2 = res.data.slice(
          (this.currentPage2 - 1) * this.pageSize2,
          this.pageSize2 * this.currentPage2
        )
        // this.$message.success('查询成功')
      }).catch(error => {
        // this.$message.error('查询过程出错')
      })
      // this.viewCommmentData2 = []

    },

    sqlcommit() {
      this.query()
    },

    InvisiableTitle() {
      for (let item in this.value) {
        let object = "is" + this.value[item]
        this[`${object}`] = false
      }
      for (let item in this.InvisiableOptions) {
        if (this.value.indexOf(this.InvisiableOptions[item].value) == -1) {
          let object = "is" + this.InvisiableOptions[item].value
          this[`${object}`] = true
        }
      }
    },

    handleSizeChange(val) {
      this.pageSize = val
      // this.getParams(this.$route.query.pid)
      if (this.ExecutionParams.conditionList.length == 0 && this.command == '' && this.filterFlag == '' && this.filterFlag2 == '' && this.barchartflag == '' && this.paramsFlag == '') {
        this.getParams(this.$route.query.pid)
      } else if (this.ExecutionParams.conditionList.length != 0) {
        this.query()
      } else if (this.filterFlag != '') {
        this.selectFilter(this.flag)
      } else if (this.filterFlag2 != '') {
        this.FilterChange()
      } else if (this.barchartflag == 1) {
        this.filterByBar(this.$route.query.pid, this.siteid)
      } else if (this.barchartflag == 2) {
        this.filterByBar2(this.$route.query.pid, this.siteid, this.flag)
      } else if (this.paramsFlag != '') {
        this.getParams2(this.$route.query.pid)
      }
    },

    handleCurrentChange(val) {
      this.currentPage = val
      if (this.ExecutionParams.conditionList.length == 0 && this.command == '' && this.filterFlag == '' && this.filterFlag2 == '' && this.barchartflag == '' && this.paramsFlag == '') {
        this.getParams(this.$route.query.pid)
      } else if (this.ExecutionParams.conditionList.length != 0) {
        this.query()
      } else if (this.filterFlag != '') {
        this.selectFilter(this.flag)
      } else if (this.filterFlag2 != '') {
        this.FilterChange()
      } else if (this.barchartflag == 1) {
        this.filterByBar(this.$route.query.pid, this.siteid)
      } else if (this.barchartflag == 2) {
        this.filterByBar2(this.$route.query.pid, this.siteid, this.flag)
      } else if (this.paramsFlag != '') {
        this.getParams2(this.$route.query.pid)
      }
      // this.getParams(this.$route.query.pid)
    },

    handleSizeChange2(val) {
      this.pageSize2 = val
      if (this.filterFlag3 == '') {
        this.viewcomment2()
      } else if (this.filterFlag3 == 1) {
        viewComments2(this.$route.query.id, this.filterLevel).then(res => {
          // console.log(res);
          this.viewCommmentData2 = []
          // this.viewCommmentData2.push(...res.data)
          this.totalCount2 = res.data.length
          this.viewCommmentData2 = res.data.slice(
            (this.currentPage2 - 1) * this.pageSize2,
            this.pageSize2 * this.currentPage2
          )
          // this.$message.success('查询成功')
        }).catch(error => {
          // this.$message.error('查询过程出错')
        })
      } else if (this.filterFlag3 == 2) {
        this.viewConfirms()
      } else if (this.filterFlag3 == 3) {
        viewComments(this.$route.query.id, this.commentRowId).then(res => {
          // console.log(res.data);
          this.viewCommmentData = []
          this.totalCount2 = res.data.length
          this.viewCommmentData = res.data.slice(
            (this.currentPage2 - 1) * this.pageSize2,
            this.pageSize2 * this.currentPage2
          )
          // this.$message.success('查询成功')
        }).catch(error => {
          // this.$message.error('查询过程出错')
        })
        this.viewCommmentData = []
      }

    },

    handleCurrentChange2(val) {
      this.currentPage2 = val
      if (this.filterFlag3 == '') {
        this.viewcomment2()
      } else if (this.filterFlag3 == 1) {
        viewComments2(this.$route.query.id, this.filterLevel).then(res => {
          // console.log(res);
          this.viewCommmentData2 = []
          // this.viewCommmentData2.push(...res.data)
          this.totalCount2 = res.data.length
          this.viewCommmentData2 = res.data.slice(
            (this.currentPage2 - 1) * this.pageSize2,
            this.pageSize2 * this.currentPage2
          )
          // this.$message.success('查询成功')
        }).catch(error => {
          // this.$message.error('查询过程出错')
        })
      } else if (this.filterFlag3 == 2) {
        this.viewConfirms()
      } else if (this.filterFlag3 == 3) {
        viewComments(this.$route.query.id, this.commentRowId).then(res => {
          // console.log(res.data);
          this.viewCommmentData = []
          this.totalCount2 = res.data.length
          this.viewCommmentData = res.data.slice(
            (this.currentPage2 - 1) * this.pageSize2,
            this.pageSize2 * this.currentPage2
          )
          // this.$message.success('查询成功')
        }).catch(error => {
          // this.$message.error('查询过程出错')
        })
        this.viewCommmentData = []
      }
    },

    timeformat(val) {
      let gstime = val.mtime.replace('T', ' ')
      return gstime;
    },

    filterCreateTime(val) {
      let gstime = val.create_time.replace('T', ' ')
      return gstime;
    },

    filterLevelId(val) {
      if (val.levelId == 1) {
        let levelId = val.levelId.replace('1', '中心层面')
        return levelId
      } else if (val.levelId == 2) {
        let levelId = val.levelId.replace('2', '受试者层面')
        return levelId
      }
    },

    buttonType(marked) {
      if (marked == 1) {
        return "warning";
      } else {
        return "";
      }
    },

    tableRowClassName({ row, rowIndex }) {
      if (row.confirmed == 1) {
        return "success-row"
      }
      return "";
    },

    getfilterSiteidItem() {
      let apiArr = []
      getSiteIdFilterList(this.$route.query.pid).then(res => {
        apiArr.push(...res.data)
      }).catch(error => {
        // this.$message.error('查询过程出错')
      })
      return apiArr;
    },

    getfilterSubjidItem() {
      let apiArr = []
      console.log(this.filterVO);
      getSubjIdFilterList(this.$route.query.pid).then(res => {
      // getSubjIdFilterList(this.filterVO).then(res => {
        apiArr.push(...res.data)
        this.FilterSubjidList = []
        this.FilterSubjidList.push(...res.data)
      }).catch(error => {
        // this.$message.error('查询过程出错')
      })
      return apiArr;
    },

    getfilterLabtestItem() {
      let apiArr = []
      getLabtestFilterList(this.$route.query.pid).then(res => {
      // getLabtestFilterList(this.filterVO).then(res => {
        apiArr.push(...res.data)
        this.FilterLabtestList = []
        this.FilterLabtestList.push(...res.data)
      }).catch(error => {
        // this.$message.error('查询过程出错')
      })
      return apiArr;
    },

    getfilterLabcatItem() {
      let apiArr = []
      getLabcatFilterList(this.$route.query.pid).then(res => {
      // getLabcatFilterList(this.filterVO).then(res => {
        apiArr.push(...res.data)
        this.FilterLabcatList = []
        this.FilterLabcatList.push(...res.data)
      }).catch(error => {
        // this.$message.error('查询过程出错')
      })
      return apiArr;
    },

    FilterChange() {
      this.ExecutionParams.conditionList = []
      this.command = ''
      this.filterFlag = ''
      this.barchartflag = ''

      this.filterVO.pid = this.$route.query.pid
      this.filterVO.currentPage = this.currentPage
      this.filterVO.pageSize = this.pageSize
      this.filterVO.flag = this.flag
      this.filterVO.sortProp = this.orderBys
      if (this.filterFlag2 == 1) {
        this.filterVO.filterSubjid = this.FilterSubjid
        this.filterVO.filterLabtest = this.FilterLabtest
        this.filterVO.filterLabcat = this.FilterLabcat
      } else if (this.filterFlag2 == 2) {
        this.filterVO.filterSubjid = this.FilterSubjid2
        this.filterVO.filterLabtest = this.FilterLabtest2
        this.filterVO.filterLabcat = this.FilterLabcat2
      }
      filter(this.filterVO).then(res => {
        this.reportData = []
        this.totalCount = res.data.total
        this.pageSize = res.data.size
        this.currentPage = res.data.current
        this.reportData.push(...res.data.records)
      }).catch(error => {
        this.$message.error('筛选出错')
      })

    },

    filterChange(filterObj) {
      // this.currentPage = 1
      this.filterFlag2 = 1;
      this.ExecutionParams.conditionList = []
      this.command = ''
      this.filterFlag = ''
      this.barchartflag = ''

      this.filterVO.pid = this.$route.query.pid
      this.filterVO.currentPage = this.currentPage
      this.filterVO.pageSize = this.pageSize
      if (filterObj.filterSubjid != null) {
        this.filterVO.filterSubjid = []
        this.FilterSubjid = []
        this.filterVO.currentPage = 1
        for (var i = 0; i < filterObj.filterSubjid.length; i++) {
          this.filterVO.filterSubjid.push(filterObj.filterSubjid[i])
          this.FilterSubjid.push(filterObj.filterSubjid[i])
        }
      }
      if (filterObj.filterLabtest != null) {
        this.filterVO.filterLabtest = []
        this.FilterLabtest = []
        this.filterVO.currentPage = 1
        for (var i = 0; i < filterObj.filterLabtest.length; i++) {
          this.filterVO.filterLabtest.push(filterObj.filterLabtest[i])
          this.FilterLabtest.push(filterObj.filterLabtest[i])
        }
      }
      if (filterObj.filterLabcat != null) {
        this.filterVO.filterLabcatt = []
        this.FilterLabcat = []
        this.filterVO.currentPage = 1
        for (var i = 0; i < filterObj.filterLabcat.length; i++) {
          this.filterVO.filterLabcat.push(filterObj.filterLabcat[i])
          this.FilterLabcat.push(filterObj.filterLabcat[i])
        }
      }
      // console.log(this.filterVO);
      filter(this.filterVO).then(res => {
        // console.log(res);
        this.reportData = []
        this.totalCount = res.data.total
        this.pageSize = res.data.size
        this.currentPage = res.data.current
        // console.log(res.data.records);
        this.reportData.push(...res.data.records)
      }).catch(error => {
        this.$message.error('筛选出错')
      })

    },

    filterChange2(filterObj) {
      this.filterFlag3 = 1
      this.filterLevel = filterObj.filterlevelId[0]
      viewComments2(this.$route.query.id, this.filterLevel).then(res => {
        this.viewCommmentData2 = []
        this.totalCount2 = res.data.length
        this.viewCommmentData2 = res.data.slice(
          (this.currentPage2 - 1) * this.pageSize2,
          this.pageSize2 * this.currentPage2
        )
      }).catch(error => {
      })
    },

    filterChangeOverride() {
      this.filterFlag2 = 2;
      this.ExecutionParams.conditionList = []
      this.command = ''
      this.filterFlag = ''
      this.barchartflag = ''

      this.filterVO.pid = this.$route.query.pid
      this.filterVO.currentPage = this.currentPage
      this.filterVO.pageSize = this.pageSize
      this.filterVO.flag = this.flag
      if (this.FilterSubjid2 != null) {
        this.filterVO.filterSubjid = []
        this.filterVO.currentPage = 1
        for (var i = 0; i < this.FilterSubjid2.length; i++) {
          this.filterVO.filterSubjid.push(this.FilterSubjid2[i])
        }
        // this.getfilterLabtestItem()
        // this.getfilterLabcatItem()
      }
      if (this.FilterLabtest2 != null) {
        this.filterVO.filterLabtest = []
        this.filterVO.currentPage = 1
        for (var i = 0; i < this.FilterLabtest2.length; i++) {
          this.filterVO.filterLabtest.push(this.FilterLabtest2[i])
        }
        // this.getfilterSubjidItem()
        // this.getfilterLabcatItem()
      }
      if (this.FilterLabcat2 != null) {
        this.filterVO.filterLabcat = []
        this.filterVO.currentPage = 1
        for (var i = 0; i < this.FilterLabcat2.length; i++) {
          this.filterVO.filterLabcat.push(this.FilterLabcat2[i])
        }
        // this.getfilterSubjidItem()
        // this.getfilterLabtestItem()
      }
      filter(this.filterVO).then(res => {
        this.reportData = []
        this.totalCount = res.data.total
        this.pageSize = res.data.size
        this.currentPage = res.data.current
        this.reportData.push(...res.data.records)
      }).catch(error => {
        this.$message.error('筛选出错')
      })

    },

    ReloadOverride(flag) {
      this.filterFlag2 = 2;
      this.ExecutionParams.conditionList = []
      this.command = ''
      this.filterFlag = ''
      this.barchartflag = ''
      if (flag == 1) {
        this.FilterSubjid2 = []
        this.filterVO.filterSubjid = []
      } else if (flag == 2) {
        this.FilterLabtest2 = []
        this.filterVO.filterLabtest = []
      } else if (flag == 3) {
        this.FilterLabcat2 = []
        this.filterVO.filterLabcat = []
      }
      this.filterVO.pid = this.$route.query.pid
      this.filterVO.currentPage = this.currentPage
      this.filterVO.pageSize = this.pageSize
      filter(this.filterVO).then(res => {
        this.reportData = []
        this.totalCount = res.data.total
        this.pageSize = res.data.size
        this.currentPage = res.data.current
        this.reportData.push(...res.data.records)
      }).catch(error => {
        this.$message.error('筛选出错')
      })

    },

    flexColumnWidth(label, prop) {
      // 1.获取该列的所有数据
      const arr = this.tableData.map((x) => x[prop]);
      arr.push(label); // 把每列的表头也加进去算
      // 2.计算每列内容最大的宽度 + 表格的内间距（依据实际情况而定）
      let maxLength = this.getMaxLength(arr);
      return maxLength + 25 + "px";
    },

    isOwner(creater) {
      if (creater == localStorage.getItem('realname')) {
        return true;
      } else {
        return false;
      }
    },

    ClickTableLocation(pid, uuid) {
      this.currentPage = 1
      location(pid, uuid, this.currentPage, this.pageSize).then(res => {
        // console.log(res.data.records);
        this.reportData = []
        this.totalCount = res.data.total
        this.pageSize = res.data.size
        this.currentPage = res.data.current
        this.reportData.push(...res.data.records)
      }).catch(error => {
        // this.$message.error('报告定位出错')
      })
      this.viewConfirmDataForm = false
      this.viewCommmentDataForm2 = false
    },

    getBarChartData(pid) {
      getBarChartData(pid).then(res => {
        this.xData = res.data.xaxisData
        this.yData = res.data.data
        this.initChart(pid)
      }).catch(error => {
      })
    },
    getBarChartData2(pid) {
      getBarChartData2(pid, this.flag).then(res => {
        this.xData = res.data.xaxisData
        this.yData = res.data.data
        this.initChart(pid)
      }).catch(error => {
      })
    },
    getPieChartData(pid) {
      getPieChartData(pid).then(res => {
        console.log(res.data);
      }).catch(error => {
      })
    },
    initChart(pid) {
      this.chart = echarts.init(document.getElementById('main'), 'macarons')
      this.chart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          top: 10,
          left: '2%',
          right: '2%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data: this.xData,

          axisTick: {
            alignWithLabel: true
          }
        }],
        yAxis: [{
          type: 'value',
          axisTick: {
            show: false
          }
        }],
        series: [{
          name: '有质疑',
          type: 'bar',
          // stack: 'vistors',
          barWidth: '60%',
          data: this.yData,
          itemStyle: {
            normal: {
              color: '#3497FF'
            }
          }
          // animationDuration
        }]
      })
      var that = this
      this.chart.on('click', function (params) {
        if (params.componentType === 'series') {
          that.siteid = params.name
          that.currentPage = 1
          if (that.flag == '') {
            that.filterByBar(pid, that.siteid)
          } else {
            that.filterByBar2(pid, that.siteid, that.flag)
          }

        }
      })
    },
    filterByBar(pid, siteid) {
      this.filterFlag2 = '';
      this.ExecutionParams.conditionList = []
      this.command = ''
      this.filterFlag = ''
      this.barchartflag = 1
      filterByBarData(pid, siteid, this.currentPage, this.pageSize).then(res => {
        // console.log(res);
        this.reportData = []
        this.totalCount = res.data.total
        this.pageSize = res.data.size
        this.currentPage = res.data.current
        // console.log(res.data.records);
        this.reportData.push(...res.data.records)
      }).catch(error => {
        // this.$message.error('报告获取出错')
      })
    },
    filterByBar2(pid, siteid, flag) {
      this.filterFlag2 = '';
      this.ExecutionParams.conditionList = []
      this.command = ''
      this.filterFlag = ''
      this.barchartflag = 2
      filterByBarData2(pid, siteid, flag, this.currentPage, this.pageSize).then(res => {
        // console.log(res);
        this.reportData = []
        this.totalCount = res.data.total
        this.pageSize = res.data.size
        this.currentPage = res.data.current
        // console.log(res.data.records);
        this.reportData.push(...res.data.records)
      }).catch(error => {
        // this.$message.error('报告获取出错')
      })
    },

    invisiableTable() {
      if (this.showTable == true) {
        this.showTable = false
        this.showTable2 = true
        return
      } else if (this.showTable == false) {
        this.showTable2 = false
        this.showTable = true
        this.getBarChartData(this.$route.query.pid)
        return
      }

    },

    sortChange({ order, prop }) {
      // this.currentPage = 1
      if (this.filterFlag2 == '') {
        this.filterFlag2 = 1;
      }
      this.ExecutionParams.conditionList = []
      this.command = ''
      this.filterFlag = ''
      this.barchartflag = ''
      // 触发的排序和缓存的排序相同时，取消该字段的排序
      if (!order || this.sortField[prop] === order) {
        this.sortField[prop] = null
      } else {
        this.sortField[prop] = order
      }
      // console.log(this.sortField)
      this.orderBys = []
      let direction = ''
      for (const i in this.sortField) {
        if (this.sortField[i] == 'ascending') {
          direction = 'true'
        } else if (this.sortField[i] == 'descending') {
          direction = 'false'
        }
        this.orderBys.push({
          "column": i,
          "direction": direction
        })
      }
      this.filterVO.pid = this.$route.query.pid
      this.filterVO.currentPage = this.currentPage
      this.filterVO.pageSize = this.pageSize
      this.filterVO.flag = this.flag
      this.filterVO.sortProp = this.orderBys
      this.filterVO.filterSubjid = this.FilterSubjid2
      this.filterVO.filterLabtest = this.FilterLabtest2
      this.filterVO.filterLabcat = this.FilterLabcat2
      // console.log(this.filterVO);
      filter(this.filterVO).then(res => {
        this.reportData = []
        this.totalCount = res.data.total
        this.pageSize = res.data.size
        this.currentPage = res.data.current
        this.reportData.push(...res.data.records)
      }).catch(error => {
        this.$message.error('筛选出错')
      })
    },

    handleHeadAddClass({ column }) {
      if (this.sortField[column.property]) {
        column.order = this.sortField[column.property]
      }
    },

    readonly() {
      this.realname = localStorage.getItem('realname')
      const obj = JSON.parse(localStorage.getItem('ProjectRole'));
      // let projectname2 = '';
      // projectname2 = this.$route.query.project.replace("I", "I");
      // projectname2 = this.$route.query.project.replace("II", "Ⅱ");
      // projectname2 = this.$route.query.project.replace("III", "Ⅲ");
      let projectname2 = this.$route.query.project;
      var rolelist = []
      for (let i = 0; i < obj.length; i++) {
        let pro
        pro = obj[i].studyCode.replace("I", "I");
        pro = obj[i].studyCode.replace("Ⅱ", "II");
        pro = obj[i].studyCode.replace("Ⅲ", "III");
        if (pro == projectname2) {
          rolelist.push(obj[i].role)
        }
      }
      if (rolelist.includes("Medical")) {
        this.efectiveRole = "Medical"
      } else {
        this.efectiveRole = rolelist[0]
      }
      console.log(this.efectiveRole);
      if (this.efectiveRole == 'Medical') {
        this.onlyread = true
      }
    },

  }
}
</script>

<style>
.el-table .warning-row {
  background: oldlace;
}

.el-table .success-row {
  background: #f0f9eb;
}

.el-dialog__header {
  padding: 20px 20px 0px;
}

.el-dialog__body {
  padding: 30px 20px 15px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

.el-table .cell {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-all;
  line-height: 23px;
  padding-left: 1px;
  padding-right: 1px;
  /* font-size: 3px; */
}

.titleColumn {
  background: #D5D9DD;
  text-align: right;
  height: 25px;
}
</style>