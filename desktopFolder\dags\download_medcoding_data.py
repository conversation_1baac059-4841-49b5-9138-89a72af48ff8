import airflow
from airflow.models import DAG
from airflow.operators.bash_operator import <PERSON>sh<PERSON>perator
from datetime import datetime
args = {
    'owner': 'zhou<PERSON>',
    'start_date': datetime(2023, 7, 7),
    'depends_on_past': False
}

dag = DAG(dag_id='download_medcoding_data', default_args=args, schedule_interval="0 17 * * *")
download_task = BashOperator(
    task_id='java_task',
    bash_command='pkill -f medcodingDownload-0.0.1-SNAPSHOT.jar && nohup java -jar /home/<USER>/home/<USER>',
    dag=dag
)

download_task