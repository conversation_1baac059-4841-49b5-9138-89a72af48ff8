package net.bioknow.cdtms.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import net.bioknow.cdtms.lightpdfSign.MapDeserializerDoubleAsIntFix;
import net.bioknow.mvc.RootAction;
import net.bioknow.mvc.tools.Language;
import net.bioknow.passport.datamng.PassportCacheUtil;
import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbcore.dbmng.CNT_DbMng;
import net.bioknow.uap.dbcore.schema.CNT_Schema;
import net.bioknow.uap.dbcore.schema.Schema;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.entitymng.EntityUtil;
import net.bioknow.uap.schemaplug.dtref.DtrefDAO;
import net.bioknow.uapplug.reportform.DAOReportform;
import net.bioknow.uapplug.reportform.ReportformUtil;
import net.bioknow.uapplug.reportform.entities.ObjTableRef;
import net.bioknow.uapplug.reportform.entities.ObjVisit;
import net.bioknow.webutil.langutil.LangCacheUtil;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.SerializationUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


public class ActionGanttIntegrate extends RootAction {

    private Date addMilliesToDate(Date date, long diffMillies) {
        long diffInMillis = diffMillies;
        return new Date(date.getTime() + diffInMillis);
    }

    private boolean containsKey(String[] keys, String key) {
        for (String k : keys) {
            if (k.equals(key)) {
                return true;
            }
        }
        return false;
    }

    private void filterMap(Map<String, Object> originalMap, String[] keysToKeep) {
        Iterator<Map.Entry<String, Object>> iterator = originalMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> entry = iterator.next();
            if (!containsKey(keysToKeep, entry.getKey())) {
                iterator.remove();
            }
        }
    }

    private String formatRadioValue(DAODbApi apidao, String fieldid, EntityScheduleTemplate k) {
        String result = "";
        Map mapF = apidao.getMapField(EntityUtil.getTableId(EntityScheduleTemplate.class), fieldid);
        Schema schema = apidao.getFieldType(EntityUtil.getTableId(EntityScheduleTemplate.class), fieldid);
        if (fieldid.equals("scheduletype")) {
            result = schema.formatToOutput(EntityUtil.getTableId(EntityScheduleTemplate.class), mapF, EntityUtil.tranToMap(k));
        } else if (fieldid.equals("preschedule")) {
            result = schema.formatToOutput(EntityUtil.getTableId(EntityScheduleTemplate.class), mapF, EntityUtil.tranToMap(k));
        }
        return result;
    }

    private List<EntityScheduleTemplate> getScheduleBytime(String projectId, String timestr) {
        String[] arr = timestr.split(";");
        Map mapTime = new HashMap();
        List<String> listTime = new ArrayList();
        for (String str : arr) {
            String[] ar = str.split(",");
            mapTime.put(ar[0], ar[1]);
            listTime.add(ar[1]);
        }
        List<EntityScheduleTemplate> listSchedule = EntityUtil.listEntity(projectId, "", "obj.num asc", 1000, 1, EntityScheduleTemplate.class);
        int i = 0;
        for (EntityScheduleTemplate schedule : listSchedule) {
            String type = schedule.scheduletype;
            if (StringUtils.equals("milestone", type)) {

                schedule.schedulestart = listTime.get(i);
                schedule.scheduleend = listTime.get(i);
                String end = listTime.size() > i + 1 ? listTime.get(i + 1) : listTime.get(i);
                try {
                    setstagetime(schedule.schedulestart, end, schedule, listSchedule);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                //formatRadioValue(apidao, "scheduletype", schedule);
                //formatRadioValue(apidao, "preschedule", schedule);
                i++;
            }
        }
        return listSchedule;
    }

    private String getSelectorValue(String key, String selector) {
        String[] sel = selector.split(";");
        for (int i = 0; i < sel.length; i++) {
            if (sel[i].equals("")) continue;
            int p = sel[i].indexOf(",");
            if (p <= 0) continue;
            String[] selA = new String[]{sel[i].substring(0, p), sel[i].substring(p + 1).trim()};
            if (selA[0].trim().equalsIgnoreCase(key)) {
                return selA[1];
            }
        }
        return "";
    }

    private void setstagetime(String start, String end, EntityScheduleTemplate ps, List<EntityScheduleTemplate> listSchedule) throws Exception {
        String projectid = SessUtil.getSessInfo().getProjectid();
        DAODbApi apidao = new DAODbApi(projectid);
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        List<EntityScheduleTemplate> list = listSchedule.stream().filter(k -> ("," + k.preschedule + ",").contains("," + ps.num + ",")).collect(Collectors.toList());
        for (EntityScheduleTemplate tm : list) {
            if ("milestone".equals(tm.scheduletype)) continue;
            tm.schedulestart = ps.scheduleend;
            try {
                Date st = sf.parse(start);
                Date et = sf.parse(end);
                long days = (et.getTime() - st.getTime()) / 1000 / 24 / 3600;
                long l = new Double(days * Double.parseDouble(tm.scheduleper)).longValue();
                tm.scheduleend = sf.format(new Date(sf.parse(tm.schedulestart).getTime() + l * 24 * 3600 * 1000));
                List<EntityScheduleTemplate> list2 = listSchedule.stream().filter(k -> ("," + k.preschedule + ",").contains("," + tm.num + ",")).collect(Collectors.toList());
                if (list2.size() > 0) {
                    setstagetime(start, end, tm, listSchedule);
                }
                //formatRadioValue(apidao, "scheduletype", tm);
                //formatRadioValue(apidao, "preschedule", tm);
            } catch (ParseException parseException) {
                parseException.printStackTrace();
            }
        }
    }

    public synchronized void callback(HttpServletRequest request, HttpServletResponse response) throws IOException {
//        try {
//            String projectId = SessUtil.getSessInfo().getProjectid();
//            DAODataMng daoDataMng = new DAODataMng(projectId);
//            String nativeeditorStatus = request.getParameter("!nativeeditor_status");
//            String ganttMode = request.getParameter("gantt_mode");
//            String text = request.getParameter("text");
////            System.out.println(text);
//            Map<String, String[]> parameterMap = request.getParameterMap();
//            HashMap<String, Object> MsgMap = new HashMap<>();
//            MsgMap.put("ganttMode", ganttMode);
//            Long taskId = Long.valueOf(request.getParameter("id"));
//            String tableId = "";
//            SimpleDateFormat SDFymdhms = new SimpleDateFormat("yyyy-MM-dd HH:mm");
//            boolean changeCycle = false;
//            switch (ganttMode) {
//                case "tasks":
//                    tableId = "schedule";
//                    break;
//                case "links":
//                    tableId = "schedule_links";
//                    break;
//            }
//
//            if (StringUtils.equals(nativeeditorStatus, "deleted")) {
////                daoDataMng.delRecord(tableId,taskId);
//                daoDataMng.delRecord(tableId, "obj.id=" + taskId);
//                MsgMap.put("status", "200");
//                Gson gson = new Gson();
//                String MsgJson = gson.toJson(MsgMap);
//                response.getOutputStream().write(MsgJson.getBytes(StandardCharsets.UTF_8));
//                response.getOutputStream().close();
//                return;
//            }
//
//            if (StringUtils.equals(ganttMode, "tasks")) {
//                HashMap<Object, Object> taskToSaveMap = new HashMap<>();
//                if (!StringUtils.equals(nativeeditorStatus, "inserted")) {
//                    taskToSaveMap.put("id", Long.valueOf(request.getParameter("id")));
//                    String type = request.getParameter("type");
//                    if (StringUtils.equals(type, "cycle")) {
//                        Map currTaskMap = daoDataMng.getRecord(tableId, Long.valueOf(request.getParameter("id")));
//                        String currCycle = (String) currTaskMap.get("cycle");
//                        Date plannedStartBaseDate = (Date) currTaskMap.get("planned_start_date");
//                        Date plannedEndBaseDate = (Date) currTaskMap.get("planned_end_date");
//                        String reqCycle = request.getParameter("cycle");
//                        String plannedStartDate = request.getParameter("planned_start_date");
//                        String plannedEndDate = request.getParameter("planned_end_date");
//                        if (!StringUtils.equals(currCycle, reqCycle) || plannedStartBaseDate.equals(SDFymdhms.parse(plannedStartDate)) || plannedEndBaseDate.equals(SDFymdhms.parse(plannedEndDate))) {
//                            changeCycle = true;
//                        }
//                    }
//                }
//
//                if (StringUtils.equals(nativeeditorStatus, "inserted")) {
//                    changeCycle = true;
//                    Long sortorder = 0L;
//                    String sortorderParm = request.getParameter("sortorder");
//                    if (StringUtils.isNotEmpty(sortorderParm)) {
//                        sortorder = Long.valueOf(sortorderParm);
//                    } else {
//                        List maxSortorderList = daoDataMng.listRecord("schedule", "obj.study_id=" + Long.valueOf(request.getParameter("studyCode")), "sortorder desc", 1);
//                        if (CollectionUtils.isNotEmpty(maxSortorderList)) {
//                            Map maxSortorder = (Map) maxSortorderList.get(0);
//                            sortorder = (Long) maxSortorder.get("sortorder");
//                        }
//                    }
//                    taskToSaveMap.put("sortorder", sortorder);
//                }
//                if (StringUtils.equals(nativeeditorStatus, "order")) {
//                    String target = request.getParameter("target");
////                    Long currentTaskId =taskId;
//                    boolean nextTask;
//                    String targetTaskId;
//                    if (target.startsWith("next:")) {
//                        targetTaskId = target.substring("next:".length());
//                        nextTask = true;
//                    } else {
//                        targetTaskId = target;
//                        nextTask = false;
//                    }
//                    Map targetTask = daoDataMng.getRecord(tableId, Long.valueOf(targetTaskId));
//                    Long targetOrder = (Long) targetTask.get("sortorder");
//                    if (nextTask) targetOrder++;
//                    List<Map> taskList = daoDataMng.listRecord(tableId, "obj.study_id=" + Long.valueOf(request.getParameter("studyCode")) + " and obj.sortorder>=" + targetOrder, null, 10000);
//                    for (Map Task : taskList) {
//                        Long sortorder = (Long) Task.get("sortorder");
//                        sortorder = sortorder + 1;
//                        Task.put("sortorder", sortorder);
//                    }
//                    taskToSaveMap.put("sortorder", targetOrder);
//                    daoDataMng.saveBatch(tableId, taskList, 2L, null);
//                }
//
//                taskToSaveMap.put("study_id", Long.valueOf(request.getParameter("studyCode")));
//                taskToSaveMap.put("event_type", request.getParameter("eventType"));
//                taskToSaveMap.put("name", request.getParameter("text"));
//                taskToSaveMap.put("owner", request.getParameter("owner"));
//                taskToSaveMap.put("desc", request.getParameter("desc"));
//                String type = request.getParameter("type");
//                String cycle = request.getParameter("cycle");
//                taskToSaveMap.put("planned_duration", Long.parseLong(request.getParameter("planned_duration"))*60);
//
//                String plannedStartDate = request.getParameter("planned_start_date");
//
//                if (StringUtils.isNotEmpty(plannedStartDate)) {
//                    taskToSaveMap.put("planned_start_date",
//                            SDFymdhms.parse(plannedStartDate)
//                    );
//                }
//                String plannedEndDate = request.getParameter("planned_end_date");
//
//                if (StringUtils.isNotEmpty(plannedEndDate)) {
//
//                    taskToSaveMap.put("planned_end_date",
//                            SDFymdhms.parse(plannedEndDate)
//                    );
//
//                }
//
//                taskToSaveMap.put("type", type);
//                taskToSaveMap.put("cycle", cycle);
//                double progress = Double.parseDouble(StringUtils.defaultIfEmpty(request.getParameter("progress"), "0"));
//                Date StartDate = SDFymdhms.parse(request.getParameter("start_date"));
//                Date EndDate = SDFymdhms.parse(request.getParameter("end_date"));
//
//                if (StringUtils.equals(type, "task")) {
//                    String actualStartDate = request.getParameter("actual_start_date");
//                    if (StringUtils.isNotEmpty(actualStartDate)) {
//                        taskToSaveMap.put("actual_start_date", SDFymdhms.parse(actualStartDate));
//                    }
//                } else {
//                    if (progress > 0) {
//                        taskToSaveMap.put("actual_start_date", StartDate);
//                    }
//                }
//
//                String actualEndDate = request.getParameter("actual_end_date");
//                if (StringUtils.equals(type, "task")) {
//                    if (StringUtils.isNotEmpty(actualEndDate)) {
//                        taskToSaveMap.put("actual_end_date", SDFymdhms.parse(actualEndDate));
//                    }
//                } else {
//                    if (progress == 1) {
//                        taskToSaveMap.put("actual_end_date", EndDate);
//                    }
//                }
//
//                String actual_duration = request.getParameter("actual_duration");
//                if (StringUtils.isNotEmpty(actual_duration)) {
//                    taskToSaveMap.put("actual_duration", Long.valueOf(actual_duration));
//                }
//                taskToSaveMap.put("constraint_types", request.getParameter("constraint_types"));
//                taskToSaveMap.put("progress", progress);
//                taskToSaveMap.put("parent_schedule_id", request.getParameter("parent").equals("0") ?null:Long.valueOf(request.getParameter("parent")));
//
//                daoDataMng.saveRecord(tableId, taskToSaveMap);
//
//                if (StringUtils.equals(type, "cycle") && changeCycle) {
//                    //todo
//                    MsgMap.put("reload", true);
////                        TaskGeneratorUtil.getExecutionTimeByNum("cycle",)
//                    Long parentId = (Long) taskToSaveMap.get("id");
//                    daoDataMng.delRecord(tableId, "obj.progress=0 and obj.parent_schedule_id=" + taskToSaveMap.get("id"));
//                    List subTaskMaxDateList = daoDataMng.listRecord(tableId, "obj.progress!=0 and obj.parent_schedule_id=" + taskToSaveMap.get("id"), "obj.plan_end_date desc", 1);
//                    Date cycleStartDate;
//
//                    if (CollectionUtils.isNotEmpty(subTaskMaxDateList)) {
//                        Map subTaskMaxDate = (Map) subTaskMaxDateList.get(0);
//                        cycleStartDate = (Date) subTaskMaxDate.get("plan_end_date");
//                    } else {
//                        cycleStartDate = SDFymdhms.parse(plannedStartDate);
//                    }
//
//                    Date cycleEndDate = SDFymdhms.parse(plannedEndDate);
//
//                    List<Date> cycleTaskList = TaskGeneratorUtil.getExecutionTimeByDateRange(cycle, cycleStartDate, cycleEndDate);
//                    List<Map> SubTaskToSaveList = new ArrayList<>();
//                    if (CollectionUtils.isNotEmpty(cycleTaskList)) {
//                        SimpleDateFormat sdfyym = new SimpleDateFormat("yy.M");
//
//                        for (Date SubTaskStartDate : cycleTaskList) {
//                            HashMap<Object, Object> SubTaskToSaveMap = new HashMap<>();
//
//                            SubTaskToSaveMap.put("parent_schedule_id", parentId);
//                            SubTaskToSaveMap.put("study_id", Long.valueOf(request.getParameter("studyCode")));
//                            SubTaskToSaveMap.put("event_type", request.getParameter("eventType"));
//                            SubTaskToSaveMap.put("type", "task");
//                            SubTaskToSaveMap.put("name", request.getParameter("text") + ":" + sdfyym.format(SubTaskStartDate));
//                            SubTaskToSaveMap.put("progress", 0d);
//                            SubTaskToSaveMap.put("owner", request.getParameter("owner"));
////                                taskToSaveMap.put("desc",request.getParameter("desc"));
//                            SubTaskToSaveMap.put("planned_duration", 480l);
//                            SubTaskToSaveMap.put("planned_start_date", SubTaskStartDate);
//                            Calendar calendar = Calendar.getInstance();
//                            calendar.setTime(SubTaskStartDate);
//                            calendar.add(Calendar.HOUR_OF_DAY, 9);
//                            Date SubTaskEndDate = calendar.getTime();
//                            SubTaskToSaveMap.put("planned_end_date", SubTaskEndDate);
//                            SubTaskToSaveList.add(SubTaskToSaveMap);
//                        }
//                        String userid = SessUtil.getSessInfo().getUserid();
//                        daoDataMng.saveBatch(tableId, SubTaskToSaveList, Long.valueOf(userid), null);
//                    }
//                }
//                if (StringUtils.equals(nativeeditorStatus, "inserted")) {
//                    MsgMap.put("tid", taskToSaveMap.get("id"));
//                }
//            }
//
//            if (StringUtils.equals(ganttMode, "links")) {
//                HashMap<Object, Object> linksToSaveMap = new HashMap<>();
//                if (!StringUtils.equals(nativeeditorStatus, "inserted")) {
//                    linksToSaveMap.put("id", Long.valueOf(request.getParameter("id")));
//                } else {
//
//                    List<Map> scheduleList = daoDataMng.listRecord("schedule", "obj.id=" + Long.valueOf(request.getParameter("source")), null, 1);
//                    if (!CollectionUtils.isNotEmpty(scheduleList)) {
//                        MsgMap.put("status", "200");
//
//                        Gson gson = new Gson();
//                        String MsgJson = gson.toJson(MsgMap);
//                        response.getOutputStream().write(MsgJson.getBytes(StandardCharsets.UTF_8));
//                        response.getOutputStream().close();
//                        return;
//                    }
//                    linksToSaveMap.put("study_id", scheduleList.get(0).get("study_id"));
//                }
//
//                linksToSaveMap.put("schedule_id", Long.valueOf(request.getParameter("source")));
//                linksToSaveMap.put("target_schedule_id", Long.valueOf(request.getParameter("target")));
//                linksToSaveMap.put("type", request.getParameter("type"));
//                Object lag = request.getParameter("lag");
//
//                if (ObjectUtils.isNotEmpty(lag)) {
//                    linksToSaveMap.put("lag", Long.valueOf(request.getParameter("lag")));
//                }
//
//                daoDataMng.saveRecord(tableId, linksToSaveMap);
//
//                if (StringUtils.equals(nativeeditorStatus, "inserted")) {
//                    MsgMap.put("tid", linksToSaveMap.get("id"));
//                }
//            }
//
//            MsgMap.put("status", "200");
//
//            Gson gson = new Gson();
//            String MsgJson = gson.toJson(MsgMap);
//            response.getOutputStream().write(MsgJson.getBytes(StandardCharsets.UTF_8));
//            response.getOutputStream().close();
//        } catch (Exception e) {
//            Log.error("", e);
//        }


    }

    public static Map<Long, Long> convertListMapToMap(List<Map<String, Object>> list, String key1, String key2) {
        Map<Long, Long> result = new HashMap<>();

        for (Map<String, Object> map : list) {
            // 获取旧键对应的值
            Long newKey = (Long) map.get(key1);
            Long newValue = (Long) map.get(key2);
            // 设置新键和新值
            result.put(newKey, newValue);
        }
        return result;
    }

    public void finishSchedule(HttpServletRequest request, HttpServletResponse response) {
        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            String time = request.getParameter("time");
            String per = request.getParameter("per");
            String respo = request.getParameter("respo");
            String studyid = request.getParameter("studyid");
            List<EntityScheduleTemplate> listSchedule = getScheduleBytime(projectId, time);

            Map mapper = new HashMap();
            String[] arr = per.split(";");
            for (String str : arr) {
                String[] ar = str.split(",");
                mapper.put(ar[0], ar[1]);
            }

            Map maprespo = new HashMap();
            String[] arr2 = respo.split(";");
            for (String str : arr2) {
                String[] ar = str.split(",");
                if (ar == null || ar.length < 2) continue;
                maprespo.put(ar[0], ar[1]);
            }

            DAODataMng dmdao = new DAODataMng(projectId);
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            dmdao.delRecord("schedule", "obj.study_id='" + studyid + "'");
            dmdao.delRecord("schedule_links", "obj.study_id='" + studyid + "'");
            //Long kk = 0L;

            Map mapStudy = dmdao.getRecord("xsht", Long.parseLong(studyid));
            Map mapStudySchedule = new HashMap();
            mapStudySchedule.put("study_id", Long.parseLong(studyid));
            mapStudySchedule.put("name", mapStudy.get("studyid"));

            mapStudySchedule.put("start_date", sf.parse(listSchedule.get(0).schedulestart));
            mapStudySchedule.put("end_date", sf.parse(listSchedule.get(listSchedule.size() - 1).scheduleend));
            mapStudySchedule.put("planned_start_date", sf.parse(listSchedule.get(0).schedulestart));
            mapStudySchedule.put("planned_end_date", sf.parse(listSchedule.get(listSchedule.size() - 1).scheduleend));
            mapStudySchedule.put("text", "");
            mapStudySchedule.put("progress", Double.parseDouble("0.0"));
            mapStudySchedule.put("duration", 0L);
            mapStudySchedule.put("planned_duration", 0L);
            mapStudySchedule.put("sortorder", 0L);
            mapStudySchedule.put("tableid", "");
            mapStudySchedule.put("is_cycle", "0");
            mapStudySchedule.put("parent_schedule_id", null);
            mapStudySchedule.put("type", "project");

            dmdao.saveRecord("schedule", mapStudySchedule);

            //阶段任务
            Map mapStage = new HashMap();
            for (EntityScheduleTemplate tem : listSchedule) {
                String stage = tem.schedulestage;
                String start = tem.schedulestart;
                String end = tem.scheduleend;
                Map map = new HashMap();
                if (mapStage.containsKey(stage)) {
                    map = (Map) mapStage.get(stage);
                } else {
                    mapStage.put(stage, map);
                }
                String s = (String) map.get("start");
                if (StringUtils.isEmpty(s)) {
                    map.put("start", start);
                }
                map.put("end", end);
            }

            Iterator ite = mapStage.keySet().iterator();
            List listTosSaveStage = new ArrayList();
            Long ii = 1L;
            while (ite.hasNext()) {
                String key = (String) ite.next();
                Map map = (Map) mapStage.get(key);
                Map mapStageSchedule = new HashMap();
                listTosSaveStage.add(mapStageSchedule);
                mapStageSchedule.put("study_id", Long.parseLong(studyid));
                mapStageSchedule.put("name", mapStudy.get("studyid"));

                mapStageSchedule.put("start_date", sf.parse((String) map.get("start")));
                mapStageSchedule.put("end_date", sf.parse((String) map.get("end")));
                mapStageSchedule.put("planned_start_date", sf.parse((String) map.get("start")));
                mapStageSchedule.put("planned_end_date", sf.parse((String) map.get("end")));
                mapStageSchedule.put("text", "");
                mapStageSchedule.put("progress", Double.parseDouble("0.0"));
                mapStageSchedule.put("duration", 0L);
                mapStageSchedule.put("planned_duration", 0L);
                mapStageSchedule.put("tableid", "");
                mapStageSchedule.put("is_cycle", "0");
                mapStageSchedule.put("parent_schedule_id", mapStudySchedule.get(CNT_DbMng.id));
                mapStageSchedule.put("type", "project");
                mapStageSchedule.put("sortorder", ii++);
            }
            String userid = SessUtil.getSessInfo().getUserid();
            String unitid = PassportCacheUtil.getUnitIdByUserid(projectId, userid);
            dmdao.saveBatchWithoutEH("schedule", listTosSaveStage, Long.parseLong(userid), unitid == null ? null : Long.parseLong(unitid));

            Map mapstage = new HashMap();
            listTosSaveStage.stream().forEach(k -> {
                Map m = (Map) k;
                mapstage.put(m.get("name"), m.get(CNT_DbMng.id));
            });

            List listtosave = new ArrayList();
            for (EntityScheduleTemplate tem : listSchedule) {
                Map maptosave = new HashMap();
                maptosave.put("study_id", Long.parseLong(studyid));
                maptosave.put("name", tem.schedulename);
                maptosave.put("start_date", sf.parse(tem.schedulestart));
                maptosave.put("end_date", sf.parse(tem.scheduleend));
                maptosave.put("planned_start_date", sf.parse(tem.schedulestart));
                maptosave.put("planned_end_date", sf.parse(tem.scheduleend));
                //maptosave.put("owner", tem.num);
                maptosave.put("text", tem.preschedule);
                maptosave.put("progress", Double.parseDouble("0.0"));
                maptosave.put("duration", (sf.parse(tem.scheduleend).getTime() - sf.parse(tem.schedulestart).getTime()) / 1000);
                maptosave.put("planned_duration", (sf.parse(tem.scheduleend).getTime() - sf.parse(tem.schedulestart).getTime()) / 1000);
                maptosave.put("sortorder", Long.parseLong(tem.sn));
                maptosave.put("tableid", tem.tableid);
                maptosave.put("menuid", tem.menuid);
                if (mapper.containsKey(tem.num)) {
                    maptosave.put("is_cycle", "1");
                    maptosave.put("cycle", mapper.get(tem.num));
                } else {
                    maptosave.put("is_cycle", "0");
                }

                maptosave.put("type", tem.scheduletype);
                if (maprespo.containsKey(tem.num)) {
                    maptosave.put("owner", maprespo.get(tem.num));
                }
                //maptosave.put("parent_schedule_id", mapStudySchedule.get(CNT_DbMng.id));
                maptosave.put("parent_schedule_id", mapstage.get(tem.schedulestage));
                maptosave.put("markfield", tem.nodefieldid);
                maptosave.put("nodefield", tem.markid);

                //maptosave.put("markpoint", tem.markid);
                listtosave.add(maptosave);
            }
            dmdao.saveBatchWithoutEH("schedule", listtosave, Long.parseLong(userid), unitid == null ? null : Long.parseLong(unitid));
            Map mapc = new HashMap();
            for (int i = 0; i < listtosave.size(); i++) {
                Map map = (Map) listtosave.get(i);
                Long rid = (Long) map.get(CNT_DbMng.id);
                String num = (String) map.get("owner");
                mapc.put("," + num + ",", rid);
            }

            List linkList = new ArrayList();
            for (int i = 0; i < listtosave.size(); i++) {
                Map mapV = (Map) listtosave.get(i);
                String num = (String) mapV.get("text");

                Map map = new HashMap();
                linkList.add(map);
                map.put("type", "0");
                map.put("study_id", Long.parseLong(studyid));
                Long numid = (Long) mapc.get(num);
                if (numid != null) {
                    map.put("schedule_id", numid);
                } else {
                    continue;
                }
                map.put("target_schedule_id", mapV.get(CNT_DbMng.id));
            }
            dmdao.saveBatch("schedule_links", linkList, Long.parseLong(userid), unitid == null ? null : Long.parseLong(unitid));

            //处理历史数据
            DAOReportform rfdao = new DAOReportform(projectId);
            DtrefDAO refdao = new DtrefDAO(projectId);
            List listsub = rfdao.listAllTableFirst("xsht");
            listsub.forEach(x -> {
                String tid = (String) x;
                String reffid = refdao.getRefField("xsht", tid);
                try {
                    List sub = dmdao.listRecord(tid, "obj." + reffid + "='" + studyid + "'", null, 1);
                    if (sub != null && sub.size() > 0) {
                        Map map = (Map) sub.get(0);
                        TaskGeneratorUtil.setScheduleFinishTime(projectId, tid, map);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

            });

        } catch (Exception e) {
            Log.error(e.getMessage(), e);
        }
    }

    public void generateTask(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            Long studyId = Long.valueOf(request.getParameter("studyId"));
            String templateId = request.getParameter("templateId");
            String studyStratDateStr = request.getParameter("studyStratDate");
            Date studyStratDate = DateUtils.parseDate(studyStratDateStr, Locale.CHINA, "yyyy-MM-dd");
            Long currentUserid = Long.valueOf(SessUtil.getSessInfo().getUserid());

            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            Map scheduleTemplateMap = daoDataMng.getRecord("schedule_template", Long.valueOf(templateId));

            byte[] ret = new sun.misc.BASE64Decoder().decodeBuffer((String) scheduleTemplateMap.get("data_json"));
            String dataJson = GzipUtil3.uncompressToString(ret);

            Date templateStartDate = (Date) scheduleTemplateMap.get("start_date");
            long diffInMillies = studyStratDate.getTime() - templateStartDate.getTime();

            Map<String, List<Map<String, Object>>> taskDataMap = JSON.parseObject(dataJson, new TypeReference<Map<String, List<Map<String, Object>>>>() {
            });

            taskDataMap.values().stream()
                    .flatMap(List::stream)
                    .forEach(map1 -> map1.replaceAll((key, value) -> {
                        if (value instanceof Number) {
                            Number number = (Number) value;
                            if (number instanceof Integer || number instanceof Long) {
                                return number.longValue();
                            } else if (number instanceof Float || number instanceof Double) {
                                return number.doubleValue();
                            }
                        }
                        return value;
                    }));

            List<Map<String, Object>> scheduleList = taskDataMap.get("schedule");
            List<Map> stuydUserList = daoDataMng.listRecord("roles", "obj.studyid=" + studyId, null, 10000);

            String studyCode = (String) daoDataMng.getRecord("xsht", studyId).get("studyid");
            for (Map<String, Object> scheduleMap : scheduleList) {
                scheduleMap.put("study_id", studyId);
                scheduleMap.put("progress", 0d);
                if (ObjectUtils.isNotEmpty(scheduleMap.get("originalParentScheduleid"))) {
                    if ((Long) scheduleMap.get("originalParentScheduleid") == 0) {
                        scheduleMap.put("name", studyCode);
                    }
                }
                Date baselineStartDate = DateUtils.parseDate((String) scheduleMap.get("planned_start_date"), Locale.CHINA, "yyyy-MM-dd HH:mm:ss");
                Date baselineEndDate = DateUtils.parseDate((String) scheduleMap.get("planned_end_date"), Locale.CHINA, "yyyy-MM-dd HH:mm:ss");
                Date baselineStartAddDayDate = addMilliesToDate(baselineStartDate, diffInMillies);
                Date baselineEndAddDayDate = addMilliesToDate(baselineEndDate, diffInMillies);
                scheduleMap.put("planned_start_date", baselineStartAddDayDate);
                scheduleMap.put("planned_end_date", baselineEndAddDayDate);
                scheduleMap.put("duration", scheduleMap.get("planned_duration"));
                scheduleMap.put("baseline_duration", scheduleMap.get("planned_duration"));
                scheduleMap.put("planned_duration", scheduleMap.get("planned_duration"));
                scheduleMap.put("start_date", baselineStartAddDayDate);
                scheduleMap.put("end_date", baselineEndAddDayDate);

                String ownerRole = (String) scheduleMap.get("owner");
                if (!StringUtils.isEmpty(ownerRole)) {

                    String[] ownerArr = ownerRole.split(",");
                    String[] ownerIdArr = stuydUserList.stream()
                            .filter(stuydUserMap -> Arrays.stream(ownerArr).anyMatch(owner -> owner.equals(stuydUserMap.get("limitnum"))))
                            .map(stuydUserMap -> String.valueOf(stuydUserMap.get("member")))
                            .distinct()
                            .toArray(String[]::new);

                    scheduleMap.put("owner", String.join(",", ownerIdArr));

                }
            }

            daoDataMng.saveBatch("schedule", scheduleList, currentUserid, null);
            List<Map<String, Object>> scheduleToUpdatePIdList = (List<Map<String, Object>>) SerializationUtils.clone((Serializable) scheduleList);
            Map<Long, Long> idReplaceMap = convertListMapToMap(scheduleToUpdatePIdList, "originalId", "id");

            for (Map scheduleToUpdatePIdMap : scheduleToUpdatePIdList) {
                Long originalParentScheduleid = (Long) scheduleToUpdatePIdMap.get("originalParentScheduleid");
                if (originalParentScheduleid == null || originalParentScheduleid == 0) continue;
                scheduleToUpdatePIdMap.put("parent_schedule_id", idReplaceMap.get(originalParentScheduleid));
            }

            daoDataMng.saveBatch("schedule", scheduleToUpdatePIdList, currentUserid, null);
            List<Map<String, Object>> linkList = taskDataMap.get("link");

            Iterator<Map<String, Object>> linkListIterator = linkList.iterator();
            while (linkListIterator.hasNext()) {
                Map linkMap = linkListIterator.next();
                linkMap.put("study_id", studyId);
                Long scheduleId = (Long) linkMap.get("schedule_id");
                Long targetScheduleId = (Long) linkMap.get("target_schedule_id");

                if (idReplaceMap.get(scheduleId) == null) {
                    linkListIterator.remove();
                    continue;
                }

                linkMap.put("schedule_id", idReplaceMap.get(scheduleId));
                linkMap.put("target_schedule_id", idReplaceMap.get(targetScheduleId));
                linkMap.put("type", linkMap.get("type"));

            }

            daoDataMng.saveBatch("schedule_links", linkList, currentUserid, null);

            HashMap<Object, Object> MsgMap = new HashMap<>();
            MsgMap.put("status", "200");
            response.getOutputStream().write(new Gson().toJson(MsgMap).getBytes(StandardCharsets.UTF_8));
            response.getOutputStream().close();
        } catch (Exception e) {
            Log.error("", e);
        }


    }

    public void getRespon(HttpServletRequest request, HttpServletResponse response) {
        try {
            String studyid = request.getParameter("studyid");
            String projectId = SessUtil.getSessInfo().getProjectid();
            String time = request.getParameter("time");
            List<EntityScheduleTemplate> listSchedule = getScheduleBytime(projectId, time);
            DAODataMng dmdao = new DAODataMng(projectId);
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");

            List<Map> listuser = dmdao.listRecord("ryjbzl", "", "", 1000);
            Map mapUser = new HashMap();
            for (Map map : listuser) {
                mapUser.put(String.valueOf(map.get(CNT_DbMng.id)), map.get("xm"));
            }

            List<Map> listroles = dmdao.listRecord("roles", "obj.studyid='" + studyid + "' and obj.active ='1'", null, 1000);

            Map maprole = new HashMap();
            for (Map map : listroles) {
                String projectRole = (String) map.get("limitnum");
                Long roleid = (Long) map.get("member");
                if (StringUtils.isNotEmpty(projectRole)) {
                    String[] str = projectRole.split(",");
                    for (String s : str) {
                        if (maprole.containsKey(s)) {
                            String roleids = (String) maprole.get(s);
                            roleids += "," + roleid;
                            maprole.put(s, roleids);
                        } else {
                            maprole.put(s, roleid + "");
                        }
                    }
                }
            }

            String html = "";
            for (EntityScheduleTemplate tem : listSchedule) {
                html += "<tr style='text-align:center'><td></td><td>" + tem.schedulename + "</td><td>";

                if (maprole.containsKey(tem.studyrole)) {
                    String roleids = (String) maprole.get(tem.studyrole);
                    String[] roles = roleids.split(",");
                    html += "<div >";
                    for (String role : roles) {
                        html += "<input id=" + tem.num + " class=role type=checkbox value=" + role + " checked>" + mapUser.get(role) + "</option>";
                    }
                    html += "</div>";
                }
                html += "</td><td>" + tem.schedulestart + "</td><td>" + tem.scheduleend + "</td><td>";
                Long diff = (sf.parse(tem.scheduleend).getTime() - sf.parse(tem.schedulestart).getTime()) / 24 / 3600 / 1000;
                html += (diff) + "</td></tr>";
            }

            response.getOutputStream().write(html.getBytes(StandardCharsets.UTF_8));
            response.getOutputStream().close();
        } catch (Exception e) {
            Log.error(e.getMessage(), e);
        }
    }

    public void getScheduleTime(HttpServletRequest request, HttpServletResponse response) {
        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            String time = request.getParameter("time");
            DAODbApi apidao = new DAODbApi(projectId);
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            List<EntityScheduleTemplate> listSchedule = getScheduleBytime(projectId, time);

            String html = "";
            Map mapF = apidao.getMapFieldCopy(EntityUtil.getTableId(EntityScheduleTemplate.class), "schedulefre");
            String selector = (String) mapF.get(CNT_Schema.selector);
            String[] sels = selector.split(";");

            for (EntityScheduleTemplate tem : listSchedule) {
                String shtml = "<select type=radio class=fre id='" + tem.num + "'>";
                for (String str : sels) {
                    String[] ar = str.split(",");
                    shtml += "<option value=" + ar[0];
                    if (ar[0].equals(tem.schedulefre)) shtml += " selected ";
                    shtml += ">" + ar[1] + "</option>";
                }

                String type = formatRadioValue(apidao, "scheduletype", tem);
                String pretype = formatRadioValue(apidao, "preschedule", tem);

                shtml += "</select>";
                html += "<tr style='text-align:center'><td></td><td>" + tem.schedulename + "</td><td>" + type
                        + "</td><td style='width:220px;'>" + StringUtils.defaultIfEmpty(pretype, "")
                        + "</td><td>" + tem.schedulestart + "</td><td>" + tem.scheduleend + "</td><td>";

                Long diff = (sf.parse(tem.scheduleend).getTime() - sf.parse(tem.schedulestart).getTime()) / 24 / 3600 / 1000;
                html += (diff) + "</td><td>";
                if ("cycle".equals(tem.scheduletype) || "周期任务".equals(tem.scheduletype)) {
                    html += shtml;
                } else {
                    html += StringUtils.defaultIfEmpty(tem.schedulefre, "");
                }
                html += "</td></tr>";
            }
            response.getOutputStream().write(html.getBytes(StandardCharsets.UTF_8));
            response.getOutputStream().close();
        } catch (Exception e) {
            Log.error(e.getMessage(), e);
        }
    }

    public void getTaskList(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            String studyId = request.getParameter("studyId");

            String getScheduleWhere = "1=2";
            String getUserWhere = "1=2";
            String getStudyWhere = "1=2";
            if (StringUtils.isNotEmpty(studyId)) {
                getScheduleWhere = "obj.study_id=" + studyId;
                getUserWhere = "obj.studyid=" + studyId;
                getStudyWhere = "obj.id=" + studyId;
            } else {

                request.setAttribute("tableid", "xsht");
                String authwhere = DAODataMng.getWhere(request);

                String where = "(obj.closed !='1' or obj.closed is null)";
                if (StringUtils.isNotEmpty(authwhere)) {
                    where += " and (" + authwhere + ")";
                }

                String[] studyIds = daoDataMng.getIdArray("xsht", where, null, 1);
                if (!ArrayUtils.isEmpty(studyIds)) {
                    getScheduleWhere = "obj.study_id in (" + String.join(",", studyIds) + ")";
                    getUserWhere = "obj.studyid in (" + String.join(",", studyIds) + ")";
                    getStudyWhere = "obj.id in (" + String.join(",", studyIds) + ")";
                }
            }

            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();
            HashMap<String, Object> TaskMap = new HashMap<>();

            List<Map> scheduleList = daoDataMng.listRecord("schedule", getScheduleWhere, "study_id,sortorder asc", 10000);
            List<Map> scheduleStudy = daoDataMng.listRecord("schedule", DAODataMng.joinWhere(getScheduleWhere, "obj.parent_schedule_id is null or obj.parent_schedule_id = ''"), "", 1);
            Long studyScheduleid = null;
            if (scheduleStudy != null && scheduleStudy.size() > 0) {
                Map map = scheduleStudy.get(0);
                studyScheduleid = (Long) map.get(CNT_DbMng.id);
            }


            SimpleDateFormat SDFymdhms = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            List<Map> scheduleToJsonList = new ArrayList<>();

            List<ObjVisit> visitList = ReportformUtil.listVisit(projectId, "xsht");
            Map mapcache = new HashMap();
            for (ObjVisit visit : visitList) {
                int index = visit.getVisitnum();
                List<ObjTableRef> listTable = visit.getListTableRef();
                for (ObjTableRef tr : listTable) {
                    String tableid = tr.getTableid();
                    int index2 = tr.getSvnum();
                    mapcache.put(tableid, index + "-" + index2);
                }
            }

            for (Map scheduleMap : scheduleList) {
                Map<String, Object> scheduleToJsonMap = new HashMap<>();
                String tableid = (String) scheduleMap.get("tableid");
                scheduleToJsonMap.put("studyCode", scheduleMap.get("study_id"));
                //scheduleToJsonMap.put("eventType", scheduleMap.get("event_type"));
                scheduleToJsonMap.put("id", scheduleMap.get("id"));
                //String value = "<span onclick='window.top.vueMaskDialogShow({title:\"临床试验项目\",url:\"/uapvue/index.html#/reportform?tableid=xsht&recordid="+scheduleMap.get("study_id")+"&menuPosition="+mapcache.get(tableid)+"\"})'>"+scheduleMap.get("name")+"</span>";
                String menuid = (String) scheduleMap.get("menuid");
                if (StringUtils.isEmpty(menuid))
                    menuid = mapcache.get(tableid) == null ? "" : (String) mapcache.get(tableid);
                scheduleToJsonMap.put("menuPosition", menuid);
                String name = (String)scheduleMap.get("name");
                if(StringUtils.isNotEmpty(name)){
                    name = LangCacheUtil.getTrans(projectId, Language.getLocString(), "schedule:name:"+name, name);
                }
                scheduleToJsonMap.put("text", name);
                scheduleToJsonMap.put("owner", scheduleMap.get("owner"));
                scheduleToJsonMap.put("desc", scheduleMap.get("desc"));
                String type = (String) scheduleMap.get("type");
                scheduleToJsonMap.put("type", "cycle".equals(type) ? "task" : type);
                scheduleToJsonMap.put("cycle", scheduleMap.get("cycle"));
                scheduleToJsonMap.put("render", scheduleMap.get("render"));

                Date planstart = (Date) scheduleMap.get("planned_start_date");
                Date planend = (Date) scheduleMap.get("planned_end_date");
                Date acstart = (Date) scheduleMap.get("actual_start_date");
                Date acend = (Date) scheduleMap.get("actual_end_date");

                scheduleToJsonMap.put("actual_duration", scheduleMap.get("actual_duration"));
                scheduleToJsonMap.put("baseline_duration", scheduleMap.get("baseline_duration"));
                scheduleToJsonMap.put("planned_start_date", planstart != null ? SDFymdhms.format(planstart) : null);
                scheduleToJsonMap.put("planned_end_date", planend != null ? SDFymdhms.format(planend) : null);
                scheduleToJsonMap.put("planned_duration", scheduleMap.get("planned_duration") != null ? (Long) scheduleMap.get("planned_duration") / 60 : 0);
                scheduleToJsonMap.put("constraint_types", scheduleMap.get("constraint_types"));
                scheduleToJsonMap.put("actual_start_date", acstart != null ? SDFymdhms.format(acstart) : null);
                scheduleToJsonMap.put("actual_end_date", acend != null ? SDFymdhms.format(acend) : null);
                double progress = scheduleMap.get("progress") == null ? 0d : (double) scheduleMap.get("progress");

                scheduleToJsonMap.put("progress", String.format("%.8f", progress).replaceAll("0*$", "").replaceAll("\\.$", ""));
                scheduleToJsonMap.put("sortorder", scheduleMap.get("sortorder"));

                scheduleToJsonMap.put("start_date", acstart != null ? SDFymdhms.format(acstart) : planstart == null ? null : SDFymdhms.format(planstart));
                scheduleToJsonMap.put("end_date", acend != null ? SDFymdhms.format(acend) : planend != null ? SDFymdhms.format(planend) : SDFymdhms.format(new Date()));
                //scheduleToJsonMap.put("duration", scheduleMap.get("planned_duration") != null ? scheduleMap.get("planned_duration") : 0);
//                if (progress == 0) {
//                    scheduleToJsonMap.put("start_date", scheduleMap.get("planned_start_date") != null ? SDFymdhms.format(scheduleMap.get("planned_start_date")) : SDFymdhms.format(new Date()));
//                    scheduleToJsonMap.put("end_date", scheduleMap.get("planned_end_date") != null ? SDFymdhms.format(scheduleMap.get("planned_end_date")) : SDFymdhms.format(new Date()));
//                    scheduleToJsonMap.put("duration", scheduleMap.get("planned_duration") != null ? scheduleMap.get("planned_duration") : 0);
//
//                } else {
//                    scheduleToJsonMap.put("start_date", scheduleMap.get("actual_start_date") != null ? SDFymdhms.format(scheduleMap.get("actual_start_date")) : SDFymdhms.format(new Date()));
//                    if (scheduleMap.get("actual_end_date") != null) {
//                        scheduleToJsonMap.put("end_date", scheduleMap.get("actual_end_date") != null ? SDFymdhms.format(scheduleMap.get("actual_end_date")) : SDFymdhms.format(new Date()));
//
//                    } else {
//                        scheduleToJsonMap.put("duration",scheduleMap.get("planned_duration") != null ? scheduleMap.get("planned_duration") : 0);
//                    }
//                }

                //status 0 未开始 无实际开始时间  1 进行中  有实际开始时间 & 无实际结束时间 & 今天 < 计划结束时间
                //2 已完成 有实际开始时间 & 有实际结束时间 3 已延期 有实际开始时间 & 无实际结束时间& 今天 > 计划结束时间
                if (acstart == null) {
                    scheduleToJsonMap.put("status", "0");
                } else if (acstart != null && acend == null && (planend == null || (new Date()).getTime() < planend.getTime())) {
                    scheduleToJsonMap.put("status", "1");
                } else if (acstart != null && acend != null) {
                    scheduleToJsonMap.put("status", "2");
                } else if (acstart != null & acend == null && planend != null && new Date().getTime() > planend.getTime()) {
                    scheduleToJsonMap.put("status", "3");
                }

                DAODbApi apidao = new DAODbApi(projectId);
                String markpoint = (String) scheduleMap.get("markpoint");
                String markfield = (String) scheduleMap.get("markfield");//分段
                String nodefield = (String) scheduleMap.get("nodefield");//标记


                if (StringUtils.isNotEmpty(markpoint)) {
                    String[] arr = markpoint.split(";");
                    List listmark = new ArrayList();
                    for (String str : arr) {
                        if (StringUtils.isBlank(str)) continue;
                        String[] ar = str.split(",");
                        String text = ar[1];
                        if(ar.length > 2) text = ar[2];
                        Map mapmark = new HashMap();
                        mapmark.put("type", ar[0]);
                        mapmark.put("text", text.replaceAll("null", ""));
                        mapmark.put("date", ar.length > 1 ?ar[1]:"");
                        listmark.add(mapmark);
                    }
                    scheduleToJsonMap.put("marks", listmark);
                }
                scheduleToJsonMap.put("open", true);
                if (progress == 1) {
                    scheduleToJsonMap.put("readonly", true);
                }

                Long parent_schedule_id = (Long) scheduleMap.get("parent_schedule_id");
                if (parent_schedule_id != null && parent_schedule_id > 0) {
                    scheduleToJsonMap.put("parent", scheduleMap.get("parent_schedule_id"));
                }

                scheduleToJsonList.add(scheduleToJsonMap);
            }
            TaskMap.put("tasks", scheduleToJsonList);

            List<Map> linkList = daoDataMng.listRecord("schedule_links", getScheduleWhere, null, 10000);

            List<Map> linkToJsonList = new ArrayList<>();


            for (Map linkMap : linkList) {

                Map<String, Object> linkToJsonMap = new HashMap<>();

                linkToJsonMap.put("source", linkMap.get("schedule_id"));
                linkToJsonMap.put("target", linkMap.get("target_schedule_id"));
                linkToJsonMap.put("type", linkMap.get("type"));
                linkToJsonMap.put("id", linkMap.get("id"));
                linkToJsonMap.put("lag", linkMap.get("lag"));

                linkToJsonList.add(linkToJsonMap);

            }


            TaskMap.put("links", linkToJsonList);


            List studyUserObjectList = daoDataMng.list("select obj.studyid as studyid,obj2.xm as label,obj2.id as key from Roles obj,Ryjbzl obj2 where " + getUserWhere + " and obj.member=obj2.id", 10000, 1);


            ArrayList<Map<String, String>> studyUserList = new ArrayList<>();
            for (Object studyUserObject : studyUserObjectList) {
                Object[] studyUserObj = (Object[]) studyUserObject;
                Map studyUserMap = new HashMap<>();
                studyUserMap.put("studyid", studyUserObj[0].toString());
                studyUserMap.put("key", studyUserObj[2].toString());
                studyUserMap.put("label", studyUserObj[1].toString());
                studyUserList.add(studyUserMap);
            }
            Map<Object, Object> allUserMap = new HashMap<>(); // 创建新的 Map 对象，用于存储排重后的结果

            for (Map<String, String> studyUserMap : studyUserList) {
                if (!allUserMap.containsKey(studyUserMap.get("key"))) {
                    allUserMap.put(studyUserMap.get("key"), studyUserMap.get("label"));
                }
            }
            Map<String, List<Map>> StudyUserOptions = studyUserList.stream().collect(Collectors.groupingBy(m -> (m.get("studyid")) + "_UserOptions"));

//            StudyUserOptions.forEach((key, StudyUserList) -> {StudyUserList.forEach(StudyUserMap -> StudyUserMap.remove("studyid"));});
            List stuydToJsonList = new ArrayList();
            List<Map> stuydList = daoDataMng.listRecord("xsht", getStudyWhere + " and (obj.closed !='1' or obj.closed is null)", null, 10000);

            for (Map studyMap : stuydList) {
                HashMap<Object, Object> studyJsonMap = new HashMap<>();
                studyJsonMap.put("key", studyMap.get("id"));
                studyJsonMap.put("label", studyMap.get("studyid"));
                stuydToJsonList.add(studyJsonMap);
            }
            Map collectionsToJsonMap = new HashMap<>();
            collectionsToJsonMap.put("StudyOptions", stuydToJsonList);
            collectionsToJsonMap.putAll(StudyUserOptions);


            Set<String> allUseruniqueKeys = new HashSet<String>();
            List<Map> allUserList = new ArrayList<Map>();

            for (Map studyUserMap : studyUserList) {
                String userId = (String) studyUserMap.get("key");
                if (!allUseruniqueKeys.contains(userId)) {
                    allUseruniqueKeys.add(userId);
                    allUserList.add(studyUserMap);
                }
            }
            collectionsToJsonMap.put("allUserOptions", allUserList);

            TaskMap.put("collections", collectionsToJsonMap);

            String TaskJson = gson.toJson(TaskMap);
            response.setContentType("application/json;charset=UTF-8");
            response.getOutputStream().write(TaskJson.getBytes(StandardCharsets.UTF_8));
            response.getOutputStream().close();
        } catch (Exception e) {
            Log.error("", e);
        }
    }

    public void getTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            List<Map> scheduleTemplateList = daoDataMng.listRecord("schedule_template", "1=1", null, 1000);
            List scheduleTemplateJsonList = new ArrayList<>();
            for (Map scheduleTemplateMap : scheduleTemplateList) {

                HashMap<Object, Object> scheduleTemplateJsonMap = new HashMap<>();
                scheduleTemplateJsonMap.put("key", scheduleTemplateMap.get("id"));
                scheduleTemplateJsonMap.put("value", scheduleTemplateMap.get("name"));
                scheduleTemplateJsonList.add(scheduleTemplateJsonMap);
            }
            Gson gson = new Gson();
            String scheduleTemplateJson = gson.toJson(scheduleTemplateJsonList);

            response.getOutputStream().write(scheduleTemplateJson.getBytes(StandardCharsets.UTF_8));
            response.getOutputStream().close();
        } catch (Exception e) {
            Log.error("", e);
        }


    }

    public void initSchedulePage(HttpServletRequest request, HttpServletResponse response) {
        try {
            String studyId = request.getParameter("studyId");
            String projectId = SessUtil.getSessInfo().getProjectid();
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");

            DAODataMng dmdao = new DAODataMng(projectId);
            List list = dmdao.listRecord("schedule", "obj.study_id='" + studyId + "' and obj.type ='milestone'", null, 100);
            Map mapname = new HashMap();
            list.stream().forEach(k -> {
                Map map = (Map) k;
                mapname.put(map.get("name"), map.get("planned_start_date"));
            });

            List<EntityScheduleTemplate> listSchedule = EntityUtil.listEntity(projectId, "obj.scheduletype='milestone'", "obj.sn asc", EntityScheduleTemplate.class);
            List listrst = new ArrayList();
            listSchedule.stream().forEach(k -> {
                Map map = new HashMap();
                map.put("name", k.schedulename);
                if (mapname.containsKey(k.schedulename))
                    map.put("planstarttime", sf.format((Date) mapname.get(k.schedulename)));
                map.put("num", k.num);
                listrst.add(map);
            });
            request.setAttribute("schedulenamelist", listrst);
            request.setAttribute("studyid", studyId);
            this.forward(request, response, "show2");
        } catch (Exception e) {
            Log.error("", e);
        }
    }

    public static void main(String[] args) throws ParseException, IOException {
        double value = 123.0; // 您的 double 值

        // 使用字符串格式化来将 double 转换为字符串并删除末尾的零

//        Date studyStratDate = DateUtils.parseDate("2023-03-13 08", Locale.CHINA, "yyyy-MM-dd HH");
////        addDaysToDate(studyStratDate, diffDays)
//        System.out.println("转换后的字符串: " + studyStratDate);

        String ret = "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";
        byte[] ret2 = new sun.misc.BASE64Decoder().decodeBuffer(ret);

        String dataJson = GzipUtil3.uncompressToString(ret2);

        System.out.println(dataJson);

    }

    public void reset(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            String studyid = request.getParameter("studyid");
            String projectid = SessUtil.getSessInfo().getProjectid();
            DAODataMng dmdao = new DAODataMng(projectid);
            dmdao.delRecord("schedule", "obj.study_id='" + studyid + "'");
            dmdao.delRecord("schedule_links", "obj.study_id='" + studyid + "'");
            response.getOutputStream().write("ok".getBytes(StandardCharsets.UTF_8));
            response.getOutputStream().close();
        } catch (Exception e) {
            Log.error("", e);
        }
    }

    public void saveAsTemplate(HttpServletRequest request, HttpServletResponse response) {
        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            String studyId = request.getParameter("studyId");
            HashMap<Object, Object> scheduleTemplateToSaveMap = new HashMap<>();
            scheduleTemplateToSaveMap.put("name", request.getParameter("templateName"));
            scheduleTemplateToSaveMap.put("desc", request.getParameter("templateDesc"));
//            HashMap<String, List<Map<String, Object>>> scheduleTemplateMap = new HashMap<>();
            Map<String, List<Map<String, Object>>> scheduleTemplateMap = new HashMap<>();

            List<Map> scheduleListCurrent = daoDataMng.listRecord("schedule", "obj.study_id=" + studyId, "planned_start_date", 10000);
            List<Map> linkListCurrent = daoDataMng.listRecord("schedule_links", "obj.study_id=" + studyId, null, 10000);
            List<Map> stuydUserList = daoDataMng.listRecord("roles", "obj.studyid=" + studyId, null, 10000);

            List<Map<String, Object>> scheduleList = (List<Map<String, Object>>) SerializationUtils.clone((Serializable) scheduleListCurrent);
            List<Map<String, Object>> linkList = (List<Map<String, Object>>) SerializationUtils.clone((Serializable) linkListCurrent);

            Map firstTaskMap = scheduleListCurrent.get(0);
            Date startDate = (Date) firstTaskMap.get("planned_start_date");
            scheduleTemplateToSaveMap.put("start_date", startDate);

            String[] reserveScheduleFileds = new String[]{"originalId", "originalParentScheduleid", "planned_end_date", "type", "planned_duration", "event_type", "sortorder", "owner", "name", "planned_start_date", "desc"};

            for (Map scheduleMap : scheduleList) {

                scheduleMap.put("originalId", scheduleMap.get("id"));
                scheduleMap.put("originalParentScheduleid", scheduleMap.get("parent_schedule_id"));

                filterMap(scheduleMap, reserveScheduleFileds);

                String baselineStartDateStr = DateFormatUtils.format((Date) scheduleMap.get("planned_start_date"), "yyyy-MM-dd HH:mm:ss", Locale.CHINA);
                String baselineEndDateStr = DateFormatUtils.format((Date) scheduleMap.get("planned_end_date"), "yyyy-MM-dd HH:mm:ss", Locale.CHINA);

                scheduleMap.put("planned_start_date", baselineStartDateStr);
                scheduleMap.put("planned_end_date", baselineEndDateStr);

                if (StringUtils.isNotEmpty((String) scheduleMap.get("owner"))) {

                    String owners = (String) scheduleMap.get("owner");
                    String[] ownerArr = owners.split(",");


                    String[] roleArr = stuydUserList.stream()
                            .filter(stuydUserMap -> Arrays.stream(ownerArr).anyMatch(owner -> owner.equals(String.valueOf(stuydUserMap.get("member")))))
                            .map(stuydUserMap -> stuydUserMap.get("limitnum"))
                            .distinct()
                            .toArray(String[]::new);

                    scheduleMap.put("owner", String.join(",", roleArr));
                }
            }

            String[] reservelinkFileds = new String[]{"target_schedule_id", "type", "schedule_id"};

            for (Map linkMap : linkList) {
                filterMap(linkMap, reservelinkFileds);

            }

            scheduleTemplateMap.put("schedule", scheduleList);
            scheduleTemplateMap.put("link", linkList);

            Gson gson = new Gson();

            String data_json = java.util.Base64.getEncoder().encodeToString(GzipUtil3.compress(gson.toJson(scheduleTemplateMap)));
            scheduleTemplateToSaveMap.put("data_json", data_json);
            daoDataMng.save("schedule_template", scheduleTemplateToSaveMap);

            HashMap<String, Object> MsgMap = new HashMap<>();

            MsgMap.put("status", "200");

            String MsgJson = gson.toJson(MsgMap);
            response.getOutputStream().write(MsgJson.getBytes(StandardCharsets.UTF_8));
            response.getOutputStream().close();

        } catch (Exception e) {
            Log.error("", e);
        }


    }

    public void show(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            String studyId = request.getParameter("studyId");

            String prrojectid = SessUtil.getSessInfo().getProjectid();
            DAODataMng dmdao = new DAODataMng(prrojectid);
            List listRecord = dmdao.listRecord("schedule", "obj.study_id='" + studyId + "' and obj.type='project'", null, 1);
//            Long pid = null;
//            if(listRecord != null && listRecord.size() >0 ){
//                Map mapRecord = (Map)listRecord.get(0);
//                pid = (Long)mapRecord.get(CNT_DbMng.id);
//            }
            String where = "obj.study_id='" + studyId + "'";
            //if(pid !=null) where += " and obj.parent_schedule_id='"+pid+"'";
            int count = dmdao.count("schedule", where);
            int count2 = dmdao.count(EntityUtil.getTableId(EntityScheduleTemplate.class), null);
            if (StringUtils.isNotEmpty(studyId) && count == 0 || count < count2) {
                this.redirectByUri(request, response, "/uapvue/index.html#/cdtms_project_progress?studyId=" + studyId);
                return;
            }

            String currRoleName = (String) SessUtil.getSessInfo().getUser().get("ryjs");

            Long userid = Long.valueOf(SessUtil.getSessInfo().getUserid());

            if (userid <= 2) {

                currRoleName = "MDM";
            }

            if (StringUtils.isNotEmpty(studyId)) {
                request.setAttribute("studyId", request.getParameter("studyId"));
                request.setAttribute("currRoleName", currRoleName);
            }
            request.setAttribute("local", "en".equals(Language.getLocString())?"en":"cn");
            this.forward(request, response, "show");
        } catch (Exception e) {
            Log.error("", e);
        }
    }
}
