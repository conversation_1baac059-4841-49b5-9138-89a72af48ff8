"C:\Program Files (x86)\WinSCP\WinSCP.exe" /command "open ftp://erkang.zhou.hengrui.com:Zero2One4?@************:990 -implicit" "cd /hengruimedicineravexftp/hengruimedicine-ravex.mdsol.com/sasondemand" "lcd D:\Projects\GRP_CDSC_PENGR\SHR-1316-III-302-EN\data\raw" "get -neweronly *hrtau.support@hengrui.com1_SHR_1316_III_302_EN*" "exit" /log=D:\Projects\GRP_CDSC_PENGR\SHR-1316-III-302-EN\data\raw\log_file_%ymd%.txt 
@echo off

set path="D:\Projects\GRP_CDSC_PENGR\SHR-1316-III-302-EN\data\raw"

if exist "%path%" (
	 dir /b /o:d "%path%\*.zip*" > D:\Projects\GRP_CDSC_PENGR\SHR-1316-III-302-EN\data\raw\output.txt
    )  

for /F "delims=" %%a in (D:\Projects\GRP_CDSC_PENGR\SHR-1316-III-302-EN\data\raw\output.txt) do (set "lastLine=%%a")
echo %lastLine%
set timestamp
setlocal enabledelayedexpansion

:: Extract the time substring
set "time=%lastLine%"
set "filename=%lastLine%"
for /f "tokens=9 delims=_" %%a in ("%time%") do (
    set "time=%%a"
)
for /f "tokens=10 delims=_" %%b in ("%filename%") do (
    set "filename=%%b"
)

:: Convert the time format to YYYY/MM/DD hh/mm
set "year=%time:~0,4%"
set "month=%time:~4,2%"
set "day=%time:~6,2%"
set "hour=%filename:~0,2%"
set "minute=%filename:~2,2%"
set "result=%year%/%month%/%day%/%hour%/%minute%"
echo %result%
set "timestamp=%result%"
rem Get current time with format yyyy/MM/dd/HH/mm/ss
for /f "tokens=2-4 delims=/ " %%a in ('date /t') do (set mydate=%%c/%%a/%%b) 
for /f "tokens=1-3 delims=/:" %%a in ('time /t/24') do (set mytime=%%a/%%b)

rem Set environment variables  
set MC_PATH=C:\Users\<USER>\Desktop\mc.exe
set SOURCE_FILE=D:\Projects\GRP_CDSC_PENGR\SHR-1316-III-302-EN\data\raw\%lastLine%   
set DEST_PATH=minios3/raw/SHR-1316-III-302-EN_sas.zip
set TAGS="key1=RAVE&key2=SHR-1316-III-302-EN&key3=%timestamp%"  

echo Upload started at %time% on %date%...  

rem Execute mc to upload file with tags
"%MC_PATH%" cp %SOURCE_FILE% %DEST_PATH% --tags %TAGS%  

if %errorlevel% neq 0 goto error 
goto success

:error  
echo Upload failed, retrying...   
"%MC_PATH%" cp %SOURCE_FILE% %DEST_PATH% --tags %TAGS%
if %errorlevel% neq 0 goto error  

:success
echo Upload completed at %time% on %date%.   

:end