package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.services.core.ApiResult;
import net.bioknow.services.uap.dbdatamng.function.DTRecordFuncActionNew;
import net.bioknow.services.uap.dbdatamng.function.FuncInfoBeanNew;
import net.bioknow.services.uap.dbdatamng.function.FuncParamBeanNew;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import net.bioknow.webutil.tools.URLUtil;
import org.apache.commons.collections4.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class DTRFlightpdfSignViewVue extends DTRecordFuncActionNew {


	public boolean canUse(int auth, String tableid, Long recordid) {

		try {
			String projectId = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng=new DAODataMng(projectId);

			List<Map<String, String>> LightpdfSignIntegrateList = DAOLightpdfSignIntegrate.listRule(projectId);
			if (CollectionUtils.isEmpty(LightpdfSignIntegrateList)) {
				return  false;
			}


			int esignInstanceCount = daoDataMng.count("esign_instance", "obj.tableid='" + tableid + "' and obj.recordid='" + recordid + "' and obj.sign_flow_id is not null");

			if (esignInstanceCount>0) {
				return true;
			}



		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}



	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBeanNew fpb) {
		try {

			String projectId = SessUtil.getSessInfo().getProjectid();
			String recordid = fpb.getRecordid();
			String tableid = fpb.getTableid();
			DAODataMng daoDataMng=new DAODataMng(projectId);
			String where ="obj.tableid='" + tableid + "' and obj.recordid='" + recordid + "' and obj.sign_flow_id is not null";
			int esignInstanceCount = daoDataMng.count("esign_instance", where);
			String url = "/uapvue/index.html#/list?&tableid=esign_instance&nvwhere="+URLUtil.urlEncode(where,"UTF-8");

			if(esignInstanceCount==1){
				List list = daoDataMng.listRecord("esign_instance",where,null,1);
				url = "/uapvue/index.html#/normalform?tableid=esign_instance&recordid="+String.valueOf(((Map)list.get(0)).get("id"));
			}

			HashMap map = new HashMap();

//			this.redirectByUri(request, response,"/LightpdfSignIntergrate.View.do?recordid="+recordid+"&tableid="+tableid);
			map.put("url", url);
			response.getOutputStream().write(ApiResult.ok("ok", map).getBytes(StandardCharsets.UTF_8));
			response.getOutputStream().close();

			return;

		} catch (Exception e) {
			Log.error("",e);
		}
	}

	public FuncInfoBeanNew getFIB(String tableid) {
		FuncInfoBeanNew fib = new FuncInfoBeanNew("cdtms_DTRFlightpdfSignViewVue");
		try{
			fib.setName(this.getLanguage().get("Signatures"));
			fib.setType(FuncInfoBeanNew.FUNCTYPE_VUE_MASK);
			fib.setWinHeight(600);
			fib.setWinWidth(800);
			fib.setSimpleViewShow(true);
			fib.setAppendParams(false);

		} catch (Exception e) {
			Log.error("",e);
		}
		return fib;
	}

}
