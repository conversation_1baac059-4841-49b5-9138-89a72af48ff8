# 🔍 Oracle Database Comparison After Data Synchronization

## 📋 Overview
This guide provides comprehensive methods to compare two Oracle databases after data synchronization to ensure data integrity, completeness, and consistency.

## 🎯 Comparison Strategies

### 1. **Row Count Comparison**
```sql
-- Compare table row counts between databases
SELECT 
    'SOURCE_DB' as DATABASE_TYPE,
    table_name,
    num_rows as ROW_COUNT
FROM user_tables@source_db_link
WHERE table_name IN (SELECT table_name FROM user_tables)
UNION ALL
SELECT 
    'TARGET_DB' as DATABASE_TYPE,
    table_name,
    num_rows as ROW_COUNT
FROM user_tables
WHERE table_name IN (SELECT table_name FROM user_tables@source_db_link)
ORDER BY table_name, DATABASE_TYPE;
```

### 2. **Data Checksum Comparison**
```sql
-- Generate checksums for table data comparison
WITH source_checksums AS (
    SELECT 
        table_name,
        SUM(ORA_HASH(ROWID||'|'||DUMP(ROWID))) as table_checksum,
        COUNT(*) as row_count
    FROM (
        SELECT 'EMPLOYEES' as table_name, ROWID, 
               ORA_HASH(employee_id||'|'||first_name||'|'||last_name||'|'||email) as row_hash
        FROM employees@source_db_link
    )
    GROUP BY table_name
),
target_checksums AS (
    SELECT 
        table_name,
        SUM(ORA_HASH(ROWID||'|'||DUMP(ROWID))) as table_checksum,
        COUNT(*) as row_count
    FROM (
        SELECT 'EMPLOYEES' as table_name, ROWID,
               ORA_HASH(employee_id||'|'||first_name||'|'||last_name||'|'||email) as row_hash
        FROM employees
    )
    GROUP BY table_name
)
SELECT 
    s.table_name,
    s.row_count as source_rows,
    t.row_count as target_rows,
    s.table_checksum as source_checksum,
    t.table_checksum as target_checksum,
    CASE 
        WHEN s.table_checksum = t.table_checksum THEN 'MATCH'
        ELSE 'MISMATCH'
    END as comparison_result
FROM source_checksums s
FULL OUTER JOIN target_checksums t ON s.table_name = t.table_name;
```

### 3. **Schema Structure Comparison**
```sql
-- Compare table structures
SELECT 
    'COLUMN_DIFF' as diff_type,
    table_name,
    column_name,
    'SOURCE_ONLY' as location
FROM user_tab_columns@source_db_link
WHERE (table_name, column_name) NOT IN (
    SELECT table_name, column_name FROM user_tab_columns
)
UNION ALL
SELECT 
    'COLUMN_DIFF' as diff_type,
    table_name,
    column_name,
    'TARGET_ONLY' as location
FROM user_tab_columns
WHERE (table_name, column_name) NOT IN (
    SELECT table_name, column_name FROM user_tab_columns@source_db_link
);
```

### 4. **Data Type and Constraint Comparison**
```sql
-- Compare column data types
SELECT 
    s.table_name,
    s.column_name,
    s.data_type as source_data_type,
    t.data_type as target_data_type,
    s.data_length as source_length,
    t.data_length as target_length,
    s.nullable as source_nullable,
    t.nullable as target_nullable
FROM user_tab_columns@source_db_link s
FULL OUTER JOIN user_tab_columns t 
    ON s.table_name = t.table_name 
    AND s.column_name = t.column_name
WHERE s.data_type != t.data_type 
   OR s.data_length != t.data_length 
   OR s.nullable != t.nullable;
```

## 🛠️ Automated Comparison Tools

### 1. **PL/SQL Comparison Procedure**
```sql
CREATE OR REPLACE PROCEDURE compare_databases(
    p_source_db_link VARCHAR2,
    p_comparison_type VARCHAR2 DEFAULT 'FULL'
) AS
    v_sql VARCHAR2(4000);
    v_count_diff NUMBER;
    v_data_diff NUMBER;
BEGIN
    -- Log comparison start
    INSERT INTO db_comparison_log 
    VALUES (SYSDATE, 'STARTED', p_source_db_link, p_comparison_type);
    
    -- Compare row counts
    FOR rec IN (SELECT table_name FROM user_tables) LOOP
        v_sql := 'SELECT COUNT(*) FROM ' || rec.table_name || '@' || p_source_db_link;
        EXECUTE IMMEDIATE v_sql INTO v_count_diff;
        
        -- Store comparison results
        INSERT INTO table_comparison_results 
        VALUES (rec.table_name, v_count_diff, 
               (SELECT COUNT(*) FROM user_tables WHERE table_name = rec.table_name),
               SYSDATE);
    END LOOP;
    
    COMMIT;
    
    -- Log completion
    INSERT INTO db_comparison_log 
    VALUES (SYSDATE, 'COMPLETED', p_source_db_link, p_comparison_type);
    COMMIT;
END;
/
```

### 2. **Data Sampling Comparison**
```sql
-- Compare sample data from both databases
WITH sample_comparison AS (
    SELECT 
        'SOURCE' as db_type,
        employee_id,
        first_name,
        last_name,
        email,
        hire_date
    FROM (
        SELECT * FROM employees@source_db_link 
        ORDER BY DBMS_RANDOM.VALUE
    ) WHERE ROWNUM <= 1000
    UNION ALL
    SELECT 
        'TARGET' as db_type,
        employee_id,
        first_name,
        last_name,
        email,
        hire_date
    FROM (
        SELECT * FROM employees 
        ORDER BY DBMS_RANDOM.VALUE
    ) WHERE ROWNUM <= 1000
)
SELECT 
    employee_id,
    COUNT(DISTINCT db_type) as db_count,
    COUNT(*) as total_records
FROM sample_comparison
GROUP BY employee_id
HAVING COUNT(DISTINCT db_type) = 1  -- Records in only one database
ORDER BY employee_id;
```

## 📊 Advanced Comparison Techniques

### 1. **Hash-Based Row Comparison**
```sql
-- Create hash for each row to detect differences
CREATE OR REPLACE FUNCTION get_row_hash(
    p_table_name VARCHAR2,
    p_rowid ROWID
) RETURN VARCHAR2 AS
    v_hash VARCHAR2(32);
    v_sql VARCHAR2(4000);
BEGIN
    v_sql := 'SELECT ORA_HASH(CONCAT_ALL_COLUMNS) FROM ' || p_table_name || 
             ' WHERE ROWID = ''' || p_rowid || '''';
    EXECUTE IMMEDIATE v_sql INTO v_hash;
    RETURN v_hash;
END;
/
```

### 2. **Incremental Data Comparison**
```sql
-- Compare only recently modified data
SELECT 
    s.table_name,
    s.primary_key_value,
    s.last_modified as source_modified,
    t.last_modified as target_modified,
    s.record_hash as source_hash,
    t.record_hash as target_hash
FROM (
    SELECT 
        'EMPLOYEES' as table_name,
        employee_id as primary_key_value,
        last_modified_date as last_modified,
        ORA_HASH(employee_id||first_name||last_name||email) as record_hash
    FROM employees@source_db_link
    WHERE last_modified_date >= SYSDATE - 1  -- Last 24 hours
) s
FULL OUTER JOIN (
    SELECT 
        'EMPLOYEES' as table_name,
        employee_id as primary_key_value,
        last_modified_date as last_modified,
        ORA_HASH(employee_id||first_name||last_name||email) as record_hash
    FROM employees
    WHERE last_modified_date >= SYSDATE - 1
) t ON s.primary_key_value = t.primary_key_value
WHERE s.record_hash != t.record_hash 
   OR s.record_hash IS NULL 
   OR t.record_hash IS NULL;
```

### 3. **Statistical Data Comparison**
```sql
-- Compare statistical summaries
SELECT 
    'EMPLOYEES' as table_name,
    'SOURCE' as database_type,
    COUNT(*) as total_records,
    MIN(hire_date) as min_hire_date,
    MAX(hire_date) as max_hire_date,
    AVG(salary) as avg_salary,
    COUNT(DISTINCT department_id) as unique_departments
FROM employees@source_db_link
UNION ALL
SELECT 
    'EMPLOYEES' as table_name,
    'TARGET' as database_type,
    COUNT(*) as total_records,
    MIN(hire_date) as min_hire_date,
    MAX(hire_date) as max_hire_date,
    AVG(salary) as avg_salary,
    COUNT(DISTINCT department_id) as unique_departments
FROM employees;
```

## 🔧 Comparison Infrastructure Setup

### 1. **Create Comparison Tables**
```sql
-- Table to store comparison results
CREATE TABLE db_comparison_results (
    comparison_id NUMBER GENERATED ALWAYS AS IDENTITY,
    comparison_date DATE DEFAULT SYSDATE,
    table_name VARCHAR2(128),
    source_count NUMBER,
    target_count NUMBER,
    count_match CHAR(1) CHECK (count_match IN ('Y', 'N')),
    data_checksum_source VARCHAR2(32),
    data_checksum_target VARCHAR2(32),
    checksum_match CHAR(1) CHECK (checksum_match IN ('Y', 'N')),
    comparison_status VARCHAR2(20),
    notes CLOB
);

-- Table to log comparison activities
CREATE TABLE db_comparison_log (
    log_id NUMBER GENERATED ALWAYS AS IDENTITY,
    log_date DATE DEFAULT SYSDATE,
    activity VARCHAR2(100),
    source_database VARCHAR2(100),
    comparison_type VARCHAR2(50),
    status VARCHAR2(20),
    error_message CLOB
);
```

### 2. **Create Database Link**
```sql
-- Create database link to source database
CREATE DATABASE LINK source_db_link
CONNECT TO username IDENTIFIED BY password
USING '(DESCRIPTION=
    (ADDRESS=(PROTOCOL=TCP)(HOST=source_host)(PORT=1521))
    (CONNECT_DATA=(SERVICE_NAME=source_service))
)';
```

## 📈 Performance Optimization

### 1. **Parallel Processing**
```sql
-- Use parallel hints for large table comparisons
SELECT /*+ PARALLEL(s,4) PARALLEL(t,4) */
    s.employee_id,
    CASE WHEN s.employee_id IS NULL THEN 'TARGET_ONLY'
         WHEN t.employee_id IS NULL THEN 'SOURCE_ONLY'
         WHEN s.data_hash != t.data_hash THEN 'DATA_DIFF'
         ELSE 'MATCH'
    END as comparison_result
FROM (
    SELECT employee_id, 
           ORA_HASH(first_name||last_name||email||salary) as data_hash
    FROM employees@source_db_link
) s
FULL OUTER JOIN (
    SELECT employee_id,
           ORA_HASH(first_name||last_name||email||salary) as data_hash
    FROM employees
) t ON s.employee_id = t.employee_id;
```

### 2. **Batch Processing**
```sql
-- Process comparisons in batches
DECLARE
    CURSOR table_cursor IS 
        SELECT table_name FROM user_tables 
        WHERE table_name NOT LIKE 'SYS%';
    
    v_batch_size NUMBER := 1000;
    v_offset NUMBER := 0;
BEGIN
    FOR table_rec IN table_cursor LOOP
        v_offset := 0;
        
        LOOP
            -- Process batch
            INSERT INTO comparison_differences
            SELECT * FROM (
                SELECT table_name, primary_key, 'DIFFERENCE_TYPE'
                FROM comparison_view
                WHERE table_name = table_rec.table_name
                ORDER BY primary_key
            ) WHERE ROWNUM <= v_batch_size
            AND ROWNUM > v_offset;
            
            EXIT WHEN SQL%ROWCOUNT < v_batch_size;
            v_offset := v_offset + v_batch_size;
            COMMIT;
        END LOOP;
    END LOOP;
END;
/
```

## 🚨 Best Practices

### 1. **Timing Considerations**
- Run comparisons during low-activity periods
- Use consistent timing for both databases
- Consider time zone differences

### 2. **Data Consistency**
- Ensure no active transactions during comparison
- Use consistent read (flashback) if available
- Document any known data transformation rules

### 3. **Error Handling**
```sql
CREATE OR REPLACE PROCEDURE safe_table_compare(
    p_table_name VARCHAR2,
    p_source_db_link VARCHAR2
) AS
    v_error_msg VARCHAR2(4000);
BEGIN
    -- Comparison logic here
    NULL;
EXCEPTION
    WHEN OTHERS THEN
        v_error_msg := SQLERRM;
        INSERT INTO db_comparison_log 
        VALUES (SYSDATE, 'ERROR', p_source_db_link, p_table_name, 'FAILED', v_error_msg);
        COMMIT;
        RAISE;
END;
/
```

## 📋 Comparison Checklist

- [ ] **Schema Comparison**: Tables, columns, data types, constraints
- [ ] **Row Count Verification**: Total records per table
- [ ] **Data Integrity**: Primary keys, foreign keys, unique constraints
- [ ] **Data Content**: Sample data verification, checksums
- [ ] **Performance Objects**: Indexes, triggers, procedures
- [ ] **Security Objects**: Users, roles, privileges
- [ ] **Statistical Analysis**: Data distribution, null values, duplicates

## 🎯 Conclusion

This comprehensive approach ensures thorough validation of data synchronization between Oracle databases. Choose the appropriate combination of techniques based on your specific requirements, data volume, and performance constraints.
