import pandas as pd                              
pd.set_option('display.max_columns',None)                                                                                                                      
import logging                                                                                                                                                 
import traceback                                                                                                                                               
import datetime                                     
import time   
import pymysql  
import numpy as np                                                                                                                                         
import shutil                                                                                                                                              
import os                                                                                                                                                  
import json                                                                                                                                                
import base64
from urllib.parse import quote_plus
from sqlalchemy import create_engine
from datetime import datetime

#正式环境路径，为绝对路径，请注意！                                                                                                                                                           
dir_path = '/tmp/hsperfdata_root/TEST'   
# UAP后台数据库ip  
ip_site = '***********'  
# UAP数据库的账户名
username = 'd2VpeDU='
# UAP数据库的密码
password = 'd2VpeEBlZGM='
# 项目列表过滤
compound_filter_list = []
# 获取当前日期
curr = datetime.now()
curr_str = curr.strftime("%Y-%m-%d")

# base64解密
def get_base64(str_to_base64):     
    return base64.b64decode(str_to_base64)                                                                                                                 
    
# 连接4.1数据库,到处需要的数据
def exportData():                                                                                                                  
    # 创建连接UAP后台数据库连接   pymysql.connect(ip, 账号，密码， 编码)                                                                                                                                                    
    conn = pymysql.connect(host=ip_site, user=str(get_base64(username),'UTF-8'), password=str(get_base64(password),'UTF-8'), charset='utf8')

    # 查找数据库中所有库名称，表名称 sql语句                                                                                                                                                        
    sql_all = 'SELECT TABLE_SCHEMA, TABLE_NAME FROM INFORMATION_SCHEMA.TABLES '                                                                               
    all_data = pd.read_sql(sql_all,conn)
    
    def putcsv(dataname, outname):
        # 找到表名称为XXX数据库并库名称不为edc_library ，edc_library 为标准库                                                                                                                                              
        all_data_1=all_data[(all_data['TABLE_NAME']==str(dataname))&(all_data['TABLE_SCHEMA']!='edc_library')]                                                   
        all_data_1.index=range(len(all_data_1))   
        # 创建唯一id                                                                                                             
        all_data_1['DNAID']=all_data_1.index                                                                                                                   
        # 找到库名表名                                                                                                                                                       
        all_data_1['sitenm']=all_data_1['TABLE_SCHEMA']+'.'+all_data_1['TABLE_NAME']  
        # 遍历函数，提取出所有的项目名称 信息                                                                                                                                                       
        dtstudy=pd.DataFrame([])                                                                                                                               
        for i in range(len(all_data_1)):                                                                                                                    
            sql_1=''                                                                                                                                           
            dtstudytemp=pd.DataFrame([])                                                                                                                       
            sql_1='select * from '+all_data_1['sitenm'][i]                                                                                                  
            dtstudytemp=pd.read_sql(sql_1, conn)                                                                                                                
            dtstudy=dtstudy.append(dtstudytemp)  
        # 变量转为字符类型，去除回车符                                                                                                                                                       
        outdf=dtstudy.applymap(lambda x: str(x)).applymap(lambda x: x.replace('\n',''))   
        # 导出csv 文件，位置 /data/dc/XXX.csv                                                                    
        outdf.to_csv(dir_path + '/data/dc/' + dataname + '_' + curr_str + '.csv', index = False,encoding='utf-8-sig') 
        
    # 临床研究的数据
    putcsv('tbl_studyinfo', 'tbl_studyinfo')
    # 中心研究者的数据
    putcsv('tbl_zxsyr', 'tbl_zxsyr')
    # 机构人员的数据
    putcsv('tbl_zxyfry', 'tbl_zxyfry')
    # 化合物中临床研究的数据
    putcsv('tbl_lcyj', 'tbl_lcyj')
    # 项目中申办方人员的数据
    putcsv('tbl_hqb1', 'tbl_hqb1')


#将数据存入MySQL数据库中
def intoDatabase(data_frame, table_name):
    #此处xxx替换为你的数据库信息
    engine = create_engine('mysql+pymysql://susaruser:Hr%40db0316@localhost:3306/susar_auto_mail')
    try:
        data_frame.to_sql(table_name, con=engine, if_exists='replace', index=False)
        print("数据存储成功")
    except Exception as e:
        print(f"数据存储失败：{e}")
    
def importData():
    # 导入临床研究
    df_studyinfo = pd.read_csv(r'/tmp/hsperfdata_root/TEST/data/dc/tbl_studyinfo_' + curr_str + '.csv', usecols=[0, 2, 3, 17, 18])
    df_studyinfo.columns = ['ID', 'STUDYID', 'STUDYNAME', 'COMPOUNDNAME','COMPOUNDCODE']
    df_studyinfo.to_csv(dir_path + '/data/dc/111_' + curr_str + '.csv', index = False, encoding='utf-8-sig') 
    # print(df_studyinfo)
    
    # 导入化合物中临床研究的数据
    df_compound_study = pd.read_csv(r'/tmp/hsperfdata_root/TEST/data/dc/tbl_lcyj_' + curr_str + '.csv', usecols=[14, 18])
    df_compound_study.columns=['STUDYID', 'COMMENT']
    
    df_studyinfo = pd.merge(df_studyinfo, df_compound_study, on=['STUDYID'], how='left')
    df_studyinfo.to_csv(dir_path + '/data/dc/111_' + curr_str + '.csv', index = False, encoding='utf-8-sig') 
    
    
    # 导入中心研究者
    df_pi_1 = pd.read_csv(r'/tmp/hsperfdata_root/TEST/data/dc/tbl_zxsyr_' + curr_str + '.csv', usecols=[2, 3, 4, 5, 8, 10, 12, 23, 35])
    # 重命名
    # YYMC 为研究机构名称，ZXMC 为研究机构ID，XM 为研究机构人员名称，YX 为研究机构人员邮箱
    # RYLB 为研究机构人员角色，SYZT 为研究机构人员状态(1 代表生效，2 代表失效)，ID 为项目编号
    df_pi_1.columns=['YYMC', 'ZXMC', 'site_no', 'XM', 'YX', 'RYLB', 'SYZT', 'ID', 'NOTE']
    df_pi_1.loc[df_pi_1['RYLB'].isin(['Site_PI']), 'NOTE'] = df_pi_1.loc[df_pi_1['RYLB'].isin(['Site_PI']), 'NOTE'].fillna('SUSAR-Y')
    #
    df_pi_1.to_csv(dir_path + '/data/dc/aaa_' + curr_str + '.csv', index = False, encoding='utf-8-sig') 
    # 对数据进行筛选
    # 去除失效的用户和保留角色Site_PI且备注为空或者备注为SUSAR-Y
    # 去除失效的用户和保留角色Site_INV且备注为SUSAR-Y
    # 去除失效的用户和保留角色Readonly且备注为SUSAR-Y的用户
    df_pi = df_pi_1[(df_pi_1['SYZT']==1) & ((df_pi_1['RYLB'].isin(['Site_PI']) & (df_pi_1['NOTE'].isin(['SUSAR-Y', 'None']))) | (df_pi_1['RYLB'].isin(['Site_INV']) & (df_pi_1['NOTE'].isin(['SUSAR-Y']))) | (df_pi_1['RYLB'].isin(['ReadOnly']) & df_pi_1['NOTE'].isin(['SUSAR-Y'])))]
    # print(df_pi)
    df_pi.to_csv(dir_path + '/data/dc/bbb_' + curr_str + '.csv', index = False,encoding='utf-8-sig') 
    
    # 导入中心机构人员
    df_susar_1 = pd.read_csv(r'/tmp/hsperfdata_root/TEST/data/dc/tbl_zxyfry_' + curr_str + '.csv', usecols=[2, 3, 6, 11, 17, 19, 20, 21])
    # 重命名
    # YYMC 为研究机构名称，ZXMC 为研究机构ID，XM 为研究机构人员名称，YX 为研究机构人员邮箱
    # RYLB 为研究机构人员角色，SYZT 为研究机构人员状态(1 代表生效，2 代表失效)，ID 为项目编号
    df_susar_1.columns=['ZXMC', 'site_no', 'SYZT', 'YX', 'XM', 'ID', 'RYLB', 'YYMC']
    # 对数据进行筛选，去除失效的用户和保留Site_PI、Site_INV角色的用户
    df_susar = df_susar_1[(df_susar_1['SYZT']==1) & (df_susar_1['RYLB'].isin(['SUSAR']))]
    # print(df_susar)
    
    # 导入申办方人员
    df_sponsor_1 = pd.read_csv(r'/tmp/hsperfdata_root/TEST/data/dc/tbl_hqb1_' + curr_str + '.csv', usecols=[4, 7, 23, 26, 29, 30, 31])
    # 重命名
    # ZXMC 为研究机构ID，XM 为研究机构人员名称，YX 为研究机构人员邮箱
    # RYLB 为研究机构人员角色，SYZT 为研究机构人员状态(1 代表生效，2 代表失效)，ID 为项目编号
    df_sponsor_1.columns=['RYLB', 'SYZT', 'ID', 'ZXMC', 'YX', 'STUDYID', 'XM']
    df_sponsor_1['site_no'] = 'CN000'
    # 将该部分数据与df_compound_study进行拼接，带上Comment
    df_sponsor_2 = pd.merge(df_sponsor_1, df_compound_study, on=['STUDYID'], how='right')
    
    # 对数据进行筛选，去除失效的用户和保留Site_PI、Site_INV角色的用户
    df_sponsor_2.to_csv(dir_path + '/data/dc/sponsor_' + curr_str + '.csv', index = False, encoding='utf-8-sig')
    df_depart_2 = df_sponsor_2[(df_sponsor_2['SYZT']==1) & (df_sponsor_2['COMMENT'].isin(['SUSAR-2'])) & (df_sponsor_2['RYLB'].isin(['PM', 'CTA']))]
    df_depart_2 = df_depart_2.drop(columns=['COMMENT'])
    # print(df_susar)
    
    df_depart_1_1 = df_sponsor_2[(df_sponsor_2['COMMENT'].isin(['SUSAR-1']))]
    df_depart_1_1['YX'] = '<EMAIL>'
    df_depart_1_1['XM'] = '公邮'
    df_depart_1_1['site_no'] = 'CN000'
    df_depart_1_1['RYLB'] = '公邮'
    df_depart_1_1.drop_duplicates(subset=['STUDYID'], inplace=True)
    df_depart_1_1.to_csv(dir_path + '/data/dc/sponsor_2_' + curr_str + '.csv', index = False, encoding='utf-8-sig') 
    df_depart_1_1.drop(columns=[ 'COMMENT'], inplace=True)

    
    df_depart_1_2= df_sponsor_2[(df_sponsor_2['COMMENT'].isin(['SUSAR-1']))]
    df_depart_1_2['YX'] = '<EMAIL>'
    df_depart_1_2['XM'] = '监察部公邮'
    df_depart_1_2['site_no'] = 'CN000'
    df_depart_1_2['RYLB'] = '公邮'
    df_depart_1_2.drop_duplicates(subset=['STUDYID'], inplace=True)
    df_depart_1_2.drop(columns=['COMMENT'], inplace=True)
    
    df_depart_ma= df_sponsor_2[(df_sponsor_2['COMMENT'].isin(['SUSAR-MA']))]
    df_depart_ma['YX'] = '<EMAIL>'
    df_depart_ma['XM'] = 'MA公邮'
    df_depart_ma['site_no'] = 'CN000'
    df_depart_ma['RYLB'] = '公邮'
    df_depart_ma.drop_duplicates(subset=['STUDYID'], inplace=True)
    df_depart_ma.drop(columns=[ 'COMMENT'], inplace=True)
    
    #0718:内部收件人增加满足①时需要添加项目比那好，因为申办方人员表单的ID不全，故使用studyinfo的
    df_depart_1_new=pd.concat([df_pi, df_susar]) 
    df_depart_1_new2=pd.merge(df_depart_1_new, df_studyinfo,on=['ID'], how='left') 
    df_depart_1_new2.drop(columns=['YYMC', 'ZXMC','site_no','XM','ID','YX','RYLB','SYZT','NOTE','STUDYNAME','COMPOUNDNAME','COMPOUNDCODE','COMMENT'], inplace=True)
    df_depart_1_new2.drop_duplicates(subset=['STUDYID'], inplace=True)
    df_depart_2_new=pd.concat([ df_depart_2, df_depart_1_1, df_depart_1_2, df_depart_ma])
    df_depart_2_new_1=pd.merge(df_depart_2_new, df_depart_1_new2, on=['STUDYID'], how='inner')
    #df_depart_2_new_1.drop(columns=[ 'STUDYID'], inplace=True)
    
    # 将中心研究者和中心机构人员进行合并
    #df_pi_susar = pd.concat([df_pi, df_susar, df_depart_2, df_depart_1_1_t, df_depart_1_2, df_depart_ma])
    df_pi_susar_new=pd.merge(df_depart_1_new, df_studyinfo,on=['ID'], how='left')
    df_pi_susar_new.drop(columns=['STUDYNAME','COMPOUNDNAME','COMPOUNDCODE','COMMENT'], inplace=True)
    df_pi_susar = pd.concat([df_pi_susar_new,df_depart_2_new_1])
    df_pi_susar.to_csv(dir_path + '/data/dc/ccc_' + curr_str + '.csv', index = False, encoding='utf-8-sig') 
    
    # 拼表：临床研究和中心研究者以项目编号进行拼接
    studyinfo_pi = pd.merge(df_studyinfo, df_pi_susar, on=['STUDYID'], how='left')  
    studyinfo_pi_NoID = studyinfo_pi.drop(columns=['ID_x','ID_y'])
    studyinfo_pi_NoID.to_csv(dir_path + '/data/dc/ddd_' + curr_str + '.csv', index = False, encoding='utf-8-sig') 
    
    # 在此处对输出结果进行按照事先预定好的进行过滤
    # 定义化合物筛选的函数
    def filter_compound(x):
        # 如果录入的化合物中存在英文逗号
        if ',' in x:
            # 先对系统中录入的化合物按照英文逗号进行分割
            str_split = x.split(',')
            for str in str_split:
                # 如果分割后的化合物代码存在于设定的列表中，则返回 True
                if str in compound_filter_list:
                    return True
        else:
            # 如果录入的化合物中没有英文逗号且存在于设定的列表中，则返回 True
            if x in compound_filter_list:
                return True
        # 上面两个都不满足的，则返回 False
        return False
    
    # 定义化合物符号替换的函数，对于有些化合物会有/进行分割，
    def alter_split(x):
        result = []
        if ',' in x:
            result_split = []
            str_split = x.split(',')
            for str in str_split:
                if '/' in str:
                    str = str.replace('/', '(') + ')'
                result_split.append(str)
            return ','.join(result_split)
        else:
            if '/' in x:
                str = x.replace('/', '(') + ')'
                return str
            else:
                return x
    studyinfo_pi_NoID["COMPOUNDCODE"] = studyinfo_pi_NoID["COMPOUNDCODE"].astype(str)
    studyinfo_pi_NoID.to_csv('/tmp/hsperfdata_root/TEST/data/dc/project' + curr_str + '.csv', index=False,encoding='utf-8-sig')
    if len(compound_filter_list) == 0:
        studyinfo_pi_NoID = studyinfo_pi_NoID
    else:
        # 根据指定的COMPUND列表，筛选项目
        studyinfo_pi_NoID = studyinfo_pi_NoID[studyinfo_pi_NoID["COMPOUNDCODE"].apply(filter_compound)]
    studyinfo_pi_NoID.to_csv('/tmp/hsperfdata_root/TEST/data/dc/project_filter' + curr_str + '.csv', index=False,encoding='utf-8-sig')
    # print(studyinfo_pi_NoID)
    # 将COMPOUND的/转换成英文的括号
    studyinfo_pi_NoID['COMPOUNDCODE'] = studyinfo_pi_NoID['COMPOUNDCODE'].apply(lambda x: alter_split(x))
    # 生成自动递增的 ID
    studyinfo_pi_new = studyinfo_pi_NoID
    studyinfo_pi_new['ID'] = studyinfo_pi_NoID['STUDYID'] + studyinfo_pi_NoID['site_no']
    
    # 将数据分为两部分，分别存储到数据库的 t_site 和 t_user 表中
    # site 表，保留ID、化合物编号、化合物名称、研究编号、研究名称、中心编号、中心名称
    studyinfo_site = studyinfo_pi_new.loc[:, ['ID', 'COMPOUNDCODE', 'COMPOUNDNAME', 'STUDYID', 'STUDYNAME', 'site_no', 'YYMC']]
    # 重命名列
    studyinfo_site_renamed = studyinfo_site.rename(columns = {'COMPOUNDCODE': 'compound_no', 'COMPOUNDNAME': 'compound_name', 'STUDYID': 'study_no', 'STUDYNAME': 'study_name', 'site_no': 'site_no', 'YYMC': 'site_name'})
    studyinfo_site_renamed.to_csv('/tmp/hsperfdata_root/TEST/data/dc/project_rename' + curr_str + '.csv', index=False,encoding='utf-8-sig')
    studyinfo_site_filter = studyinfo_site_renamed[(studyinfo_site_renamed['site_no'].notna())]
    studyinfo_site_filter.to_csv('/tmp/hsperfdata_root/TEST/data/dc/project_filter_' + curr_str + '.csv', index=False,encoding='utf-8-sig')
    studyinfo_site_unique = studyinfo_site_filter.drop_duplicates()
    studyinfo_site_unique.to_csv('/tmp/hsperfdata_root/TEST/data/dc/t_site_' + curr_str + '.csv', index=False,encoding='utf-8-sig')
    intoDatabase(studyinfo_site_unique, 't_site')
    
    # user 表，保留ID、研究机构人员角色、研究机构邮箱
    studyinfo_user = studyinfo_pi_new.loc[:, ['ID','STUDYID', 'RYLB', 'YX']]
    # 按照 RYLB 列生成新的一列 category
    def categorize_role(role):
        if role == 'Site_PI':
            return 1
        elif role == 'SUSAR':
            return 2
        elif role in ('公邮','PM','CTA'):
            return 4        
        else:
            return 3
    studyinfo_user['site_user_role'] = studyinfo_user['RYLB'].apply(categorize_role)
    studyinfo_user_noRYLB = studyinfo_user.drop(columns=['RYLB'])
    studyinfo_user_rename = studyinfo_user_noRYLB.rename(columns = {'YX': 'site_user_email', 'ID': 'site_id'})
    studyinfo_user_unique = studyinfo_user_rename.drop_duplicates()
    studyinfo_site_filter = studyinfo_user_unique.dropna(subset=['site_id'])
    studyinfo_user_reset = studyinfo_site_filter.reset_index().rename(columns={'index': 'ID'})
    studyinfo_site_reindex = studyinfo_user_reset.reindex(columns=['ID', 'site_user_role', 'site_user_email', 'site_id'])
    studyinfo_site_reindex.to_csv('/tmp/hsperfdata_root/TEST/data/dc/t_user_' + curr_str + '.csv', index=False,encoding='utf-8-sig')
    intoDatabase(studyinfo_site_reindex, 't_user')


start_time=time.time()                                                                                                                             
print('start time:', start_time)   
#运行导出数据函数
exportData()                                                                                                                                               
#运行导入数据函数
importData()                                                                                                                                                     
#运行结束时间                                                                                                                 
end_time=time.time()                                                                                                                               
print('end time:', end_time)                                                                                                                        
print('During:', end_time-start_time)    
