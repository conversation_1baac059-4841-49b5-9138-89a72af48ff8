/*
 Navicat MySQL Data Transfer

 Source Server         : 测试环境
 Source Server Type    : MySQL
 Source Server Version : 80028 (8.0.28)
 Source Host           : ***********:3306
 Source Schema         : susar_auto_mail

 Target Server Type    : MySQL
 Target Server Version : 80028 (8.0.28)
 File Encoding         : 65001

 Date: 20/04/2023 23:00:55
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for file_store_folder
-- ----------------------------
DROP TABLE IF EXISTS `file_store_folder`;
CREATE TABLE `file_store_folder`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `folder_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `folder_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `compound_folder` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_deleted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of file_store_folder
-- ----------------------------
INSERT INTO `file_store_folder` VALUES (1, '2023-04', 'smb://zhouh36:HR9cf3cbd8@10.10.5.56/susar/SHR-A1811/2023-04/', 'SHR-A1811', '2023-04-18 11:45:00', 'N');
INSERT INTO `file_store_folder` VALUES (2, '2023-04', 'smb://zhouh36:HR9cf3cbd8@10.10.5.56/susar/SHR-1316/2023-04/', 'SHR-1316', '2023-04-18 11:45:01', 'N');
INSERT INTO `file_store_folder` VALUES (3, '2023-04', 'smb://zhouh36:HR9cf3cbd8@10.10.5.56/susar/SHR6390/2023-04/', 'SHR6390', '2023-04-18 11:45:01', 'N');
INSERT INTO `file_store_folder` VALUES (4, '2023-04', 'smb://zhouh36:HR9cf3cbd8@10.10.5.56/susar/SHR-1701/2023-04/', 'SHR-1701', '2023-04-19 11:00:00', 'N');

SET FOREIGN_KEY_CHECKS = 1;
