import json
import pymysql
import os
import re
import pymysql
host = '***********' 
port = 3306
user = 'weix5'
password = 'weix@edc'
database = 'report_server'
with pymysql.connect(host=host, port=port, user=user, passwd=password, db=database) as conn:
  with conn.cursor() as cursor:
    sql = "SELECT REPORTFILENAME, REPORTFILEPATH, EXPORTTIME FROM tbl_report_pro WHERE (REPORTFILENAME like '%SAS%' )and REPORTFILENAME like '%INS068-302%' ORDER BY EXPORTTIME desc LIMIT 1"
    cursor.execute(sql)
    results = cursor.fetchall()
    for row in results:
      # 格式化时间
      export_time = row[2].strftime("%Y/%m/%d/%H/%M")
      print(export_time)
cursor.close()
conn.close()
