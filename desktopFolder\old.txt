<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=utf-8" %>
<%@ page language="java" pageEncoding="utf-8" %>
<%@ taglib uri="/webutil/tlib/bioknow-tlib.tld" prefix="bt" %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Composer</title>
    <link href="/cdtms/LightpdfSign/js/bootstrap.min.css" rel="stylesheet">
    <script type="text/javascript" src="/cdtms/LightpdfSign/js/jquery.min.js"></script>

    <link href="/cdtms/LightpdfSign/js/select2.min.css" rel="stylesheet"/>
    <script src="/cdtms/LightpdfSign/js/select2.min.js"></script>


    <link href="/cdtms/LightpdfSign/js/style.css" rel="stylesheet">

    <script src="/cdtms/LightpdfSign/js/dropzone.min.js"></script>
    <link
            rel="stylesheet"
            href="/cdtms/LightpdfSign/js/dropzone.min.css"
            type="text/css"
    />

    <script src="/cdtms/LightpdfSign/js/flatpickr.js"></script>
    <link rel="stylesheet" href="/cdtms/LightpdfSign/js/flatpickr.min.css">

    <link rel=stylesheet type="text/css" href="/public/css/public.css"></link>
    <style>
        body {
            /*background-color: #f8f9fa;*/
        }

        .container {
            margin-top: 50px;
        }

        label {
            font-weight: bold;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .editor-wrapper {
            border: 1px solid #ccc;
        }

        #toolbar-container {
            border-bottom: 1px solid #ccc;
        }

        #editor-container {
            height: 200px;
        }

        button[type="submit"] {
            display: block;
            width: 100%;
            padding: 10px;
            font-size: 18px;
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        /* Custom styles for Select2 and Bootstrap integration */
        .select2-container {
            width: 100% !important;
        }
        .select2-search__field{
            /* height: 28px; */
            border:none !important;
            padding-right:0px !important;
            /* background-color: #ffffff; */
            /* border-radius: 4px; */
            /* outline: none; */
            /* padding-left: 5px; */
            /* padding-right: 5px; */
            /* font-size: 12px; */
            /* color: #606266; */
            /* transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1); */
        }



        #overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        #iframeContainer {
            position: relative;
            width: 80%;
            max-width: 500px; /* Adjust the maximum width as needed */
            height: 200px;
            background-color: #fff; /* Adjust the background color as needed */
            border-radius: 8px;
            overflow: hidden;
            padding: 20px
        }

        #closeButton {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
        }
        .requiredFields {
            color: red;
        }
        .btn-primary:disabled {
            background-color: #00000075 !important;
        }
        #bodyLoading {
            position: absolute;
            left: 48.4%;
            top: 38%;
            border: 5px solid #cbcbca;
            border-top: 5px solid #0a44b3;
            border-radius: 50%;
            width: 41px;
            height: 41px;
            animation: spin 2s linear infinite;
        }
        #spinWrap {
            width: 100%;
            height: 100%;
            position: absolute;
            right: 0%;
            top: 0%;
            background: rgba(255, 255, 255, 0.9);
            z-index: 999;
            display: none;
        }
        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        #loadingMessage {
            box-sizing: border-box;
            width: 100%;
            text-align: center;
            z-index: 100;
            outline: none;
            top: 44%;
            position: absolute;
            font-size: 14px;
            font-weight: 600;
            color: rgba(48, 49, 51, .5);
        }
    </style>
</head>
<body style="height: auto !important;">
<div id="spinWrap">
    <div id="bodyLoading"></div>
    <div id="loadingMessage"></div>
</div>
<div class="container">
    <form id="signForm">

        <div class="form-group row">
            <label for="type" class="col-sm-2 col-form-label"><span class="requiredFields">*</span>类型:</label>
            <div class="col-sm-10" id="type">
                <label style="display: inline-block;">
                    <input type="radio" name="signatureType" value="general" checked> 通用文件
                </label>
                &nbsp;&nbsp;&nbsp;&nbsp;
                <label style="display: inline-block;">
                    <input type="radio" name="signatureType" value="project"> 项目文件
                </label>
            </div>
        </div>

        <div class="form-group row" id="div_studyid">
            <label for="studyid" class="col-sm-2 col-form-label"><span class="requiredFields">*</span>研究代码:</label>

            <div class="col-sm-10" >
                <select id="studyid" class="form-control" ></select>
            </div>
        </div>

        <div class="form-group row">
            <label for="recipients" class="col-sm-2 col-form-label"><span class="requiredFields">*</span>签署人:</label>
            <div class="col-sm-10">
                <select id="recipients" class="form-control" multiple="multiple" required></select>
            </div>
        </div>
        <%--        <div class="form-group row">
                    <label for="subject" class="col-sm-2 col-form-label">主题:</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="subject" required>
                    </div>
                </div>--%>

        <div class="form-group row">
            <label for="signFlowExpireTime" class="col-sm-2 col-form-label"><span class="requiredFields">*</span>过期天数(自然日):</label>
            <div class="col-sm-4">
                <%--                <input type="text" class="form-control" autocomplete="off" id="signFlowExpireTime"--%>
                <%--                       name="signFlowExpireTime" placeholder="签署截止日期" required>--%>

                <select id="signFlowExpireTime" class="form-control">
                    <option value="1">1天</option>
                    <option value="3" selected>3天</option>
                    <option value="5">5天</option>

                </select>

            </div>

            <label for="language" class="col-sm-2 col-form-label"><span class="requiredFields">*</span>待签文件语种:</label>
            <div class="col-sm-4">
                <select id="language" class="form-control">
                    <option value="cn">中文</option>
                    <option value="en">English</option>
                </select>
            </div>
        </div>

        <div class="form-group">
            <div class="editor-wrapper">
                <div id="toolbar-container"></div>
                <div id="editor-container"></div>
            </div>
        </div>
        <div class="form-group">
            <label for="subject" class="col-sm-2 col-form-label"><span class="requiredFields">*</span>待签文件:</label>
            <div class="col-sm-10">


                <%--                <table class="table table-xs w-100">--%>
                <%--                    <tbody><tr height="1">--%>
                <%--                        <td>--%>
                <%--                            <div id="filelistdivsignpage">1. C:\fakepath\1111111.pdf<img class="cursor-pointer ml-2" title="Delete" src="/webutil/fileup/close.gif" onclick="deleteFilesignpage(1)"></div>--%>
                <%--                            <input type="hidden" size="80" id="signpage" name="signpage" value="C:\fakepath\1111111.pdf*7033D904876F4655AC624BA1984B337E.pdf|">--%>
                <%--                        </td>--%>
                <%--                    </tr>--%>

                <%--                    </tbody></table>--%>
            <form id="frm" name=form_input action="" method="post" target="_self">
                <c:if test="${not empty signedFile}">
                    <table class="table table-xs w-100">
                        <tbody>
                        <tr height="1">
                            <td>
                                <lable class="signedFileNameLabel">${signedFileName}</lable>
                            </td>
                        </tr>

                        </tbody>
                    </table>
                </c:if>


                <c:if test="${signedFile==null || signedFile==''}">

                    <jsp:include page="/webutil/fileup/fileinput.jsp">
                        <jsp:param name="name" value="signpage"/>
                        <jsp:param name="formname" value="form_input"/>
                        <jsp:param name="maxfile" value="1"/>
                        <jsp:param name="filetype" value="pdf"/>
                    </jsp:include>
                    <%--                <input type="text" class="form-control" id="signpage" hidden value="">--%>
                </c:if>
            </form>
            </div>
        </div>
        <p id="errorMessage" style="color: red;"></p>
        <p id="successMessage" style="color: green;"></p>
        <button id="submitbtn" class="btn btn-primary" style="width: 100%;padding: 10px;" onclick="submitbtn()">提交</button>

    </form>
</div>


<div id="overlay">
    <div id="iframeContainer">
        <div id="closeButton" onclick="closeForgetPsw()">X</div>
        <form id="signer">

            <div class="form-group row">
                <label for="type" class="col-sm-2 col-form-label">姓名：</label>

                <div class="col-sm-10">
                    <input class="input-field" type="text" style="width: 100%" required="" id="signer_name">
                </div>

            </div>
            <div class="form-group row">
                <label for="type" class="col-sm-2 col-form-label">邮箱：</label>

                <div class="col-sm-10">
                    <input class="input-field" type="text"  style="width: 100%" required="" id="signer_mail">
                </div>

            </div>

            <button type="submit" id="signerbtn" class="btn btn-primary">确认</button>
        </form>
    </div>
</div>

<script lang="javascript">
    function deleteFilesignpage(){
        $("#filelistdivsignpage").html("");
        $("#signpage").val("");
        $("#fileup_frmsignpage").show();
    }
    /***
     * 开启loading
     */
    function startLoading(msg) {
        let spinWrap = document.getElementById("spinWrap");
        let loadingMessage = document.getElementById("loadingMessage");
        spinWrap.style.display = "inline-block";
        //获取返回值并赋值到loadingMsg
        loadingMessage.innerText = msg;
    }

    /***
     * 关闭loading
     */
    function closeLoading() {
        let spinWrap = document.getElementById("spinWrap");
        spinWrap.style.display = "none";
    }

</script>

<script type="text/javascript">
    var threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);

    const signFlowExpireTime = flatpickr("#signFlowExpireTime", {
        enableTime: true,
        dateFormat: "Y-m-d H:i:S",
        minDate: "today",
        locale: "zh",
        defaultDate: threeDaysFromNow


    });


</script>
<script>

    $('#div_studyid').hide();

    // Add change event handler to radio buttons
    $('input[name="signatureType"]').change(function () {
        if ($(this).val() === 'project') {
            // If '项目签字' is selected, hide type and show div_studyid
            $('#div_studyid').show();
        } else {
            // If '通用签字' is selected, show type and hide div_studyid
            $('#div_studyid').hide();
        }
        $("#studyid").empty();
    });
</script>

<script>
    $recipients= $('#recipients').select2({
        multiple: true,
        // allowClear: option.allowClear,
        // minimumInputLength: 1,
        closeOnSelect:false,//Controls whether the dropdown is closed after a selection is made.
        ajax: {
            url: "/lightpdfSign.signerSearch.do",
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    param:$("#studyid").val(),
                    q: params.term, // search term
                    page: params.page
                };
            },
            processResults: function (data, params) {
                // parse the results into the format expected by Select2
                // since we are using custom formatting functions we do not need to
                // alter the remote JSON data, except to indicate that infinite
                // scrolling can be used
                params.page = params.page || 1;

                return {
                    results: data.data.results,
                    pagination: {
                        more: (params.page * 30) < data.total_count
                    }
                }
            },
            cache: true

        },
        tags: true,
        createTag: function (params) {
            var term = $.trim(params.term);

            if (term === '') {
                return null;
            }

            return {
                id: term,
                text: term,
                newTag: true
            };
        },
        templateResult: function (data) {
            // Customize the display of results
            if (data.newTag) {
                return $('<a style="color: red; ">没有想要的结果，去添加: ' + data.text + '</a>');
            }
            return data.text;
        },

    }).on('select2:select', function (e) {
        // Prevent selecting the "New" tag
        var isNewTag = e.params.data.newTag;
        if (isNewTag) {
            // e.preventDefault();
            var selectedOptions = $(this).val() || [];
            selectedOptions.pop(); // 移除最后一个元素，即“New”标签
            $(this).val(selectedOptions).trigger('change');
            // Unselect the "New" tag
            // $(this).val(null).trigger('change');

            // alert(JSON.stringify(e.params.data.id));

            // Handle the click event, e.g., show a modal
            // openModal(e.params.data.id);
            $('#signer_name').val(e.params.data.id);
            $('#signer_mail').val(e.params.data.id);
            showForgetPsw();


        }
    });




</script>


<script>
    $studyid=$("#studyid").select2({
        ajax: {
            url: "/lightpdfSign.studySearch.do",
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    q: params.term, // search term
                    page: params.page
                };
            },
            processResults: function (data, params) {
                // parse the results into the format expected by Select2
                // since we are using custom formatting functions we do not need to
                // alter the remote JSON data, except to indicate that infinite
                // scrolling can be used
                params.page = params.page || 1;

                return {
                    results: data.data.results,
                    pagination: {
                        more: (params.page * 30) < data.total_count
                    }
                };
            },
            cache: true

        },

        // placeholder: 'Search for a repository',
        minimumInputLength: 1,
        // templateResult: formatRepo,
        // templateSelection: formatRepoSelection
    });

</script>
<script src="/cdtms/LightpdfSign/js/index.js"></script>

<script>
    const {createEditor, createToolbar} = window.wangEditor

    const editorConfig = {
        placeholder: '已预设邮件签字部分，其他部分按需添加',
        onChange(editor) {
            const html = editor.getHtml()
            //console.log('editor content', html)
            // 也可以同步到 <textarea>
        }
    }

    const editor = createEditor({
        selector: '#editor-container',
        html: '<p><br></p>',
        config: editorConfig,
        mode: 'simple' // or 'simple'
    })

    const toolbarConfig = {}

    const toolbar = createToolbar({
        editor,
        selector: '#toolbar-container',
        config: toolbarConfig,
        mode: 'simple' // or 'simple'
    })
</script>


<script>


    const errorMessage = document.getElementById("errorMessage");
    const successMessage = document.getElementById("successMessage");
    function submitbtn(){
        try {
            debugger
            startLoading("正在提交,请稍后...");
            // $('#submitbtn').attr('disabled',true)
            let verifyMsg = verifyForm();
            if (verifyMsg.length !== 0){
                errorMessage.textContent = verifyMsg;
                closeLoading();
                // $('#submitbtn').attr('disabled',false)
                return;
            }
           let data = JSON.stringify({
                recipients: $('#recipients').select2('val').join(";"),
                studyid: $('#studyid').val(),
                subject: $('#subject').val(),
                signpage: $('#signpage').val(),
                language: $('#language').val(),
                signPageFile: '${signedFile}',
                signFlowExpireTime: $('#signFlowExpireTime').val(),
                type:  $('input[name="signatureType"]:checked').val(),
                body: editor.getHtml()
            });
            // $.post("lightpdfSign.createCheck.do", data, function (data){
            //     if (data.status == 200) {
            //         successMessage.textContent = "验证成功！";
            //         window.open(data.data.setUrl);
            //         window.location = data.data.redirectUrl;
            //     } else {
            //         successMessage.textContent = "";
            //     }
            //     closeLoading();
            // },"json");
            $.ajax("lightpdfSign.createCheck.do", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                data: data,
                success: function (data) {
                    if (data.status == 200) {
                        successMessage.textContent = "验证成功！";
                        window.open(data.data.setUrl);
                        window.location = data.data.redirectUrl;
                    } else {
                        successMessage.textContent = "";
                    }
                    closeLoading()
                    // $('#submitbtn').attr('disabled',false)
                }
            });
        }catch (e) {
            closeLoading();
        }
    }

    signForm.addEventListener("submit", async (e) => {

        $('#submitbtn').attr('disabled',true)

        e.preventDefault();
        console.log($('#recipients').select2('data'));
        let verifyMsg = verifyForm();
        if (verifyMsg.length !== 0){
            errorMessage.textContent = verifyMsg;
            return;
        }
        const response = await fetch("lightpdfSign.createCheck.do", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                recipients: $('#recipients').select2('val').join(";"),
                studyid: $('#studyid').val(),
                subject: $('#subject').val(),
                signpage: $('#signpage').val(),
                language: $('#language').val(),
                signPageFile: '${signedFile}',
                signFlowExpireTime: $('#signFlowExpireTime').val(),
                type:  $('input[name="signatureType"]:checked').val(),
                body: editor.getHtml()

            })
        });
        const data = await response.json();
        if (data.status == 200) {
            successMessage.textContent = "验证成功！";
            window.open(data.data.setUrl);
            window.location = data.data.redirectUrl;
        } else {
            // errorMessage.textContent = "验证码或邮箱错误";
            successMessage.textContent = "";
        }
        $('#submitbtn').attr('disabled',false)
    });



    signer.addEventListener("submit", async (e) => {


        e.preventDefault();


        if (!isValidName($('#signer_name').val())) {
            alert('姓名仅允许包含大小写字母、空格或只包含中文字符');
            return;
        }
        if (!isEmailValid($('#signer_mail').val())) {
            alert('邮箱格式错误！');
            return;
        }
        var selectedValues = $('#recipients').select2('val');
        // alert(JSON.stringify(selectedValues));

// 要检查的目标值
        var targetValue = '<'+$('#signer_mail').val()+'>';
        // alert(JSON.stringify(targetValue));

// 判断目标值是否部分匹配已选中的值中的任何一个
        if(selectedValues){
            var isValueSelected = selectedValues.some(function(value) {
                // 使用indexOf进行部分匹配（不区分大小写）
                return value.toLowerCase().includes(targetValue.toLowerCase());
            });
        }


        if(isValueSelected){
            alert('邮箱已存在！');
            return;
        }

        var item=$('#signer_name').val()+'<'+$('#signer_mail').val()+'>';
        var option = new Option(item, item, true, true);
        $recipients.append(option);
        $recipients.trigger('change');
        closeForgetPsw();
    });

     function verifyForm(){
        let recipients = $('#recipients').select2('val')
        let studyid = $('#studyid').val()
        let language = $('#language').val()
        let signFlowExpireTime = $('#signFlowExpireTime').val()
        let signatureType = $('input[name="signatureType"]:checked').val()
        let msgArray = new Array();
        if (signatureType === null){
            msgArray.push("类型必填");
        }
        if (signatureType === "project"&& studyid.length === null){
            msgArray.push("研究代码必填！");
        }
        if (recipients === null){
            msgArray.push("签署人必填");
        }
        if (signFlowExpireTime === null){
            msgArray.push("过期天数(自然日)必填");
        }
        if (language === null){
            msgArray.push("待签文件语种必填！");
        }

        if($("#filelistdivsignpage").length > 0){
         let fileItem = $("#filelistdivsignpage").text()

         if (fileItem === ""){
            msgArray.push("待签文件必填！")
        }
        }
        return msgArray.join("，");
    }
    function isEmailValid(email) {
        // 使用正则表达式匹配邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function isValidName(name) {
        // 正则表达式：只包含大小写字母、空格或只包含中文字符
        const regex = /^([\u4e00-\u9fa5]+|[A-Za-z\s]+)$/;
        return regex.test(name);
    }
</script>
<script>

function showForgetPsw() {
// var overlay = document.getElementById("overlay");
// var iframe = document.getElementById("forgetPswIframe");
//
// Set the iframe src to your forget password page URL
<%--iframe.src = "lightpdfSign.changepassword.do?email=${currUserInfoMap.email}"; // Replace with the actual URL of your forget password page--%>

// Display the overlay
overlay.style.display = "flex";
}

function closeForgetPsw() {
// var overlay = document.getElementById("overlay");
// var iframe = document.getElementById("forgetPswIframe");

// Hide the overlay
overlay.style.display = "none";

// Reset the iframe src
// iframe.src = "";
}


<c:if test="${not empty signedFile}">

var ReType='${ReType}';
var ReStudyId='${ReStudyId}';
var ReLanguage='${ReLanguage}';
var ReStudyName='${ReStudyName}';
var ReSignerOptions='${ReSignerOptions}';
$("#language").val(ReLanguage);

if(ReStudyId){

    $studyid.append(new Option( ReStudyName,ReStudyId, true, true));
}


if(ReType=='project'){

    document.getElementsByName('signatureType')[1].checked = true;
    $('#div_studyid').show();

}


if(ReSignerOptions){

    ReSignerOptions=ReSignerOptions.split("|");
    ReSignerOptions.forEach(ReSignerOption => {
    $recipients.append(new Option(ReSignerOption, ReSignerOption, true, true));

    });
}


</c:if>

    </script>

    </body>
    </html>
