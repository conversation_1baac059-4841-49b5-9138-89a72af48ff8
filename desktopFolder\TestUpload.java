package net.bioknow.cdtms.edmFileOutput;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;

public class TestUpload {
	
	public static void main(String[] args) {
		try {
			String url = "https://meduap-tst.hengrui.com:8085/usersyn/upload?"
					+ "token=C989349273904F00B85A415F3E174912"
					+ "&formid=599E4E92506A469A83E574E3ABC8C2AB"
					+ "&tableid=ecrf_build"
					+ "&recordid=3321593856"
					+ "&fid=logs"
					+ "&fn=" + URLEncoder.encode("SAS-Validation-duplicaterecord_UAT_4A8D087A05E54B378493EE56F3C1AF74_a", "UTF8")+".zip";

			String url2="https://meduap-tst.hengrui.com:8085/remoteButtonTask/upload?taskId=3344957449&formId=72167236AB5C4D20A2D46B5E634AF65F&fid=file&fn="+URLEncoder.encode("SAS-Validation-duplicaterecord_UAT_4A8D087A05E54B378493EE56F3C1AF74_a", "UTF8")+".zip"+"&projectId=cdtmsen_val";
			File f = new File("d:/SAS-Validation-duplicaterecord_UAT_4A8D087A05E54B378493EE56F3C1AF74_a.zip");
			FileInputStream fis = new FileInputStream(f);
			String ret = postContent(url2,fis);
			System.out.println(ret);
		}catch(Exception e) {
			e.printStackTrace();
		}
	}
	
	public static String postContent(String urlStr, InputStream inputstream) throws Exception {
		URL url = new URL(urlStr);
		HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
		httpConn.setDoInput(true);
		httpConn.setDoOutput(true);
		httpConn.setRequestMethod("POST");
		httpConn.setConnectTimeout(3000);
		httpConn.setReadTimeout(3000);

		httpConn.setRequestProperty("content-type", "application/octet-stream");

		httpConn.connect();
		int len = 0;
		byte[] byteA = new byte[4096];
		OutputStream outStream = httpConn.getOutputStream();
		while ((len = inputstream.read(byteA)) > 0) {
			outStream.write(byteA, 0, len);
		}
		httpConn.getOutputStream().flush();
		httpConn.getOutputStream().close();

		OutputStream os = new ByteArrayOutputStream();

		InputStream is = httpConn.getInputStream();
		byte[] bA = new byte[40960];
		len = is.read(bA);
		while (len > 0) {
			os.write(bA, 0, len);
			len = is.read(bA);
		}
		os.close();
		is.close();

		return os.toString();
	}
}
