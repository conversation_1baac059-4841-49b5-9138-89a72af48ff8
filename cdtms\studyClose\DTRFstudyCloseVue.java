package net.bioknow.cdtms.studyClose;

import net.bioknow.services.uap.dbdatamng.function.DTTableFuncActionNew;
import net.bioknow.services.uap.dbdatamng.function.FuncInfoBeanNew;
import net.bioknow.services.uap.dbdatamng.function.FuncParamBeanNew;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTTableFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;


public class DTRFstudyCloseVue extends DTTableFuncActionNew {


    @Override
    public boolean canUse(int auth, String tableid, String refinfo, boolean readonly) {
        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            String userid = SessUtil.getSessInfo().getUserid();
            List<Map<String, String>> studyCloseIntegrateList = DAOstudyCloseIntegrate.listRule(projectId);
            String studyCloseIntegrateTableid = studyCloseIntegrateList.get(0).get(DAOstudyCloseIntegrate.tableid);
            String studyCloseAuthorizedRole = studyCloseIntegrateList.get(0).get(DAOstudyCloseIntegrate.AuthorizedRole);
            Map currentUserMap = SessUtil.getSessInfo().getUser();
            String[] AuthorizedRole = studyCloseAuthorizedRole.split(",");
            String[] currentRole = String.valueOf(currentUserMap.get("ryjs_org")).split(",");
            if (StringUtils.equals(tableid, studyCloseIntegrateTableid) && (!Collections.disjoint(Arrays.asList(AuthorizedRole), Arrays.asList(currentRole)) || Long.parseLong(userid)<10)) {

                    return true;
            }


        } catch (Exception e) {
            Log.error("", e);
        }
        return false;
    }



    public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBeanNew fpb) {
        try {


            String projectId = SessUtil.getSessInfo().getProjectid();
            Long userid = Long.valueOf(SessUtil.getSessInfo().getUserid());
            String recordid = fpb.getRecordid();
            String selids = fpb.getSelids();
            String tableid = fpb.getTableid();
            String where = fpb.getWhere();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            int sum = 0;
            ArrayList<Map> StudyList = new ArrayList<>();
            if (StringUtils.isEmpty(selids)) {
                this.gotoMsgWin(request, response, "关闭失败，请至少选中一个项目", 1000, "window.parent.reloadFromInnerWindow();");
                return;
            }


            String[] selidMap = selids.split(",");
            long currentTime = new Date().getTime();
            String errMsg="<br>";
            for (String selid : selidMap) {

                Map studyMap = daoDataMng.getRecord("xsht", Long.valueOf(selid));

                Long studyId = (Long) studyMap.get("id");
                String studyCode = (String) studyMap.get("studyid");
                String studyStatus = (String) studyMap.get("zt");
//                String studyErrMsg=studyCode+"关闭失败：";

                HashMap<Object, Object> StudySaveMap = new HashMap<>();
                StudySaveMap.put("id", studyId);
                StudySaveMap.put("closed", "1");
                StudyList.add(StudySaveMap);

//                if (StringUtils.equals("50", String.valueOf(studyStatus)) || StringUtils.equals("60", String.valueOf(studyStatus))) {
//                    List<Map> studyProcessList = daoDataMng.listRecord("spmxb", "obj.sjb ='xsht' and obj.jl='" + studyId + "'", "obj.blsj desc", 1);
//                    studyErrMsg+="\"已完成”/\"已终止\"，在 90 天内；";
//
//                    if (CollectionUtils.isNotEmpty(studyProcessList)) {
//                        Map studyProcessMap = studyProcessList.get(0);
//                        Date ProcessDate = (Date) studyProcessMap.get("blsj");
//
//                        long diffInMillies = currentTime - ProcessDate.getTime();
//                        long diffDays = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);
//
//                        if (diffDays >= 90) {
//                            HashMap<Object, Object> StudySaveMap = new HashMap<>();
//                            StudySaveMap.put("id", studyId);
//                            StudySaveMap.put("closed", "1");
//                            StudyList.add(StudySaveMap);
//
//                            continue;
//                        }
//
//                    }
//                }


//                if (StringUtils.equals("2", (String) studyMap.get("active"))) {
//                    List<Map> stuydDiaryList = daoDataMng.listRecord("xmrznr", "obj.studyid='" + studyCode + "'", "obj.createtime desc", 1);
//                    studyErrMsg+="活跃状态为否，在180天内；";
//
//                    if (CollectionUtils.isNotEmpty(stuydDiaryList)) {
//
//                        Map stuydDiaryMap = stuydDiaryList.get(0);
//                        Date createDate = (Date) stuydDiaryMap.get("createtime");
//
//                        long diffInMillies = currentTime - createDate.getTime();
//                        long diffDays = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);
//
//
//
//                        if (diffDays >= 210) {
//                            HashMap<Object, Object> StudySaveMap = new HashMap<>();
//                            StudySaveMap.put("id", studyId);
//                            StudySaveMap.put("closed", "1");
//                            StudyList.add(StudySaveMap);
//                            continue;
//                        }
//
//                    }
//                }

//                errMsg+=studyErrMsg+"<br>";
            }
            sum = StudyList.size();

            daoDataMng.saveBatch(tableid, StudyList, userid, null);

            this.gotoMsgWin(request, response, "已成功关闭" + sum + "个项目"+errMsg, 5000, "window.parent.reloadFromInnerWindow();");


        } catch (Exception e) {
            Log.error("", e);
        }
    }

    public FuncInfoBeanNew getFIB(String tableid) {
        FuncInfoBeanNew fib = new FuncInfoBeanNew("cdtms_DTRFstudyCloseVue");
        try {
            fib.setName("关闭项目");
            fib.setType(FuncInfoBeanNew.FUNCTYPE_INNERWINDOW);
            fib.setSimpleViewShow(true);
            fib.setConfirmStr("项目列表将不再显示选中的项目");
        } catch (Exception e) {
            Log.error("", e);
        }
        return fib;
    }

    public static void main(String[] args) {
        System.out.println(StringUtils.indexOfAny("dance1", new String[]{"sing", "dance", "rap"}));
    }

}
