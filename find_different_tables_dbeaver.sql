-- =====================================================
-- FIND TABLES WITH DIFFERENT CONTENT - DBEAVER VERSION
-- Compatible with <PERSON>eaver (No SQL*Plus commands)
-- Source: CDTMS_PILOT, Target: CDTMS_TEMP
-- =====================================================

-- Step 1: Create a temporary table to store results
CREATE GLOBAL TEMPORARY TABLE temp_table_comparison (
    table_name VARCHAR2(128),
    source_count NUMBER,
    target_count NUMBER,
    difference NUMBER,
    status VARCHAR2(20),
    error_msg VARCHAR2(500)
) ON COMMIT PRESERVE ROWS;

-- Step 2: Compare all tables from 比对范围.txt
-- Insert comparison results for each table
INSERT INTO temp_table_comparison (table_name, source_count, target_count, difference, status, error_msg)
SELECT 
    table_name,
    source_count,
    target_count,
    ABS(source_count - target_count) as difference,
    CASE 
        WHEN source_count = -1 AND target_count = -1 THEN 'BOTH_MISSING'
        WHEN source_count = -1 THEN 'SOURCE_MISSING'
        WHEN target_count = -1 THEN 'TARGET_MISSING'
        WHEN source_count = target_count THEN 'IDENTICAL'
        ELSE 'DIFFERENT'
    END as status,
    error_msg
FROM (
    -- Table comparisons (first 20 tables for testing)
    SELECT 'tbl_attachment' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_attachment) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_attachment) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_mdchapter_menuid' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_mdchapter_menuid) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_mdchapter_menuid) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_mdchapter' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_mdchapter) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_mdchapter) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_eclinichistory' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_eclinichistory) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_eclinichistory) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_esign_account' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_esign_account) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_esign_account) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_study_visit_set' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_study_visit_set) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_visit_set) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_study_visit_table' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_study_visit_table) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_visit_table) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_study_crf_visit' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_study_crf_visit) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_crf_visit) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_study_crf_visit_table' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_study_crf_visit_table) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_crf_visit_table) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_study_crf_table_pctpt' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_study_crf_table_pctpt) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_crf_table_pctpt) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_log_event' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_log_event) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_log_event) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_systemcode' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_systemcode) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_systemcode) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_xmgt' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_xmgt) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_xmgt) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_study_partner' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_study_partner) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_partner) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_proj_plan' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_proj_plan) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_proj_plan) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_yqsq' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_yqsq) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_yqsq) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_risk_management' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_risk_management) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_risk_management) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_study_qc' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_study_qc) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_qc) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_schedule' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_schedule) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_schedule) as target_count,
           NULL as error_msg
    FROM dual
    UNION ALL
    SELECT 'tbl_crf_design' as table_name,
           (SELECT COUNT(*) FROM CDTMS_PILOT.tbl_crf_design) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_crf_design) as target_count,
           NULL as error_msg
    FROM dual
);

-- Step 3: Display summary of all comparisons
SELECT 
    'COMPARISON SUMMARY' as report_section,
    COUNT(*) as total_tables_checked,
    SUM(CASE WHEN status = 'DIFFERENT' THEN 1 ELSE 0 END) as tables_with_differences,
    SUM(CASE WHEN status = 'IDENTICAL' THEN 1 ELSE 0 END) as identical_tables,
    SUM(CASE WHEN status LIKE '%MISSING%' THEN 1 ELSE 0 END) as missing_tables
FROM temp_table_comparison;

-- Step 4: Show only tables with differences
SELECT 
    table_name as "Table Name",
    source_count as "Source (PILOT)",
    target_count as "Target (TEMP)", 
    difference as "Difference",
    status as "Status"
FROM temp_table_comparison
WHERE status IN ('DIFFERENT', 'SOURCE_MISSING', 'TARGET_MISSING')
ORDER BY difference DESC, table_name;

-- Step 5: Show tables that are identical (for verification)
SELECT 
    table_name as "Identical Tables",
    source_count as "Row Count",
    'Both schemas have same count' as "Status"
FROM temp_table_comparison
WHERE status = 'IDENTICAL'
ORDER BY table_name;

-- Step 6: Show any missing tables
SELECT 
    table_name as "Missing Tables",
    status as "Issue"
FROM temp_table_comparison
WHERE status LIKE '%MISSING%'
ORDER BY table_name;

-- Step 7: Quick verification commands for tables with differences
SELECT 
    'Manual verification commands for tables with differences:' as instructions
FROM dual
WHERE EXISTS (SELECT 1 FROM temp_table_comparison WHERE status = 'DIFFERENT');

SELECT 
    'SELECT COUNT(*) FROM CDTMS_PILOT.' || table_name || '; -- Source' as "SQL Commands"
FROM temp_table_comparison 
WHERE status = 'DIFFERENT'
UNION ALL
SELECT 
    'SELECT COUNT(*) FROM CDTMS_TEMP.' || table_name || '; -- Target' as "SQL Commands"
FROM temp_table_comparison 
WHERE status = 'DIFFERENT';

-- Clean up temporary table
DROP TABLE temp_table_comparison;

-- Final result: List of tables that need attention
SELECT 
    'TABLES REQUIRING ATTENTION:' as final_result
FROM dual
WHERE EXISTS (SELECT 1 FROM temp_table_comparison WHERE status != 'IDENTICAL');

-- Note: If you see "table or view does not exist" errors, 
-- it means those tables don't exist in one or both schemas.
-- This is normal and indicates missing tables that need to be created.
