package net.bioknow.cdtms.schedule;

import net.bioknow.cdtms.esign.DTRFeSign;
import net.bioknow.mvc.ioc.AppStartFace;
import net.bioknow.uap.dbdatamng.function.FuncFactory;
import net.bioknow.webio.filedb.FileDBUtil;
import javax.servlet.ServletConfig;

public class EHAppStart implements AppStartFace{

    public void onStart(ServletConfig arg) throws Exception {
        FuncFactory.addRecordFunc(new DTRFeSign());


        FileDBUtil.addFilePubHandler(new FilePubHandler());


    }

}
