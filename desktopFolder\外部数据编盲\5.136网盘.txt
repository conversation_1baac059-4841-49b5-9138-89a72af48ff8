




//10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/ on /home/<USER>
//10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log on /home/<USER>/blind_sas_log type cifs (rw,relatime,vers=3.1.1,cache=strict,username=zhouh36,uid=0,forceuid,gid=0,forcegid,addr=10.10.5.56,file_mode=0666,dir_mode=0770,soft,nounix,serverino,mapposix,rsize=4194304,wsize=4194304,bsize=1048576,echo_interval=60,actimeo=1)
//10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/ on /home/<USER>
//10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log on /home/<USER>/blind_sas_log type cifs (rw,relatime,vers=3.1.1,cache=strict,username=zhouh36,uid=0,forceuid,gid=0,forcegid,addr=10.10.5.56,file_mode=0666,dir_mode=0770,soft,nounix,serverino,mapposix,rsize=4194304,wsize=4194304,bsize=1048576,echo_interval=60,actimeo=1)
//10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/csv_raw on /home/<USER>/ex_original_csv type cifs (rw,relatime,vers=3.1.1,cache=strict,username=zhouh36,uid=0,forceuid,gid=0,forcegid,addr=10.10.5.56,file_mode=0666,dir_mode=0770,soft,nounix,serverino,mapposix,rsize=4194304,wsize=4194304,bsize=1048576,echo_interval=60,actimeo=1)
//10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/json_blind on /home/<USER>/ex_json_file type cifs (rw,relatime,vers=3.1.1,cache=strict,username=zhouh36,uid=0,forceuid,gid=0,forcegid,addr=10.10.5.56,file_mode=0666,dir_mode=0770,soft,nounix,serverino,mapposix,rsize=4194304,wsize=4194304,bsize=1048576,echo_interval=60,actimeo=1)
























 
  mount.cifs //SHNVWSASECT01/EDMtst/EDM/blind_sas_log /home/<USER>/blind_sas_log -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root
 

  
  
  
  287  mount.cifs //10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log /home/<USER>/blind_sas_log -o file_mode=0666,dir_mode=0770,username=zhouh36,password=Zh123456,gid=root,uid=root




  301  mount.cifs //SHNVWSASECT01/EDMtst /home/<USER>/ -o file_mode=0666,dir_mode=0770,username='zhouh36',password='HR9cf3cbd8',gid=root,uid=root
file_mode=0666,dir_mode=0770,username='zhouh36',password='HR9cf3cbd8',gid=root,uid=root  //10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log /home/<USER>

  525  mount.cifs //10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/ /home/<USER>'zhouh36',password='Zh123456',gid=root,uid=root





  532  mount.cifs //10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log /home/<USER>/blind_sas_log -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root

 mount.cifs //10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/csv_raw /home/<USER>/ex_original_csv -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root
 mount.cifs //10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/json_blind /home/<USER>/ex_json_file -o file_mode=0666,dir_mode=0770,username='zhouh36',password='Zh123456',gid=root,uid=root
 
 
 
 nohup java -jar blind_back-0.0.1-SNAPSHOT.jar >log.txt
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 

//10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/ on /home/<USER>
//10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log on /home/<USER>/blind_sas_log type cifs (rw,relatime,vers=3.1.1,cache=strict,username=zhouh36,uid=0,forceuid,gid=0,forcegid,addr=10.10.5.56,file_mode=0666,dir_mode=0770,soft,nounix,serverino,mapposix,rsize=4194304,wsize=4194304,bsize=1048576,echo_interval=60,actimeo=1)
binfmt_misc on /proc/sys/fs/binfmt_misc type binfmt_misc (rw,relatime)
tracefs on /sys/kernel/debug/tracing type tracefs (rw,relatime)
//10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/ on /home/<USER>
//10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/blind_sas_log on /home/<USER>/blind_sas_log type cifs (rw,relatime,vers=3.1.1,cache=strict,username=zhouh36,uid=0,forceuid,gid=0,forcegid,addr=10.10.5.56,file_mode=0666,dir_mode=0770,soft,nounix,serverino,mapposix,rsize=4194304,wsize=4194304,bsize=1048576,echo_interval=60,actimeo=1)
//10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/csv_raw on /home/<USER>/ex_original_csv type cifs (rw,relatime,vers=3.1.1,cache=strict,username=zhouh36,uid=0,forceuid,gid=0,forcegid,addr=10.10.5.56,file_mode=0666,dir_mode=0770,soft,nounix,serverino,mapposix,rsize=4194304,wsize=4194304,bsize=1048576,echo_interval=60,actimeo=1)
//10.10.5.56/CDM/Projects/dev/GRP_CDSC_liz119/URS_EDM/json_blind on /home/<USER>/ex_json_file type cifs (rw,relatime,vers=3.1.1,cache=strict,username=zhouh36,uid=0,forceuid,gid=0,forcegid,addr=10.10.5.56,file_mode=0666,dir_mode=0770,soft,nounix,serverino,mapposix,rsize=4194304,wsize=4194304,bsize=1048576,echo_interval=60,actimeo=1)
 
 
 
 
 
 
 
 
 
 
 
 
 
http://***********:9000/dmreview/HR070803-301_LAB_AE.csv(String), 
CN014(String), CN014003(String), CN014002(String), 2024-01-15(String), D-二聚体(String), 是(String), CN014(String), CN014003(String), CN014002(String), 2024-01-15(String), D-二聚体(String), 是(String), CN014(String), CN014003(String), CN014002(String), 2024-01-15(String), D-二聚体(String), 是(String), CN014(String), CN014003(String), CN014002(String), 2024-01-15(String), D-二聚体(String), 是(String), CN014(String), CN014003(String), CN014002(String), 2024-01-15(String), D-二聚体(String), 是(String), CN014(String), CN014003(String), CN014002(String), 2024-01-15(String), D-二聚体(String), 是(String) 
 
 
 
 
 
 
 
 with minio_tbl AS (SELECT *, toString(sipHash64(*)) AS lab_data_id FROM s3('http://***********:9000/dmreview/HR070803-301_LAB_AE.csv', 'minioadmin', 'minioadmin','CSVWithNames')), temp1 AS ( SELECT * FROM minio_tbl ) SELECT count(1) AS count FROM temp1 where temp1.lab_data_id IN ( SELECT distinct lab_data_id FROM mysql('***********:3306','dm_platform', 'dm_lab_ae_comments', 'susaruser', 'Hr@db0316') where notEquals(lab_comment,'') and equals(is_deleted,'N') ) AND ( `中心` in ( 'CN014' ,'CN014003' , 'CN014002' , ' 2024-01-15', 'D-二聚体' , '是' ) AND `受试者代码` in ( 'CN014' ,'CN014003' , 'CN014002' , ' 2024-01-15', 'D-二聚体' , '是'  ) AND `知情同意书签字日期-D` in ( 'CN014' ,'CN014003' , 'CN014002' , ' 2024-01-15', 'D-二聚体' , '是'  ) AND `关联检查项` in ( 'CN014' ,'CN014003' , 'CN014002' , ' 2024-01-15', 'D-二聚体' , '是'  ) AND `表名称` in ( ? , ? , ? , ? , ? , ? ) AND `转归/是否持续(检查类)` in ('CN014' ,'CN014003' , 'CN014002' , ' 2024-01-15', 'D-二聚体' , '是'  ) )



