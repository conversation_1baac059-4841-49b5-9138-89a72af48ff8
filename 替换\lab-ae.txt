options VALIDVARNAME=V7;

%let dir=%sysfunc(getoption(work));
%put &dir.;

x "cd &dir";x "mkdir ./pgm";
%global system studyid root version minioid;


%let m_minio = minios3-t;
%let studyid=HRS-2261-201;
%let lang="CH";
/*%let studyid=HRS-2261-201;*/


%let minioid = &m_minio.;
%let isminio = Y;
%let json_path = &minioid./sdv/json/&studyid._LAB-AE.json;


x "mc find minios3/ --name ""*.sas"" | xargs -I{} mc cp {} ./pgm/";
options mautolocdisplay mautosource sasautos = ("&dir/pgm/");
%include "&dir/pgm/M_gen_pro_alstrans.sas";
%include "&dir/pgm/m_post2s3.sas";
%include "&dir/pgm/m_gets3data.sas";
option mprint symbolgen validvarname=v7;

%m_post2s3(studyid=&studyid.,env = uat, ravepsw = );
%m_gets3data(studyid=&studyid.,env = uat, data=@);


x "mc find &minioid./ --name ""*.sas"" | xargs -I{} mc cp {} ./pgm/";
options mautolocdisplay mautosource sasautos = ("&dir/pgm/");
%include "&dir/pgm/m_lab_ae.sas";
%include "&dir/pgm/m_lab_ae_inputfile.sas";
%include "&dir/pgm/m_lab_ae_predata_bio4.sas";
%include "&dir/pgm/m_lab_ae_predata_bio4_eng.sas";
%include "&dir/pgm/m_lab_ae_predata_rave.sas";
%include "&dir/pgm/m_lab_ae_warning.sas";
%include "&dir/pgm/m_lab_ae_outputfile.sas";
%include "&dir/pgm/m_exportxlsx_dmreview.sas";
%include "&dir/pgm/M_gen_pro_coding.sas";
%include "&dir/pgm/m_custom_function.sas";


%m_lab_ae;


