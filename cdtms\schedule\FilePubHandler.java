package net.bioknow.cdtms.schedule;

import com.mchange.v1.db.sql.ConnectionUtils;
import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbcore.schema.CNT_Schema;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uapplug.publicworkflow.CNT_Publicworkflow;
import net.bioknow.uapplug.publicworkflow.DAOPublicworkflow;
import net.bioknow.webio.filedb.FilePubFace;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class FilePubHandler implements FilePubFace {


	public void afterPub(String projectId,Set setDiffName) {
		try{

			DAODataMng daoDataMng = new DAODataMng(projectId);


			List<Map> scheduleWorkDateCnfList = daoDataMng.listRecord("schedule_work_date_cnf", "1=1", null, 1000);

			if (CollectionUtils.isEmpty(scheduleWorkDateCnfList)) {
				return;
			}


			CacheMgr cm=CacheMgr.getInstance();
			cm.addCache(projectId, scheduleWorkDateCnfList);//在缓存加值

		}catch(Exception e) {
			Log.error("",e);
		}
	}

}
