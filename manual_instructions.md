# 📋 Manual Instructions: Add TABLE_NAME_CH Column to Sheet2

## 🎯 Objective
Add a new column 'TABLE_NAME_CH' to Sheet2 that contains Chinese names from Sheet1 based on TABLE_NAME matching.

## 📝 Step-by-Step Instructions

### Step 1: Open the Excel File
1. Open `meduap未同步表数据.xlsx` in Excel
2. You should see two sheets: Sheet1 and Sheet2

### Step 2: Analyze Sheet1 Structure
1. Go to Sheet1
2. Look for columns that contain:
   - Table names (likely containing 'tbl_' prefixes)
   - Chinese names (containing Chinese characters)
3. Note the column letters/positions

### Step 3: Analyze Sheet2 Structure  
1. Go to Sheet2
2. Find the column that contains table names (TABLE_NAME)
3. Note which column this is

### Step 4: Add New Column Header
1. In Sheet2, find the last column with data
2. In the next empty column, add the header: `TABLE_NAME_CH`

### Step 5: Create VLOOKUP Formula
1. In the first data row under TABLE_NAME_CH, create a VLOOKUP formula:

```excel
=IFERROR(VLOOKUP(A2,Sheet1!A:B,2,FALSE),"NA")
```

**Adjust the formula based on your actual columns:**
- `A2` = the cell containing the table name in Sheet2
- `Sheet1!A:B` = the range in Sheet1 containing table names and Chinese names
- `2` = the column number of Chinese names in the Sheet1 range
- `FALSE` = exact match

### Step 6: Copy Formula Down
1. Select the cell with the VLOOKUP formula
2. Copy it down to all rows with data in Sheet2
3. You can do this by:
   - Double-clicking the fill handle (small square at bottom-right of cell)
   - Or selecting the range and pressing Ctrl+D

### Step 7: Convert to Values (Optional)
1. Select all the VLOOKUP formulas
2. Copy (Ctrl+C)
3. Paste Special > Values to convert formulas to static values

### Step 8: Save the File
1. Save as a new file: `meduap未同步表数据_updated.xlsx`

## 🔧 Alternative Formula Examples

If your columns are different, adjust the VLOOKUP formula:

**Example 1:** If table names are in column A of Sheet1 and Chinese names in column C:
```excel
=IFERROR(VLOOKUP(A2,Sheet1!A:C,3,FALSE),"NA")
```

**Example 2:** If you want to use INDEX/MATCH instead:
```excel
=IFERROR(INDEX(Sheet1!B:B,MATCH(A2,Sheet1!A:A,0)),"NA")
```

**Example 3:** For case-insensitive matching:
```excel
=IFERROR(VLOOKUP(UPPER(A2),UPPER(Sheet1!A:B),2,FALSE),"NA")
```

**Example 4:** To handle extra spaces:
```excel
=IFERROR(VLOOKUP(TRIM(A2),Sheet1!A:B,2,FALSE),"NA")
```

## 🎯 Expected Result
- Sheet2 will have a new column TABLE_NAME_CH
- Each row will show the Chinese name corresponding to the table name
- Rows without matches will show "NA"

## 📊 Sample Result
```
TABLE_NAME          | TABLE_NAME_CH
tbl_attachment      | 附件表
tbl_log_event       | 日志事件表
tbl_systemcode      | 系统代码表
tbl_unknown         | NA
```

## 🔍 Troubleshooting

**If you get #N/A errors:**
- Check that table names match exactly between sheets
- Ensure there are no extra spaces
- Verify column references in the formula

**If you get #REF! errors:**
- Check that the column references are correct
- Make sure Sheet1 exists and has the expected columns

**If some matches are missing:**
- Check for case sensitivity issues
- Look for trailing/leading spaces in table names
- Verify that the table names exist in both sheets

## ✅ Verification
After completing, verify that:
1. The new column TABLE_NAME_CH exists in Sheet2
2. Most table names have corresponding Chinese names
3. Unmatched tables show "NA"
4. No formula errors are present

## 🚀 Quick Summary
1. **Open** the Excel file with both sheets
2. **Add** TABLE_NAME_CH column header to Sheet2
3. **Use** VLOOKUP formula: `=IFERROR(VLOOKUP(A2,Sheet1!A:B,2,FALSE),"NA")`
4. **Adjust** column references based on your actual data layout
5. **Copy** formula down to all rows
6. **Save** as updated file

The formula will automatically match table names from Sheet2 with Chinese names from Sheet1!
