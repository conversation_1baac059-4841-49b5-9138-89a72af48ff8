﻿

%global system studyid root version dir lang m_minio repdat;
proc datasets lib=work nolist kill; quit;%let dir=%sysfunc(getoption(work));

x "cd &dir";x "mkdir ./pgm";x 'mc find minios3/pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';
%include "&dir/pgm/*.sas";
option mprint symbolgen validvarname=v7;

%let m_minio=minios3;
%let repdat=&sysdate.;

%let studyid=&studyid.;

%let lang="&lang.";
/*%put &lang.;*/

 
/*	data a;*/
/*	set raw.lab;*/
/*if subject eq "CN001003";*/
/*run;*/

/*%let i=1;*/

%let querul=edcserver/query_summary_protocol_violation;

%m_post2s3(studyid=&studyid.);
%m_gets3data(studyid=&studyid.,data=@);

%M_std_dm_pd;


