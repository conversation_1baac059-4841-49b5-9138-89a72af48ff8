SELECT 
    table_name,
    source_count,
    target_count,
    ABS(source_count - target_count) as difference,
    CASE 
        WHEN source_count = target_count THEN 'IDENTICAL'
        ELSE 'DIFFERENT'
    END as status
FROM (
    SELECT 'tbl_attachment' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_attachment) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_attachment) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_mdchapter_menuid' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_mdchapter_menuid) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_mdchapter_menuid) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_mdchapter' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_mdchapter) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_mdchapter) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_eclinichistory' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_eclinichistory) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_eclinichistory) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_esign_account' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_esign_account) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_esign_account) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_visit_set' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_visit_set) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_visit_set) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_visit_table' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_visit_table) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_visit_table) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_crf_visit' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_crf_visit) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_crf_visit) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_crf_visit_table' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_crf_visit_table) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_crf_visit_table) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_crf_table_pctpt' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_crf_table_pctpt) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_crf_table_pctpt) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_log_event' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_log_event) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_log_event) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_systemcode' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_systemcode) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_systemcode) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_xmgt' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_xmgt) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_xmgt) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_partner' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_partner) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_partner) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_proj_plan' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_proj_plan) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_proj_plan) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_yqsq' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_yqsq) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_yqsq) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_risk_management' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_risk_management) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_risk_management) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_study_qc' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_study_qc) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_study_qc) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_schedule' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_schedule) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_schedule) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_crf_design' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_crf_design) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_crf_design) as target_count
    FROM dual
    UNION ALL
    SELECT 'tbl_manual_rev_plan' as table_name,
           (SELECT COUNT(*) FROM CDTMSEN_VAL.tbl_manual_rev_plan) as source_count,
           (SELECT COUNT(*) FROM CDTMS_TEMP.tbl_manual_rev_plan) as target_count
    FROM dual
)
WHERE source_count != target_count
ORDER BY ABS(source_count - target_count) DESC;
