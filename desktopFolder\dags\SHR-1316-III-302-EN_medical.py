import airflow
from airflow.models import DAG
from airflow.operators.python import Python<PERSON><PERSON>ator
from airflow.operators.bash import BashOperator  
from airflow.contrib.operators.ssh_operator import SSHOperator
from airflow.utils.dates import days_ago
import pymysql
import json
import saspy
import subprocess
from airflow.utils.log.logging_mixin import LoggingMixin
import os
import re
from datetime import datetime,timedelta 

args = {
    'owner': 'weix',
    'start_date': datetime(2023, 9, 22),
}

dag = DAG(dag_id='SHR-1316-III-302-EN_medical', default_args=args, schedule_interval="0 17 * * *")

def download_and_execute_sas_script(**kwargs):
	filename = kwargs['filename']
	sas = saspy.SASsession()
	sas.submit('''%global system studyid root version dir;''')
	sas.submit('''proc datasets lib=work nolist kill; run;quit;%let dir=%sysfunc(getoption(work));x "cd &dir";x "mkdir ./pgm";x 'mc find minios3/pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';%include "&dir/pgm/*.sas";''')
	sas.submit('''%let studyid=SHR-1316-III-302-EN;''')
	sas.submit('''%m_post2s3(studyid=&studyid.);''')
	sas.submit('''%let lang="EN";''')
	sas.submit('''option mprint symbolgen validvarname=v7;''')
	sas.submit('''%m_gets3data(studyid=&studyid.,data=@);''')
	sas.submit('''%m_medical_joinaecm;''')
	sas.submit('''%clin_consistency;''')
	sas.submit('''%m_medical_aedrugeq;''')
	sas.submit('''%m_out2s3(studyid=&studyid.);''')
	sas.submit('''%m_med_formfie;''')
	sas.submit('''%m_med_stadm;''')
	sas.submit('''%m_med_starand;''')
	sas.submit('''%m_med_stasub;''')
	sas.submit('''%m_med_staae;''')
	sas.submit('''%m_med_stalb;''')
	sas.submit('''%m_create_hrtaubuild_lab;''')
	sas.submit('''%m_derive_hrtaubuild(folder=csvdata_lab);''')
	sas.submit('''%m_create_hrtau_lab(is_als=N);''')
	sas.submit('''%M_std_dvs_labreview;''')
	sas.submit('''%m_out2s3T(studyid=&studyid.,suffix=labctc);''')
	
	#AE_EX
	
	sas.submit('''%let studyid = SHR-1316-III-302-EN;''')
	sas.submitLOG('''%M_gen_pro_coding(formoid=ae,folder=,output=Y);''')
	sas.submitLOG('''%readin_coding;''')
	sas.submitLOG('''%m_med_dseos_pre_process(dseos=dseos,dsstdat =dsstdat);''')
	
	sas.submitLOG('''%m_med_dsrand_pre_process(dsrand=exinf,regime = extrt);''')
	sas.submit('''proc sort nodupkey; by _all_; run;''')
	sas.submitLOG('''%m_med_rave_ae_pre_process(aerel = aerel, ctcae = aetoxgr);''')
	sas.submitLOG('''%m_med_rave_ex_pre_process(data = exinf,siteid = sitenumber,expdost=expdose,exdosadj=);''')
	sas.submitLOG('''%m_med_rave_dseot_pre_process(dseot=dseot, siteid = sitenumber, dsstdat =dsstdat ,dsdecod = dsdecod,dsterm = dsterm);''')
	sas.submitLOG('''%ae_ex_process;''')
	sas.submitLOG('''%label_ae_ex;''')
	sas.submitLOG('''data ae_ex_med1; set out.ae_ex; run;''')
	sas.submitLOG('''data ex_med1; set out.ex; run;''')
	
	sas.submitLOG('''%m_med_dsrand_pre_process(dsrand=exinfa,regime = extrt);''')
	sas.submit('''proc sort nodupkey; by _all_; run;''')
	sas.submitLOG('''%m_med_rave_ae_pre_process(aerel = aerel_a, ctcae = aetoxgr);''')
	sas.submitLOG('''%m_med_rave_ex_pre_process(data = exinfa,siteid = sitenumber,expdost=expdosea,exdosadj=exdosadj);''')
	sas.submitLOG('''%m_med_rave_dseot_pre_process(dseot=dseota, siteid = sitenumber, dsstdat =dsstdat ,dsdecod = dsdecod,dsterm = dsterm);''')
	sas.submitLOG('''%ae_ex_process;''')
	sas.submitLOG('''%label_ae_ex;''')
	sas.submitLOG('''data ae_ex_med2; set out.ae_ex; run;''')
	sas.submitLOG('''data ex_med2; set out.ex; run;''')
	
	sas.submitLOG('''%m_med_dsrand_pre_process(dsrand=exinfb,regime = extrt);''')
	sas.submit('''proc sort nodupkey; by _all_; run;''')
	sas.submitLOG('''%m_med_rave_ae_pre_process(aerel = aerel_b, ctcae = aetoxgr);''')
	sas.submitLOG('''%m_med_rave_ex_pre_process(data = exinfb,siteid = sitenumber,expdost=expdosea,exdosadj=exdosadj);''')
	sas.submitLOG('''%m_med_rave_dseot_pre_process(dseot=dseotb, siteid = sitenumber, dsstdat =dsstdat ,dsdecod = dsdecod,dsterm = dsterm);''')
	sas.submitLOG('''%ae_ex_process;''')
	sas.submitLOG('''%label_ae_ex;''')
	sas.submitLOG('''data ae_ex_med3; set out.ae_ex; run;''')
	sas.submitLOG('''data ex_med3; set out.ex; run;''')
	
	sas.submitLOG('''%m_med_dsrand_pre_process(dsrand=exinfc,regime = extrt);''')
	sas.submit('''proc sort nodupkey; by _all_; run;''')
	sas.submitLOG('''%m_med_rave_ae_pre_process(aerel = aerel_c, ctcae = aetoxgr);''')
	sas.submitLOG('''%m_med_rave_ex_pre_process(data = exinfc,siteid = sitenumber,expdost=expdosec,exdosadj=exdosadj);''')
	sas.submitLOG('''%m_med_rave_dseot_pre_process(dseot=dseotc, siteid = sitenumber, dsstdat =dsstdat ,dsdecod = dsdecod,dsterm = dsterm);''')
	sas.submitLOG('''%ae_ex_process;''')
	sas.submitLOG('''%label_ae_ex;''')
	sas.submitLOG('''data ae_ex_med4; set out.ae_ex; run;''')
	sas.submitLOG('''data ex_med4; set out.ex; run;''')

	sas.submitLOG('''data out.ae_ex(label='AE_EX');set ae_ex_med1 ae_ex_med2 ae_ex_med3 ae_ex_med4;run;''')
	sas.submitLOG('''data out.ex(label='EX');set ex_med1 ex_med2 ex_med3 ex_med4;run;''')
	sas.submitLOG('''%m_out2s3T(studyid=&studyid.,suffix=AEEX);''')
	sas.submitLOG('''%m_trans_SASToExcel;''')
	sas.submitLOG('''%m_exportxlsx_simple(studyid=SHR-1316-III-302-EN);''')
	sas.endsas()
# 任务4:执行sas脚本 
task1 = PythonOperator(
	task_id ='download_and_execute_sas_script', 
	python_callable = download_and_execute_sas_script,
	op_kwargs={
			'filename': 'SHR-1316-III-302-EN'
		},
		dag=dag
)

task2 = SSHOperator(
    ssh_conn_id='dbt', task_id='dbt_seed_ssh',
    command='cd /root/dbti/minio_mysql/ && python deliver_miniofile_mysql.py medical SHR-1316-III-302-EN_AEEX.zip', 
    # python deliver_miniofile_mysql.py bucket_name filename1,filename2,filename3...
    dag=dag
) 

task1 >> task2