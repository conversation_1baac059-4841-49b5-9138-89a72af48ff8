import cx_Oracle
user = 'gousy'
password = 'hrgsy@cdtms'
host = '**********:1521'
service_name = 'orcl'
conn_str = f"{user}/{password}@{host}/{service_name}"
connect = cx_Oracle.connect(conn_str)
cursor = connect.cursor()
sql="""
SELECT DISTINCT COL_STUDYID FROM CDTMSEN_VAL.TBL_XSHT WHERE COL_ZT != '40' OR COL_ZT != '50' OR COL_ZT != '60' OR COL_ZT != '70'
"""
cursor.execute(sql)
rows = cursor.fetchall()
for row in rows:
 print(row[0])
cursor.close()
connection.close()