package net.bioknow.cdtms.schedule;


import net.bioknow.uap.entitymng.AbsEntity;

public class EntityScheduleTemplate extends AbsEntity {
    public String schedulestage;
    public String schedulename;
    public String scheduletype;
    public String preschedule;
    public String studyrole;
    public String schedulestart;
    public String scheduleend;
    public String scheduleper;
    public String schedulefre;
    public String num;
    public String sn;
    public String tableid;
    public String tablename;
    public String menuid;

    public String nodefieldid;
    public String nodefieldname;
    public String finishfieldid;
    public String finishfieldname;
    public String finishstatusvalue;
    public String finishstatusname;
    public String markid;
    public String markname;
    public String extractid;
    public String extractname;
    public String subtaskid;
    public String subtaskname;
    public String endtimefield;
}
