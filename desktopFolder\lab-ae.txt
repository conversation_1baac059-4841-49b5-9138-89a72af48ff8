%global system studyid root version dir lang m_minio repdat isminio minioid;
proc datasets lib=work nolist kill; quit;%let dir=%sysfunc(getoption(work));

x "cd &dir";x "mkdir ./pgm";x 'mc find minios3/pgm/ --name "*.sas" --exec "mc cp {} ./pgm/"';
%include "&dir/pgm/*.sas";
option mprint symbolgen validvarname=v7;

%let m_minio=minios3;
%let minioid=minios3;
%let repdat=&sysdate.;

%let studyid=&studyid.;

%let lang="CH";
/*%put &lang.;*/


%m_post2s3(studyid=&studyid.,env=uat);
%m_gets3data(studyid=&studyid.,data=@,env=uat);

option mprint symbolgen validvarname=v7;


/*%let repdat=&sysdate.;*/
/*%let m_minio=minios3;*/
x "mc find &m_minio./sdv/json --name ""&studyid._LAB-AE.json"" | xargs -I{} mc cp {} ./doc/";
filename y "&root./doc/&studyid._LAB-AE.json";
libname jsonrec json fileref=y;
proc copy in=jsonrec out=work;
run;

data macro_var;
        set alldata;
        if P1 = 'sascode' then do;
            if Value ^= '' then call symputx("sascode",Value);
                else call symputx("sascode","");
        if P1 = 'system' then call symputx("system",Value);
        end;
run;
%put &system.;
 
%m_custom_function;   
&sascode.



%m_lab_ae;

