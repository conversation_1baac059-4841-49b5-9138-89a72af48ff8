import airflow
from airflow.models import DAG
from airflow.operators.python import Python<PERSON><PERSON><PERSON>
from airflow.operators.bash import Ba<PERSON><PERSON>perator  
from airflow.utils.dates import days_ago
import pymysql
import json
import saspy
import subprocess
from airflow.utils.log.logging_mixin import LoggingMixin
import os
import re
from datetime import datetime,timedelta 

args = {
    'owner': 'zhouhui',
    'start_date': datetime(2023, 6, 12)
}

dag = DAG(dag_id='HRS9531-102_sas_process_dag', default_args=args, schedule_interval='@daily')
# 任务1:使用PyMySQL查询文件路径和文件名,并根据文件路径上传至minio,上传时以文件名命名
def mysql_query_upload():
	host = '***********'
	port = 3306
	user = 'weix5'
	password = 'weix@edc'
	database = 'report_server'
	conn = pymysql.connect(host=host,port=port,user=user,passwd=password,db=database)
	# 获取游标
	cursor = conn.cursor()
	# 执行SQL查询并获取结果
	sql = "SELECT REPORTFILENAME, REPORTFILEPATH FROM tbl_report_pro WHERE (REPORTFILENAME like '%SAS%' )and REPORTFILENAME like '%HRS9531-102%' ORDER BY EXPORTTIME desc LIMIT 1"
	cursor.execute(sql)
	results = cursor.fetchall()
	data = []
	# 获取minios3/raw目录下的文件列表
	list_cmd = 'mc ls minios3/raw'
	list_file_name = os.popen(list_cmd).read()
	for row in results:
		project=row[0][:row[0].index('_')]
		full_path=project+'_sas.zip'
		# 检查是否有同名文件
		if row[0].replace("(", "[").replace(")", "]") in list_file_name:
			result = re.split('/|\\\\', row[1])
			path_segment1 = result[-3]
			path_segment2 = result[-2]
			path_segment3 = result[-1]
			local_md5_cmd = f'md5sum /home/<USER>/{path_segment1}/{path_segment2}/{path_segment3}'
			local_md5 = os.popen(local_md5_cmd).read().split(' ')[0]
			remote_md5_cmd = f'mc cat minios3/raw/{row[0].replace("(", "[").replace(")", "]")} | md5sum' 
			remote_md5 = os.popen(remote_md5_cmd).read().split(' ')[0]
			# 比较md5,如果不同则上传并覆盖
			if local_md5 != remote_md5:
				result = re.split('/|\\\\', row[1])
				path_segment1 = result[-3]
				path_segment2 = result[-2]
				path_segment3 = result[-1]	
				upload_cmd = f'mc cp /home/<USER>/{path_segment1}/{path_segment2}/{path_segment3} minios3/raw/{full_path} --tags "key1=HRTAU&key2={project}"' 
				os.system(upload_cmd)
		else:
			result = re.split('/|\\\\', row[1])
			path_segment1 = result[-3]
			path_segment2 = result[-2]
			path_segment3 = result[-1]	
			upload_cmd =f'mc cp /home/<USER>/{path_segment1}/{path_segment2}/{path_segment3} minios3/raw/{full_path} --tags "key1=HRTAU&key2={project}"' 
			os.system(upload_cmd)
	# 重命名文件并上传文件
		# upload_cmd = f'mc cp /home/<USER>/raw/{row[0].replace("(", "[").replace(")", "]")} --tags "key1=HRTAU&key2={row[0].replace("(", "[").replace(")", "]")}"' 
		# os.system(upload_cmd)    # 上传到Minio并添加标签
		data.append({
			    "dag_id": row[0],
			    "fileloc": row[1]
		})
	data = json.dumps(data)
	with open('/home/<USER>', 'w') as f:
		f.write(str(data))
	cursor.close() 
	conn.close()

task2 = PythonOperator(
    task_id='mysql_query_upload',  
    python_callable=mysql_query_upload,
    dag=dag,  
)

def remove_file(): 
        rm_cmd= f'rm -rf /home/<USER>'
        os.system(rm_cmd)
       
task3 = PythonOperator(
    task_id='remove_file',
    python_callable=remove_file,
    dag=dag,
)
def download_and_execute_sas_script(**kwargs):
	filename = kwargs['filename']
	#     下载m_inits3.sas和m_post2s3.sas到/tmp/hsperfdata_root/sas_script
	download_cmds = [
			'mc cp minios3/raw/pgm/m_inits3.sas /tmp/hsperfdata_root/sas_script',
			'mc cp minios3/raw/pgm/m_post2s3.sas /tmp/hsperfdata_root/sas_script'
		]
	for cmd in download_cmds:
			os.system(cmd)
			sas = saspy.SASsession()
			sas.submit('''proc datasets lib=work nolist kill; run;quit;%let dir=%sysfunc(getoption(work));x "cd &dir";x "mkdir ./pgm";x 'mc find minios3/ --name "*.sas" --exec "mc cp {} ./pgm/"';%include "&dir/pgm/*.sas";''')
			print('''%m_post2s3(studyid=''' + filename +  ''');''')
			sas.submit('''%m_post2s3(studyid=''' + filename +  ''');''')
			sas.submit('''%m_gets3data(studyid=''' + filename + ''',data=*);''')
			sas.endsas()   
# 任务4:执行sas脚本 
task4 = PythonOperator(
	task_id ='download_and_execute_sas_script', 
	python_callable = download_and_execute_sas_script,
	op_kwargs={
			'filename': 'HRS9531-102'
		},
		dag=dag
)

task2>>task3>>task4