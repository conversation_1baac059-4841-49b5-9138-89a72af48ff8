#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive script to process all 377 records and fix Chinese character encoding
"""

import json
import codecs
import re
from collections import defaultdict

def analyze_garbled_patterns(data):
    """
    Analyze patterns in garbled Chinese text to understand the encoding issue
    """
    garbled_patterns = defaultdict(list)
    
    for record in data:
        name = record.get('name', '')
        nameen = record.get('nameen', '')
        
        if name and nameen and name != nameen:
            # Store garbled Chinese with its English equivalent
            garbled_patterns[name].append(nameen)
    
    return garbled_patterns

def create_translation_mapping():
    """
    Create a mapping of English terms to proper Chinese translations
    """
    translation_map = {
        # Basic system functions
        "Website Content": "网站内容",
        "Link Settings": "链接设置",
        "Statistics Settings": "统计设置",
        "Export Configuration Definition": "导出配置定义",

        # Workflow related
        "Workflow Definitions": "工作流定义",
        "Workflow Definition Details": "工作流定义详情",
        "Workflow Status Codelists": "工作流状态代码列表",
        "Workflow Records": "工作流记录",
        "Workflow Details": "工作流详情",

        # Role and user management
        "Role": "角色",
        "Role Data": "角色数据",
        "System Role Mapping": "系统角色映射",
        "Cross-Function Role Setting": "跨功能角色设置",
        "Data Center Managers": "数据中心管理员",
        "Other Team Members": "其他团队成员",

        # Clinical research
        "Clinical Studies": "临床研究",
        "EDC": "电子数据采集",
        "Protocol": "试验方案",
        "RTSM": "随机化试验供应管理",
        "Medical Coding Plan": "医学编码计划",
        "Site and Investigator": "研究中心和研究者",
        "Safety Data Recocilliation": "安全数据核对",
        "Coding Dictionary & System": "编码字典和系统",
        "CDSC SOPs": "CDSC标准操作程序",

        # Communication and project management
        "Communications": "沟通交流",
        "Communication Plan": "沟通计划",
        "Task Transition": "任务交接",
        "Project Plan": "项目计划",
        "Extention Application": "延期申请",
        "External Data Management": "外部数据管理",

        # Common terms that might appear
        "": "",  # Empty string mapping
        "esign_engine": "电子签名引擎",
        "esign_log": "电子签名日志",
        "esign_account": "电子签名账户",
        "attachment": "附件",
        "log_event": "日志事件",
        "modify_history": "修改历史",

        # Add more specific translations based on your domain
    }
    return translation_map

def fix_chinese_encoding_comprehensive(input_file, output_file=None):
    """
    Comprehensive fix for all 377 records
    """
    if output_file is None:
        output_file = input_file.replace('.json', '_chinese_fixed.json')
    
    try:
        # Load the cleaned JSON data
        with codecs.open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"Processing {len(data)} records...")
        
        # Get translation mapping
        translation_map = create_translation_mapping()
        
        # Analyze patterns
        patterns = analyze_garbled_patterns(data)
        print(f"Found {len(patterns)} unique garbled patterns")
        
        # Process each record
        fixed_count = 0
        for i, record in enumerate(data):
            original_name = record.get('name', '')
            english_name = record.get('nameen', '')
            
            # Try to fix using English translation
            if english_name and english_name in translation_map:
                record['name'] = translation_map[english_name]
                fixed_count += 1
            elif original_name and not english_name:
                # Keep original if no English name available
                pass
            elif not original_name and english_name:
                # Use English name if no Chinese name
                record['name'] = english_name
            
            # Progress indicator
            if (i + 1) % 50 == 0:
                print(f"Processed {i + 1}/{len(data)} records...")
        
        # Save the fixed data
        with codecs.open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ Processing complete!")
        print(f"📊 Fixed {fixed_count} records with proper Chinese translations")
        print(f"💾 Saved to: {output_file}")
        
        return True, data
        
    except Exception as e:
        print(f"❌ Error processing records: {e}")
        return False, None

def generate_translation_report(data, output_file="translation_report.txt"):
    """
    Generate a report of all English-Chinese pairs for manual review
    """
    try:
        patterns = analyze_garbled_patterns(data)
        
        with codecs.open(output_file, 'w', encoding='utf-8') as f:
            f.write("TRANSLATION REPORT - All English-Chinese Pairs\n")
            f.write("=" * 60 + "\n\n")
            
            # Group by English names
            english_to_garbled = defaultdict(set)
            for garbled, english_list in patterns.items():
                for english in english_list:
                    if english:  # Only if English name exists
                        english_to_garbled[english].add(garbled)
            
            for english, garbled_set in sorted(english_to_garbled.items()):
                f.write(f"English: {english}\n")
                for garbled in garbled_set:
                    f.write(f"  Garbled: {garbled}\n")
                f.write(f"  Suggested Chinese: [NEEDS TRANSLATION]\n")
                f.write("-" * 40 + "\n")
        
        print(f"📋 Translation report saved to: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ Error generating report: {e}")
        return False

def create_minimal_dataset(data, output_file="minimal_dataset.json"):
    """
    Create a minimal dataset with only essential fields for easier review
    """
    try:
        minimal_data = []
        
        for record in data:
            minimal_record = {
                "id": record.get('id', ''),
                "name": record.get('name', ''),
                "nameen": record.get('nameen', ''),
                "uuid": record.get('uuid', '')
            }
            minimal_data.append(minimal_record)
        
        with codecs.open(output_file, 'w', encoding='utf-8') as f:
            json.dump(minimal_data, f, ensure_ascii=False, indent=2)
        
        print(f"📄 Minimal dataset saved to: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating minimal dataset: {e}")
        return False

if __name__ == "__main__":
    input_file = "json_simple_fixed.json"
    
    print("🚀 Starting comprehensive processing of all 377 records...")
    print("=" * 60)
    
    # Step 1: Fix what we can automatically
    success, data = fix_chinese_encoding_comprehensive(input_file)
    
    if success and data:
        # Step 2: Generate translation report for manual review
        generate_translation_report(data)
        
        # Step 3: Create minimal dataset for easier review
        create_minimal_dataset(data)
        
        print("\n✨ All processing complete!")
        print("\nFiles created:")
        print("1. json_simple_fixed_chinese_fixed.json - Main output with fixed Chinese")
        print("2. translation_report.txt - Report for manual translation review")
        print("3. minimal_dataset.json - Simplified version for easier review")
    else:
        print("❌ Processing failed!")
