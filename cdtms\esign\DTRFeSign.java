package net.bioknow.cdtms.esign;

import net.bioknow.cdtms.lightpdfSign.DAOLightpdfSignIntegrate;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTRecordFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


public class DTRFeSign extends DTRecordFuncAction {


	public boolean canUse(int auth, String tableid, Long recordid) {

		try {
			String projectId = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng=new DAODataMng(projectId);

			List<Map<String, String>> eSignIntegrateList = DAOeSignIntegrate.listRule(projectId);
			if (CollectionUtils.isEmpty(eSignIntegrateList)) {
				return  false;
			}



			List engineList = daoDataMng.listRecord("esign_engine","obj.tableid='"+tableid+"'",null,100);

			if (CollectionUtils.isEmpty(engineList)) {
				return  false;
			}

			Map engineMap = (Map) engineList.get(0);
			String showWhere = (String) engineMap.get("where");

			if (StringUtils.isEmpty(showWhere)) {
				return true;
			}
			int count = daoDataMng.count(tableid, "obj.id=" + recordid + " and (" + showWhere + ")");
			if(count>0){
				return true;
			}

		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}

	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBean fpb) {
		try {

			String projectId = SessUtil.getSessInfo().getProjectid();
			String recordid = fpb.getRecordid();
			String tableid = fpb.getTableid();
			DAODataMng daoDataMng=new DAODataMng(projectId);
			List esignInstanceList = daoDataMng.listRecord("esign_instance", "obj.active = 1 and obj.tableid='" + tableid + "' and obj.recordid='" + recordid + "' and obj.sign_flow_id is not null", null, 1);

			if (!CollectionUtils.isEmpty(esignInstanceList)) {
				this.redirectByUri(request, response,"/eSignIntergrate.View.do?recordid="+recordid+"&tableid="+tableid);
				return;
//
			}
//

			this.redirectByUri(request, response,"/eSignIntergrate.eSign.do?recordid="+recordid+"&tableid="+tableid);



		} catch (Exception e) {
			Log.error("",e);
		}
	}

	public FuncInfoBean getFIB(String tableid) {
		FuncInfoBean fib = new FuncInfoBean();
		try{
			fib.setName("在线签署");
			fib.setType(FuncInfoBean.FUNCTYPE_MASKDIV);
			fib.setWinHeight(800);
			fib.setWinWidth(800);
			fib.setSimpleViewShow(true);
		} catch (Exception e) {
			Log.error("",e);
		}
		return fib;
	}

}
