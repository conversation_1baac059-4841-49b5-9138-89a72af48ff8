  public static void smbGetFile(String shareUrl, String localDirectory) throws Exception {
        NtlmPasswordAuthentication auth = new NtlmPasswordAuthentication("", "zhouh36", "HR9cf3cbd8");
        SmbFile remoteFile = new SmbFile(shareUrl, auth);
        if (!remoteFile.exists()) {
            log.info("共享文件不存在");
            return;
        }
        // 有文件的时候再初始化输入输出流
        InputStream in = null;
        OutputStream out = null;
        log.info("下载共享目录的文件 shareUrl 到 localDirectory");
        try {
            String fileName = remoteFile.getName();
            File localFile = new File(localDirectory + File.separator + fileName);
            File fileParent = localFile.getParentFile();
            if (null != fileParent && !fileParent.exists()) {
                fileParent.mkdirs();
            }
            in = new BufferedInputStream(new SmbFileInputStream(remoteFile));
            out = new BufferedOutputStream(new FileOutputStream(localFile));
            byte[] buffer = new byte[1024];
            while (in.read(buffer) != -1) {
                out.write(buffer);
                buffer = new byte[1024];
            }
            out.flush(); //刷新缓冲区输出流

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            out.close();
            in.close();
        }
    }